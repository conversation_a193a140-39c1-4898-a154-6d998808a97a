package com.xmhaibao.account.dialog

import android.os.Bundle
import android.os.CountDownTimer
import android.view.View
import com.xmhaibao.account.bean.AccountNewFemaleApplyWindowBean
import com.xmhaibao.account.bean.AccountWechatInfoBean
import com.xmhaibao.account.databinding.AccountQuickApplyDialogBinding
import com.xmhaibao.account.helper.AccountNewFemaleMvpHelper
import com.xmhaibao.account.repository.AccountRepository
import com.xmhaibao.account.tracker.AccountCommonTracker
import hb.utils.AppUtils
import hb.utils.ToastUtils
import hb.xrequest.RequestCallback
import hb.xstyle.xdialog.XLoadingDialog
import hb.xstyle.xdialogfragment.XDialogFragment
import kale.sharelogin.LoginListener
import kale.sharelogin.ShareLoginLib
import kale.sharelogin.weixin.WeiXinPlatform

/**
 * 类描述：快提现奖励弹窗
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
class AccountQuickApplyDialog : XDialogFragment(), View.OnClickListener {
    private val binding by lazy { AccountQuickApplyDialogBinding.inflate(layoutInflater) }
    private var autoCloseCountdown: CountDownTimer? = null
    private lateinit var accountApplyWindowBean: AccountNewFemaleApplyWindowBean
    private var dismissCallback: (() -> Unit)? = null

    companion object {
        fun newInstance(
            accountApplyWindowBean: AccountNewFemaleApplyWindowBean,
            callback: (() -> Unit)? = null
        ) =
            AccountQuickApplyDialog().apply {
                dismissCallback = callback
                val params = Bundle()
                params.putSerializable(
                    AccountNewFemaleMvpHelper.KEY_ACCOUNT_APPLY_BEAN, accountApplyWindowBean
                )
                arguments = params
            }
    }

    override fun onCreateContentView() = binding.root

    override fun initConfig(argumentBundle: Bundle) {
        setConfig(
            ConfigStyle.CENTER.config()
                .setCancelable(false)
                .setCanceledOnTouchOutside(false)
                .setLeftAndRightMargin(0)
        )
    }

    override fun initViews(rootView: View?) {
        binding.ivAlipay.setOnClickListener(this)
        binding.ivWechat.setOnClickListener(this)
        binding.tvClose.setOnClickListener(this)

    }

    override fun initData(savedInstanceState: Bundle?) {
        accountApplyWindowBean =
            arguments?.getSerializable(AccountNewFemaleMvpHelper.KEY_ACCOUNT_APPLY_BEAN) as AccountNewFemaleApplyWindowBean
        AccountCommonTracker.Instance.newFemaleWithdrawPopupExpo(accountApplyWindowBean.scene)
        binding.ivDialogBg.setImageFromUrl(accountApplyWindowBean.bgImg)
        binding.tvAwardNum.text = accountApplyWindowBean.amount ?: ""
        binding.tvAwardUnit.text = accountApplyWindowBean.unit ?: ""
        autoCloseCountdown =
            object : CountDownTimer(accountApplyWindowBean.countDownInt() * 1000L, 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    binding.tvClose.text = "我不要钱，关闭弹窗(${(millisUntilFinished / 1000) + 1})"
                }

                override fun onFinish() {
                    binding.tvClose.isEnabled = true
                    binding.tvClose.text = "我不要钱，关闭弹窗"
                }
            }
        autoCloseCountdown!!.start()
    }

    override fun onClick(v: View?) {
        when (v) {
            //支付宝提现
            binding.ivAlipay -> handleCashByType(AccountNewFemaleMvpHelper.CASH_TYPE_ALIPAY)
            //微信提现
            binding.ivWechat -> {
                // TODO 由于支付宝提现渠道暂未打通，先调用handleCashByWechat，打通后重新调用handleCashByType
                handleCashByWechat()
//                handleCashByType(AccountNewFemaleMvpHelper.CASH_TYPE_WECHAT)
            }
            // 关闭弹窗
            binding.tvClose -> closeAndCancelCountdown()
        }
    }

    private fun closeAndCancelCountdown() {
        dismissCallback?.invoke()
        AccountCommonTracker.Instance.newFemaleWithdrawPopupClick(
            "关闭",
            accountApplyWindowBean.scene
        )
        autoCloseCountdown?.cancel()
        dismiss()
    }

    /**
     * 根据用户选择的提现类型进行提现（支付宝or微信）
     */
    private fun handleCashByType(cashType: String) {
        AccountCommonTracker.Instance.newFemaleWithdrawPopupClick(
            if (AccountNewFemaleMvpHelper.CASH_TYPE_ALIPAY == cashType) "支付宝提现" else "微信提现",
            accountApplyWindowBean.scene
        )
        AccountCashByTypeDialog.newInstance(cashType, accountApplyWindowBean) {
            dismiss()
        }.showDialog(parentFragmentManager.beginTransaction())
    }

    /**
     * 微信提现——先微信授权，成功后再弹窗确认（头像、昵称）
     */
    private fun handleCashByWechat() {
        XLoadingDialog.showLoadingbar(context)
        if (ShareLoginLib.isAppInstalled(AppUtils.getApp(), WeiXinPlatform::class.java).not()) {
            XLoadingDialog.hideLoadingbar()
            ToastUtils.show("请下载微信")
            return
        }
        AccountCommonTracker.Instance.newFemaleWithdrawPopupClick(
            "微信提现", accountApplyWindowBean.scene
        )
        activity?.let {
            ShareLoginLib.doLogin(it, WeiXinPlatform.LOGIN, object : LoginListener() {
                override fun onReceiveToken(
                    accessToken: String, uId: String, expiresIn: Long,
                    wholeData: String?
                ) {
                    super.onReceiveToken(accessToken, uId, expiresIn, wholeData)
                    requestWechatUserInfo(uId)
                }

                override fun onError(errorMsg: String) {
                    XLoadingDialog.hideLoadingbar()
                    super.onError(errorMsg)
                    ToastUtils.show("$errorMsg")
                }

                override fun onCancel() {
                    XLoadingDialog.hideLoadingbar()
                    super.onCancel()
                }
            })
        }
    }

    /**
     * 获取微信信息
     */
    private fun requestWechatUserInfo(code: String) {
        AccountRepository.queryWechatUserInfo(code)
            .execute(object : RequestCallback<AccountWechatInfoBean>() {
                override fun onSuccess(isCache: Boolean, wechatInfoBean: AccountWechatInfoBean?) {
                    XLoadingDialog.hideLoadingbar()
                    if (wechatInfoBean == null || wechatInfoBean.openId.isNullOrBlank()) {
                        ToastUtils.show("授权失败，请重试")
                        return
                    }
                    wechatInfoBean.amount = accountApplyWindowBean.amount
                    wechatInfoBean.unit = accountApplyWindowBean.unit
                    wechatInfoBean.scene = accountApplyWindowBean.scene
                    AccountWechatApplyDialog.newInstance(wechatInfoBean) {
                        dismiss()
                    }.showDialog(parentFragmentManager.beginTransaction())
                }

                override fun onFailure(code: Int, responseStatus: String?, responseMsg: String?) {
                    XLoadingDialog.hideLoadingbar()
                    super.onFailure(code, responseStatus, responseMsg)
                }
            })
    }
}