package com.xmhaibao.account.page.personalprofile.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import cn.taqu.lib.base.router.RouterLaunch
import cn.taqu.lib.base.utils.SexTypeUtils
import com.xmhaibao.account.databinding.PersonalProfileMedalViewBinding
import com.xmhaibao.account.page.personalprofile.bean.PersonalProfileMedalBean
import hb.utils.StringUtils

/**
 * 个人主页勋章信息
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
class PersonalProfileMedalView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val binding: PersonalProfileMedalViewBinding by lazy {
        PersonalProfileMedalViewBinding.inflate(LayoutInflater.from(context), this)
    }

    /**
     * 是否主态
     */
    private var isMySelf = false

    /**
     * 勋章信息
     */
    private var medalBean: PersonalProfileMedalBean? = null


    override fun onFinishInflate() {
        super.onFinishInflate()
        init()
    }

    private fun init() {
        binding.tvWealthLevel.setOnClickListener {
            if (isMySelf) {
                RouterLaunch.dealJumpData(context, medalBean?.wealthRelation)
            }
        }

        binding.tvCharmLevel.setOnClickListener {
            if (isMySelf) {
                RouterLaunch.dealJumpData(context, medalBean?.charmRelation)
            }
        }

    }

    /**
     * 设置个人信息
     *
     * @param info 用户信息
     * @param isMySelf 是否主态
     */
    fun setData(info: PersonalProfileMedalBean?, isMySelf: Boolean) {
        info?.apply {
            <EMAIL> = isMySelf
            medalBean = this
            //设置财富值（）
            if (StringUtils.isNotEmpty(wealthLevel) && StringUtils.stringToInt(wealthLevel) >= 0) {
                if (isMySelf || SexTypeUtils.isSexTypeBoy(sexType)) {
                    //男性且财富值大于0，不显示财富值
                    binding.tvWealthLevel.visibility = VISIBLE
                    binding.tvWealthLevel.text = "财富级 $wealthLevel"
                } else {
                    binding.tvWealthLevel.visibility = GONE
                }
            } else {
                binding.tvWealthLevel.visibility = GONE
            }
            //设置魅力值
            if (StringUtils.isNotEmpty(charmLevel) && StringUtils.stringToInt(charmLevel) >= 0) {
                binding.tvCharmLevel.visibility = VISIBLE
                binding.tvCharmLevel.text = "活力级 $charmLevel"
            } else {
                binding.tvCharmLevel.visibility = GONE
            }

            //设置v标
            if (medalName.isNullOrEmpty()) {
                binding.tvMedal.visibility = GONE
            } else {
                binding.tvMedal.visibility = VISIBLE
                binding.tvMedal.text = medalName
            }
        }
    }
}