package com.xmhaibao.account.liveauth.utils

import android.app.Activity
import android.os.Environment
import android.os.Looper
import android.util.Base64
import cn.taqu.lib.base.constants.XUploadBucket
import cn.taqu.lib.base.constants.XUploadTags
import cn.taqu.lib.base.sdk.face.OnTxCloudFaceSdkResultCallback
import cn.taqu.lib.base.sdk.face.TxCloudFaceUtils
import com.xmhaibao.account.contract.IAccountCompleteView
import com.xmhaibao.account.liveauth.bean.FaceCompareResultForSceneBean
import com.xmhaibao.account.repository.AccountRepository
import com.xmhaibao.core.utils.UiHandler
import com.xmhaibao.livedetect.bean.LiveDetectionTxPreInfoBean
import hb.logupload.XLogUpload
import hb.upload.file.OnUploadListener
import hb.upload.file.XUpload
import hb.upload.file.XUploadResponseInfo
import hb.utils.*
import hb.xrequest.RequestCallback
import hb.xstyle.xdialog.XLoadingDialog
import hb.xthread.XThreadPool
import java.io.File
import java.io.FileOutputStream

/**
 * 人脸比对（场景）
 * 2023年 2月1期新增需求
 * 需求：https://o15vj1m4ie.feishu.cn/wiki/wikcn1g9rgt2NcWtlrGsXUPYPph
 * 技术：https://o15vj1m4ie.feishu.cn/wiki/wikcn1g9rgt2NcWtlrGsXUPYPph
 *
 * <AUTHOR>
 * @date 2023-02-07
 */
class FaceCompareForSceneHelper(
    private var mActivity: Activity?,
    /**
     * mScene 认证场景 参考 FaceCompareForSceneHelper.LIVE_AUTH_SCENE_xxx
     */
    private val mScene: String? = ""
) {
    companion object {
        private const val TAG = "FaceLiveAuthForSceneHelper"

        /**
         * 人脸比对-高风险行为限制场景
         */
        const val LIVE_AUTH_SCENE_HIGH_RISK_AUTH = "HIGH_RISK_CERT"
    }

    /**
     * 检测结果回调
     */
    var mDetectionCallback: DetectionResultCallback? = null

    /**
     * 开始检测
     * @param detectionCallback 检测结果回调
     */
    fun startDetection(detectionCallback: DetectionResultCallback?) {
        mDetectionCallback = detectionCallback
        requestPreVerifyData()
    }

    fun showLoadingBar() {
        val activity = mActivity ?: return
        if (activity is IAccountCompleteView) {
            activity.showLoadingBar()
            return
        }
        XLoadingDialog.showLoadingbar(activity)

    }

    fun hideLoadingBar() {
        val activity = mActivity ?: return
        if (activity is IAccountCompleteView) {
            activity.hideLoadingBar()
            return
        }
        XLoadingDialog.hideLoadingbar()
    }

    /**
     * 请求配置信息
     */
    private fun requestPreVerifyData() {
        showLoadingBar()
        AccountRepository.getFaceDetectConfigForScene(mScene)
            .execute(object : RequestCallback<LiveDetectionTxPreInfoBean?>() {
                override fun onSuccess(isCache: Boolean, obj: LiveDetectionTxPreInfoBean?) {
                    if (obj == null) {
                        notifyOnFailure("获取人脸检测配置失败,请稍后重试")
                        return
                    }
                    callDetectionSDK(obj)
                }

                override fun onFailure(code: Int, responseStatus: String?, responseMsg: String?) {
                    notifyOnFailure(responseMsg)
                }

            })
    }

    /**
     * 调用人脸检测sdk
     */
    private fun callDetectionSDK(txPreInfo: LiveDetectionTxPreInfoBean) {
        if (mActivity == null) {
            uploadLogError("activity 为空")
            notifyOnFailure("比对失败：3001")
            return
        }
        //仅活体检测不需要faceId，直接拉起sdk
        TxCloudFaceUtils.openCloudFaceService(
            mActivity, txPreInfo.orderNo, txPreInfo.sign, "",
            txPreInfo.nonce, txPreInfo.userUuid, false,
            object : OnTxCloudFaceSdkResultCallback {
                override fun onTxCloudFaceSdkResult(
                    success: Boolean, userImageString: String?,
                    errorMsg: String?, errorCode: String?
                ) {
                    if (!success) {
                        //检测失败
                        onDetectionSDKResult(false, errorMsg)
                        return
                    }
                    onDetectionSDKResult(true, "")
                }

                override fun onBackPress() {
                    notifyOnFailure("已取消人脸比对操作")
                }
            }, false
        )
    }

    /**
     * 活体检测结果
     * @param  success true 检测成功
     * @param  baseImgUrl 人脸头像url 活体检测成功后才有值
     * @param  sdkErrorMsg 错误信息 人脸检测失败有值
     */
    private fun onDetectionSDKResult(success: Boolean, sdkErrorMsg: String?) {
        if (success) {
            confirmLiveDetectResult(true)
        } else {
            hideLoadingBar()
            confirmLiveDetectResult(false)
            mDetectionCallback?.onFailure(sdkErrorMsg)
            uploadLogError(sdkErrorMsg)
        }
    }

    /**
     * 上传错误日志
     */
    private fun uploadLogError(msg: String?) {
        XLogUpload.newBuilder("FaceCompare_Error")
            .params("msg", msg ?: "")
            .post()
    }

    private fun notifyOnFailure(msg: String?) {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            // 在主线程
            hideLoadingBar()
            mDetectionCallback?.onFailure(msg)
        } else {
            UiHandler.post {
                hideLoadingBar()
                mDetectionCallback?.onFailure(msg)
            }
        }
    }

    /**
     * 从服务端确认检测结果
     * @param baseImgUrl 为空时 表示人脸检测失败 调用这个接口只是为了统计次数
     */
    private fun confirmLiveDetectResult(success: Boolean) {
        AccountRepository.confirmDetectFaceResultForScene(mScene, "")
            .execute(object : RequestCallback<FaceCompareResultForSceneBean>() {
                override fun onSuccess(isCache: Boolean, obj: FaceCompareResultForSceneBean?) {
                    if (!success) {
                        return
                    }
                    //LIVE_AUTH_SCENE_HIGH_RISK_AUTH场景下 允许返回空
                    if (mScene == LIVE_AUTH_SCENE_HIGH_RISK_AUTH) {
                        onFaceCompareResult(true, obj ?: FaceCompareResultForSceneBean(), "人脸比对成功")
                        return
                    }
                    if (obj == null) {
                        onFaceCompareResult(true, obj, "人脸比对成功")
                    } else {
                        onFaceCompareResult(false, obj, "人脸比对失败")
                    }

                }

                override fun onFailure(code: Int, responseStatus: String?, responseMsg: String?) {
                    if (!success) {
                        return
                    }
                    onFaceCompareResult(false, null, responseMsg)
                }

            })

    }

    /**
     * 人脸比对结果
     */
    private fun onFaceCompareResult(
        success: Boolean,
        resultForScene: FaceCompareResultForSceneBean?,
        errorMsg: String?
    ) {
        hideLoadingBar()
        if (success) {
            mDetectionCallback?.onSuccess(resultForScene)
        } else {
            mDetectionCallback?.onFailure(errorMsg)
        }
    }

    /**
     * 人脸比对结果
     *
     * <AUTHOR>
     * @date 2022-07-01
     */
    interface DetectionResultCallback {
        /**
         * 检测成功
         * @param  forSceneBean 比对成功服务端返回
         */
        fun onSuccess(forSceneBean: FaceCompareResultForSceneBean?)

        /**
         * 检测失败
         */
        fun onFailure(msg: String?)

    }
}