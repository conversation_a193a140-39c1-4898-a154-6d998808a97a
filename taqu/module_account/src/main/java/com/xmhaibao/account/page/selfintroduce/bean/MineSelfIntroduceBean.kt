package com.xmhaibao.account.page.selfintroduce.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import com.xmhaibao.account.page.selfintroduce.bean.MineSelfIntroducePicBean.Companion.TYPE_IMG_URL
import hb.common.helper.HostHelper
import hb.utils.*

/**
 * <AUTHOR>
 * @desc 自我介绍实体类
 * @date 2021-12-15 9:21
 */
class MineSelfIntroduceBean : IDoExtra {

    /**
     * 图片地址
     */
    @SerializedName("pass_introduction_imgs")
    var picUrls= ArrayList<MineSelfIntroducePicBean>()

    /**
     * 内容
     */
    @SerializedName("pass_introduction_content")
    var content: String? = ""

    /**
     * 看看别人怎么写公共跳转
     */
    @SerializedName("otherIntroductionUrl")
    var otherIntroductionUrl: String? = null

    /**
     * 审核状态
     */
    @SerializedName("pass_introduction_status")
    var status = ""

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        //需要判断是否为本地图片还是网络图片
        picUrls.forEach {
            if (StringUtils.isNotEmpty(it.picUrl)) {
                it.picUrl = WebpUtils.getWebpUrl(it.picUrl, HostHelper.getInstance().hostAvatar)
                it.picType = TYPE_IMG_URL
            }
        }

        if (CollectionUtils.isEmpty(picUrls) || picUrls.size < 2 && !hasAddBtn(picUrls)) {
            picUrls.add(MineSelfIntroducePicBean.getDefaultAddBean())
        }
    }

    companion object{

        /**
         * 是否存在添加按钮
         */
        fun hasAddBtn(picUrls:ArrayList<MineSelfIntroducePicBean>): Boolean {
            picUrls.forEach {
                if (it.picType == MineSelfIntroducePicBean.TYPE_ADD) {
                    return true
                }
            }
            return false
        }
    }

}
