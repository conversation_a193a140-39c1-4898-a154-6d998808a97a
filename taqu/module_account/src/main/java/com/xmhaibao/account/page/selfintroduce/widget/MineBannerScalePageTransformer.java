package com.xmhaibao.account.page.selfintroduce.widget;

import android.view.View;

import androidx.viewpager2.widget.ViewPager2;

/**
 * ViewPage 移动放大缩小 带透明度
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
public class MineBannerScalePageTransformer implements ViewPager2.PageTransformer {

    final float SCALE_MAX = 0.8f;
    final float ALPHA_MAX = 0.5f;


    @Override
    public void transformPage(View page, float position) {
        float scale = (position < 0)
                ? ((1 - SCALE_MAX) * position + 1)
                : ((SCALE_MAX - 1) * position + 1);
        float alpha = (position < 0)
                ? ((1 - ALPHA_MAX) * position + 1)
                : ((ALPHA_MAX - 1) * position + 1);
        if (position < 0) {
            page.setPivotX(page.getWidth());
            page.setPivotY((float) page.getHeight() /2);
        } else {
            page.setPivotX(0);
            page.setPivotY((float) page.getHeight() /2);
        }
        page.setScaleX(scale);
        page.setScaleY(scale);
        page.setAlpha(Math.abs(alpha));
    }
}