package com.xmhaibao.account.page.accountdestroy.bean

import androidx.room.Ignore
import com.google.gson.annotations.SerializedName

data class AccountDestroyReasonBean(
    @SerializedName("list")
    var concreteReason: List<AccountDestroyReasonBeanItem>? = null
)

data class AccountDestroyReasonBeanItem(
    @SerializedName("children")
    var concreteReason: List<ConcreteReason>? = null,
    @SerializedName("reason_id")
    var reasonId: String? = null,
    @SerializedName("content")
    var content: String? = null,

    @Ignore
    var isCheck: Boolean = false
) {
    /**
     * “其它”类型（用户手动输入原因）
     */
    fun isOtherReason() = reasonId == "0"
}

data class ConcreteReason(
    @SerializedName("reason_id")
    var reasonId: String? = null,
    @SerializedName("content")
    var content: String? = null,

    @Ignore
    var isCheck: Boolean = false,
    @Ignore
    var parentId: String? = null
)



