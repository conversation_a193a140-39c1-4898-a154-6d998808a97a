package com.xmhaibao.account.page.accountdestroy.viewholder

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.isNotEmpty
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import com.xmhaibao.account.R
import com.xmhaibao.account.databinding.AccountNewDestroyReasonItemBinding
import com.xmhaibao.account.page.accountdestroy.bean.AccountDestroyReasonBeanItem
import com.xmhaibao.account.page.accountdestroy.bean.ConcreteReason
import dp
import hb.drawable.shape.extend.shape
import hb.kotlin_extension.getCompatColor
import hb.xadapter.XBaseViewHolder
import padding_bottom
import padding_end
import padding_start
import padding_top

/**
 * 类描述：
 *
 * <AUTHOR>
 * @date 2025-04-10
 */
class AccountDestroyReasonViewHolder(parent: ViewGroup) :
    XBaseViewHolder<AccountDestroyReasonBeanItem>(parent, R.layout.account_new_destroy_reason_item),
    View.OnClickListener {

    private val binding = AccountNewDestroyReasonItemBinding.bind(itemView)
    private var listener: OnItemCheckChangeListener? = null
    private var parentReasonItem: AccountDestroyReasonBeanItem? = null


    init {
        binding.tvTitle.setOnClickListener(this)

        binding.etInputTxt.addTextChangedListener(afterTextChanged = { text ->
            listener?.onChildItemChange(parentItem = parentReasonItem, content = text.toString())
        })
    }


    override fun onBindView(item: AccountDestroyReasonBeanItem) {
        // 设置标题文本并标记当前项
        binding.tvTitle.tag = item
        binding.tvTitle.text = item.content
        binding.cbCheck.isChecked = item.isCheck
        parentReasonItem = item
        // 根据 isCheck 状态更新 UI 可见性
        updateUiVisibility(item)
    }

    private fun inflateChildViews(item: AccountDestroyReasonBeanItem) {
        if (binding.flowReasons.isNotEmpty()) {
            binding.flowReasons.removeAllViews()
        }

        item.concreteReason?.forEach {
            it.isCheck = false
            val view = createLabelView(it)
            binding.flowReasons.addView(view)
        }
    }

    /**
     * 更新 UI 元素的可见性
     */
    private fun updateUiVisibility(item: AccountDestroyReasonBeanItem) {
        val isFlowReasonsVisible = item.isCheck && !item.isOtherReason()
        val isEtInputTxtVisible = item.isCheck && item.isOtherReason()
        binding.flowReasons.isVisible = isFlowReasonsVisible
        binding.etInputTxt.isVisible = isEtInputTxtVisible
        if (isFlowReasonsVisible) {
            inflateChildViews(item)
        }
    }


    override fun onClick(v: View) {
        when (val bean = v.tag) {
            // 点击父级标签，切换item
            is AccountDestroyReasonBeanItem -> {
                if (bean.isCheck) {
                    return
                }
                if (bean.isOtherReason()) {
                    val inputContent = binding.etInputTxt.text.toString()
                    listener?.onChildItemChange(content = inputContent)
                } else {
                    listener?.onChildItemChange()
                }
                listener?.onParentItemCheckChange(bindingAdapterPosition)
            }

            // 点击子标签，进行切换
            is ConcreteReason -> {
                if (bean.isCheck) {
                    return
                }
                for (i in 0 until binding.flowReasons.childCount) {
                    val childView = binding.flowReasons.getChildAt(i)
                    val childLabel = childView.tag
                    if (childLabel is ConcreteReason && childLabel.isCheck) {
                        childLabel.isCheck = false
                        childView.shape(
                            radius = 20f.dp,
                            solidColor = v.context.getCompatColor(cn.taqu.lib.base.R.color.TH_Navy100)
                        )
                    }
                }

                bean.isCheck = true
                listener?.onChildItemChange(parentItem = parentReasonItem, childItem = bean)
                v.shape(
                    radius = 20f.dp,
                    solidColor = v.context.getCompatColor(cn.taqu.lib.base.R.color.TH_Yellow600)
                )

            }
        }

    }


    /**
     * 创建子标签视图
     */
    private fun createLabelView(label: ConcreteReason): TextView {
        return TextView(itemView.context).apply {
            text = label.content
            setTextColor(context.getCompatColor(cn.taqu.lib.base.R.color.TH_Gray990))
            shape(
                radius = 20f.dp,
                solidColor = if (label.isCheck) context.getCompatColor(cn.taqu.lib.base.R.color.TH_Yellow600)
                else context.getCompatColor(cn.taqu.lib.base.R.color.TH_Navy100)
            )
            layoutParams = com.wefika.flowlayout.FlowLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).also {
                it.bottomMargin = 10.dp
                it.rightMargin = 10.dp
            }
            padding_start = 10
            padding_end = 10
            padding_top = 8
            padding_bottom = 8
            textSize = 12f
            setOnClickListener(this@AccountDestroyReasonViewHolder)
            tag = label
        }
    }

    fun onItemCheckChangeListener(listener: OnItemCheckChangeListener) {
        this.listener = listener
    }

    interface OnItemCheckChangeListener {
        fun onParentItemCheckChange(newPosition: Int)
        fun onChildItemChange(
            parentItem: AccountDestroyReasonBeanItem? = null,
            childItem: ConcreteReason? = null,
            content: String? = null
        )
    }
}