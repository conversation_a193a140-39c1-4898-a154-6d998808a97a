package com.xmhaibao.account.widget

import android.content.Context
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import com.xmhaibao.account.bean.AccountNewFemaleApplyBean
import com.xmhaibao.account.databinding.AccountMvpCashNotifyViewBinding
import com.xmhaibao.account.tracker.AccountCommonTracker
import hb.utils.ActivityUtils
import hb.utils.ColorUtils
import hb.utils.SizeUtils
import hb.utils.SpanUtils

/**
 * 类描述：快提现成功后的站内吸顶通知view
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
class AccountMvpCashNotifyView(context: Context) : ConstraintLayout(context) {
    private val binding: AccountMvpCashNotifyViewBinding =
        AccountMvpCashNotifyViewBinding.inflate(LayoutInflater.from(context), this)

    /**
     * 5s后滚出动效
     */
    private var countDown: CountDownTimer? = null

    /**
     * 显示站内吸顶通知（滑出动效）
     */
    fun show(applyBean: AccountNewFemaleApplyBean, ceilingType: String) {
        AccountCommonTracker.Instance.newFemaleNextDayCeilingExpo(
            applyBean.selectedCashType, ceilingType
        )
        // 添加到根布局
        val rootContent =
            ActivityUtils.getTopActivity().findViewById<ViewGroup>(android.R.id.content) ?: return
        val distance = height.toFloat() + SizeUtils.dp2px(48f)
        val duration = 300L
        this.translationY = -distance
        rootContent.addView(
            this,
            ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
        )
        binding.ivCashType.setImageFromUrl(applyBean.icon)
        // 标题内容
        binding.tvTitle.text = SpanUtils()
            .append(applyBean.title ?: "")
            .setForegroundColor(ColorUtils.getColor(hb.xstyle.R.color.TH_Navy990))
            .append(applyBean.titleLight ?: "")
            .setForegroundColor(ColorUtils.getColor(hb.xstyle.R.color.TH_Red600))
            .create()
        // 描述内容
        applyBean.desc?.let { binding.tvDesc.text = it }
        // 出现动画
        animate().translationY(0f).setDuration(duration).start()
        // 10s后消失
        countDown = object : CountDownTimer(10000, 1000) {
            override fun onTick(millisUntilFinished: Long) {}
            override fun onFinish() {
                animate().translationY(-distance).setDuration(duration)
                    .withEndAction { rootContent.removeView(this@AccountMvpCashNotifyView) }.start()
            }
        }.start()
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        // 消耗事件，避免继续传递误触到首页其他view
        return true
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        animate().cancel()
        countDown?.cancel()
    }

}