package com.xmhaibao.account.code;

import android.app.Activity;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chuanglan.shanyan_sdk.view.CmccLoginAuthActivity;
import com.cmic.gen.sdk.view.GenLoginAuthActivity;
import com.xmhaibao.account.api.bean.XjbAccountInfo;
import com.xmhaibao.account.api.helper.AccountInfoEditHelper;
import com.xmhaibao.account.api.helper.AccountInfoHelper;
import com.xmhaibao.account.helper.AccountRegisterInviteCodeHelper;
import com.xmhaibao.account.interf.ICheckNickNameCallback;
import com.xmhaibao.account.repository.AccountRepository;
import com.xmhaibao.account.utils.AccountBaseUtils;

import org.json.JSONObject;

import cn.taqu.lib.base.common.AppAccountBean;
import cn.taqu.lib.base.constants.CommonConstants;
import cn.taqu.lib.base.event.EventRegisteredSuccess;
import cn.taqu.lib.base.helper.AddrInfoHelper;
import cn.taqu.lib.base.http.LifeCycleGsonCallBack;
import cn.taqu.lib.base.router.ARouterManager;
import cn.taqu.lib.base.utils.LogUploadUtil;
import cn.taqu.lib.okhttp.callback.BaseCallback;
import cn.taqu.lib.okhttp.callback.GsonCallBack;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import hb.common.data.AccountHelper;
import hb.location.bean.LocationIdBean;
import hb.utils.ActivityUtils;
import hb.utils.Loger;
import hb.utils.StringUtils;
import hb.utils.ToastUtils;
import hb.xrequest.XRequest;
import hb.xstatic.tools.XEventBus;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * @author：杨浩 项目：taqu
 * 创建日期：2019/5/9
 * 功能简介：
 */
public class AccountProcess {
    private static final String TAG = "AccountProcess";

    /**
     * 昵称违规词未命中
     */
    public static String PASS = "pass";
    /**
     * 昵称违规词命中
     */
    public static String BLOCK = "block";

    /**
     * 获取验证码
     *
     * @param context
     * @param mobile      手机号
     * @param codeType    验证码类型
     * @param sendType
     * @param responseDao 回调
     */
    public static void getVerifyCode(Context context, String mobile, int codeType, String sendType, BaseCallback<Object> responseDao) {
        // code_type 0：忘记密码，1:注册，2:绑定手机号码，3:修改手机号码
        Loger.e(TAG, "mobile=" + mobile);
        if (!StringUtils.isNumeric(mobile)) {
            mobile = "";
        }
        if (StringUtils.isEmpty(mobile)) {
            return;
        }
        XRequest<Object> request;
        if (codeType == CommonConstants.CODE_TYPE_FORGET_PWD) {
            request = AccountRepository.sendResetPasswordCode(mobile, sendType);
        } else if (codeType == CommonConstants.CODE_TYPE_BIND_PHONE) {
            request = AccountRepository.sendBindMobileCode(AccountHelper.getUserTicketId(), mobile, sendType);
        } else if (codeType == CommonConstants.CODE_TYPE_MODIFY_PHONE) {
            //更换手机,用另外一个接口UrlAccount.SEND_MODIFY_MOBILECODE。
            return;
        } else {
            ToastUtils.makeToast(context, "异常code=" + codeType);
            return;
        }
        request.execute(responseDao);
    }

    public static boolean topActivityIsShanYan() {
        Activity topActivity = ActivityUtils.getTopActivity();
        if (topActivity == null) {
            return false;
        }
//        移动授权页
        boolean b = topActivity instanceof GenLoginAuthActivity;
//        联通授权页
        boolean b1 = topActivity instanceof CmccLoginAuthActivity;
        return b1 || b;
    }


    public static void postNickName(String nickName, boolean isReConfirm, BaseCallback<XjbAccountInfo> responseDao) {
        AccountRepository.setAccountNickname(AccountHelper.getTicketId(), nickName, isReConfirm)
                .execute(responseDao);
    }

    /**
     * 检测昵称违规
     */
    public static void checkNickNameViolation(String name, Context context, ICheckNickNameCallback callback) {
        if (StringUtils.isEmpty(name) || context == null || callback == null) {
            return;
        }
        AccountRepository.postCheckNickname(AccountHelper.getTicketId(), name)
                .execute(new LifeCycleGsonCallBack<String>(context) {


                    @Override
                    public String parseResponse(boolean isCache, @NonNull IResponseInfo response) throws Exception {
                        String suggestion = PASS;
                        if (response != null && response.getDataObject() != null) {
                            suggestion = response.getDataObject().optString("suggestion");
                            //检测人
                            String checker = response.getDataObject().optString("checker");
                        }
                        return suggestion;
                    }

                    @Override
                    public void onLifeCycleFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {

                    }


                    /**
                     * @param isCache
                     * @param suggestion block-命中敏感词，pass-未命中敏感词
                     * @param response
                     */
                    @Override
                    public void onLifeCycleSuccess(boolean isCache, @Nullable String suggestion, @NonNull IResponseInfo response) {
                        if (callback != null) {
                            callback.checkNickName(PASS.equals(suggestion));
                        }
                    }
                });
    }

    /**
     * 请求用户是否首次充值过
     */
    public static void requestAccountRechargeStatus(BaseCallback<String> callback) {
        if (!AccountHelper.isUserLogined()) {
            return;
        }
        AccountRepository.getRechargeStatus(AccountHelper.getUserTicketId())
                .execute(new GsonCallBack<String>() {

                    @Override
                    public String parseResponse(boolean isCache, @NonNull IResponseInfo response) throws Exception {
                        JSONObject dataObject = response.getDataObject();
                        String status = "0";
                        if (dataObject != null) {
                            status = dataObject.optString("status");
                        }
                        return status;
                    }

                    /**
                     *
                     * @param isCache
                     * @param obj   1. 充值过   0未充值过
                     * @param response
                     */
                    @Override
                    public void onSuccess(boolean isCache, @Nullable String obj, @NonNull IResponseInfo response) {
                        AppAccountBean.get().setIsRecharge(obj);
                        if (callback != null) {
                            callback.onSuccess(isCache, obj, response);
                        }
                    }

                    @Override
                    public void onFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {
                        if (callback != null) {
                            callback.onFailure(isServiceFailure, response);
                        }
                    }
                });


    }
    /**
     * 注册成功处理通用逻辑
     * 备注：现在虽然有2个地方调用，但是，AccountRegisteredCompleteAvatarActivity这个界面已经不用了
     *
     * @param inviteType         邀请类型：工会/普通邀请
     * @param mInviteCode        邀请码
     * @param info               注册成功返回的信息
     * @param isNormalRegistered true:普通注册 false:第三方注册
     */
    public static void registeredSuccess(int inviteType, String mInviteCode, XjbAccountInfo info, boolean isNormalRegistered, Function0<Unit> callback) {
        // 登陆成功 不需要发送登录登录成功EventBus
        AccountBaseUtils.loginSuccess(info,true);
        ARouterManager.friendForBaseService().checkHitFriendHomeSlideCard(true, () -> {
            // 用户注册成功监听
            XEventBus.post(new EventRegisteredSuccess());
            if (inviteType == AccountRegisterInviteCodeHelper.TYPE_GUILD) {
                AccountInfoHelper.onBindInviteGuildCode(mInviteCode);
            }
            //2020/8/13 改为普通手机注册才直接绑定邀请码，第三方登陆,需要绑定手机后才进行绑定邀请码
            //2021/03/29 现在第三方注册后，就可以绑定邀请码了，不需要等到绑定手机后
            else if (inviteType == AccountRegisterInviteCodeHelper.TYPE_COMMON) {
                if (AccountRegisterInviteCodeHelper.COMMON_TYPE_USER_FISSION == info.getCommonInviteCodeType()) {
                    //用户裂变邀请
                    String ticketId = AccountHelper.getTicketId();
                    if (info != null && StringUtils.isNotEmpty(info.getTicketId())){
                        ticketId = info.getTicketId();
                    }
                    AccountInfoHelper.onBindInviteCode(ticketId,mInviteCode);
                } else if (AccountRegisterInviteCodeHelper.COMMON_TYPE_UNION == info.getCommonInviteCodeType()) {
                    //趣聊公会邀请(T开头的邀请码)
                    AccountInfoHelper.onBindInviteCodeTalkUnion(mInviteCode);
                }
            }
            LocationIdBean locationIdBean = AddrInfoHelper.getLocationIdBean();
            if (locationIdBean != null &&
                    //有省份，没有省份ID
                    (StringUtils.isEmpty(locationIdBean.getProvinceId())
                            && StringUtils.isNotEmpty(locationIdBean.getProvince())
                            //有城市，没城市ID
                            || StringUtils.isEmpty(locationIdBean.getCityId())
                            && StringUtils.isNotEmpty(locationIdBean.getCity())
                            //省份、城市俩个中有一个是空的
                            || !(StringUtils.isNotEmpty(locationIdBean.getCity())
                            && StringUtils.isNotEmpty(locationIdBean.getProvince()))
                    )
            ) {
                //异常上报
                LogUploadUtil.postBaseAddrError(locationIdBean + " ProvinceId=" + locationIdBean.getProvinceId()
                        + " cityId=" + locationIdBean.getCityId());
            }
            if (locationIdBean != null && StringUtils.isNotEmpty(locationIdBean.getProvinceId())
                    && StringUtils.isNotEmpty(locationIdBean.getCityId())) {
                //注册成功后，将当前定位作为所在地上传
                AccountInfoEditHelper.requestChangeBaseAddr(locationIdBean.getProvinceId() + "," + locationIdBean.getCityId());
            }
            ToastUtils.show("注册成功");
            callback.invoke();
            return null;
        });

    }
}
