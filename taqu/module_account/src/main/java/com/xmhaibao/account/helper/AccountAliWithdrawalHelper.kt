package com.xmhaibao.account.helper

import android.app.Activity
import android.os.Bundle
import com.alipay.sdk.app.OpenAuthTask
import com.xmhaibao.account.bean.AccountAliWithdrawalLoginAuthBean
import com.xmhaibao.xjson.XJson
import hb.logupload.XLogUpload
import hb.utils.ActivityUtils
import hb.utils.Loger
import hb.utils.Utils
import java.lang.ref.WeakReference

/**
 * description:支付宝提前，拉起登录授权
 *
 * <AUTHOR>
 * @date 2022/03/10 18:10
 */
class AccountAliWithdrawalHelper {


    companion object {
        private const val TAG = "AccountAliWithdrawalHelper"

        /**
         * 通用跳转授权业务
         */
        fun openAuthScheme(url: String, callback: Utils.Callback<String>?) {
            Loger.i(TAG, "start auth")
            XLogUpload.newBuilder("Monitor_withdrawAuthStart")
                .text1(url)
                .post()
            val topActivity = ActivityUtils.getTopActivity()
            if (topActivity == null) {
                uploadAuthFailure("open", "topActivity == null", null)
                return
            }

            // 传递给支付宝应用的业务参数
            val bizParams = hashMapOf<String, String>()
            bizParams.put("mqpScheme", "hb_taqu_alipay_login_auth")
            bizParams.put("url", url)
            // 支付宝回跳到您的应用时使用的 Intent Scheme。
            // 请设置为一个不和其它应用冲突的值，并在 AndroidManifest.xml 中为 AlipayResultActivity 的 android:scheme 属性
            // 指定相同的值。实际使用时请勿设置为 __alipaysdkdemo__ 。
            // 如果不设置，OpenAuthTask.execute() 在用户未安装支付宝，使用网页完成业务流程后，将无法回跳至您的应用。
            // 防止在支付宝 App 被强行退出等意外情况下，OpenAuthTask.Callback 一定时间内无法释放，导致
            // Activity 泄漏
            val ctxRef = WeakReference<Activity>(topActivity)
            // 唤起授权业务
            val task = OpenAuthTask(topActivity)

            Loger.i(TAG, "execute auth")
            task.execute(
                "hb_taqu_alipay_login_auth",    // Intent Scheme，这个值AndroidManifest.xml 中为 AlipayResultActivity 的 android:scheme 属性
                OpenAuthTask.BizType.AccountAuth, // 业务类型
                bizParams, // 业务参数
                object : OpenAuthTask.Callback {
                    /**
                     * @param code
                     * OpenAuthTask.OK =  9000               - 调用成功
                     * OpenAuthTask.Duplex =  5000           -  3 s 内快速发起了多次支付 / 授权调用。稍后重试即可。
                     * OpenAuthTask.NOT_INSTALLED =  4001    - 用户未安装支付宝 App。
                     * OpenAuthTask.SYS_ERR =  4000          - 其它错误，如参数传递错误。
                     */
                    override fun onResult(code: Int, msg: String?, bundle: Bundle?) {
                        Loger.i(TAG, "auth result code:", code, "msg:", msg)
                        try {
                            if (bundle == null) {
                                uploadAuthFailure("result", "code: $code, msg: $msg", null)
                                return
                            }
                            val activity = ctxRef.get()
                            if (activity == null || activity.isFinishing) {
                                uploadAuthFailure("finished", "code: $code, msg: $msg", bundle)
                                return
                            }
                            val accountAliWithdrawalLoginAuthBean = AccountAliWithdrawalLoginAuthBean()
                            accountAliWithdrawalLoginAuthBean.statusCode = code.toString()
                            if (code == OpenAuthTask.OK) {
                                /* 数据样例
                                    "app_id" = ****************;
                                    "auth_code" = 41f084c3ab4b4be6b6dd8d25dac1YF46;
                                    "result_code" = SUCCESS;
                                    "scope" = auth_user;
                                    "state" = XXXX（自定义 base64 编码）;
                                */
                                if (isCancelByUser(bundle)) {
                                    accountAliWithdrawalLoginAuthBean.statusCode = "-4"
                                } else {
                                    val authCode = bundle.getString("auth_code")
                                    accountAliWithdrawalLoginAuthBean.authCode = authCode
                                }
                            } else {
                                //收集不成功的日志，以便排查线上
                                uploadAuthFailure("failure", "code: $code, msg: $msg", bundle)
                            }
                            val toJson = XJson
                                .toJson(accountAliWithdrawalLoginAuthBean)
                            callback?.onCall(toJson)
                            Loger.d(
                                "Account_ali_withdrawal",
                                "结果码", code.toString(),
                                "结果信息", msg,
                                "结果数据", bundleToString(bundle)
                            )
                        } catch (e: Exception) {
                            uploadAuthFailure("error", "code: $code, msg: $msg, e: ${e.message}", bundle)
                        }
                    }

                }, // 业务结果回调
                true
            ) // 是否需要在用户未安装支付宝 App 时，使用 H5 中间页中转。建议设置为 true。

        }


        /**
         *
         * 手动取消 是这样的  记录一下
         * authStatus=>USER_CANCEL
         *
         */
        private fun bundleToString(bundle: Bundle?): String {
            if (bundle == null) {
                return "null"
            }
            val stringBuilder = StringBuilder()
            for (key in bundle.keySet()) {
                stringBuilder.append(key).append("=>").append(bundle.get(key)).append("\n")
            }
            return stringBuilder.toString()
        }

        /**
         * 是否被用户拒绝或者按back返回
         */
        private fun isCancelByUser(bundle: Bundle): Boolean {
            val authStatus = bundle.getString("authStatus")
            return "USER_CANCEL" == authStatus || "USER_BACK" == authStatus
        }


        /**
         * 上传日志
         */
        private fun uploadDisSuccessLog(code: Int, msg: String?, bundle: Bundle) {
            if (code == OpenAuthTask.OK) {
                return
            }
            XLogUpload.newBuilder("WITHDRAWAL_ALI_LOGIN_AUTH")
                .params("code", code.toString())
                .params("msg", msg)
                .params("detail", bundleToString(bundle))
        }

        /**
         * 上报提现认证失败日志
         */
        private fun uploadAuthFailure(step: String, message: String, bundle: Bundle?) {
            XLogUpload.newBuilder("Monitor_withdrawAuthFailure")
                .text1(step)
                .params("message", message)
                .params("detail", bundleToString(bundle))
                .post()
        }
    }


}