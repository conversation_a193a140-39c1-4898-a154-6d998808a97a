package com.xmhaibao.account.personinfo

import cn.taqu.lib.okhttp.callback.GsonCallBack
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.xmhaibao.account.api.router.AccountPinsRouter
import com.xmhaibao.mine.api.router.MinePinsRouter
import com.xmhaibao.account.personinfo.bean.MineGuideCompleteCheckList
import com.xmhaibao.account.repository.AccountRepository
import hb.utils.ActivityUtils
import hb.utils.CollectionUtils
import hb.utils.StringUtils
import hb.xstyle.xdialog.XLoadingDialog
import hb.xtoast.XToastUtils

/**
 * 引导完善资料功能类
 *
 * <AUTHOR>
 * @date 2021-08-02
 */
class MineGuideCompleteInfoUtils {


    companion object {
        //语音签名
        const val VOICE_SIGNATURE = "voiceSignature"

        //签名(个人简介)
        const val PROFILE = "profile"

        //出生日期
        const val BIRTHDAY = "birthday"

        //身高
        const val HEIGHT = "height"

        //体重
        const val WEIGHT = "weight"

        //故乡
        const val HOMETOWN = "hometown"

        //情感状况
        const val MARITAL_STATUS = "maritalStatus"

        //职业
        const val PROFESSION = "profession"

        //学历
        const val EDUCATION = "education"

        //年收入
        const val INCOME = "income"

        //标签
        const val PERSONALITY_LABEL = "personalityLabel"

        fun startGuideCompleteInfo(checks: String, source: String, userType: String?, guideType: String?, inviterUuid: String?,
            taskType: String?) {
            val infoItems = checks.split(",")
            var totalCount = 0
            infoItems.forEach {
                if (StringUtils.isNotEmpty(it)) {
                    totalCount++
                }
            }
            XLoadingDialog.showLoadingbar(ActivityUtils.getTopActivity())
            AccountRepository.getChecksPersonInfo(checks).execute(object : GsonCallBack<MineGuideCompleteCheckList>() {
                override fun onSuccess(isCache: Boolean, obj: MineGuideCompleteCheckList?,
                    response: IResponseInfo<out IResponseInfo<*>>) {
                    XLoadingDialog.hideLoadingbar()
                    if (obj != null && CollectionUtils.isNotEmpty(obj.checkList)) {
                        AccountPinsRouter.launchMineGuideCompleteInfoActivity(totalCount, obj.checkList, source, userType,
                            guideType,inviterUuid,taskType)
                    } else if (obj != null && CollectionUtils.isEmpty(obj.checkList)) {
                        XToastUtils.show(response.getResponseMsg("你的个人资料已完善"))
                    }
                }

                override fun onFailure(isServiceFailure: Boolean, response: IResponseInfo<out IResponseInfo<*>>) {

                }
            })
        }

        fun getTitleByType(type: String): String {
            when (type) {
                VOICE_SIGNATURE -> {
                    return "语音签名"
                }
                PROFILE -> {
                    return "个性签名"
                }
                BIRTHDAY -> {
                    return "出生日期"
                }
                HEIGHT -> {
                    return "身高"
                }
                WEIGHT -> {
                    return "体重"
                }
                HOMETOWN -> {
                    return "故乡"
                }
                MARITAL_STATUS -> {
                    return "情感状况"
                }
                PROFESSION -> {
                    return "职业"
                }
                EDUCATION -> {
                    return "学历"
                }
                INCOME -> {
                    return "年收入"
                }
                PERSONALITY_LABEL -> {
                    return "标签"
                }
            }
            return ""
        }
    }
}