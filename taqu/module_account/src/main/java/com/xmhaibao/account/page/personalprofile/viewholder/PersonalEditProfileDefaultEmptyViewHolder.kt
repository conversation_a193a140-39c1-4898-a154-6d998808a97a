package com.xmhaibao.account.page.personalprofile.viewholder

import android.app.Activity
import android.view.ViewGroup
import com.xmhaibao.account.R
import com.xmhaibao.account.api.bean.PersonalAccountInfo
import com.xmhaibao.account.api.personal.constants.PersonalProfileTaskConstant
import com.xmhaibao.account.api.router.AccountPinsRouter
import com.xmhaibao.account.databinding.AccountPersonalProfileDefaultItemViewHolderBinding
import com.xmhaibao.account.page.personalprofile.bean.PersonalEditProfileEmptyBean
import com.xmhaibao.account.page.personalprofile.bean.PersonalProfileMyVoiceBean
import com.xmhaibao.account.api.tracker.PersonalProfileTracker
import com.xmhaibao.mine.api.constants.MineIntentConstant
import com.xmhaibao.mine.api.router.MinePinsRouter
import hb.utils.ActivityUtils
import hb.xadapter.XBaseViewHolder

/**
 * 个人主页编辑状态默认空状态
 * <AUTHOR>
 * @date 2024-12-02
 */
class PersonalEditProfileDefaultEmptyViewHolder(
    private val parent: ViewGroup
) : XBaseViewHolder<PersonalEditProfileEmptyBean>(
    parent,
    R.layout.account_personal_profile_default_item_view_holder
) {


    private val binding: AccountPersonalProfileDefaultItemViewHolderBinding by lazy {
        AccountPersonalProfileDefaultItemViewHolderBinding.bind(itemView)
    }

    private var type:String = ""

    init {
        binding.root.setOnClickListener {
            when(type){
                PersonalProfileTaskConstant.REAL_AVATAR_CONFIRMED->{
                    //真人认证
                    PersonalProfileTracker.Instance.trackMyInfoEditClick("认证")
                    MinePinsRouter.launchMyAuthActivity()
                }
                PersonalProfileTaskConstant.STYLE_SIGN->{
                    //个性签名
                    PersonalProfileTracker.Instance.trackMyInfoEditClick("个性签名")
                    AccountPinsRouter.launchMineProfileActivity(
                        ActivityUtils.getActivity(it.context),
                        PersonalAccountInfo(),
                        MineIntentConstant.REQUEST_CODE_START_PERSONAL_PROFILE
                    )
                }
                PersonalProfileTaskConstant.MY_LABEL_CONFIRMED -> {
                    PersonalProfileTracker.Instance.trackMyInfoEditClick("我的标签")

                    //我的标签
                    AccountPinsRouter.launchPersonalMyInterestTabActivity()
                }
                PersonalProfileTaskConstant.MY_VOICE -> {
                    PersonalProfileTracker.Instance.trackMyInfoEditClick("我的声音")
                    //我的声音
                    val voiceData = itemView.tag
                    if (voiceData is PersonalProfileMyVoiceBean) {
                        AccountPinsRouter.navigationToVoiceRecordPage(voiceData.signInfo)
                    } else if (voiceData is PersonalEditProfileEmptyBean) {
                        AccountPinsRouter.navigationToVoiceRecordPage(voiceData.profileInfo?.voiceSignInfo)
                    }
                }
                PersonalProfileTaskConstant.MY_LIFE -> {
                    PersonalProfileTracker.Instance.trackMyInfoEditClick("我的生活")
                    AccountPinsRouter.launchPersonalMyLifeActivity()
                }
                PersonalProfileTaskConstant.MY_IDEAL_TYPE -> {
                    PersonalProfileTracker.Instance.trackMyInfoEditClick("我的理想型")
                    AccountPinsRouter.launchPersonalIdealTypeActivity()
                }

                PersonalProfileTaskConstant.SELF_INTRO -> {
                    PersonalProfileTracker.Instance.trackMyInfoEditClick("自我介绍")
                    val profileData = itemView.tag
                    if (profileData is PersonalEditProfileEmptyBean) {
                        if (profileData.profileInfo?.profileVerifyStatus != "1") {
                            MinePinsRouter.mineService().showRealAvatarDialog(itemView.context)
                        } else {
                            AccountPinsRouter.launchMySelfIntroduceActivity(
                                itemView.context as Activity,
                                MineIntentConstant.REQUEST_CODE_SELF_INTRO
                            )
                        }
                    }

                }
            }
        }
    }

    override fun onBindView(item: PersonalEditProfileEmptyBean?) {
        item ?: return
        itemView.tag = item
        binding.headTitleView.setData(item)
        binding.emptyView.setEmptyInfo(item)
        this.type = item.type
    }

}