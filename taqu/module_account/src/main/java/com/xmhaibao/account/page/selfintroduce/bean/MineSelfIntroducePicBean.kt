package com.xmhaibao.account.page.selfintroduce.bean

import androidx.room.Ignore
import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import hb.common.helper.HostHelper
import hb.utils.WebpUtils

/**
 * <AUTHOR>
 * @desc 自我介绍图片实体类
 * @date 2021-12-15 9:21
 */
class MineSelfIntroducePicBean : IDoExtra {

    companion object {
        /**
         * add图标
         */
        const val TYPE_ADD: Int = 1

        /**
         * 网络图片
         */
        const val TYPE_IMG_URL: Int = 2

        /**
         * 本地图片
         */
        const val TYPE_IMG_FILE = 3

        /**
         * 返回默认的添加按钮
         */
        fun getDefaultAddBean(): MineSelfIntroducePicBean {
            val addBean = MineSelfIntroducePicBean()
            addBean.picType = TYPE_ADD
            return addBean

        }

    }

    /**
     * 图片地址
     */
    @SerializedName("img_name")
    var picUrl: String? = ""

    /**
     * 图片本地file地址
     */
    @Ignore
    var localPicPath: String? = ""

    /**
     * 图片类型
     */
    @Ignore
    var picType = TYPE_ADD


    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        picUrl = WebpUtils.getWebpUrl(picUrl, HostHelper.getInstance().hostAvatar)
    }

}
