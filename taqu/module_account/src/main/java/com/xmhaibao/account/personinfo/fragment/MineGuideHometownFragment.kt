package com.xmhaibao.account.personinfo.fragment

import cn.taqu.lib.base.mall.model.ConsigneeInfo
import cn.taqu.lib.base.widget.AddressView
import cn.taqu.lib.okhttp.callback.SimpleCallback
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.xmhaibao.account.api.helper.AccountInfoEditHelper
import com.xmhaibao.account.databinding.MineGuideAddressFragmentBinding
import com.xmhaibao.base.repository.BaseRepository
import com.xmhaibao.message.api.repository.MessagePinsRepository
import hb.utils.StringUtils
import hb.utils.ToastUtils

/**
 * 资料设置高度
 * <AUTHOR>
 * @date 2021-08-04
 */
class MineGuideHometownFragment : MineGuideBaseFragment() {

    private lateinit var mBinding: MineGuideAddressFragmentBinding

    companion object {
        fun getFragment(): MineGuideHometownFragment {
            return MineGuideHometownFragment()
        }
    }

    override fun onCreateContentView(): Any {
        mBinding = MineGuideAddressFragmentBinding.inflate(layoutInflater)
        return mBinding.root
    }

    override fun initViews() {
        super.initViews()
        mBinding.btnSave.setOnClickListener {
            save()
        }
        mBinding.addressView.setHideArea(true)
        mBinding.addressView.setupView(false, false, null, object : AddressView.OnAddressCallBack {
            override fun onCancel() {
                //do nothing
            }

            override fun onConfirmAddress(info: ConsigneeInfo?) {

                checkInvite(info!!.provinceId + "," + info.cityId)
            }

            override fun showAddress(Province: String?, City: String?, Area: String?) {
                //do nothing
            }
        })
    }

    /**
     * 检查是否有执行邀请完善资料任务。没有的情况直接改家乡
     */
    private fun checkInvite(homeTown: String) {
        val inviterUuid = mCallback.getInviterUuid()
        val taskType = mCallback.getTaskType()
        showLoadingBar()
        if (StringUtils.isNotEmpty(inviterUuid) && StringUtils.isNotEmpty(taskType)) {
            requestInviteToCompleteMaterial(inviterUuid!!, taskType!!,homeTown)
        } else {
            submitHomeTown(homeTown)
        }
    }

    private fun requestInviteToCompleteMaterial(inviterUuid: String, taskType: String,homeTown: String) {
        BaseRepository.inviteToCompleteMaterial(inviterUuid, taskType).execute(object : SimpleCallback<Any?>() {
            override fun onFinish(isCache: Boolean, obj: Any?, response: IResponseInfo<*>) {
                super.onFinish(isCache, obj, response)
                submitHomeTown(homeTown)
            }
        })
    }

    private fun submitHomeTown(homeTown: String) {
        AccountInfoEditHelper.requestChangeHomeTown(homeTown, object : SimpleCallback<Any?>() {
            override fun onSuccess(isCache: Boolean, obj: Any?, response: IResponseInfo<*>) {
                super.onSuccess(isCache, obj, response)
                hideLoadingBar()
                mCallback.onSaveSuccess()
            }

            override fun onFailure(isServiceFailure: Boolean, response: IResponseInfo<*>) {
                super.onFailure(isServiceFailure, response)
                hideLoadingBar()
                ToastUtils.show(response.getResponseMsg("设置失败，请检查网络"))
            }
        })
    }

    override fun save() {
        mBinding.addressView.confirmAddress()
    }
}