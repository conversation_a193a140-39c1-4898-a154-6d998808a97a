<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="500dp">

    <ImageView
        android:id="@+id/ivBg"
        android:layout_width="327dp"
        android:layout_height="443dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/lite_wechat_diversion_dialog_bg" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="104dp"
        android:text="保存成功"
        android:textColor="#FA2457"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@id/ivBg"
        app:layout_constraintStart_toStartOf="@id/ivBg"
        app:layout_constraintTop_toTopOf="@id/ivBg" />

    <TextView
        android:id="@+id/tvSubTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="17dp"
        android:text="还差一步，领取神秘礼物"
        android:textColor="#FA2457"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle" />

    <hb.drawable.shape.view.HbConstraintLayout
        android:id="@+id/clContent"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginRight="30dp"
        android:layout_marginTop="13dp"
        android:gravity="center"
        android:orientation="vertical"
        app:corner="16dp"
        app:layout_constraintEnd_toEndOf="@id/ivBg"
        app:layout_constraintStart_toStartOf="@id/ivBg"
        app:layout_constraintTop_toBottomOf="@id/tvSubTitle"
        app:solid="@color/white">

        <ImageView
            android:id="@+id/ivGift"
            android:layout_width="88dp"
            android:layout_height="84dp"
            android:layout_marginTop="2dp"
            android:src="@drawable/lite_wechat_diversion_dialog_gift_ic"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/ivStep1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="22dp"
            android:layout_marginTop="10dp"
            android:src="@drawable/lite_wechat_step_1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivGift" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:text="打开微信扫一扫，添加好友"
            android:textColor="#1F1F1F"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/ivStep1"
            app:layout_constraintStart_toEndOf="@+id/ivStep1"
            app:layout_constraintTop_toTopOf="@+id/ivStep1" />

        <ImageView
            android:id="@+id/ivStepLine"
            android:layout_width="wrap_content"
            android:layout_height="52dp"
            android:src="@drawable/lite_wechat_step_line"
            app:layout_constraintEnd_toEndOf="@+id/ivStep1"
            app:layout_constraintStart_toStartOf="@+id/ivStep1"
            app:layout_constraintTop_toTopOf="@+id/ivStep1" />

        <ImageView
            android:id="@+id/ivStep2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="22dp"
            android:layout_marginTop="10dp"
            android:src="@drawable/lite_wechat_step_2"
            app:layout_constraintBottom_toBottomOf="@+id/ivStepLine"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/tvCopyContent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            tools:text="将ID：123123123123123123 发送给小助手"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="5dp"
            app:layout_constraintEnd_toEndOf="parent"
            android:textColor="#1F1F1F"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@+id/ivStep2"
            app:layout_constraintTop_toTopOf="@+id/ivStep2" />
    </hb.drawable.shape.view.HbConstraintLayout>

    <TextView
        android:id="@+id/btnCopy"
        android:layout_width="wrap_content"
        android:layout_height="47dp"
        android:layout_marginBottom="29dp"
        android:background="@drawable/lite_wechat_diversion_dialog_copy_btn"
        android:gravity="center"
        android:paddingBottom="10dp"
        app:layout_constraintHorizontal_chainStyle="packed"
        android:layout_marginEnd="8dp"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintTop_toTopOf="@+id/btnOpen"
        app:layout_constraintEnd_toStartOf="@id/btnOpen"
        app:layout_constraintStart_toStartOf="@id/ivBg" />

    <TextView
        android:id="@+id/btnOpen"
        android:layout_width="wrap_content"
        android:layout_height="60dp"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/clContent"
        android:layout_marginBottom="29dp"
        android:background="@drawable/lite_wechat_diversion_dialog_open_btn"
        android:gravity="center"
        android:paddingBottom="10dp"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@id/ivBg"
        app:layout_constraintEnd_toEndOf="@id/ivBg"
        app:layout_constraintStart_toEndOf="@id/btnCopy" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:text="第三方活动真实有效"
        android:textColor="#888888"
        android:textSize="10sp"
        app:layout_constraintBottom_toBottomOf="@id/ivBg"
        app:layout_constraintEnd_toEndOf="@id/ivBg"

        app:layout_constraintStart_toStartOf="@id/ivBg" />

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="32dp"
        android:layout_height="32dp"

        android:src="@drawable/lite_newbie_red_packet_close_ic"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>