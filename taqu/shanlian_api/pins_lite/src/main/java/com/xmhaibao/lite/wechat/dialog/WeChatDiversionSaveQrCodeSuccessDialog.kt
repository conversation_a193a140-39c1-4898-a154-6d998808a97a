package com.xmhaibao.lite.wechat.dialog

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import com.ushengsheng.utils.AppNameUtils
import com.xmhaibao.lite.utils.LiteUtils
import com.xmhaibao.pins.shanlian.api.R
import com.xmhaibao.pins.shanlian.api.databinding.LiteWechatDiversionSaveQrCodeSuccessDialogBinding
import hb.utils.SizeUtils
import hb.utils.ToastUtils
import hb.xstyle.xdialog.XLifecycleDialog
import hb.xthread.XThreadPool
import kale.sharelogin.ShareLoginLib
import kale.sharelogin.weixin.WeiXinPlatform

/**
 * 企微导流 保存图片成功弹窗
 *
 * <AUTHOR>
 * @date 2023-05-17
 */
class WeChatDiversionSaveQrCodeSuccessDialog(context: Context) :
    XLifecycleDialog(context,cn.taqu.lib.base. R.style.CustomTheme_Dialog) {

    companion object {
        @JvmStatic
        fun newInstance(context: Context,
                        content: String?): WeChatDiversionSaveQrCodeSuccessDialog {
            val dialog = WeChatDiversionSaveQrCodeSuccessDialog(context)
            dialog.content = content ?: ""
            return dialog
        }
    }

    private val mBinding by lazy {
        LiteWechatDiversionSaveQrCodeSuccessDialogBinding.inflate(LayoutInflater.from(context))
    }

    private var isCopyClick = false
    private var content: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(mBinding.root)
        mBinding.root.layoutParams.height = SizeUtils.dp2px(500f)
        setCancelable(true)
        setCanceledOnTouchOutside(false)
        initView()
        initListener()

        mBinding.tvCopyContent.text = "将ID:${content} 发送给小助手"
        copyToClip(prompt = false)
    }


    private fun initView() {
        mBinding.ivBg.setImageResource(R.drawable.lite_wechat_diversion_dialog_bg)
    }

    private fun initListener() {
        mBinding.btnCopy.setOnClickListener {
            if (copyToClip()) {
                isCopyClick = true
            }
        }
        mBinding.btnOpen.setOnClickListener {
            if (isCopyClick) {
                openWechat()
            } else {
                copyToClip(true)
                XThreadPool.Main().delay(2000).execute {
                    openWechat()
                }
            }
        }
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }

    }

    private fun copyToClip(prompt: Boolean = true): Boolean {
        val clip = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        try {
            val copyText = "${AppNameUtils.getAppName()}ID:$content"
            clip.setPrimaryClip(ClipData.newPlainText(null, copyText))
            if (prompt) {
                ToastUtils.show("ID已复制到剪贴板，记得发给小助手哦")
            }
            return true
        } catch (ignored: Exception) {
        }
        return false
    }

    private fun openWechat() {
        val isInstalled = ShareLoginLib.isAppInstalled(context, WeiXinPlatform::class.java)
        if (!isInstalled) {
            ToastUtils.show("您尚未安装微信，请安装后重试")
            return
        }
        val intent = LiteUtils.getStartWechatIntent()
        val result = intent.resolveActivity(context.packageManager)
        if (result == null) {
            ToastUtils.show("启动微信失败，请您手动打开微信")
            return
        }
        context.startActivity(intent)
        dismiss()
    }
}