package com.xmhaibao.base.monitor;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import androidx.preference.PreferenceManager;

import com.bytedance.rheatrace.TraceManager;
import com.bytedance.rheatrace.trace.sampling.SamplingConfig;
import com.bytedance.rheatrace.utils.ProcessUtils;

import java.io.File;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicBoolean;

import cn.taqu.lib.base.constants.XUploadBucket;
import cn.taqu.lib.base.constants.XUploadTags;
import cn.taqu.lib.base.router.ARouterManager;
import hb.common.data.AccountHelper;
import hb.common.data.AppEnvironment;
import hb.common.data.ApplicationHelper;
import hb.upload.file.IUploadNameCreater;
import hb.upload.file.OnUploadListener;
import hb.upload.file.XUpload;
import hb.upload.file.XUploadResponseInfo;
import hb.utils.Loger;

/**
 * 接入字节BTrace工具，支持测试环境默认全开，支持线上APM实验控制
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public class BTraceMonitor {

    private static final String TAG = "BTraceMonitor";

    /**
     * btrace采样开关
     */
    public static final String KEY_BTRACE_HIT = "monitor_btrace_hit";

    /**
     * 问题ID，用于统一问题的文件名前缀，并添加到友盟extra里
     */
    private static String sTraceID;

    /**
     * 随机值，防止重复
     */
    private static Random sRandom = new Random();

    /**
     * 是否命中采样
     */
    private static AtomicBoolean sBTraceHit;

    private static Context sContext;

    private static AtomicBoolean sIsInited = new AtomicBoolean(false);

    private static AtomicBoolean sIsTracing = new AtomicBoolean(false);

    /**
     * 时间格式化
     */
    private static SimpleDateFormat sFormat = new SimpleDateFormat("yyMMddHHmmss");

    /**
     * 控制stopTrace的频率，避免出现频繁卡顿，频繁生成trace文件
     */
    private static long sLastSaveFileTime = 0;

    /**
     * FIXME 影响性能考虑
     * 在Application attach时调用
     *
     * @param context
     */
    public static void init(Context context) {
        // RheaTrace now only support trace main process.
        if (!ProcessUtils.isMainProcess(context)) {
            return;
        }
        sContext = context;
        // 设置trace采集buffer最大上限，超过则覆盖最久的，先试试50000
        SamplingConfig.OFFLINE_BUFFER_SIZE_DEFAULT = 50_000;
        // 命中采样开关才初始化BTrace采集
//        if (isBTrace()) {
        // 手动启动
        startTrace(false);
        TraceManager.getInstance().init(context, false, true);
        sIsInited.set(true);
        Log.i(TAG, "init success.");
//        }
    }

    /**
     * true：命中btrace采样
     *
     * @return
     */
    public static boolean isBTrace() {
        if (sBTraceHit == null) {
            SharedPreferences defaultSharedPreferences = PreferenceManager.getDefaultSharedPreferences(sContext);
            boolean hit = defaultSharedPreferences.getBoolean(KEY_BTRACE_HIT, false);
            sBTraceHit = new AtomicBoolean(hit);
            Log.i(TAG, "isBTrace: " + hit);
        }
        return sBTraceHit.get();
    }

    /**
     * 设置是否命中采样开关
     *
     * @param enable
     */
    public static void setBTrace(boolean enable) {
        if (sBTraceHit == null) {
            sBTraceHit = new AtomicBoolean(enable);
        }
        // 如果值相同，则不做任何处理
        else if (sBTraceHit.get() == enable) {
            return;
        }
        sBTraceHit.set(enable);
        SharedPreferences defaultSharedPreferences = PreferenceManager.getDefaultSharedPreferences(sContext);
        defaultSharedPreferences.edit().putBoolean(KEY_BTRACE_HIT, enable).apply();
        Log.i(TAG, "setBTrace: sBTraceHit: " + sBTraceHit.get());
        // 当前状态是开启的，但未初始化，则进行初始化
        if (sBTraceHit.get()) {
            if (!sIsInited.get()) {
                // 初始化并启动trace
                init(sContext);
            } else if (!sIsTracing.get()) {
                startTrace(true);
            }
        } else {
            if (sIsTracing.get()) {
                stopTrace(null);
            }
        }
    }

    /**
     * 开启trace
     *
     * @param async 是否异步启动
     */
    private static boolean startTrace(boolean async) {
        boolean startTracing = TraceManager.getInstance().startTracing(async);
        sIsTracing.set(true);
        Log.i(TAG, "startTrace: async: " + async);
        return startTracing;
    }

    /**
     * 停止trace
     *
     * @param runnable
     */
    private static boolean stopTrace(Runnable runnable) {
        boolean stopTracing = TraceManager.getInstance().stopTracing(runnable);
        sIsTracing.set(false);
        Log.i(TAG, "stopTrace: ");
        return stopTracing;
    }

    /**
     * 在问题发生时调用，生成trace文件并上传文件
     *
     * @return
     */
    public static void report(String stacktrace) {
        // 生成Trace
        APMHookTraceReporter.APMLogInfo info = APMHookTraceReporter.generateLogInfo(stacktrace);
        info.logTime = System.currentTimeMillis();
        createTraceID(info.logTime);

        long current = System.currentTimeMillis();

        // 防抖，避免严重影响性能
        boolean shouldStop = current - sLastSaveFileTime >= 5000;
        if (shouldStop) {
            sLastSaveFileTime = current;
        }
        // trace写入文件
        boolean isStoped = shouldStop && stopTrace(new Runnable() {
            @Override
            public void run() {
                findTraceAndUpload(info);
            }
        });

        Loger.i(TAG, "report: shouldStop: ", shouldStop, ", isStoped: ", isStoped);

        // 停止失败直接上报卡顿日志
        if (!isStoped) {
            reportAndRestartTrace(info);
        }
    }

    /**
     * 找到trace文件并上传
     *
     * @param info
     */
    private static void findTraceAndUpload(APMHookTraceReporter.APMLogInfo info) {
        try {
            Field tracingDirPathField = TraceManager.class.getDeclaredField("tracingDirPath");
            tracingDirPathField.setAccessible(true);
            String tracingDirPath = tracingDirPathField.get(TraceManager.getInstance()).toString();
            File file = new File(tracingDirPath);
            if (file.exists()) {
                List<String> traceFiles = new ArrayList<>();
                File sampling = new File(file, "sampling");
                File samplingMapping = new File(file, "sampling-mapping");
                if (sampling.exists()) {
                    traceFiles.add(sampling.getAbsolutePath());
                }
                if (samplingMapping.exists()) {
                    traceFiles.add(samplingMapping.getAbsolutePath());
                }

                // 上传trace文件
                startUpload(getTraceID(), traceFiles, new BTraceUploadCallback() {
                    @Override
                    public void onTraceSuccess(List<String> traceUrls) {
                        if (traceUrls != null && !traceUrls.isEmpty()) {
                            for (int i = 0; i < traceUrls.size(); i++) {
                                info.otherLogPath.put(traceUrls.get(i));
                            }
                        }
                        reportAndRestartTrace(info);
                    }

                    @Override
                    public void onTraceFailure(String error) {
                        reportAndRestartTrace(info);
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            reportAndRestartTrace(info);
        }
    }

    /**
     * 上报带有trace的日志，并重新开始抓取堆栈
     *
     * @param info
     */
    private static void reportAndRestartTrace(APMHookTraceReporter.APMLogInfo info) {
        Loger.i(TAG, "reportAndRestartTrace: 上报日志并重新开始trace");
        APMHookTraceReporter.reportLag(info);

        // debug环境下保留文件，方便观察文件生成情况
        if (!AppEnvironment.DEBUG) {
            TraceManager.getInstance().clearAfterTracing();
        }
        startTrace(true);
    }

    /**
     * 上传文件
     *
     * @param traceID  自定义traceID
     * @param files    sampling文件、sampling-mapping文件
     * @param callback 上传结果
     */
    private static void startUpload(String traceID, List<String> files, BTraceUploadCallback callback) {
        Loger.i(TAG, "startUpload: traceID: ", traceID, ", files:", files);
        XUpload.newBuilder()
                .setBucketType(XUploadBucket.LOG)
                .setFiles(files)
                .setFeatureTag(XUploadTags.Other.CRASH_FILE_601)
                .setNameCreater(new IUploadNameCreater() {
                    @Override
                    public void refresh() {

                    }

                    @Override
                    public String generateName(XUpload.Builder builder, String filePath, int size, int index) {
                        int start = filePath.lastIndexOf('/');
                        String fileName = null;
                        if (start != -1) {
                            fileName = filePath.substring(start + 1);
                        }
                        return traceID + "/" + fileName;
                    }
                })
                .setOnUploadListener(new OnUploadListener() {
                    @Override
                    public void onUploadSuccess(String bucket, List<XUploadResponseInfo> list) {
                        List<String> urls = new ArrayList<>();
                        if (list != null && !list.isEmpty()) {
                            for (int i = 0; i < list.size(); i++) {
                                urls.add(list.get(i).getCompleteName());
                            }
                        }
                        Loger.i(TAG, "onUploadSuccess: ");
                        if (callback != null) {
                            callback.onTraceSuccess(urls);
                        }
                    }

                    @Override
                    public void onUploadFailure() {
                        Loger.i(TAG, "onUploadFailure: ");
                        if (callback != null) {
                            callback.onTraceFailure("trace文件上传失败");
                        }
                    }

                    @Override
                    public void onUploadProgress(int currentSuccessCount, double percent) {
                        // NO-OP
                    }
                })
                .start();
    }

    /**
     * 创建一个问题ID，用于统一问题的文件名前缀，并添加到友盟extra里
     *
     * @param time
     */
    public static void createTraceID(long time) {
        // FIXME 后面再重构完善基础信息的赋值
        String unique = null;
        if (AccountHelper.isUserLogined()) {
            unique = AccountHelper.getAccountUuid();
        } else {
            unique = ApplicationHelper.getDeviceId();
        }
        String channel = ApplicationHelper.getChannel();
        int appVersion = ApplicationHelper.getVersionCode();
        String buildId = ARouterManager.buildConfig().getBuildId();

        int random = sRandom.nextInt(1000);
        // 设备或用户标识：确保唯一
        // 渠道 + 版本号 + BuildID：确定具体哪个包，用于下载app的mapping文件。
        // 时间戳：区分不同问题。
        // 随机值：确保唯一
        String timeStr = sFormat.format(new Date(time));
        sTraceID = "xtrace_" + unique + "_" + channel + "_" + appVersion + "_" + buildId + "_" + timeStr + "_" + random;
    }

    /**
     * 用于统一问题的文件名前缀，并添加到友盟extra里
     *
     * @return
     */
    public static String getTraceID() {
        return sTraceID;
    }


    /**
     * BTrace文件上传回调
     */
    public interface BTraceUploadCallback {

        void onTraceSuccess(List<String> traceUrls);


        void onTraceFailure(String error);
    }
}
