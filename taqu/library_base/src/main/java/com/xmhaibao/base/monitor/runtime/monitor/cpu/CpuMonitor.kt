package com.xmhaibao.base.monitor.runtime.monitor.cpu

import android.text.TextUtils
import androidx.annotation.WorkerThread
import androidx.collection.SparseArrayCompat
import cn.taqu.lib.base.model.BaseAppConfigBean
import com.xmhaibao.base.monitor.ab.APMAbTest
import com.xmhaibao.base.monitor.runtime.monitor.cpu.constant.CpuMonitorConstant
import hb.common.data.ApplicationHelper
import hb.utils.Loger
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Cpu监控
 * <AUTHOR>
 * @date 2025-05-21
 */
object CpuMonitor {



    const val TAG = "CpuMonitor"

    /**
     * 是否开启监控
     */
    private val isEnableMonitor: AtomicBoolean = AtomicBoolean(false)




    @Volatile
    private var cpuInfoReader: CpuMonitorActuator? = null


    val cpuFreqDir: File by lazy {
        return@lazy File(CpuMonitorConstant.CPU_FREQ_DIR_PATH)
    }

    /**
     * Cpu监控轮训时间 单位秒
     */
    val cpuMonitorIntervalTime: Int by lazy {
        return@lazy BaseAppConfigBean.getAppConfig().cpuMonitorIntervalTime
    }


    /**
     * Cpu 频率 文件数组
     */
    val cpuPolicyFiles: Array<File> by lazy {
        val result = cpuFreqDir.listFiles { it ->
            it.isDirectory && it.name.startsWith(CpuMonitorConstant.POLICY_DIR_PREFIX)
        } ?: arrayOf<File>()
        result.sort()
        return@lazy result
    }


    /**
     * 是否命中CPU 实验组
     */
    private val isHitCpuMonitor: Boolean by lazy {
        return@lazy if (ApplicationHelper.isDevChannel()) {
            //测试环境根据全量开启
            true
        } else {
            //根据实验
            TextUtils.equals(APMAbTest.getAbValue("cpuMonitor"), "open")
        }
    }



    private val cpuFreqPolicyDirsById: SparseArrayCompat<File> = SparseArrayCompat()

    /**
     * 是否支持 Cpu 监控.
     * 不是所有的设备架构都支持 Cpu 监控。部分设备是不对外开放。
     */
    private fun isSupportCpuMonitor(): Boolean {
        if (cpuPolicyFiles.isEmpty()) {
            return false
        }
        populateCpuFreqPolicyDirsById(cpuPolicyFiles)
        val hasTimeInStateFile = (0 until cpuFreqPolicyDirsById.size()).any { i ->
            File(cpuFreqPolicyDirsById.valueAt(i), CpuMonitorConstant.TIME_IN_STATE_FILE).exists()
        }
        return hasTimeInStateFile
    }

    private fun populateCpuFreqPolicyDirsById(policyDirs: Array<File>) {
        cpuFreqPolicyDirsById.clear()
        for (policyDir in policyDirs) {
            val policyIdStr = policyDir.name.substring(CpuMonitorConstant.POLICY_DIR_PREFIX.length)
            if (policyIdStr.isEmpty()) {
                continue
            }
            val policyId = policyIdStr.toInt()
            cpuFreqPolicyDirsById.append(policyId, policyDir)
        }
    }


    /**
     * 开启 Cpu 监控
     */
    @WorkerThread
    fun openMonitor(): Boolean {
        if (isEnableMonitor.get()) {
            return true
        }
        if (!isHitCpuMonitor) {
            return false
        }
        if (!isSupportCpuMonitor()) {
            Loger.d(TAG,"不支持Cpu监控")
            //不支持
            return false
        }
        Loger.d(TAG,"支持Cpu监控")
        isEnableMonitor.set(true)
        cpuInfoReader =
            CpuMonitorActuator(cpuMonitorIntervalTime * 1000L).apply {
                open()
            }
        return true
    }

    /**
     * 关闭监控
     */
    fun closeMonitor() {
        if (isEnableMonitor.get() == false) {
            return
        }
        cpuInfoReader?.close()
        cpuInfoReader = null
        isEnableMonitor.set(false)
    }


    fun isEnableMonitor():Boolean{
        return isEnableMonitor.get()
    }





}