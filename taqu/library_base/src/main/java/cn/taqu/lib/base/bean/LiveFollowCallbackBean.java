package cn.taqu.lib.base.bean;

import com.google.gson.annotations.SerializedName;

/**
 * 直播关注回调bean
 *
 * <AUTHOR>
 * @date 2022-04-08
 */
public class LiveFollowCallbackBean {
    @SerializedName("response_status")
    private String responseStatus;
    private String msg;

    public String getResponseStatus() {
        return responseStatus;
    }

    public void setResponseStatus(String responseStatus) {
        this.responseStatus = responseStatus;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
