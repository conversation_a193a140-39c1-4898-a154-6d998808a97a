package cn.taqu.lib.base.utils;

import android.app.Activity;
import android.util.Log;

import cn.taqu.lib.base.R;
import cn.taqu.lib.base.appcommandIm.XLogFileSource;
import cn.taqu.lib.base.appcommandIm.XLogImUtil;
import cn.taqu.lib.base.event.EventKeFuNotify;
import cn.taqu.lib.base.event.EventVerify;
import cn.taqu.lib.base.http.HBHttpShowDialogBean;
import cn.taqu.lib.base.router.ARouterManager;
import cn.taqu.lib.base.router.RouterLaunch;
import cn.taqu.lib.base.verify.VerifyHelper;
import cn.taqu.lib.base.widget.AppInnerNotifyView;
import hb.utils.ActivityUtils;
import hb.utils.AppUtils;
import hb.utils.EventBusUtils;
import hb.utils.StringUtils;
import hb.xnotification.AppInnerNotifyBean;
import hb.xnotification.XNotification;


/**
 * 在应用启动时，注册EventBus监听
 *
 * <AUTHOR>
 * @date 2018-11-29
 */
public class ApplicaionEventBusUtils {

    private static final String TAG = "ApplicaionEventBusUtils";

    private static class Holder {
        private final static ApplicaionEventBusUtils INSTANCE = new ApplicaionEventBusUtils();
    }

    public static ApplicaionEventBusUtils getInstance() {
        return Holder.INSTANCE;
    }

    private boolean isInited = false;

    public void init() {
        if (isInited()) {
            Log.i(TAG, "init: 不需要再初始化");
            return;
        }
        Log.i(TAG, "init: 初始化成功，启动监听EventBus");
        isInited = true;
        EventBusUtils.register(this);
    }

    public void unregister() {
        EventBusUtils.unregister(this);
    }

    public boolean isInited() {
        return isInited;
    }

    /**
     * 监听验证，跳转到验证页面
     */
    public void onEventMainThread(EventVerify event) {
        // 检测是否马上发起验证
        VerifyHelper.checkVerify(event);
    }

    /**
     * show_dialog 弹窗显示
     *
     * @param event
     */
    public void onEventMainThread(HBHttpShowDialogBean event) {
        try {
            Activity currentActivity = ActivityUtils.getTopActivity();
            if (currentActivity != null && !currentActivity.isFinishing()) {
                TaquDialogUtils.showHttpErrorDialog(currentActivity, event);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onEventMainThread(EventKeFuNotify eventKeFuOrder) {
        Activity activity = ActivityUtils.getTopActivity();
        if (activity == null) {
            return;
        }
        if (!AppUtils.isAppForeground()) {
            NotificationCommonUtils.showNotification(XNotification.CATEGORY_SERVICE,
                    "m=me&a=service&t=" + "客服" + "&msg=" + eventKeFuOrder.getContent());
        } else {
            // isNewCustomerService
            AppInnerNotifyBean bean = new AppInnerNotifyBean();
            bean.setName("客服");
            bean.setContent(eventKeFuOrder.getContent());
            bean.setExtra(AppInnerNotifyView.TYPE_PRIVATE);
            XNotification.showAppInNotification(bean, view -> {
                if (StringUtils.isNotEmpty(eventKeFuOrder.getUrl())) {
                    RouterLaunch.dealJumpData(activity, eventKeFuOrder.getUrl());
                    XLogImUtil.setLogType(XLogImUtil.LOGFILE_REPORT, 0, XLogFileSource.CUSTOMER_SERVICE);
                } else {
                    ARouterManager.sobotService().startSobot(view.getContext());
                }

            });
        }
    }


}
