package cn.taqu.lib.base.share;

import android.content.Context;

import com.xmhaibao.base.repository.BaseRepository;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import cn.taqu.lib.base.router.ARouterManager;
import cn.taqu.lib.base.share.bean.InviteShareInfo;
import cn.taqu.lib.base.utils.GrowingIOUtils;
import cn.taqu.lib.okhttp.callback.GsonCallBack;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import hb.common.data.AccountHelper;
import hb.common.xstatic.activity.BaseActivity;
import hb.thirdtools.sharelogin.share.OtherPlatform;
import hb.thirdtools.sharelogin.share.ShareDialog;
import hb.thirdtools.sharelogin.share.ShareInfo;
import hb.thirdtools.sharelogin.share.channels.BaseShareChannel;
import hb.utils.ClipboardManagerUtils;
import hb.utils.ToastUtils;
import kale.sharelogin.qq.QQPlatform;
import kale.sharelogin.weibo.WeiBoPlatform;
import kale.sharelogin.weixin.WeiXinPlatform;

/**
 * <AUTHOR>
 * @date 2020/6/2
 * @desc
 */
public class TouchShareHelper {

    /**
     * 邀请分享信息
     */
    public static void requestInviteShareInfo(Context context, boolean isShareDialog) {
        if (!AccountHelper.isUserLogined()) {
            ARouterManager.accountForBaseService().launchLogin();
            return;
        }
        BaseRepository.getInviteShareInfo(AccountHelper.getUserTicketId())
                .execute(new GsonCallBack<InviteShareInfo>() {
                    @Override
                    public void onStart(boolean isCache) {
                        super.onStart(isCache);
                        if (context instanceof BaseActivity) {
                            ((BaseActivity) context).showLoadingBar();
                        }
                    }

                    @Override
                    public void onSuccess(boolean isCache, @Nullable InviteShareInfo shareInfo, @NonNull IResponseInfo response) {
                        if (context instanceof BaseActivity) {
                            ((BaseActivity) context).hideLoadingBar();
                        }
                        if (shareInfo == null) {
                            ToastUtils.show("获取分享信息失败，请稍后再试");
                            return;
                        }
                        if (isShareDialog) {
                            ShareDialog shareDialog = new ShareDialog(context, shareInfo);
                            shareDialog.setChannels(WeiXinPlatform.FRIEND, WeiXinPlatform.TIMELINE, QQPlatform.FRIEND, TouchSharePlatform.COPY_TEXT_WX, TouchSharePlatform.INVITE_QR_CODE);
                            setInviteShareDialogTrace(shareDialog);
                            shareDialog.show();
                        } else {
                            ClipboardManagerUtils.copyText(context, shareInfo.getShareUrlTextAmount());
                            SharePasteTextTipsDialog.showDialog(context);
                        }
                    }

                    @Override
                    public void onFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {
                        if (context instanceof BaseActivity) {
                            ((BaseActivity) context).hideLoadingBar();
                        }
                    }
                });
    }

    public static void setInviteShareDialogTrace( ShareDialog shareDialog) {
        shareDialog.setShareActionDoExtraListener(new DefaultShareInfoActionExtraListener() {
            @Override
            public void onActionDoExtra(BaseShareChannel baseShareChannel, ShareInfo shareInfo) {
                super.onActionDoExtra(baseShareChannel,shareInfo);
                String type = baseShareChannel.getType();
                if (WeiXinPlatform.FRIEND.equals(type) ) {
                    GrowingIOUtils.trackShareInviteClick(1);
                }else if(WeiXinPlatform.TIMELINE.equals(type)){
                    GrowingIOUtils.trackShareInviteClick(2);
                } else if (QQPlatform.FRIEND.equals(type) || QQPlatform.ZONE.equals(type)) {
                    GrowingIOUtils.trackShareInviteClick(3);
                } else if (WeiBoPlatform.TIMELINE.equals(type)) {
                    GrowingIOUtils.trackShareInviteClick(6);
                } else if (OtherPlatform.COPY_TEXT.equals(type)) {
                    GrowingIOUtils.trackShareInviteClick(7);
                } else if (OtherPlatform.COPY_URL.equals(type)) {
                    GrowingIOUtils.trackShareInviteClick(5);
                } else if (TouchSharePlatform.COPY_TEXT_WX.equals(type)) {
                    GrowingIOUtils.trackShareInviteClick(5);
                } else if (TouchSharePlatform.INVITE_QR_CODE.equals(type)) {
                    GrowingIOUtils.trackShareInviteClick(4);
                } else {
                    //怀疑有漏报
                    GrowingIOUtils.trackShareInviteClick(99);
                }
            }
        });
    }
}
