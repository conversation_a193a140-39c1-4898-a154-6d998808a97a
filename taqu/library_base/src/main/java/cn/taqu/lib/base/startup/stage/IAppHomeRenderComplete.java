package cn.taqu.lib.base.startup.stage;

import androidx.annotation.MainThread;

/**
 * [首页渲染完毕后的回调]
 *
 * <AUTHOR>
 * @date 2024-07-02
 */
public interface IAppHomeRenderComplete {

    /**
     * 首页的 首个有意义帧(FMP first meaning paint) 渲染完毕后的回调
     * 如何定义首个有意义帧以及触发方式？{@link cn.taqu.lib.base.startup.LaunchCoordinator}
     * 如何添加 app 启动任务 {@link cn.taqu.lib.base.startup.LaunchCoordinator#runAfterAppStart(Runnable)}
     * 如何添加 FMP 任务 {@link cn.taqu.lib.base.startup.LaunchCoordinator#runAfterFMPInit(Runnable)}
     */
    @MainThread
    void onAppHomeRenderComplete();
}
