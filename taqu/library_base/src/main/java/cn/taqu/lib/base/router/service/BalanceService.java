package cn.taqu.lib.base.router.service;

import android.content.Context;

import androidx.annotation.Nullable;

import com.alibaba.android.arouter.facade.template.IProvider;

import cn.taqu.lib.base.callback.OnCommonCallBack;
import cn.taqu.lib.base.router.interf.IRechargeCallLowPriceCallBack;

/**
 * <AUTHOR>
 * @date 2019/6/21
 * @desc 趣豆、贵族趣豆、趣币余额
 */
public interface BalanceService extends IProvider {

    void showBeanListDoalog(Context context, String type);

    void showBeanListDoalog(Context context, String type, Object config);

    /**
     * 调起充值弹窗，
     * @param context
     * @param type
     * @param config
     * @param newMaleRechargeConfig 新男实验的充值配置，可空
     */
    void showBeanListDoalog(Context context, String type, Object config,@Nullable Object newMaleRechargeConfig);

    /**
     * 调起充值弹窗，
     * @param newMaleRechargeConfig 新男实验的充值配置，可空
     * @param isBalanceNotEnough 是否是余额不足场景
     */
    void showBeanListDoalog(Context context, String type, Object config, @Nullable Object newMaleRechargeConfig, boolean isBalanceNotEnough);

    /**
     * 直接调起充值弹窗
     *
     * @param money       充值金额
     * @param type        充值场景
     * @param trackSource 充值场景埋点source
     * @param showFlip 是否显示充值大转盘提示语（仅首充弹窗、我的页面进入的需要）
     */
    void showTqBeanPayDialog(Context context,String money,String type, String trackSource, boolean showFlip);

    /**
     * 调起通话包购买弹窗
     * @param context 上下文
     * @param type  充值场景
     * @param rechargeBusinessPayParams 通话包购买参数
     */
    void showCallRecordPayDialog(Context context,String type, Object rechargeBusinessPayParams);

    /**
     * 贵族开通支付弹窗
     * @param desc 描述
     * @param money 金额
     * @param agreementUrl 协议地址
     * @param code 贵族code
     * @param month 开通月数
     */
    void showNobleOpenPayDialog(String desc, String money, String agreementUrl, int code, int month);

    void launchTqBeanActivity(String source);


    /**
     * 获取是否是新男充值实验
     * @param resultCallback value = true ，说明命中实验，非空非空非空
     */
    void requestCheckNewMaleRechargeAb( OnCommonCallBack<Object> resultCallback);

    /**
     * 注册充值逻辑相关的全局IM
     */
    void registerRechargeImGlobalEvent();

    /**
     * 新版充值
     * @param context 上下文
     * @param source 场景source
     * @param config 配置
     */
    void showNewFirstRechargeDialog(Context context, String source, Object config);

    /**
     * 重置弹框状态
     */
    default void resetNewFirstRechargeDialog(){
        //do nothing
    }

    /**
     * vip充值弹框
     */
    default void showVipRechargeDialog() {
        //do nothing
    }

    /**
     * 首充优化实验弹窗
     *
     * @param context 上下文
     * @param gioPosition 埋点来源
     * @param obj 充值数据 类型：MessageBalanceNotEnoughBean
     */
    default void showRechargeFirstOptDialog(Context context,String gioPosition, Object obj) {

    }

    /**
     * 低价营销首充实验弹窗
     * @param context
     * @param gioPosition
     */
    default void showLowMarketingFirstOptDialog(Context context, String gioPosition) {

    }

    /**
     * 命中留存新男促充值实验弹窗
     *
     * @param context
     * @param gioPosition
     */
    default void showNewMaleRemainDialog(String gioPosition, String scene) {

    }

    /**
     * 清空流程新男充值缓存数据
     */
    default void cleanNewManRemainData(){}

    /**
     * 留存新男促充值banner入口
     */
    default void showNewMaleRemainBanner(){}

    /**
     * 留存新男促充值实验——上报充值页面曝光弹窗
     * @param gioPosition
     */
    default void setShowRechargeProp(String gioPosition) {

    }

    /**
     * vip充值档位弹框
     *
     * @param showDetail 是否显示【查看详情】1：显示
     * @param source     充值来源
     */
    default void showVipPayDialog(String showDetail, String source,String dailyReward) {
        //do nothing
    }

    /**
     * vip开通付款弹框
     *
     * @param payDesc      支付描述
     * @param money        支付金额
     * @param agreementUrl 充值协议
     * @param productId    档位id
     * @param packetType        vip有效时长
     * @param source       充值来源
     * @param abCodeType   会员类型
     */
    default void showVipPurchaseDialog(String payDesc, String money, String agreementUrl, String productId, String packetType, String source, String abCodeType) {
        //do nothing
    }
    /**
     * 检查是否回流用户，如果回流用户则弹出充值弹窗，返回True，否则返回False（原业务继续处理）
     */
    default void checkReturnUserAndShowDialog(Context context, String source, OnCommonCallBack<Boolean> callback){
        // do nothing
    };

    /**
     * 趣聊充值任务
     *
     * @param context                 上下文
     */
    default void showRechargeCallStandbyDialog(Context context) {
        //do nothing
    }

    /**
     * 充值弹窗（全业务可调用）
     * @param context
     * @param gioPosition
     */
    default void showRechargeDialog(Context context, String gioPosition) {

    }

    /**
     * 显示趣聊超低价1元续聊策略实验 （<a href="https://o15vj1m4ie.feishu.cn/wiki/A6N2wEyR8iPUD0kw4lVcj7OmnCd">超低价1元续聊策略-技术文档</a>）
     *
     * @param context  上下文
     * @param source   充值来源
     * @param callBack 趣聊充值-超低价1元请求弹窗接口回调
     */
    default void checkShowCallRechargeDialog(Context context, String source, IRechargeCallLowPriceCallBack callBack) {
        //do nothing
    }

    /**
     * 首充送会员
     * @param activity
     * @param type
     */
    default void showVipFirstOptDialog(Context context, String type){}
}
