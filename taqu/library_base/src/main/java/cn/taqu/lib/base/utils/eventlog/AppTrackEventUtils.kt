package cn.taqu.lib.base.utils.eventlog

import android.text.TextUtils
import cn.taqu.lib.base.utils.SexTypeUtils
import hb.common.data.AccountHelper
import hb.utils.Loger
import hb.utils.StringUtils
import hb.xtracker.OnEventCreatedListener
import hb.xtracker.XTracker
import org.json.JSONException
import org.json.JSONObject

/**
 * 埋点工具类
 * <AUTHOR>
 * @date 2021-09-28
 */
class AppTrackEventUtils {

    companion object {


        @JvmStatic
        fun trackEvent(eventKey: String) {
            trackEvent(eventKey, null)
        }
        /**
         * 埋点入口
         */
        @JvmStatic
        fun trackEvent(eventKey: String, eventObject: JSONObject? = null) {
            trackEvent(eventKey, eventObject, null)
        }

        /**
         * 统一埋点入口
         */
        @JvmStatic
        fun trackEvent(
            eventKey: String,
            eventObject: JSONObject? = null,
            listener: OnEventCreatedListener?
        ) {
            if (TextUtils.isEmpty(eventKey)) {
                Loger.e(Loger.TAG, "trackEvent key is null")
                return
            }
            // XTracker自建平台埋点
            XTracker.track(eventKey, eventObject, listener)
        }

        /**
         * 7·私信详情页入口
         * 8·个人主页入口
         * 10-其他入口
         * 11-聊天室大厅
         */
        const val SOURCE_MSG = "7"
        const val SOURCE_PERSONAL = "8"
        const val SOURCE_OTHER = "10"
        const val SOURCE_CHAT_ROOM_HALL = "11"


        /**
         * 家族内聊天
         *
         * @param familyUUID 家族uuid
         * @param type 文本 语音 图片 其他
         */
        @JvmStatic
        fun trackFamilyChat(familyUUID: String?, type: String) {
            if(StringUtils.isEmpty(familyUUID)){
                return
            }
            val paramsObj = JSONObject()
            try {
                paramsObj.put("family_uuid", familyUUID)
                paramsObj.put("type", type)
                paramsObj.put("uuid_status", "非游客")
            } catch (e: JSONException) {
                e.printStackTrace()
            }
            trackEvent("family_chat", paramsObj)
        }

        /**
         * 退出家族
         * @param familyUUID 家族uuid
         */
        @JvmStatic
        fun trackLeaveFamily(familyUUID: String?) {
            if (StringUtils.isEmpty(familyUUID)) {
                return
            }
            val paramsObj = JSONObject()
            try {
                paramsObj.put("family_uuid", familyUUID)
            } catch (e: JSONException) {
                e.printStackTrace()
            }
            trackEvent("leave_family", paramsObj)
        }

        /**
         * 家族群内用户互动
         * @param familyUUID 家族uuid
         * @param activeUuid 被点击用户uuid
         */
        @JvmStatic
        fun trackFamilyInteract(familyUUID: String?, activeUuid: String?, type: String) {
            if (StringUtils.isEmpty(familyUUID) || StringUtils.isEmpty(activeUuid)) {
                return
            }
            val paramsObj = JSONObject()
            try {
                paramsObj.put("family_uuid", familyUUID)
                paramsObj.put("active_uuid", activeUuid)
                paramsObj.put("type", type)
                paramsObj.put("family_interact", "非游客")
            } catch (e: JSONException) {
                e.printStackTrace()
            }
            trackEvent("family_interact", paramsObj)
        }

        /**
         *  新人通用引导弹窗曝光
         */
        fun trackAuctionGuideOne() {
            try {
                var sex = "无"
                if (SexTypeUtils.isSexTypeBoy()) {
                    sex = "男"
                } else if (SexTypeUtils.isSexTypeGirl()) {
                    sex = "女"
                }
                val jsonObject = JSONObject()
                jsonObject.put("paipai_new_popup_type",sex)
                trackEvent("chatroom_relationship_new_popup_show", jsonObject)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }



        /**
         * 进入聊天广场
         *
         * @param source 聊天广场类型：恋爱交友、互守滴滴、快速脱单
         */
        @JvmStatic
        fun trackEnterChatSquare(source: String?) {
            try {
                val jsonObject = JSONObject()
                jsonObject.put("source", source)
                trackEvent("enter_chat_square", jsonObject)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }


        /**
         * 聊天广场发言
         *
         * @param source 聊天广场类型
         */
        @JvmStatic
        fun trackChatSquareMessage(source: String?) {
            try {
                val jsonObject = JSONObject()
                jsonObject.put("source", source)
                trackEvent("chat_square_message", jsonObject)
            } catch (e: Exception) {
                // do nothing
            }
        }


        /**
         * 聊天广场互动
         *
         * @param source    聊天广场类型
         * @param activeUid 互动id
         * @param type      类型：送礼、发红包、领红包
         * @param price     实际消费金额
         */
        @JvmStatic
        fun trackChatSquareInteract(
            source: String?,
            activeUid: String?,
            type: String?,
            price: Long?,
        ) {
            try {
                val jsonObject = JSONObject()
                jsonObject.put("source", source)
                jsonObject.put("active_uuid", activeUid)
                jsonObject.put("type", type)
                jsonObject.put("price", price)
                trackEvent("chat_square_interact", jsonObject)
            } catch (e: Exception) {
                // do nothing
            }
        }


        /**
         * 聊天广场 tab曝光  互守滴滴那三个tab的id
         *
         * @param tab       底部tab：附近，最近访问，关注
         * @param familyUid
         * @param aquareUid
         */
        @JvmStatic
        fun trackChatSquareExpose(aquareUid: String?) {
            try {
                val jsonObject = JSONObject()
                jsonObject.put("aquare_uuid", aquareUid)
                trackEvent("chat_square_expose", jsonObject)
            } catch (e: Exception) {
                // do nothing
            }
        }

        /**
         * 聊天广场tab点击
         *
         * @param tab 附近、最近访问、关注
         */
        @JvmStatic
        fun trackChatSquareTabClick(tab: String?) {

            try {
                val jsonObject = JSONObject()
                jsonObject.put("tab", tab)
                trackEvent("chat_square_tabclick", jsonObject)
            } catch (e: Exception) {
                // do nothing
            }

        }


        /**
         * 公开家族点击
         *
         * @param tab       附近，最近访问，关注
         * @param familyUid
         */
        fun trackOpenFamilyClick(tab: String, familyUid: String) {
            try {
                val jsonObject = JSONObject()
                jsonObject.put("tab", tab)
                jsonObject.put("family_uuid", familyUid)
                trackEvent("open_family_click", jsonObject)
            } catch (e: Exception) {
                // do nothing
            }
        }


        /**
         * 家族公开按钮
         * @param isOpen
         */
        @JvmStatic
        fun trackFamilyStatusOpenClick(isOpen: Boolean) {
            try {
                val jsonObject = JSONObject()
                jsonObject.put("type", if (isOpen) "开启" else "关闭")
                trackEvent("family_status_openclick", jsonObject)
            } catch (e: Exception) {
                // do nothing
            }
        }

        /**
         * 禁止游客发言
         * @param isOpen
         */
        @JvmStatic
        fun trackVisitorProhibitClick(isOpen: Boolean) {
            try {
                val jsonObject = JSONObject()
                jsonObject.put("type", if (isOpen) "开启" else "关闭")
                trackEvent("visitor_prohibit_click", jsonObject)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        /**
         * 离开家族聊天页面
         *
         * @param stayTime 停留时长（毫秒）
         */
        @JvmStatic
        fun trackFamilyStay(stayTime: String) {
            try {
                val jsonObject = JSONObject()
                jsonObject.put("uuid_status", "非游客")
                jsonObject.put("stay_time", stayTime)
                trackEvent("failmy_stay", jsonObject)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }


        /**
         * 申请其他家族弹窗曝光
         * 未达等级，引导用户申请其他家族，弹窗曝光量,显示一次上报一次
         */
        @JvmStatic
        fun trackApplyOtherFamilyExpose() {
            try {
                trackEvent("apply_otherfamily_expose")
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        /**
         * 申请其他家族弹窗点击
         * 未达等级，引导用户申请其他家族，弹窗点击量,点击一次上报一次
         *
         *
         */
        @JvmStatic
        fun trackApplyOtherFamilyClick() {
            try {
                trackEvent("apply_otherfamily_click")
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        /**
         * 【登陆】同意授权进入应用弹窗展现
         */
        @JvmStatic
        fun trackLoginLicenceShow() {
            val jsonObject = JSONObject()
            trackEvent("login_licence_show", jsonObject)
        }

        /**
         * 【登陆】同意授权进入应用弹窗点击
         */
        @JvmStatic
        fun trackLoginLicenceClick(position: String) {
            val jsonObject = JSONObject()
            jsonObject.put("position", position)
            trackEvent("login_licence_click", jsonObject)
        }

        /**
         * 【登陆】内部同意协议点击
         */
        @JvmStatic
        fun trackOpenLoginLicenceClick(position: String) {
            val jsonObject = JSONObject()
            jsonObject.put("position", position)
            trackEvent("open_login_licence_click", jsonObject)
        }

        /**
         * 家族-召唤空投icon点击次数
         */
        @JvmStatic
        fun trackAirDropIconClickReport() {
            trackEvent("family_kongtou_icon_click")
        }

        /**
         * 家族-召唤空投
         *
         * @param name 套餐名称
         * @param id 套餐id
         * @param familyUuid 家族uuid
         */
        @JvmStatic
        fun trackAirDropClickReport(name: String?, id: String?, familyUuid: String?) {
            val jsonObject = JSONObject()
            jsonObject.put("name", name)
            jsonObject.put("id", id)
            jsonObject.put("family_uuid", familyUuid)
            trackEvent("family_kongtou_click", jsonObject)
        }

        /**
         * 召唤空投-点击领取按钮
         *
         * @param type 浮窗/消息
         */
        @JvmStatic
        fun trackAirDropSuccess(type: String?) {
            val jsonObject = JSONObject()
            jsonObject.put("type", type)
            trackEvent("family_kongtou_pop_success", jsonObject)
        }

        /**
         * 私信内页激励红包icon曝光
         * @param groupId 男用户群
         * @param maleAccountUuid 男用户UUID
         * @param conversationId 会话ID
         * @param redPackType 红包类型
         */
        @JvmStatic
        fun trackSocialContinuousChatReadPackIconExposure(
            groupId: String?, maleAccountUuid: String?,
            conversationId: String?, redPackType: String?,
        ) {
            val jsonObject = JSONObject()
            jsonObject.put("group_id", groupId)
            jsonObject.put("male_account_uuid", maleAccountUuid)
            jsonObject.put("conversation_id", conversationId)
            jsonObject.put("redpack_type", redPackType)
            trackEvent("social_continuous_chat_redpack_icon_exposure", jsonObject)
        }

        /**
         * 私信内页激励红包icon点击
         * @param groupId 男用户群
         * @param maleAccountUuid 男用户UUID
         * @param conversationId 会话ID
         */
        @JvmStatic
        fun trackSocialContinuousChatReadPackIconClick(groupId: String?, maleAccountUuid: String?, conversationId: String?) {
            val jsonObject = JSONObject()
            jsonObject.put("group_id", groupId)
            jsonObject.put("male_account_uuid", maleAccountUuid)
            jsonObject.put("conversation_id", conversationId)
            trackEvent("social_continuous_chat_redpack_icon_clk", jsonObject)
        }


        /**
         * 家族主页曝光
         * @param source 来源：聊天广场资料卡片,聊天广场昵称,个人主页
         */
        @JvmStatic
        fun trackFamilyHomepageDetailExpose(source:String) {
            runCatching {
                val jsonObject = JSONObject()
                jsonObject.put("source",source)
                trackEvent("family_homepage_detail_expose",jsonObject)
            }
        }


        /**
         * 裂变邀请 v2.04 用户承诺弹窗曝光
         * @param ruleId 规则ID，现为规则图片文件名
         */
        @JvmStatic
        fun trackAccountFissionDialogShow(ruleId: String?) {
            if (StringUtils.isEmpty(ruleId)) {
                return
            }
            val jsonObject = JSONObject()
            jsonObject.put("rule_id", ruleId)
            //客户端固定为“注册”
            jsonObject.put("tab", "注册")
            trackEvent("invite_essentialrule_show", jsonObject)
        }
        /**
         * 裂变邀请 v2.04 必读新规确认按钮点击
         * @param ruleId 规则ID，现为规则图片文件名
         */
        @JvmStatic
        fun trackAccountFissionConfirmClick(ruleId: String?) {
            if (StringUtils.isEmpty(ruleId)) {
                return
            }
            val jsonObject = JSONObject()
            jsonObject.put("rule_id", ruleId)
            //客户端固定为“注册”
            jsonObject.put("tab", "注册")
            trackEvent("invite_essentialrule_click", jsonObject)
        }

        /**
         * 【大盘】我的收益点击
         */
        @JvmStatic
        fun trackMeMyRewardClick() {
            val jsonObject = JSONObject()
            trackEvent("me_myreward_click", jsonObject)
        }

        /**
         * 拍拍关系，进入拍拍房来源
         */
        @JvmStatic
        fun trackChatRoomFamilySource(source:String) {
            val jsonObject = JSONObject()
            jsonObject.put("source", source)
            trackEvent("chatroom_family_source", jsonObject)
        }


        /**
         * 家族拍拍关系弹窗曝光埋点
         */
        @JvmStatic
        fun trackFamilyPaipaiCardExpose(position:String,type:String) {
            val jsonObject = JSONObject()
            jsonObject.put("position", position)
            jsonObject.put("type",type)
            trackEvent("family_paipai_card_expose", jsonObject)
        }

        /**
         * 家族拍拍关系弹窗，点击前往关系速配埋点
         */
        @JvmStatic
        fun trackFamilyPaiPaiCardClick(position: String,type:String) {
            val jsonObject = JSONObject()
            jsonObject.put("position", position)
            jsonObject.put("click_type",type)
            trackEvent("family_paipai_card_click", jsonObject)
        }


        /**
         * 家族活跃度宝箱点击上报埋点
         */
        @JvmStatic
        fun trackFamilyChestClick(){
            trackEvent("family_chest_click")
        }


        /**
         * 家族邀请入口点击
         * @param source 上报位置
         */
        @JvmStatic
        fun trackFamilyInviteClick(source:String) {
            val jsonObject = JSONObject()
            jsonObject.put("source", source)
            trackEvent("family_invite_click", jsonObject)
        }

        /**
         * 家族邀请用户点击
         */
        @JvmStatic
        fun trackFamilyInviteUserClick(source:String,accountId:String) {
            val jsonObject = JSONObject()
            jsonObject.put("source",source)
            jsonObject.put("account_uuid",accountId)
            trackEvent("family_invite_user_click",jsonObject)
        }
        /**
         * 家族榜单消息曝光
         * */
        @JvmStatic
        fun trackFamilyRankMsgExposure(title:String) {
            val jsonObject = JSONObject()
            jsonObject.put("type_title", title)
            trackEvent("family_top_exposure",jsonObject)
        }

        /**
         * 家族榜单消息 按钮点击
         * */
        @JvmStatic
        fun trackFamilyRankMsgBtnClick(title:String, btnText: String) {
            val jsonObject = JSONObject()
            jsonObject.put("type_title", title)
            jsonObject.put("position_title", btnText)
            trackEvent("family_top_click",jsonObject)
        }

        /**
         * 深色模式-【通用】系统模式选择
         */
        fun trackDarkModeChoose(type: String?) {
            val jsonObject = JSONObject()
            jsonObject.put("type", type)
            trackEvent("type_chose", jsonObject)
        }

        /**
         * 深色模式-启动曝光
         */
        @JvmStatic
        fun trackDarkModeLaunch(type: String?) {
            val jsonObject = JSONObject()
            jsonObject.put("value", type)
            trackEvent("dark_mode", jsonObject)
        }

        /**
         * 家族关系尾巴曝光
         */
        @JvmStatic
        fun trackFamilyRelationExpose(uuid:String?,relationName:String?,position:String?) {
            val jsonObject = JSONObject()
            jsonObject.put("expose_uuid",uuid)
            jsonObject.put("relation_name",relationName)
            jsonObject.put("position",position)
            trackEvent("family_relation_expose",jsonObject)
        }

        /**
         * 家族成功拍下拍拍关系的消息卡片曝光
         */
        @JvmStatic
        fun trackFamilyPaiPaiSucceedExpose(uuids:String?,relationName:String?) {
            val jsonObject = JSONObject()
            jsonObject.putOpt("expose_uuid",uuids)
            jsonObject.putOpt("relation_name",relationName)
            trackEvent("family_paipai_succeed_expose",jsonObject)
        }

        /**
         * 家族成功拍下拍拍关系的消息卡片，点击埋点
         */
        @JvmStatic
        fun trackFamilyPaiPaiSucceedClick(uuids:String,relationName:String?) {
            val jsonObject = JSONObject()
            jsonObject.put("click_uuid",uuids)
            jsonObject.put("relation_name",relationName)
            trackEvent("family_paipai_succeed_click",jsonObject)
        }


        /**
         * 怕拍关系列表曝光
         */
        @JvmStatic
        fun trackFamilyRelationCardExpose(position:String,exposeUuid:String?,type:String) {
            val jsonObject = JSONObject()
            jsonObject.put("position",position)
            jsonObject.put("expose_uuid",exposeUuid)
            jsonObject.put("type",type)
            trackEvent("family_relation_card_expose",jsonObject)
        }


        /**
         *  拍拍列表点击
         */
        @JvmStatic
        fun trackFamilyRelationCardClick(action:String) {
            val jsonObject = JSONObject()
            jsonObject.put("action",action)
            trackEvent("family_relation_card_click",jsonObject)
        }


        /**
         * 进入家族聊天界面的时候上报埋点
         * @param familyUuid 家族uuid
         */
        @JvmStatic
        fun trackFamilyDetailShowTemp(familyUuid:String?) {
            val obj = JSONObject().apply {
                familyUuid?.let {
                    put("family_uuid", it)
                }
            }
            trackEvent("family_detail_show_temp",obj)
        }

        /**
         * 【家族】活跃宝箱-点击
         * event: family_active_chest_click
         * https://o15vj1m4ie.feishu.cn/wiki/wikcnmsNMV6ChNBRb0z3PNfzX8F
         * @param position
         *      - 第一个宝箱
         *      - 第二个宝箱
         *      - 第三个宝箱
         *      - 第四个宝箱
         *      - 活跃宝箱
         *      - 近7日活跃度
         * */
        @JvmStatic
        fun trackFamilyActiveChestClick(position: String,familyUuid:String?,chestType:String?) {
            val obj = JSONObject().apply {
                put("position", position)
                familyUuid?.let {
                    put("family_uuid",familyUuid)
                }
                chestType?.let {
                    put("chest_type",chestType)
                }
            }
            trackEvent("family_active_chest_click",obj)
        }

        /**
         * 【家族】活跃宝箱-成功领取
         * event: family_active_chest_success
         * https://o15vj1m4ie.feishu.cn/wiki/wikcnmsNMV6ChNBRb0z3PNfzX8F
         * @param position
         *      - 第一个宝箱
         *      - 第二个宝箱
         *      - 第三个宝箱
         *      - 第四个宝箱
         * */
        @JvmStatic
        fun trackFamilyActiveChestSuccess(position: String,familyUuid: String?,chestType: String?) {
            val obj = JSONObject().apply {
                put("position", position)
                chestType?.let {
                    put("chest_type",it)
                }
                familyUuid?.let {
                    put("family_uuid",it)
                }
            }
            trackEvent("family_active_chest_success",obj)
        }

        /**
         * 女用户语音引导曝光
         */
        fun trackSocialFirstMsgVoiceGuideShow(targetUuid:String,conversationId:String){
            val obj = JSONObject().apply {
                put("to_uuid",targetUuid)
                put("conversation_id",conversationId)
            }
            trackEvent("social_firstmsg_voiceguaid_show",obj)
        }

        /**
         * 谁是大赢家 入口曝光
         */
        @JvmStatic
        fun trackChatRoomWhoWinsEnterExp(source: String?){
            val obj = JSONObject().apply {
                put("source",source)
            }
            trackEvent("chatroom_whowins_enter_exposure",obj)
        }

        /**
         * 谁是大赢家 入口点击
         */
        @JvmStatic
        fun trackChatRoomWhoWinsEnterClick(source: String?){
            val obj = JSONObject().apply {
                put("source",source)
            }
            trackEvent("chatroom_whowins_enter_click",obj)
        }

        /**
         * 家族 开启/关闭 消息免打扰
         * @param enable true 当前操作打开了 / false 当前操作关闭了  消息免打扰
         * */
        @JvmStatic
        fun trackFamilyNoDisturbClick(enable: Boolean, source: String, familyUuid: String) {
            val obj = JSONObject().apply {
                put("position",source)
                put("result","${if (enable) {"开启"} else {"关闭"}}消息勿扰")
                put("family_uuid",familyUuid)
            }
            trackEvent("family_nodisturb_click",obj)
        }

        /**
         * 家族点击推荐送礼
         */
        @JvmStatic
        fun trackFamilyGiftRecomClick(uuid:String?) {
            if(TextUtils.isEmpty(uuid)) {
                return
            }
            val obj = JSONObject().apply {
                put("receive_uuid",uuid)
            }
            trackEvent("family_gift_recom_click",obj)
        }

        /**
         * app升级-开始下载
         *
         * @param source 下载来源
         */
        fun trackAppDownloadStart(source: String?) {
            val obj = JSONObject().apply {
                put("source", source)
            }
            trackEvent("app_download_start", obj)
        }

        /**
         * app升级-下载完成
         *
         * @param source 下载来源
         */
        fun trackAppDownloadSuccess(source: String?) {
            val obj = JSONObject().apply {
                put("source", source)
            }
            trackEvent("app_download_success", obj)
        }

        //regione 家族聊天体验优化
        /**
         * 【家族】家族聊天室卡片曝光
         */
        @JvmStatic
        fun trackFamilyChatRoomChatExpose(familyUuid:String?,exposeUuid:String?,roomType:String?,subType:String?,btnName:String?){
            val obj = JSONObject().apply {
                putOpt("family_uuid",familyUuid)
                putOpt("be_exposed_uuid",exposeUuid)
                putOpt("biz_type",roomType)
                putOpt("sub_type",subType)
                putOpt("btn_name",btnName)
            }
            trackEvent("family_chat_card_expose",obj)
        }

        /**
         * 【家族】家族聊天室卡片点击
         */
        @JvmStatic
        fun trackFamilyChatRoomChatClick(familyUuid:String?,clickUuid:String?,roomType:String?,subType:String?,btnName:String?){
            val obj = JSONObject().apply {
                putOpt("family_uuid",familyUuid)
                putOpt("be_clicked_uuid",clickUuid)
                putOpt("biz_type",roomType)
                putOpt("sub_type",subType)
                putOpt("btn_name",btnName)
            }
            trackEvent("family_chat_card_click",obj)
        }
        //endregione


        /**
         * 家族顶部挂件曝光
         */
        fun trackFamilyDomainExposure(type:String?,activityName:String?,title:String?,
                                      familyUuid: String?,stage:String?) {
            val obj = JSONObject().apply {
                put("type", type)
                put("activity_name", activityName)
                put("title",title)
                put("family_uuid",familyUuid)
                put("stage",stage)
            }
            trackEvent("family_domain_exposure", obj)
        }

        /**
         * 挂件按钮点击上报
         */
        fun trackFamilyDomainClick(position:String,activityName: String?,title:String?,
                                   familyUuid: String?,btnName:String?,stage:String?) {
            val obj = JSONObject().apply {
                put("position", position)
                put("activity_name", activityName)
                put("family_uuid",familyUuid)
                put("title",title)
                put("button_name",btnName)
                put("stage",stage)
            }
            trackEvent("family_domain_click", obj)
        }


        /**
         * 家族踢人弹窗曝光
         * @param familyUuid
         * @param content
         */
        fun trackFamilyKickPersonExpose(familyUuid:String?,content:String?) {
            val obj = JSONObject().apply {
                put("family_uuid", familyUuid)
                put("content",content)
            }
            trackEvent("family_kickperson_expose", obj)
        }

        /**
         * 踢人提醒弹窗点击埋点
         */
        fun  trackFamilyKickPersonClick(familyUuid:String?,content:String?,result:String) {
            val obj = JSONObject().apply {
                put("family_uuid",familyUuid)
                put("content",content)
                put("result",result)
            }
            trackEvent("family_kickperson_click",obj)
        }

        /**
         * 家族互动宝箱曝光
         */
        fun trackFamilyActiveChestShow(familyUuid:String, tab:String) {
            val obj = JSONObject().apply {
                put("family_uuid",familyUuid)
                put("tab",tab)
            }
            trackEvent("family_active_chest_show",obj)
        }

        /**
         * 家族广场 - 家族列表tab 曝光
         * https://o15vj1m4ie.feishu.cn/wiki/wikcnatj5Ik3qAnWTA3dLUamVvd
         * @param tab 家族列表对应的Tab
         * */
        fun trackFamilySquareListExposure(tab: String?) {
            val obj = JSONObject().apply {
                put("tab", tab)
            }
            trackEvent("family_square_show", obj)
        }

        /**
         * 家族申请 - 未达到家族准入标准时的弹窗
         * https://o15vj1m4ie.feishu.cn/wiki/wikcnatj5Ik3qAnWTA3dLUamVvd
         * */
        fun trackFamilyAdmittanceExposure(familyUuid: String?) {
            val obj = JSONObject().apply {
                put("family_uuid", familyUuid)
            }
            trackEvent("family_admittance_expose", obj)
        }

        /**
         * 家族申请 - 未达到家族准入标准时的弹窗
         * https://o15vj1m4ie.feishu.cn/wiki/wikcnatj5Ik3qAnWTA3dLUamVvd
         * */
        fun trackFamilyAdmittanceClick(familyUuid: String?, type: String?) {
            val obj = JSONObject().apply {
                put("family_uuid", familyUuid)
                put("type", type)
            }
            trackEvent("family_admittance_click", obj)
        }

        /**
         * 家族签到奖励弹窗曝光
         */
        fun trackFamilySignLevelAwardExp(familyUUID: String?){
            val obj = JSONObject().apply {
                put("family_uuid", familyUUID)
            }
            trackEvent("family_sign_award_expose", obj)
        }

        /**
         * 家族推荐弹窗曝光
         */
        @JvmStatic
        fun trackFamilyRecomPopExps(familyUuid: String?, scene: String?, tagList: List<String>?) {
            val obj = JSONObject().apply {
                put("family_uuid", familyUuid)
                putOpt("sence", scene)
                if(tagList != null) put("label_tag", tagList.joinToString(",", "[", "]"))
            }
            trackEvent("family_recompop_exps", obj)
        }

        /**
         * 家族推荐弹窗点击进入家族
         */
        @JvmStatic
        fun trackFamilyRecomPopClick(familyUuid: String?, scene: String?, tagList: List<String>?) {
            val obj = JSONObject().apply {
                put("family_uuid", familyUuid)
                putOpt("sence", scene)
                tagList?.let {
                    put("label_tag", tagList.joinToString(",", "[", "]"))
                }
            }
            trackEvent("family_recompop_click", obj)
        }

        /**
         * 首页-开屏页-生日祝福曝光
         */
        @JvmStatic
        fun trackSplashBirthCoopenExpo() {
            trackEvent("birth_coopen_expoure")
        }

        /**
         * 首页-开屏页-生日祝福点击
         */
        @JvmStatic
        fun trackSplashBirthCoopenClick() {
            trackEvent("birth_coopen_click")
        }

        /**
         * 【话题助手】奖励弹窗曝光
         *
         * @param rewardId 奖励id
         */
        fun trackChatAssistantRewardId(rewardId: String?) {
            val obj = JSONObject().apply {
                put("reward_id", rewardId)
            }
            trackEvent("chatassistant_reward_show", obj)
        }
    }
}