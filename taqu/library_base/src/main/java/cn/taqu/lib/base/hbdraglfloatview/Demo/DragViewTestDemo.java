package cn.taqu.lib.base.hbdraglfloatview.Demo;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import cn.taqu.lib.base.R;
import cn.taqu.lib.base.databinding.DragDemoBinding;
import cn.taqu.lib.base.hbdraglfloatview.DragFloatType;
import cn.taqu.lib.base.hbdraglfloatview.HBDragFloatView;
import hb.utils.ColorUtils;
import hb.utils.Loger;
import hb.utils.SizeUtils;
import hb.utils.ToastUtils;

/**
 * 拖拽球的Demo
 * [飞书需求]()
 *
 * <AUTHOR>
 * @date 2024-08-25
 */
public class DragViewTestDemo implements HBDragFloatView {
    private String TAG ="DragViewTestDemo";
    private DragDemoBinding mBinding;
    @Override
    public String type() {
        return DragFloatType.PARTY_GAME;
    }

    @Override
    public View onCreateView(Context context, FrameLayout rootView) {
        mBinding = DragDemoBinding.inflate(LayoutInflater.from(context));
        return mBinding.getRoot();
    }

    @Override
    public void initViewLayoutParams(Context context, FrameLayout.LayoutParams params) {
        Loger.d(TAG,"initViewLayoutParams");
//        params.height = SizeUtils.dp2px(80);
//        params.topMargin = 300;
//        params.leftMargin = 300;
    }

    @Override
    public void onViewCreated(FrameLayout rootView) {
        Loger.d(TAG,"onViewCreated");
        rootView.getChildAt(0).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ToastUtils.show("fdafdas");
            }
        });
        rootView.getChildAt(0).setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                ToastUtils.show("onLongClick");
                return true;
            }
        });
    }

    @Override
    public void onResume() {
        Loger.d(TAG,"onResume");
    }

    @Override
    public void onPause() {
        Loger.d(TAG,"onPause");
    }

    @Override
    public void hideFloatView() {
        mBinding.getRoot().setVisibility(View.GONE);
    }

    @Override
    public void showFloatView() {
        mBinding.getRoot().setVisibility(View.VISIBLE);
    }

    @Override
    public View interceptDragView() {
        return mBinding.scrollView;
    }

    @Override
    public void onDestroy() {
        Loger.d(TAG,"onDestroy");
    }

    @Override
    public int rightEdgeSize() {
        return SizeUtils.dp2px(10);
    }

    @Override
    public boolean enableDrag() {
        return false;
    }
}
