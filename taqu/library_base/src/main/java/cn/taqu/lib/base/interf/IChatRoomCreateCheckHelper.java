package cn.taqu.lib.base.interf;

import android.content.Context;

/**
 * <AUTHOR>
 * create time : 2020/9/9
 * desc :
 */
public interface IChatRoomCreateCheckHelper {
    void onStartCheck(Context context);

    void onStartCheck(Context context, String roomType, String subRoomType);

    void onDeleteRoom();

    void setGenderCertification(String genderCertification);

    void setDeleteJumpAgree(boolean isDetelRoom);

    /**
     * 从家族聊天室创建房间的流程
     *
     * @param roomTpy 房间类型
     * @param subType 房间子类型
     * @param gameSn  游戏类型
     */
    void onMessageCreateRoom(int roomTpy, int subType, String gameSn);

    /**
     * 私信邀请对方uuid
     * @param msgInviteUuid
     */
    void setMsgInviteReceiveUuid(String msgInviteUuid);

    /**
     * 设置实名认证需要的弹窗文案
     * @param content
     */
    void setRealNameTipContent(String content);

    /**
     * 设置创建房间定位的roomType
     * @param roomType
     */
    void setOpenChatRoomType(int roomType);


}
