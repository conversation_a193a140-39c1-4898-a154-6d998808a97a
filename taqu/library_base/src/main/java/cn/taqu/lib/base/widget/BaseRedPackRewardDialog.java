package cn.taqu.lib.base.widget;

import android.content.Context;
import android.graphics.Camera;
import android.graphics.Matrix;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.OvershootInterpolator;
import android.view.animation.Transformation;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.Group;
import androidx.recyclerview.widget.RecyclerView;

import com.ushengsheng.widget.HorizontalItemDecoration;

import java.util.List;

import cn.taqu.lib.base.R;
import cn.taqu.lib.base.bean.BaseRedPackRewardItemBean;
import cn.taqu.lib.base.holder.BaseRedPackRewardNewViewHolder;
import cn.taqu.lib.base.holder.BaseRedPackRewardViewHolder;
import cn.taqu.lib.base.xtracker.BaseCommonRewardDialogTracker;
import hb.actionqueue.annotation.XActionQueue;
import hb.utils.ScreenUtils;
import hb.utils.SizeUtils;
import hb.xadapter.XBaseAdapter;
import hb.ximage.fresco.BaseDraweeView;
import hb.xstyle.xdialog.XLifecycleDialog;


/**
 * 奖励红包弹框
 *
 * <AUTHOR>
 * @date 2020-12-02
 */
@XActionQueue(type = XActionQueue.TYPE_DIALOG)
public class BaseRedPackRewardDialog<T> extends XLifecycleDialog {
    protected ImageView mIvClose;
    protected TextView mTvTaskTitle;
    protected RecyclerView mRvReward;
    protected Group mGroupContent;

    private XBaseAdapter mXBaseAdapter;
    private T mBean;
    protected TextView mRewardDesc;
    protected TextView mTvTitle;

    /**
     * 礼物背景
     */
    protected BaseDraweeView mIvRewardBg;
    /**
     * 任务名称
     */
    protected String mTaskName;

    /**
     * 按钮
     */
    protected TextView mTvTake;

    public BaseRedPackRewardDialog(@NonNull Context context, @Nullable T data) {
        super(context, R.style.CustomTheme_Dialog);
        mBean = data;
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (getWindow() != null) {
            WindowManager.LayoutParams lp = getWindow().getAttributes();

            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
            lp.gravity = Gravity.CENTER;
            getWindow().setAttributes(lp);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(getLayoutId());
        setCanceledOnTouchOutside(false);
        setCancelable(false);

        initView();
        initRewardRecycler();
        mGroupContent.setVisibility(View.VISIBLE);
        bindViewData(mBean);
    }

    public int getLayoutId(){
        return R.layout.base_red_pack_reward_dialog;
    }

    protected boolean showNewRewardHolderView() {
        return false;
    }

    private void initView() {
        mIvClose = findViewById(R.id.ivClose);
        mTvTaskTitle = findViewById(R.id.tvTaskTitle);
        mRvReward = findViewById(R.id.recyclerView);
        mGroupContent = findViewById(R.id.groupContent);
        mRewardDesc = findViewById(R.id.tvRewardDesc);
        mTvTitle = findViewById(R.id.tvTitle);
        mIvRewardBg = findViewById(R.id.ivRewardBg);
        if (!showNewRewardHolderView()) {
            mIvRewardBg.setImageFromResource(R.drawable.base_red_package_pop_ic);
            // 只有旧版UI才有这个按钮
            mTvTake = findViewById(R.id.tvTake);
            mTvTake.setOnClickListener(v -> {
                        BaseCommonRewardDialogTracker.INSTANCE.trackTaskRewardPopupClick(mTaskName, BaseCommonRewardDialogTracker.CLICK_POSITON_RELATION);
                        dismiss();
                    }
            );
        } else {
            // 以宽度为基准，宽高等比适配
            int resize = ScreenUtils.getScreenWidth();
            mIvRewardBg.getLayoutParams().height = resize;
            mIvRewardBg.getLayoutParams().width = resize;
            mIvRewardBg.setImageFromResource(R.drawable.base_brand_red_packet_dialog_bg);
        }
        mIvClose.setOnClickListener(v -> {
            BaseCommonRewardDialogTracker.INSTANCE.trackTaskRewardPopupClick(mTaskName, BaseCommonRewardDialogTracker.CLICK_POSITON_CLOSE);
            dismiss();
        });
    }

    private void initRewardRecycler() {
        mXBaseAdapter = new XBaseAdapter(getContext());
        if (showNewRewardHolderView()) {
            mXBaseAdapter.register(BaseRedPackRewardItemBean.class, BaseRedPackRewardNewViewHolder.class);
        } else {
            mXBaseAdapter.register(BaseRedPackRewardItemBean.class, BaseRedPackRewardViewHolder.class);
        }
        mRvReward.setAdapter(mXBaseAdapter);
    }

    protected void bindViewData(T bean){

    }

    public <T extends BaseRedPackRewardItemBean> void setItems(List<T> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        mXBaseAdapter.setItems(list);

        //设置居中显示
        if (showNewRewardHolderView()) {
            if (list.size() >= 3) {
                mRvReward.getLayoutParams().width = ScreenUtils.getScreenWidth() - SizeUtils.dp2px(140);
            }
        } else {
            if (list.size() == 2) {
                final HorizontalItemDecoration horizontalItemDecoration =
                        new HorizontalItemDecoration(SizeUtils.dp2px(0f), SizeUtils.dp2px(0f), SizeUtils.dp2px(26f));
                mRvReward.addItemDecoration(horizontalItemDecoration);
            } else if (list.size() > 2) {
                final HorizontalItemDecoration horizontalItemDecoration =
                        new HorizontalItemDecoration(SizeUtils.dp2px(39f), SizeUtils.dp2px(39f), SizeUtils.dp2px(26f));
                mRvReward.addItemDecoration(horizontalItemDecoration);
            }
        }


        mXBaseAdapter.notifyDataSetChanged();
    }

    public static class RotateYAnimation extends Animation {
        private int mCenterX = 0;
        private int mCenterY = 0;
        private Camera mCamera = new Camera();

        /**
         * 获取坐标，定义动画时间
         *
         * @param width
         * @param height
         * @param parentWidth
         * @param parentHeight
         */
        @Override
        public void initialize(int width, int height, int parentWidth, int parentHeight) {
            super.initialize(width, height, parentWidth, parentHeight);
            //获得中心点坐标
            mCenterX = width / 2;
            mCenterY = width / 2;
            //动画执行时间 自行定义
            setInterpolator(new OvershootInterpolator());
            setDuration(1000);
        }

        /**
         * 旋转的角度设置
         *
         * @param interpolatedTime
         * @param t
         */
        @Override
        protected void applyTransformation(float interpolatedTime, Transformation t) {
            super.applyTransformation(interpolatedTime, t);
            Matrix matrix = t.getMatrix();
            mCamera.save();
            //设置camera的初始位置
            mCamera.setLocation(0f, 0f, 180f);
            mCamera.rotateY(360 * interpolatedTime);
            //把我们的摄像头加在变换矩阵上
            mCamera.getMatrix(matrix);
            //设置翻转中心点
            matrix.preTranslate(-mCenterX, -mCenterY);
            matrix.postTranslate(mCenterX, mCenterY);
            mCamera.restore();
        }
    }

}