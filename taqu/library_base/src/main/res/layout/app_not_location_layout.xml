<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    tools:parentTag="cn.taqu.lib.base.widget.AppNoLocationView">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/ivEmpty"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginTop="120dp"
            app:actualImageScaleType="centerCrop" />

        <TextView
            android:id="@+id/tvNoLocationTitle"
            style="@style/Text.H3.G990"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="19dp"
            android:textStyle="bold"
            android:text="开启定位后才能看到哦" />

        <TextView
            android:id="@+id/tvNoLocationSubTitle"
            style="@style/Text.B2.G600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:text="设置-应用管理-他趣-权限-定位" />

        <hb.drawable.shape.view.HbTextView
            android:id="@+id/btnRequestLocation"
            android:layout_width="120dp"
            android:layout_marginTop="24dp"
            android:gravity="center"
            style="@style/Btn_A.XL"
            android:text="去开启" />
    </LinearLayout>
</merge>