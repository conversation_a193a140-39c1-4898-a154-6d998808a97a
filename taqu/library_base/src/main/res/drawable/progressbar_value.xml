<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:id="@android:id/background">
        <shape>
            <corners android:radius="5dp" />

            <stroke
                android:width="0.5sp"
                android:color="#ffff00" />

            <gradient
                android:angle="90"
                android:endColor="#99000000"
                android:startColor="#99000000" />

            <padding
                android:bottom="2dp"
                android:left="2dp"
                android:right="2dp"
                android:top="2dp" />
        </shape>
    </item>
    <item android:id="@android:id/secondaryProgress">
        <scale android:scaleWidth="100%">
            <shape>
                <corners android:radius="5dp" />

                <gradient
                    android:angle="45"
                    android:endColor="#fc3414"
                    android:startColor="#ffd321" />
            </shape>
        </scale>
    </item>

</layer-list>