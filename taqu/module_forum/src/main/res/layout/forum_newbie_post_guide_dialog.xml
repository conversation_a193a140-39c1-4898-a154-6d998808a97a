<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="bottom">

    <View
        android:id="@+id/container"
        android:background="@drawable/forum_top_radius_16_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="16dp"/>

    <ImageView
        android:id="@+id/ivHi"
        android:layout_width="106dp"
        android:layout_height="106dp"
        android:src="@drawable/forum_post_guide_hi_ic"
        android:scaleType="fitXY"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="16dp"/>

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/Text.B1.G990"
        android:textStyle="bold"
        android:textSize="20sp"
        android:textColor="@color/TH_Gray990"
        android:text="嗨！初次见面\n发动态让更多人认识你吧！"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/container"
        android:layout_marginTop="32dp"
        android:layout_marginStart="16dp"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/albumContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/title"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="24dp">

        <hb.drawable.shape.view.HbImageView
            android:id="@+id/emptyAlbumBg"
            android:layout_width="match_parent"
            android:layout_height="117dp"
            android:scaleType="fitCenter"
            android:paddingTop="44dp"
            android:paddingBottom="44dp"
            android:src="@drawable/forum_album_add_ic"
            app:layout_constraintTop_toTopOf="parent"
            app:solid="@color/TH_Navy110"
            app:corner="7dp"/>

        <TextView
            android:id="@+id/tvTips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/Text.B4.G400"
            android:textSize="12sp"
            android:textColor="@color/TH_Gray400"
            android:text="上传本人自拍生活照会获得更多曝光和搭讪哦～"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/emptyAlbumBg"
            android:layout_marginTop="4dp"/>
        
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvImageList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:visibility="gone"/>

    </androidx.constraintlayout.widget.ConstraintLayout>



    <hb.drawable.shape.view.HbEditText
        android:id="@+id/edInput"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        style="@style/Text.B1.G990"
        android:textColor="@color/TH_Gray990"
        android:textSize="15sp"
        android:text="新人报道，请多指教！听说他趣能脱单新人报道，请多指教！听说他趣新人报道，请多指教！听说他趣新人报道，请多指教！听说他趣？"
        app:layout_constraintTop_toBottomOf="@id/albumContainer"
        android:layout_marginTop="16dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:singleLine="true"
        android:paddingStart="16dp"
        android:paddingEnd="15dp"
        android:ellipsize="end"
        app:solid="@color/TH_Navy110"
        app:corner="8dp"/>

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/editSuffix"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        style="@style/Text.B1.G990"
        android:textColor="@color/TH_Gray990"
        android:textSize="15sp"
        android:gravity="center"
        android:text="新人报道，请多指教！听说他趣能脱单新人报道，请多指教！听说他趣新人报道，请多指教！听说他趣新人报道，请多指教！听说他趣？"
        app:layout_constraintTop_toBottomOf="@id/albumContainer"
        android:layout_marginTop="16dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:singleLine="true"
        android:paddingStart="13dp"
        android:paddingEnd="15dp"
        android:ellipsize="end"
        app:solid="@color/TH_Navy110"
        app:corner="8dp"/>

    <ImageView
        android:id="@+id/ivClearText"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@drawable/forum_edit_text_clear_ic"
        android:scaleType="fitXY"
        app:layout_constraintTop_toTopOf="@id/edInput"
        app:layout_constraintBottom_toBottomOf="@id/edInput"
        app:layout_constraintEnd_toEndOf="@id/edInput"
        android:layout_marginEnd="16dp"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/btnPublish"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        style="@style/BtnA.Large"
        android:text="发布动态"
        android:textSize="16sp"
        app:layout_constraintTop_toBottomOf="@id/edInput"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>