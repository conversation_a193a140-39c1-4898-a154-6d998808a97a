<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="10dp">

    <!--include 的ID 故意写成llUserInfoContainer 跟layout里面的一样，因为
    这样 app:layout_constraintTop_toBottomOf="@+id/llUserInfoContainer" 不会提示
    The id "llUserInfoContainer" is not referring to any views in this layout
    其实真正有作用的是layout里面的id llUserInfoContainer,include 的id 在ConstraintLayout中无效-->
    <include
        android:id="@+id/llUserInfoContainer"
        layout="@layout/forum_layout_forum_list_top_user"
        android:layout_width="match_parent"
        android:layout_height="68dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

<!--    <View-->
<!--        android:id="@+id/spaceLlUserInfoContainer"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="0dp"-->
<!--        android:layout_marginLeft="16dp"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/llUserInfoContainer" />-->

<!--    <hb.ximage.fresco.BaseDraweeView-->
<!--        android:id="@+id/imgTitleStamper"-->
<!--        android:layout_width="25dp"-->
<!--        android:layout_height="20dp"-->
<!--        android:paddingRight="5dp"-->
<!--        android:src="@drawable/forum_stamper_best_ic"-->
<!--        android:visibility="gone"-->
<!--        app:actualImageScaleType="fitXY"-->
<!--        app:layout_constraintStart_toStartOf="@+id/spaceLlUserInfoContainer"-->
<!--        app:layout_constraintTop_toTopOf="@+id/spaceLlUserInfoContainer"-->
<!--        tools:placeholderImage="@drawable/forum_stamper_best_ic"-->
<!--        tools:visibility="visible" />-->

<!--    <hb.xemoji.view.XEmojiTextView-->
<!--        android:id="@+id/tvTitle"-->
<!--        style="@style/Text.T5_2.G1"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginRight="16dp"-->
<!--        android:ellipsize="end"-->
<!--        android:includeFontPadding="false"-->
<!--        android:maxLines="1"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toEndOf="@+id/spaceLlUserInfoContainer"-->
<!--        app:layout_constraintTop_toTopOf="@+id/spaceLlUserInfoContainer"-->
<!--        tools:text="这里是标题    是这里是标题是这里是标题是这里是标题是"-->
<!--        tools:visibility="visible" />-->

<!--    <Space-->
<!--        android:id="@+id/spaceImgContentStamper"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="0dp"-->
<!--        android:layout_marginLeft="16dp"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />-->

<!--    <hb.ximage.fresco.BaseDraweeView-->
<!--        android:id="@+id/imgContentStamper"-->
<!--        android:layout_width="25dp"-->
<!--        android:layout_height="20dp"-->
<!--        android:paddingRight="5dp"-->
<!--        android:src="@drawable/forum_stamper_best_ic"-->
<!--        android:visibility="gone"-->
<!--        app:actualImageScaleType="fitXY"-->
<!--        app:layout_constraintStart_toStartOf="@+id/spaceImgContentStamper"-->
<!--        app:layout_constraintTop_toTopOf="@+id/spaceImgContentStamper"-->
<!--        tools:placeholderImage="@drawable/forum_stamper_best_ic" />-->

    <com.xmhaibao.forum.api.widget.CollapseTextView
        android:id="@+id/tvContent"
        style="@style/Text.T5_2.G1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="16dp"
        android:baselineAlignBottom="true"
        android:ellipsize="end"
        android:lineSpacingExtra="2dp"
        android:maxLines="3"
        android:layout_marginLeft="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/llUserInfoContainer"
        android:visibility="gone"
        tools:text="如果可以重来一次，还会跟Ta在一还会跟Ta在一还会跟Ta在一"
        tools:visibility="visible" />



    <cn.taqu.library.widget.dynamicPhotoLayout.DyIrreGridPhotoLayout
        android:id="@+id/frameGridWrapper"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginRight="16dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tvContent"
        app:layout_constraintTop_toBottomOf="@+id/tvContent"
        android:layout_marginLeft="16dp"
        app:oriGridCornersRadius="0dp"
        app:singleGridPicWidth="196dp"
        app:edgeGridCornersRadius="8dp"
        app:gridSpace="2dp"
        tools:layout_height="50dp"
        tools:visibility="visible" />

    <cn.taqu.lib.base.widget.RCRelativeLayout
        android:id="@+id/rlVideo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="6dp"
        android:visibility="gone"
        android:layout_marginRight="16dp"
        app:actualImageScaleType="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/frameGridWrapper"
        app:rc_round_corner_bottom_left="8dp"
        app:rc_round_corner_bottom_right="8dp"
        app:rc_round_corner_top_left="8dp"
        app:rc_round_corner_top_right="8dp"
        tools:layout_height="120dp"
        tools:visibility="visible">

        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/imgVideoCover"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/post_video_bg" />

        <FrameLayout
            android:id="@+id/flVideoView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/imgVideoCover2"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/ivVideoPlay"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_centerInParent="true"
            android:src="@drawable/forum_post_list_new_play_video_ic" />

        <ProgressBar
            android:id="@+id/load_bar_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:indeterminateDrawable="@drawable/forum_video_load_progress"
            android:visibility="gone"
            tools:visibility="visible" />
    </cn.taqu.lib.base.widget.RCRelativeLayout>

    <TextView
        android:id="@+id/tvTopic"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:layout_marginLeft="67dp"
        android:layout_marginRight="16dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintTop_toBottomOf="@+id/rlVideo"
        tools:text="1111"
        android:paddingTop="13dp"
        android:gravity="center_vertical"
        android:drawableLeft="@drawable/forum_post_list_topic_ic"
        android:drawablePadding="3dp"
        android:textColor="@color/black"
        android:textSize="@dimen/t7" />

    <include
        android:id="@+id/llForumListTimeCityMedal"
        layout="@layout/forum_layout_forum_list_time_city_medal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginLeft="67dp"
        android:layout_marginRight="16dp"
        app:layout_constraintTop_toBottomOf="@+id/tvTopic" />

    <RelativeLayout
        android:id="@+id/relHotReview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginRight="16dp"
        android:layout_marginLeft="67dp"
        android:visibility="gone"
        android:background="@drawable/bg_g7_radius_8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/llForumListTimeCityMedal">

        <ImageView
            android:id="@+id/imgHotReview"
            android:layout_width="30dp"
            android:layout_height="12dp"
            android:src="@drawable/forum_post_list_hot_review_ic" />

        <hb.ximage.fresco.AvatarDraweeView
            android:id="@+id/imgHotReviewAvatar"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="14dp" />

        <TextView
            android:id="@+id/tvHotReviewNickName"
            style="@style/Text.T6.G1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="13dp"
            android:layout_toRightOf="@+id/imgHotReviewAvatar"
            android:ellipsize="end"
            android:lines="1"
            tools:text="是苏一妹妹啦" />

        <cn.taqu.library.widget.dynamicPhotoLayout.DynamicPhotoView
            android:id="@+id/imgPostHotReview"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_below="@+id/tvHotReviewNickName"
            android:layout_alignLeft="@+id/imgHotReviewAvatar"
            android:layout_marginTop="9dp"
            android:layout_marginBottom="10dp" />

        <hb.xemoji.view.XEmojiTextView
            android:id="@+id/tvHotReview"
            style="@style/Text.T6.G2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignWithParentIfMissing="true"
            android:layout_below="@+id/tvHotReviewNickName"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="9dp"
            android:layout_marginRight="48dp"
            android:layout_toRightOf="@+id/imgPostHotReview"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:lineSpacingExtra="2dp"
            android:maxLines="2"
            android:paddingBottom="14dp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginBottom="14dp"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvHotReviewIcon"
                style="@style/Text.T7.G3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/forum_hot_review_like_ic"
                android:paddingLeft="16dp"
                android:paddingRight="16dp" />

            <TextView
                android:id="@+id/tvHotReviewCount"
                style="@style/Text.T7.G3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="-1dp"
                android:gravity="center"
                android:includeFontPadding="true"
                android:textColor="@color/forum_color_text_list_hot_review_like"
                tools:text="15" />
        </LinearLayout>

        <ViewStub
            android:id="@+id/viewStubHotReviewLikes"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_alignParentRight="true"
            android:layout_marginTop="-6dp"
            android:layout_marginRight="10dp"
            android:layout="@layout/forum_post_list_likes_view" />
    </RelativeLayout>

    <ImageView
        android:id="@+id/ivWetBg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="6dp"
        android:src="@drawable/forum_post_list_dianzan_btn"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/relHotReview"
        tools:visibility="gone" />

    <cn.taqu.library.widget.StrokeTextView
        android:id="@+id/tvWet"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:gravity="center|left"
        android:minWidth="16dp"
        android:paddingLeft="6dp"
        android:paddingRight="6dp"
        android:text=""
        android:textColor="@color/g2"
        android:textSize="@dimen/t7"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivWetBg"
        app:layout_constraintStart_toEndOf="@+id/ivWetBg"
        app:layout_constraintTop_toTopOf="@+id/ivWetBg"
        tools:visibility="gone" />

    <ViewStub
        android:id="@+id/viewStubLikes"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginLeft="5dp"
        android:visibility="gone"
        android:layout="@layout/forum_post_list_likes_view"
        app:layout_constraintBottom_toBottomOf="@+id/tvWet"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvWet" />

    <TextView
        android:id="@+id/tvCreatePostTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:textColor="@color/g3"
        android:textSize="@dimen/t7"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivWetBg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivWetBg"
        tools:text="2天前" />

    <ImageView
        android:id="@+id/ivComment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="4dp"
        android:paddingRight="4dp"
        android:visibility="gone"
        android:src="@drawable/forum_post_list_comment_ic"
        app:layout_constraintBottom_toBottomOf="@+id/ivShare"
        app:layout_constraintStart_toEndOf="@+id/tvWet"
        app:layout_constraintTop_toTopOf="@+id/ivShare" />

    <cn.taqu.library.widget.StrokeTextView
        android:id="@+id/tvComment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/g2"
        android:textSize="@dimen/t7"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivShare"
        app:layout_constraintStart_toEndOf="@+id/ivComment"
        app:layout_constraintTop_toTopOf="@+id/ivShare"
        tools:text="124"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/ivShare"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:paddingLeft="6dp"
        android:paddingRight="16dp"
        android:visibility="gone"
        android:src="@drawable/forum_post_list_share_ic"
        app:layout_constraintStart_toEndOf="@+id/tvComment"
        app:layout_constraintTop_toBottomOf="@+id/relHotReview" />

    <ImageView
        android:id="@+id/ivMakeAccost"
        android:layout_width="62dp"
        android:layout_height="30dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="10dp"
        android:src="@drawable/forum_home_list_accost_ic"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/line"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/btnToChatMessage"
        android:layout_width="62dp"
        android:layout_height="30dp"
        android:layout_alignWithParentIfMissing="true"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="10dp"
        android:src="@drawable/forum_home_list_message_ic"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/line"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="gone" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/accostLottieAnimationView"
        android:layout_width="62dp"
        android:layout_height="51dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/line"
        app:layout_constraintEnd_toEndOf="parent"
        app:lottie_fileName="lottie/friend_home_accost/data.json"
        app:lottie_imageAssetsFolder="lottie/friend_home_accost/images"
        tools:visibility="visible" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="6dp"
        android:background="@color/g6"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/tvWet" />

    <ViewStub
        android:id="@+id/viewStubPostReviewFailure"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:visibility="gone"
        android:layout="@layout/forum_item_forum_post_review_failure"
        app:layout_constraintTop_toBottomOf="@+id/rlVideo" />
</androidx.constraintlayout.widget.ConstraintLayout>
