<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="66dp"
    android:background="@color/TH_Navy001"
    android:paddingLeft="16dp"
    android:paddingRight="16dp">

    <cn.taqu.lib.base.widget.AvatarDressView
        android:id="@+id/avatarDressView"
        android:layout_width="44dp"
        android:layout_height="44dp"
        app:avatarWidth="44dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvNickName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:ellipsize="end"
        android:singleLine="true"
        app:layout_constraintRight_toRightOf="parent"
        android:text="我是名字"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/t5_2"
        android:textStyle="bold"
        app:layout_constraintLeft_toRightOf="@id/avatarDressView"
        app:layout_constraintTop_toTopOf="@id/avatarDressView"
        tools:text="nickname" />

    <cn.taqu.lib.base.widget.ForumGenderView
        android:id="@+id/vForumGenderView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        app:layout_constraintLeft_toLeftOf="@id/tvNickName"
        app:layout_constraintTop_toBottomOf="@id/tvNickName"
        tools:layout_height="14dp"
        tools:layout_width="32dp" />


</androidx.constraintlayout.widget.ConstraintLayout>
