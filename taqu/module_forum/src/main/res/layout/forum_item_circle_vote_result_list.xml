<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/relVoteResultItem"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="12dp"
        android:orientation="vertical"
        android:paddingRight="10dp">

        <ImageView
            android:id="@+id/imgVoteRanking"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginLeft="6dp" />

        <TextView
            android:id="@+id/tvVoteName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="3dp"
            android:layout_toRightOf="@+id/imgVoteRanking"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="@color/g2"
            android:textSize="@dimen/t6" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:layout_alignLeft="@+id/tvVoteName"
            android:layout_below="@+id/tvVoteName"
            android:layout_marginTop="6dp">

            <ProgressBar
                android:id="@+id/viewVoteBarChart"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="16dp"
                android:layout_centerVertical="true"
                android:layout_toLeftOf="@+id/tvVotePercentage"
                android:max="100"
                android:progressDrawable="@drawable/forum_progress_bar_vote_list_progress" />

            <TextView
                android:id="@+id/tvVoteCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:text="10"
                android:textColor="@color/g3"
                android:textSize="@dimen/t7" />

            <TextView
                android:id="@+id/tvVotePercentage"
                android:layout_width="28dp"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="10dp"
                android:text="300%"
                android:textColor="@color/g3"
                android:textSize="@dimen/t7" />
        </RelativeLayout>
    </RelativeLayout>
</LinearLayout>