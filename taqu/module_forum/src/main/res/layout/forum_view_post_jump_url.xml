<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/linearJumpUrl"
        android:layout_width="match_parent"
        android:layout_height="121dp"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginTop="@dimen/post_item_spacing"
        android:orientation="horizontal">

        <hb.ximage.fresco.BaseDraweeView xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/imgJumpUrl"
            android:layout_width="91dp"
            android:layout_height="91dp"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:scaleType="centerCrop"
            android:src="@drawable/forum_post_jump_img_bg"
            app:placeholderImage="@drawable/forum_post_jump_url_bg" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@+id/imgJumpUrl"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:paddingLeft="15dp">

            <TextView
                android:id="@+id/tvJumpUrl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/g1"
                android:textSize="@dimen/t6" />

            <TextView
                android:id="@+id/tvContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/g2"
                android:textSize="@dimen/t7" />
        </LinearLayout>

        <View
            style="@style/line2"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_alignParentBottom="true" />
    </RelativeLayout>

    <TextView
        android:id="@+id/tvDescription"
        style="@style/text_post_description"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />
</LinearLayout>