package com.xmhaibao.forum.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import com.xmhaibao.forum.api.bean.PostListCommonInfo
import com.xmhaibao.forum.api.constants.CirclePostListType
import hb.common.helper.HostHelper
import java.io.Serializable

/**
 * 帖子社区 - 推广帖子- 聊天室帖子参数
 * @since 2023-3-2
 * <AUTHOR>
 * @see https://o15vj1m4ie.feishu.cn/wiki/wikcnKqW8f23DyJrBIM6wX6jOWb
 * */
class PostListAdChatRoomInfo: PostListCommonInfo() {
    companion object {
        // 聊天室推广帖后缀，用于拼接唯一ID
        const val SUFFIX_POST_CHAT_ROOM = "_is_post_chatting_now"
    }
    @SerializedName("chatroom_info")
    var chatRoomInfo : ForumPostChatRoomInfo? = null
    override fun getListItemType(): CirclePostListType.PostListItemType {
        return CirclePostListType.PostListItemType.POST_TYPE_IS_AD_CHAT_ROOM
    }
}

/**
 * 聊天室展示参数
 * */
class ForumPostChatRoomInfo: Serializable,IDoExtra {
    // 房主UUID
    @SerializedName("host_uuid")
    var hostUuid = ""
    // 帖子展示标题
    @SerializedName("title")
    var title = ""
    // 副标题
    @SerializedName("sub_title")
    var subTitle = ""
    // "距离你xxx公里"
    @SerializedName("distance_content")
    var distContent = ""
    // 点击帖子的跳转链接
    @SerializedName("relation")
    var relation = ""
    // 聊天室成员头像
    @SerializedName("member_avatar_list")
    var members :MutableList<String> = mutableListOf()
    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        // 图片URL拼接
        val webpList = mutableListOf<String>()
        members.forEach {
            webpList.add(HostHelper.getAvatarHost().getWebpUrl_4_1(it))
        }
        members = webpList
    }

}