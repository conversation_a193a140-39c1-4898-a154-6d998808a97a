package com.xmhaibao.forum.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.xmhaibao.forum.R;
import com.xmhaibao.forum.bean.ForumUserInfo;

import cn.taqu.lib.base.utils.SexTypeUtils;

import java.util.List;

import hb.ximage.fresco.AvatarDraweeView;
import hb.utils.StringUtils;

/**
 * 感兴趣的人
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/1/19.
 */
public class HomeInterestedAdapter extends RecyclerView.Adapter<HomeInterestedAdapter.InterestedViewHolder> {

    private List<ForumUserInfo> mUserList;

    public HomeInterestedAdapter(List<ForumUserInfo> userList) {
        mUserList = userList;
    }

    public List<ForumUserInfo> getInterestedList() {
        return mUserList;
    }

    @Override
    public InterestedViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.forum_item_home_interested, parent, false);
        return new InterestedViewHolder(view);
    }

    @Override
    public void onBindViewHolder(InterestedViewHolder holder, int position) {
        ForumUserInfo info = mUserList.get(position);
        holder.imgAvatar.setImageFromUrl(info.getAvatar());
        holder.tvNickName.setText(info.getNickname());
        if (StringUtils.isNotEmpty(info.getRecommend_reason())) {
            holder.tvRecommendReason.setVisibility(View.VISIBLE);
            holder.tvRecommendReason.setText(info.getRecommend_reason());
        } else {
            holder.tvRecommendReason.setVisibility(View.GONE);
        }
        if (SexTypeUtils.isSexTypeBoy(info.getSex_type())) {
            holder.imgSexType.setVisibility(View.VISIBLE);
            holder.imgSexType.setImageResource(cn.taqu.lib.base.R.drawable.ic_male);
        } else if (SexTypeUtils.isSexTypeGirl(info.getSex_type())) {
            holder.imgSexType.setVisibility(View.VISIBLE);
            holder.imgSexType.setImageResource(cn.taqu.lib.base.R.drawable.ic_female);
        } else {
            holder.imgSexType.setVisibility(View.GONE);
        }
    }

    @Override
    public int getItemCount() {
        return mUserList != null ? mUserList.size() : 0;
    }

    public static class InterestedViewHolder extends RecyclerView.ViewHolder {

        AvatarDraweeView imgAvatar;
        TextView tvNickName;
        TextView tvRecommendReason;
        ImageView imgSexType;

        public InterestedViewHolder(View itemView) {
            super(itemView);
            imgAvatar = itemView.findViewById(R.id.imgAvatar);
            tvNickName = itemView.findViewById(R.id.tvNickName);
            tvRecommendReason = itemView.findViewById(R.id.tvRecommendReason);
            imgSexType = itemView.findViewById(R.id.imgSexType);
        }
    }

}
