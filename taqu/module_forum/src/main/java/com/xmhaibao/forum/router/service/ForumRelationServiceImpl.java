package com.xmhaibao.forum.router.service;

import static com.xmhaibao.forum.constants.ForumRelationConstants.PUSH_TYPE_ACTION_GUIDE_TYPE;
import static com.xmhaibao.forum.constants.ForumRelationConstants.PUSH_TYPE_ACTION_USE_TYPE;
import static cn.taqu.lib.base.constants.PushConstants.PUSH_VALUE_REWARD;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.xmhaibao.account.api.router.AccountPinsRouter;
import com.xmhaibao.call.api.router.CallPinsRouter;
import com.xmhaibao.family.api.router.FamilyPinsRouter;
import com.xmhaibao.forum.api.bean.ForumPostActivityParams;
import com.xmhaibao.forum.api.bean.ForumVideoSlidingParams;
import com.xmhaibao.family.bean.gift.FamilyShowGiftChooseMemberBean;
import com.xmhaibao.forum.api.constants.ForumIntentConstants;
import com.xmhaibao.forum.api.constants.ForumPinsRelationConstants;
import com.xmhaibao.forum.api.router.ForumPinsRouter;
import com.xmhaibao.forum.api.router.ForumRouterPath;
import com.xmhaibao.forum.api.router.service.ForumRelationService;
import com.xmhaibao.forum.constants.ForumRelationConstants;
import com.xmhaibao.forum.router.ForumRouter;
import com.xmhaibao.livedetect.LiveDetector;
import com.xmhaibao.livedetect.router.LiveDetectRouter;
import com.xmhaibao.message.api.router.MessagePinsRouter;
import com.xmhaibao.mine.api.router.MinePinsRouter;
import com.xmhaibao.mine.api.router.MyBeanPinsRouter;
import com.xmhaibao.xjson.XJson;

import java.util.HashMap;

import cn.taqu.lib.base.common.AppAccountBean;
import cn.taqu.lib.base.common.XjbApplicationHelper;
import cn.taqu.lib.base.constants.CommonConstants;
import cn.taqu.lib.base.constants.IntentConstants;
import cn.taqu.lib.base.constants.NavigationTabs;
import cn.taqu.lib.base.constants.PostForumType;
import cn.taqu.lib.base.constants.PushConstants;
import cn.taqu.lib.base.router.ARouterManager;
import cn.taqu.lib.base.router.RouterLaunchOther;
import cn.taqu.lib.base.utils.RechargeSourceManager;
import cn.taqu.lib.base.utils.SexTypeUtils;
import hb.common.data.AccountHelper;
import hb.utils.ActivityUtils;
import hb.utils.StringUtils;
import hb.utils.ToastUtils;
import hb.xtoast.XToastUtils;

/**
 * 社区公共跳转服务实现
 *
 * <AUTHOR>
 * @date 2021-3-31
 */
@Route(path = ForumRouterPath.ForumRelationServiceImpl)
public class ForumRelationServiceImpl implements ForumRelationService {

    /**
     * 处理社区数据
     *
     * @param activity
     * @param action
     * @param paramMap
     */
    @Override
    public void dealRelation(Context activity, String action, HashMap<String, String> paramMap) {
        String source = paramMap.get(PushConstants.PUSH_TYPE_SOURCE);
        // 2018-07-12 小米渠道隐藏社区相关功能 屏蔽掉跳转至社区
        if (XjbApplicationHelper.isForumHide()) {
            ToastUtils.makeToast(activity, cn.taqu.lib.base.R.string.current_version_not_available);
            return;
        }
        String title = paramMap.get(PushConstants.PUSH_VALUE_TITLE);
        String id = paramMap.get(PushConstants.PUSH_VALUE_ID);
        String cid = paramMap.get(PushConstants.PUSH_VALUE_CID);
        String user_could_post = paramMap.get(ForumRelationConstants.PUSH_USER_COULD_POST);
        String gender_type = paramMap.get(ForumRelationConstants.PUSH_VALUE_GENDER_TYPE);
        String post_type = paramMap.get(ForumRelationConstants.PUSH_VALUE_POST_TYPE);
        String posts_id = paramMap.get(ForumRelationConstants.PUSH_VALUE_POST_ID);
        String review_id = paramMap.get(ForumRelationConstants.PUSH_VALUE_REVIEW_ID);
        String floor = paramMap.get(ForumRelationConstants.PUSH_VALUE_FLOOR);
        String name = paramMap.get(PushConstants.PUSH_VALUE_NAME);
        String key = paramMap.get(PushConstants.PUSH_VALUE_KEY);
        String shortVideoSliding = paramMap.get(ForumRelationConstants.PUSH_VALUE_SLIDING);
        String tab = paramMap.get("tab");
        String useType = paramMap.get(PUSH_TYPE_ACTION_USE_TYPE);
        String guideType = paramMap.get(PUSH_TYPE_ACTION_GUIDE_TYPE);
        if (PushConstants.PUSH_TYPE_ACTION_DETAIL.equalsIgnoreCase(action)) {
            if (!SexTypeUtils.checkSexTypeLimit(gender_type, activity, false)) {
                return;
            }
            boolean isXquPost = TextUtils.equals("1", paramMap.get("isXiaoQu"));
            String operPosition = paramMap.get("operPosition");
            String isVideo = paramMap.get(ForumRelationConstants.PUSH_VALUE_IS_VIDEO);
            if ("1".equalsIgnoreCase(isVideo)) {
                ForumVideoSlidingParams params = new ForumVideoSlidingParams(id);
                if (!TextUtils.isEmpty(shortVideoSliding)) {
                    params.setType(shortVideoSliding);
                }
                if (StringUtils.isNotEmpty(source)) {
                    params.setFromSource(source);
                }
                params.setSourceFromFirstLevel(operPosition);
                ForumPinsRouter.launchForumVideoSlidingActivity(params);
            } else if (isXquPost) {
                ForumPinsRouter.launchXquPostActivity(activity,id);
            } else {
                ForumPostActivityParams params  = new ForumPostActivityParams(id);
                params.setAllowToCate(true);
                if (StringUtils.isNotEmpty(source)) {
                    params.setFromSource(source);
                }
                params.setSourceFromFirstLevel(operPosition);
                ForumPinsRouter.launchForumPostActivity(activity,params);
            }
        } else if (ForumRouterPath.PUSH_TYPE_ACTION_REPLYME.equalsIgnoreCase(action)) {
            if (!AccountHelper.isUserLogined()) {
                AccountPinsRouter.accountService().launchLogin();
                return;
            } else {
                ForumPinsRouter.launchForumReplyMeListActivity();
            }
        } else if (PushConstants.PUSH_TYPE_ACTION_HOME.equalsIgnoreCase(action)) {
            Activity currentActivity = ActivityUtils.getTopActivity();
            if (ARouterManager.appService().isNavigationActivity(currentActivity)) {
                ARouterManager.appService().setForumHomeTab(NavigationTabs.TAB_TAG_BBS, tab);
                return;
            }
            Bundle bundle = new Bundle();
            bundle.putString(IntentConstants.INTENT_PARAM_TAB_SELECT, NavigationTabs.TAB_TAG_BBS);
            bundle.putString(IntentConstants.INTENT_PARAM_FORUM_HOME_TAB_SELECT, tab);
            RouterLaunchOther.launchNavigationActivity(currentActivity, bundle, Intent.FLAG_ACTIVITY_NEW_TASK);
        } else if (ForumRelationConstants.PUSH_TYPE_ACTION_GROWTH_CENTER.equalsIgnoreCase(action)) {
            MinePinsRouter.launchTaskActivity(source);
        } else if (ForumRouterPath.PUSH_TYPE_ACTION_FLOOR.equalsIgnoreCase(action)) {
            ForumPinsRouter.launchForumFloorActivity(post_type, posts_id, review_id, floor);
        } else if (ForumRouterPath.PUSH_TYPE_ACTION_POST_TAG.equalsIgnoreCase(action)) {
            ForumPinsRouter.launchForumTopicDetailActivity(id);
        } else if (PushConstants.PUSH_TYPE_ACTION_SEARCH.equalsIgnoreCase(action)) {
            ForumPinsRouter.launchSearchActivity(activity, "", CommonConstants.SEARCH_TAB_TYPE_FORUM);
        } else if (ForumRouterPath.PUSH_TYPE_ACTION_SEARCH_RESULT.equalsIgnoreCase(action)) {
            ForumPinsRouter.lanuchSearchResultActivity(key, CommonConstants.SEARCH_KEYWORD_TYPE_SETTING, CommonConstants.SEARCH_TAB_TYPE_FORUM);
        } else if (ForumRelationConstants.PUSH_TYPE_ACTION_DRIVER_HOME.equalsIgnoreCase(action)) {
            if (!AccountHelper.isUserLogined()) {
                AccountPinsRouter.accountService().launchLogin();
                return;
            }
            ForumPinsRouter.launchForumDriverActivity();
        } else if (ForumRouterPath.PUSH_TYPE_ACTION_VIDEO_LIST.equalsIgnoreCase(action)) {
            ForumPinsRouter.launchShortVideoListActivity(tab);
        } else if (PushConstants.PUSH_TYPE_ACTION_CREATE_POST.equalsIgnoreCase(action)) {
            String type = paramMap.get(PushConstants.PUSH_VALUE_TYPE);
            //520表白墙的对方uuid
            String loveUuid = paramMap.get(ForumRelationConstants.PUSH_TYPE_VALUE_LOVE_UUID);
            if (StringUtils.equalsIgnoreCase(type, ForumRelationConstants.PUSH_TYPE_ACTION_520WALL) && StringUtils.isNotEmpty(
                    loveUuid)) {
                ForumPinsRouter.launchMomentForumCreateActivityBy520Wall(activity, PostForumType.TEXT, loveUuid, type);
            } else if (StringUtils.isNotEmpty(cid)) {
                ForumPinsRouter.launchMomentForumCreateActivity(activity, PostForumType.TEXT, cid, source);
            } else if (ForumRelationConstants.PUSH_VALUE_MOMENT.equals(type)) {
                ForumPinsRouter.launchMomentForumCreateActivity(activity, PostForumType.TEXT, source);
            } else if (ForumRelationConstants.PUSH_VALUE_ARTICLE.equalsIgnoreCase(type)) {
                ForumPinsRouter.launchMomentForumCreateActivity(activity, PostForumType.TEXT, source);
            } else if (ForumRelationConstants.PUSH_VALUE_VIDEO.equalsIgnoreCase(type)) {
                ForumPinsRouter.launchMomentForumCreateActivity(activity, PostForumType.VIDEO, source);
            }

        } else if (ForumRouterPath.PUSH_TYPE_ACTION_AT_SOMEONE.equalsIgnoreCase(action)) {
            ForumPinsRouter.launchSomeoneAtMeActivity();
        } else if (ForumRouterPath.PUSH_TPYE_ACTION_VIEW_MINE.equalsIgnoreCase(action)) {
            if (!AccountHelper.isUserLogined()) {
                AccountPinsRouter.accountService().launchLogin();
                return;
            }
            if ("post".equals(tab)) {
                ForumPinsRouter.launchPersonalToViewMineListActivity(activity,ForumIntentConstants.TO_VIEW_MINE_POST_LIST);
            } else {
                ForumPinsRouter.launchPersonalToViewMineListActivity(activity,ForumIntentConstants.TO_VIEW_MINE_PERSONAL_HOME_LIST);
            }
        } else if (ForumRelationConstants.PUSH_TYPE_ACTION_PROMOTION_SIGNIN.equalsIgnoreCase(action)) {
            //签到
        } else if (ForumRouterPath.PUSH_TYPE_ACTION_GROW_SCORE_HOME.equalsIgnoreCase(action)) {
            ForumPinsRouter.launchForumGrowScoreHomeActivity();
        } else if (ForumRouterPath.PUSH_TYPE_ACTION_GROW_SCORE_DETAIL.equalsIgnoreCase(action)) {
            ForumPinsRouter.launchForumGrowScoreDetailActivity();
        } else if (ForumRouterPath.PUSH_TYPE_ACTION_GROW_SCORE_HISTORY.equalsIgnoreCase(action)) {
            ForumPinsRouter.launchForumGrowScoreHistoryActivity();
        } else if (ForumRelationConstants.PUSH_TYPE_ALI_LIVE_AUTHENTICITY.equalsIgnoreCase(action)) {
            //重新认证头像
            boolean authenticationAvatar = "1".equals(paramMap.get("authenticationAvatar"));
            if (authenticationAvatar) {
                ToastUtils.show("你的头像非真人照片，上传本人照片即可认证");
                MinePinsRouter.launchPersonalInfoActivity("");
            } else if (AppAccountBean.get().isAvatarRealPhotoAuth()) {
                ToastUtils.show("您已通过真人照片认证");
            } else {
                LiveDetector.Companion.start();
            }
        } else if (ForumRelationConstants.PUSH_TYPE_ALI_LIVE_AUTHENTICITY_V2.equalsIgnoreCase(action)) {
            String from = "";
            if (PUSH_VALUE_REWARD.equals(useType)) {
                from = PUSH_VALUE_REWARD+":"+guideType;
            }
            String inviterUuid = paramMap.get(PushConstants.PUSH_VALUE_INVITER_UUID);
            String taskType = paramMap.get(PushConstants.PUSH_VALUE_TASK_TYPE);
            LiveDetector.Companion.start(from, inviterUuid, taskType, false);
        } else if (ForumRelationConstants.PUSH_TYPE_ALI_LIVE_AUTHENTICITY_V3.equalsIgnoreCase(action)) {
            LiveDetector.Companion.start(true);
        } else if (ForumRouterPath.PUSH_TYPE_LIVE_AUTH_FOR_SCENE.equalsIgnoreCase(action)) {
            String scene = paramMap.get(ForumRelationConstants.PUSH_TYPE_LIVE_AUTH_SCENE);
            if (StringUtils.isEmpty(scene)) {
                return;
            }
            //支持多场景人脸比对 目前有高风险行为限制场景
            AccountPinsRouter.launchLiveDetectForSceneActivity(scene);
        } else if (PushConstants.PUSH_TYPE_FRIEND.equalsIgnoreCase(action)) {
            Activity currentActivity = ActivityUtils.getTopActivity();
            if (ARouterManager.appService().isNavigationActivity(currentActivity)) {
                ARouterManager.appService().setFriendHomeTab(NavigationTabs.TAB_TAB_MAKE_FRIEND, CommonConstants.NEW_FRIEND_TAB);
                return;
            }
            Bundle bundle = new Bundle();
            bundle.putString(IntentConstants.INTENT_PARAM_TAB_SELECT, NavigationTabs.TAB_TAB_MAKE_FRIEND);
            RouterLaunchOther.launchNavigationActivity(currentActivity, bundle, Intent.FLAG_ACTIVITY_NEW_TASK);
        } else if (ForumRouterPath.PUSH_TYPE_ACTION_FORUM_MY_CAR.equalsIgnoreCase(action)) {
            if (AccountHelper.isUserLogined()) {
                ForumPinsRouter.launchForumCarDecorationActivity(AccountHelper.getAccountUuid());
            } else {
                AccountPinsRouter.accountService().launchLogin();
            }
        } else if (ForumRouterPath.PUSH_TYPE_ACTION_FORUM_CAR_SHOP.equalsIgnoreCase(action)) {
            ForumPinsRouter.launchForumCarDecorationShopActivity();
        } else if(ForumRouterPath.PUSH_TYPE_ACTION_FORUM_TOPIC_SQUARE.equals(action)) {
            ForumRouter.launchForumTopicSquareActivity();
        } else if(ForumRelationConstants.FEMALE_WX_WITHDRAW_DIALOG.equals(action)) {
            //新增source参数
            if (TextUtils.isEmpty(source)) {
                //默认为女用户成长策略提现
                source = "0";
            }
            MessagePinsRouter.messageService().showWeiXinWithDrawDialog(activity, source);
        } else if (ForumRelationConstants.PUSH_FAMILY_GUIDE_SEND_MESSAGE.equalsIgnoreCase(action)) {
            //家族引导-一键发布
            final String content = paramMap.get(ForumRelationConstants.PUSH_FAMILY_GUIDE_CONTENT);
            final String uuid = paramMap.get(ForumRelationConstants.PUSH_FAMILY_GUIDE_UUID);
            final String autoSendMessage = paramMap.get(ForumRelationConstants.PUSH_FAMILY_GUIDE_AUTO_SEND);
            MessagePinsRouter.launchMessageSquareChatActivity(uuid, "", content, StringUtils.equalsIgnoreCase("1", autoSendMessage),
                    "", "", false);
        } else if (ForumRelationConstants.PUSH_FAMILY_GUIDE_SEND_RED_PACKAGE.equalsIgnoreCase(action)) {
            //家族引导-发红包
            final String uuid = paramMap.get(ForumRelationConstants.PUSH_FAMILY_GUIDE_UUID);
            MessagePinsRouter.launchMessageSquareChatActivity(uuid, "", "", false,
                    "", "", true);
        } else if(ForumPinsRelationConstants.MESSAGE_CHARGE_SYSTEM_ENTRY_DIALOG.equalsIgnoreCase(action)) {
            if (TextUtils.isEmpty(source)) {
                source = RechargeSourceManager.POSITION_RECHARGE_EARS;
            }
            MessagePinsRouter.messageService().showChargeSystemEntryDialog(ActivityUtils.getTopActivity(), false, source);
        } else if (ForumRelationConstants.PUSH_FAMILY_GUIDE_PERFECT_INFORMATION.equalsIgnoreCase(action)) {
            //家族设置
            final String conversationId =
                    paramMap.get(ForumRelationConstants.PUSH_FAMILY_GUIDE_CONVERSATION_ID);
            FamilyPinsRouter.INSTANCE.launchMessageFamilySettingActivity(conversationId);
        } else if(ForumPinsRelationConstants.PUSH_FORUM_GIFT_PANEL.equalsIgnoreCase(action)) {
            String scene = paramMap.get("scene");
            String giftId = paramMap.get("giftId");
            if(ForumPinsRelationConstants.PUSH_FORUM_GIFT_PANEL_PRIVATE.equalsIgnoreCase(scene)) {
                //私信场景
                MessagePinsRouter.messageService().showGiftPanelPrivate(giftId);
            }else if (ForumPinsRelationConstants.PUSH_FORUM_GIFT_PANEL_SCENE_CALL.equalsIgnoreCase(scene)) {
                //趣聊
                CallPinsRouter.imCallService().showGiftPanelCallById(giftId);
            } else if (ForumPinsRelationConstants.PUSH_FORUM_GIFT_PANEL_SCENE_FAMILY.equalsIgnoreCase(scene)) {
                //家族
                String familyId = paramMap.get("familyId");
                FamilyPinsRouter.INSTANCE.familyService().showGiftPanelFamily(giftId,familyId);

            }

        }else if(ForumPinsRelationConstants.FAMILY_AUCTION.equalsIgnoreCase(action)) {
            //我的拍拍关系
            MessagePinsRouter.messageService().jumpToMyRelationPage();
        } else if (ForumPinsRelationConstants.PROFILE_VERIFY_RESULT.equalsIgnoreCase(action)) {
            //真人认证结果
            Activity currentActivity = ActivityUtils.getTopActivity();
            if (currentActivity != null) {
                LiveDetectRouter.Companion.launchLiveDetectionResultActivity(currentActivity);
            }
        }else if (ForumRelationConstants.FAMILY_DYNAMIC_RELATION.equalsIgnoreCase(action)) {
            FamilyPinsRouter.INSTANCE.familyService().handleFamilyDynamicRelation(paramMap);
        }else if (ForumRelationConstants.PUSH_FAMILY_GIFT_PANEL.equalsIgnoreCase(action)) {
            String familyId = paramMap.get(ForumRelationConstants.PUSH_FAMILY_GIFT_PANEL_FAMILY_ID);
            String json = paramMap.get(ForumRelationConstants.PUSH_FAMILY_GIFT_PANEL_ACCOUNT);
            String giftId = paramMap.get(ForumRelationConstants.PUSH_FAMILY_GIFT_PANEL_GIFT_ID);
            String toast = paramMap.get(ForumRelationConstants.PUSH_FAMILY_GIFT_PANEL_TOAST);
            FamilyShowGiftChooseMemberBean bean = XJson.fromJson(json, FamilyShowGiftChooseMemberBean.class);
            if (bean != null){
                bean.doExtra(null);
            }
            if (StringUtils.isNotEmpty(toast)){
                XToastUtils.show(toast);
            }
            FamilyPinsRouter.INSTANCE.familyService().showGiftPanelFamilyAndChooseMember(giftId,bean);
        } else if (ForumRelationConstants.PUSH_TYPE_VALUE_VIP_OPTION_POP.equalsIgnoreCase(action)) {
            //1：显示【查看详情】0：不显示
            final String showDetail = paramMap.get(ForumRelationConstants.PUSH_VIP_OPTION_POP_SHOW_DETAIL);
            final String paySource = paramMap.get(ForumRelationConstants.PUSH_VIP_OPTION_POP_PAY_SOURCE);
            //dailyReward = 1 是新 VIP else 旧版。
            final String dailyReward = paramMap.get(ForumRelationConstants.PUSH_VIP_OPTION_POP_DAILY_REWARD);
            ARouterManager.tqbeanService().showVipPayDialog(showDetail, paySource, dailyReward);
        } else if (StringUtils.equalsIgnoreCase(ForumRelationConstants.PUSH_TYPE_VALUE_VIP_CENTER, action)) {
            @Nullable
            String renew = paramMap.get(ForumRelationConstants.PUSH_VIP_MEMBER_DETAILS_RENEW); // 续费时档位
            //dailyReward = 1 是新 VIP else 旧版。
            final String dailyReward = paramMap.get(ForumRelationConstants.PUSH_VIP_MEMBER_DETAILS_DAILY_REWARD);
            MyBeanPinsRouter.launchVipMemberDetailsActivity("", renew, dailyReward);
        } else if (ForumRelationConstants.PUSH_SOCIAL_MATCH_PREFER_CHOOSE.equalsIgnoreCase(action)) {
            MessagePinsRouter.launchSocialMatchChoosePreferActivity();
        }
    }

    @Override
    public void init(Context context) {

    }
}
