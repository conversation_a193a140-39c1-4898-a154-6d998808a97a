package com.xmhaibao.forum.activity;

import android.annotation.SuppressLint;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.core.widget.NestedScrollView;
import androidx.lifecycle.ViewModelProviders;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.facade.enums.RouteFlag;
import com.xmhaibao.forum.api.router.ForumRouterPath;
import com.xmhaibao.forum.bean.ForumGrowScoreHomeInfo;
import com.xmhaibao.forum.databinding.ForumGrowScoreHomeActivityBinding;
import com.xmhaibao.forum.dialog.ForumGrowScoreTargetLevelPrivilegeDialog;
import com.xmhaibao.forum.helper.ForumGrowScoreHomeMadelScrollHelper;
import com.xmhaibao.forum.helper.ForumGrowScoreHomeUpgradeLevelAnimationHelper;
import com.xmhaibao.forum.holder.ForumGrowScoreHomeMedalPlaceholderViewHolder;
import com.xmhaibao.forum.holder.ForumGrowScoreHomeMedalViewHolder;
import com.xmhaibao.forum.holder.ForumGrowScoreHomePrivilegesViewHolder;
import com.xmhaibao.forum.router.ForumRouter;
import com.xmhaibao.forum.util.ForumCommonUtils;
import com.xmhaibao.forum.viewModel.ForumGrowScoreHomeViewModel;

import org.jetbrains.annotations.NotNull;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

import hb.common.xstatic.activity.BaseActivity;
import hb.utils.BarUtils;
import hb.utils.ColorUtils;
import hb.utils.SizeUtils;
import hb.utils.SpanUtils;
import hb.xadapter.XBaseAdapter;

/**
 * @author：杨浩 项目：taqu
 * 创建日期：2019-12-25
 * 功能简介：有趣值主页
 */
@Route(path = ForumRouterPath.ForumGrowScoreHomeActivity,name = "有趣值主页", extras = RouteFlag.RELATION_DOC_ENABLED)
public class ForumGrowScoreHomeActivity extends BaseActivity {
    private ForumGrowScoreHomeActivityBinding mBinding;


    /**
     * 是否已经绑定了 动画升级的界面
     */
    private boolean isBindLevelAnimationLayout;

    /**
     * 高级权限列表
     */
    private XBaseAdapter mSeniorPrivilegesAdapter;

    /**
     * 普通权限
     */
    private XBaseAdapter mNormalPrivilegesAdapter;

    /**
     * 勋章列表
     */
    private XBaseAdapter mLevelMedalAdapter;

    /**
     * 是否隐藏高级权限模块
     */
    private boolean isHintSeniorPrivilegesModule = false;

    /**
     * 是否隐藏普通权限模块
     */
    private boolean isHintNormalPrivilegesModule = false;

    /**
     * 页面信息缓存
     */
    @Nullable
    private ForumGrowScoreHomeInfo mGrowScoreHomeInfo;

    /**
     * 收起权限列表-只剩 8 个
     */
    private final String PRIVILEGE_LIST_COLLAPSE = "collapse";

    /**
     * 展开权限列表
     */
    private final String PRIVILEGE_LIST_EXPAND = "expand";

    /**
     * 顶部栏滑动动画的最终高度
     */
    private final int TOOLBAR_SCROLL_ANIMATION_HEIGHT = SizeUtils.dp2px(33);

    /**
     * 当前的等级信息
     */
    @Nullable
    private ForumGrowScoreHomeInfo.LevelBean mCurrentLevelBean;

    /**
     * 内部复用
     */
    @NotNull
    private RecyclerView.RecycledViewPool mRecycledViewPool;

    @Override
    protected Object onCreateContentView() {
        mBinding = ForumGrowScoreHomeActivityBinding.inflate(getLayoutInflater());
        return mBinding.getRoot();
    }

    @SuppressLint("CheckResult")
    @Override
    public void initViews() {
        super.initViews();
        showLoadingBar(false);
        setupToolbar("").backgroundAlpha(0).immerseContent(true);
        hideToolbar();
        // 动态计算头部高度
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            int height = BarUtils.getStatusBarHeight();
            mBinding.statusBar.getLayoutParams().height = height;
            mBinding.viewForumGrowScoreToolbar.getLayoutParams().height = SizeUtils.dp2px(44) + height;
            mBinding.viewForumGrowScoreLevelBg.getLayoutParams().height = SizeUtils.dp2px(292) + height;
            mBinding.rvForumGrowScoreLevel.getLayoutParams().height = SizeUtils.dp2px(292) + height;
            mBinding.rvForumGrowScoreLevel.setPadding(mBinding.rvForumGrowScoreLevel.getPaddingLeft(), mBinding.rvForumGrowScoreLevel.getPaddingTop() + height + SizeUtils.dp2px(73), mBinding.rvForumGrowScoreLevel.getPaddingRight(), mBinding.rvForumGrowScoreLevel.getPaddingBottom());
            mBinding.tvForumGrowScoreActivityTitle.setPadding(0, height, 0, 0);
            mBinding.tvForumGrowScoreDetails.setPadding(mBinding.tvForumGrowScoreDetails.getPaddingLeft(), mBinding.tvForumGrowScoreDetails.getPaddingTop() + height, mBinding.tvForumGrowScoreDetails.getPaddingRight(), mBinding.tvForumGrowScoreDetails.getPaddingBottom());
            mBinding.tvForumGrowScoreBack.setPadding(mBinding.tvForumGrowScoreBack.getPaddingLeft(), mBinding.tvForumGrowScoreBack.getPaddingTop() + height, mBinding.tvForumGrowScoreBack.getPaddingRight(), mBinding.tvForumGrowScoreBack.getPaddingBottom());
            ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) mBinding.viewForumGrowScoreLevelSpace.getLayoutParams();
            layoutParams.setMargins(0, SizeUtils.dp2px(73) + height, 0, 0);
        }
        mBinding.tvForumGrowScoreSeniorPrivilegesScaling.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dealWithSeniorPrivilegesState(mBinding.tvForumGrowScoreSeniorPrivilegesScaling, true);
            }
        });
        mBinding.tvForumGrowScoreNormalPrivilegesScaling.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dealWithSeniorPrivilegesState(mBinding.tvForumGrowScoreNormalPrivilegesScaling, false);
            }
        });
        // 查看下级解锁的权限
        mBinding.tvForumGrowScoreLevelUnlockHint.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showNextLevelUnLockDialog();
            }
        });
        mBinding.tvForumGrowScoreSeniorPrivilegesHint.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showNextLevelUnLockDialog();
            }
        });
        mBinding.tvForumGrowScoreNormalPrivilegesHint.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showNextLevelUnLockDialog();
            }
        });
        mBinding.btnForumGrowScoreUpLevel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ForumRouter.launchForumGrowScoreDetailActivity();
            }
        });
        mBinding.tvForumGrowScoreDetails.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ForumRouter.launchForumGrowScoreDetailActivity();
            }
        });
        mBinding.tvForumGrowScoreBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        mRecycledViewPool = new RecyclerView.RecycledViewPool();
        initSeniorPrivilegesRecyclerView();
        initNormalPrivilegesRecyclerView();
        initLevelMedalRecyclerView();
        initToolbarScroll();
    }

    @Override
    public void initData(Bundle savedInstanceState) {
        super.initData(savedInstanceState);
        ForumCommonUtils.requestCheckShowAccountBlackDialog(this);
        getViewModel()
                .requestData()
                .getGrowScoreHomeInfo()
                .observe(this, this::initLevelInfo);
    }

    /**
     * 初始化顶部栏滑动效果
     */
    private void initToolbarScroll() {
        mBinding.scrollViewForumGrowScore.setOnScrollChangeListener((NestedScrollView.OnScrollChangeListener) (nestedScrollView, scrollX, scrollY, oldScrollX, oldScrollY) -> {
            float alpha;
            Drawable backDrawable;
            int titleColor;
            int detailsColor;
            // 判断是否滑动到顶部了，如果到顶部就不需要去计算了
            if (scrollY > TOOLBAR_SCROLL_ANIMATION_HEIGHT) {
                // 如果上次已经是完全显示了，就什么都不处理，节约性能
                if (mBinding.viewForumGrowScoreToolbar.getAlpha() == 1) {
                    return;
                }
                alpha = 1;
                backDrawable = ColorUtils.tintDrawable(ContextCompat.getDrawable(getContext(), cn.taqu.lib.base.R.drawable.btn_back_white), ContextCompat.getColor(getContext(), hb.xstyle.R.color.g3));
                titleColor = ContextCompat.getColor(getContext(), hb.xstyle.R.color.c3);
                detailsColor = ContextCompat.getColor(getContext(), hb.xstyle.R.color.g2);
            } else {
                alpha = (float) scrollY / TOOLBAR_SCROLL_ANIMATION_HEIGHT;
                backDrawable = ColorUtils.tintDrawable(ContextCompat.getDrawable(getContext(), cn.taqu.lib.base.R.drawable.btn_back_white), ColorUtils.getColor(ContextCompat.getColor(getContext(), hb.xstyle.R.color.white), ContextCompat.getColor(getContext(), hb.xstyle.R.color.g3), alpha));
                titleColor = ColorUtils.getColor(ContextCompat.getColor(getContext(), hb.xstyle.R.color.white), ContextCompat.getColor(getContext(), hb.xstyle.R.color.c3), alpha);
                detailsColor = ColorUtils.getColor(ContextCompat.getColor(getContext(), hb.xstyle.R.color.white), ContextCompat.getColor(getContext(), hb.xstyle.R.color.g2), alpha);
            }
            mBinding.viewForumGrowScoreToolbar.setAlpha(alpha);
            mBinding.tvForumGrowScoreActivityTitle.setTextColor(titleColor);
            mBinding.tvForumGrowScoreDetails.setTextColor(detailsColor);
            mBinding.tvForumGrowScoreBack.setImageDrawable(backDrawable);
        });
    }

    /**
     * 显示顶部的有趣值信息
     *
     * @param forumGrowScoreHomeInfo 有趣值信息
     */
    private void initLevelInfo(@Nullable ForumGrowScoreHomeInfo forumGrowScoreHomeInfo) {
        hideLoadingBar();
        mGrowScoreHomeInfo = forumGrowScoreHomeInfo;
        if (forumGrowScoreHomeInfo == null) {
            showEmptyView();
            return;
        }
        mBinding.tvForumGrowScoreTitle.setText(MessageFormat.format("{0}分", forumGrowScoreHomeInfo.getGrowScore()));
        // 显示高级特权
        if (!isHintSeniorPrivilegesModule) {
            SpanUtils seniorPrivilegesHint = new SpanUtils();
            seniorPrivilegesHint
                    .append(forumGrowScoreHomeInfo.getSeniorPrivilegesGetNumber() + "/")
                    .setForegroundColor(0xFFE4BF58)
                    .append(forumGrowScoreHomeInfo.getSeniorPrivilegesMaxNumber() + "").setForegroundColor(0xFFA3A3A3);
            mBinding.tvForumGrowScoreSeniorPrivilegesContent.setText(seniorPrivilegesHint.create());
            if (ForumGrowScoreHomeViewModel.EXPAND_CRITICAL_VALUE < forumGrowScoreHomeInfo.getSeniorPrivileges().size()) {
                mBinding.tvForumGrowScoreSeniorPrivilegesScaling.setText("展开全部");
                mBinding.tvForumGrowScoreSeniorPrivilegesScaling.setCompoundDrawablesWithIntrinsicBounds(0, 0, cn.taqu.lib.base.R.drawable.arrow_down_g3_ic, 0);
                mBinding.tvForumGrowScoreSeniorPrivilegesScaling.setTag(PRIVILEGE_LIST_EXPAND);
                mBinding.tvForumGrowScoreSeniorPrivilegesScaling.setVisibility(View.VISIBLE);
                mBinding.viewForumGrowScoreSeniorPrivilegesExpandBg.setVisibility(View.VISIBLE);
            }
        }
        // 显示普通特权
        if (!isHintNormalPrivilegesModule) {
            SpanUtils normalPrivilegesHint = new SpanUtils();
            normalPrivilegesHint
                    .append(forumGrowScoreHomeInfo.getNormalPrivilegesGetNumber() + "/")
                    .setForegroundColor(0xFFE4BF58)
                    .append(forumGrowScoreHomeInfo.getNormalPrivilegesMaxNumber() + "").setForegroundColor(0xFFA3A3A3);
            mBinding.tvForumGrowScoreNormalPrivilegesContent.setText(normalPrivilegesHint.create());
            if (ForumGrowScoreHomeViewModel.EXPAND_CRITICAL_VALUE < forumGrowScoreHomeInfo.getNormalPrivileges().size()) {
                mBinding.tvForumGrowScoreNormalPrivilegesScaling.setText("展开全部");
                mBinding.tvForumGrowScoreNormalPrivilegesScaling.setCompoundDrawablesWithIntrinsicBounds(0, 0, cn.taqu.lib.base.R.drawable.arrow_down_g3_ic, 0);
                mBinding.tvForumGrowScoreNormalPrivilegesScaling.setTag(PRIVILEGE_LIST_EXPAND);
                mBinding.tvForumGrowScoreNormalPrivilegesScaling.setVisibility(View.VISIBLE);
            }
        }
        if (forumGrowScoreHomeInfo.getLevel() != null && !forumGrowScoreHomeInfo.getLevel().isEmpty()) {
            List<Object> levelTemp = new ArrayList<>();
            levelTemp.add(new ForumGrowScoreHomeInfo.LevelBeanPlaceholder());
            levelTemp.addAll(forumGrowScoreHomeInfo.getLevel());
            levelTemp.add(new ForumGrowScoreHomeInfo.LevelBeanPlaceholder());
            mLevelMedalAdapter.setItems(levelTemp);
            mLevelMedalAdapter.notifyDataSetChanged();
            // 当前等级的信息
            ForumGrowScoreHomeInfo.LevelBean currentLevelBean = forumGrowScoreHomeInfo.getLevel().get(forumGrowScoreHomeInfo.getLevelPosition());
            // 当前的滑动位置
            int currentPosition = forumGrowScoreHomeInfo.getLevelPosition();
            // 找到并显示 左右中，这三张背景图
            if (currentPosition - 1 > 0) {
                mBinding.ivForumGrowScoreLevelLeftBg.setImageFromUrl(forumGrowScoreHomeInfo.getLevel().get(currentPosition - 1).getBackGround());
            } else {
                mBinding.ivForumGrowScoreLevelLeftBg.setImageFromUrl("");
            }
            mBinding.ivForumGrowScoreLevelCurrentBg.setImageFromUrl(currentLevelBean.getBackGround());
            if (currentPosition + 1 < forumGrowScoreHomeInfo.getLevel().size()) {
                mBinding.ivForumGrowScoreLevelRightBg.setImageFromUrl(forumGrowScoreHomeInfo.getLevel().get(currentPosition + 1).getBackGround());
            } else {
                mBinding.ivForumGrowScoreLevelRightBg.setImageFromUrl("");
            }
            mBinding.rvForumGrowScoreLevel.scrollToPosition(currentPosition);
            // 由于需求是要求 Item 居屏幕中间对齐，如果自己去算，很麻烦，所以利用 snapHelper.attachToRecyclerView() 会自动对齐一次 item 的特点来实现
            // 非常规方案，暂时未出现问题，如果有什么不明白的请 @杨浩
            mBinding.rvForumGrowScoreLevel.post(() -> {
                if (isFinishing() || isDestroyed()) {
                    return;
                }
                // attachToRecyclerView 会触发调用 onPageSelected 回调
                if (mBinding.rvForumGrowScoreLevel.getLayoutManager() instanceof ForumGrowScoreHomeMadelScrollHelper) {
                    ((ForumGrowScoreHomeMadelScrollHelper) mBinding.rvForumGrowScoreLevel.getLayoutManager()).attachToRecyclerView();
                }
            });
            // onPageSelected 回调会调用以下俩个方法
            // 显示等级信息
            // showUnLockLevelInfo(currentLevelBean);
            // 显示等级勋章信息
            // showLevelMedalInfo(currentLevelBean);
            // 判断是否需要显示升级动画
            showUpgradeLevelAnimation(currentLevelBean);
        }
    }

    /**
     * 显示升级动画
     *
     * @param levelBean 等级信息
     */
    private void showUpgradeLevelAnimation(@Nullable ForumGrowScoreHomeInfo.LevelBean levelBean) {
        // 这个动画只执行一次
        if (isBindLevelAnimationLayout || levelBean == null || !levelBean.isReachNote() || mGrowScoreHomeInfo == null) {
            return;
        }
        isBindLevelAnimationLayout = true;
        if (mGrowScoreHomeInfo.getLevelPosition() != 0) {
            ForumGrowScoreHomeInfo.LevelBean oldLevel = mGrowScoreHomeInfo.getLevel().get(mGrowScoreHomeInfo.getLevelPosition() - 1);
            int privilegeNumber = mGrowScoreHomeInfo.getNextLevelNormalPrivilegesSize(oldLevel.getReachGrowScore(), levelBean.getReachGrowScore());
            privilegeNumber += mGrowScoreHomeInfo.getNextLevelSeniorPrivilegesSize(oldLevel.getReachGrowScore(), levelBean.getReachGrowScore());
            ForumGrowScoreHomeUpgradeLevelAnimationHelper.showAnimation(mBinding.viewStubForumGrowScoreUpgradeLevelAnimation, levelBean, privilegeNumber);
        }
    }

    /**
     * 处理高级权限列表，展开收起的状态
     *
     * @param textView
     * @param isSeniorPrivileges 是否高级权限列表
     */
    private void dealWithSeniorPrivilegesState(TextView textView, boolean isSeniorPrivileges) {
        if (TextUtils.equals(PRIVILEGE_LIST_COLLAPSE, (CharSequence) textView.getTag())) {
            textView.setText("展开全部");
            textView.setCompoundDrawablesWithIntrinsicBounds(0, 0, cn.taqu.lib.base.R.drawable.arrow_down_g3_ic, 0);
            textView.setTag(PRIVILEGE_LIST_EXPAND);
            textView.setVisibility(View.VISIBLE);
            textView.setVisibility(View.VISIBLE);
            getViewModel().collapsePrivileges(isSeniorPrivileges);
        } else {
            textView.setText("收起");
            textView.setCompoundDrawablesWithIntrinsicBounds(0, 0, cn.taqu.lib.base.R.drawable.arrow_up_g3_ic, 0);
            textView.setTag(PRIVILEGE_LIST_COLLAPSE);
            textView.setVisibility(View.GONE);
            getViewModel().expandPrivileges(isSeniorPrivileges);
        }
    }

    /**
     * 显示等级勋章信息
     *
     * @param levelBean
     */
    private void showLevelMedalInfo(ForumGrowScoreHomeInfo.LevelBean levelBean) {
        if (mGrowScoreHomeInfo == null) {
            return;
        }
        // 已获得和未获得的 Ui 不一样
        if (levelBean.isObtain() || levelBean.isCurrentLevel()) {
            mBinding.tvForumGrowScoreLevelTitle.setText(levelBean.getName());
            mBinding.tvForumGrowScoreLevelTitle.setTextSize(19);
            mBinding.tvForumGrowScoreLevelTitle.setTextColor(0xFFFFFFFF);
            mBinding.tvForumGrowScoreLevelTitle.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            controlView(mBinding.ivForumGrowScoreLevelLeft, View.VISIBLE);
            controlView(mBinding.ivForumGrowScoreLevelRight, View.VISIBLE);
            controlView(mBinding.tvForumGrowScoreLevelNumber, View.VISIBLE);
            mBinding.tvForumGrowScoreLevelNumber.setText(levelBean.isCurrentLevel() ? "当前等级" : "已达成");
            controlView(mBinding.tvForumGrowScoreLevelUnlockHint, View.GONE);
        } else {
            controlView(mBinding.tvForumGrowScoreLevelNumber, View.INVISIBLE);
            mBinding.tvForumGrowScoreLevelTitle.setText(MessageFormat.format("{0}·{1}分解锁", levelBean.getName(), levelBean.getReachGrowScore()));
            mBinding.tvForumGrowScoreLevelTitle.setTextSize(14);
            mBinding.tvForumGrowScoreLevelTitle.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            mBinding.tvForumGrowScoreLevelTitle.setTextColor(0x80FFFFFF);
            controlView(mBinding.ivForumGrowScoreLevelLeft, View.GONE);
            controlView(mBinding.ivForumGrowScoreLevelRight, View.GONE);
            int privilegesSize = levelBean.getNextUnlockNormalPrivilege() + levelBean.getNextUnlockSeniorPrivilege();
            if (privilegesSize > 0) {
                controlView(mBinding.tvForumGrowScoreLevelUnlockHint, View.VISIBLE);
                mBinding.tvForumGrowScoreLevelUnlockHint.setText(MessageFormat.format("解锁{0}项权限/特权", privilegesSize));
            } else {
                controlView(mBinding.tvForumGrowScoreLevelUnlockHint, View.GONE);
            }
        }
        // 已经获得并且非当前等级，需要隐藏
        if (levelBean.isObtain() && !levelBean.isCurrentLevel() || levelBean.isHighestLevel()) {
            controlView(mBinding.ivForumGrowScoreLevelProgressBg, View.GONE);
            controlView(mBinding.tvForumGrowScoreLevelProgressHint, View.GONE);
            controlView(mBinding.progressBarForumGrowScoreLevel, View.GONE);
            controlView(mBinding.viewBarForumGrowScoreLevelBg, View.GONE);
            controlView(mBinding.tvForumGrowScoreLevelProgressNumber, View.GONE);
        } else {
            // 计算距离此等级还差多少分
            controlView(mBinding.ivForumGrowScoreLevelProgressBg, View.VISIBLE);
            controlView(mBinding.tvForumGrowScoreLevelProgressHint, View.VISIBLE);
            controlView(mBinding.progressBarForumGrowScoreLevel, View.VISIBLE);
            controlView(mBinding.viewBarForumGrowScoreLevelBg, View.VISIBLE);
            controlView(mBinding.tvForumGrowScoreLevelProgressNumber, View.VISIBLE);
            if (levelBean.isCurrentLevel()) {
                mBinding.tvForumGrowScoreLevelProgressHint.setText("距下等级");
            } else {
                mBinding.tvForumGrowScoreLevelProgressHint.setText("距此等级");
            }
            // 根据百分比计算长度
            int growScore = mGrowScoreHomeInfo.getGrowScore();
            if (growScore <= 0) {
                growScore = 0;
                mBinding.progressBarForumGrowScoreLevel.setVisibility(View.INVISIBLE);
            } else {
                growScore = (int) ((growScore / (float) levelBean.getNextUnlockNumber()) * SizeUtils.dp2px(180));
                mBinding.progressBarForumGrowScoreLevel.getLayoutParams().width = growScore;
                mBinding.progressBarForumGrowScoreLevel.setVisibility(View.VISIBLE);
            }
            SpanUtils score = new SpanUtils();
            score.append("还需").setForegroundColor(0xFFFFFFFF).append(levelBean.getNextUnlockNumber() - mGrowScoreHomeInfo.getGrowScore() + "").setForegroundColor(0xFFFF4456).append("分").setForegroundColor(0xFFFFFFFF);
            mBinding.tvForumGrowScoreLevelProgressNumber.setText(score.create());
        }
    }

    /**
     * 显示解锁信息
     *
     * @param levelBean
     * @return 总的解锁个数
     */
    private int showUnLockLevelInfo(@Nullable ForumGrowScoreHomeInfo.LevelBean levelBean) {
        if (mGrowScoreHomeInfo == null) {
            return 0;
        }
        mCurrentLevelBean = levelBean;
        if (levelBean == null) {
            controlView(mBinding.tvForumGrowScoreSeniorPrivilegesHint, View.GONE);
            controlView(mBinding.tvForumGrowScoreNormalPrivilegesHint, View.GONE);
            return 0;
        }
        int seniorPrivilegesSize = 0;
        // 显示下一级解锁的高级特权
        if (!isHintSeniorPrivilegesModule) {
            seniorPrivilegesSize = levelBean.getNextUnlockSeniorPrivilege();
            if (!levelBean.isHighestLevel() && seniorPrivilegesSize != 0) {
                controlView(mBinding.tvForumGrowScoreSeniorPrivilegesHint, View.VISIBLE);
                SpanUtils hint = new SpanUtils();
                if (levelBean.isCurrentLevel()) {
                    hint.append("下一级");
                } else {
                    hint.append("此等级");
                }
                hint.setForegroundColor(0xFFA3A3A3).append("解锁" + seniorPrivilegesSize + "项").setForegroundColor(0xFF353535);
                mBinding.tvForumGrowScoreSeniorPrivilegesHint.setText(hint.create());
            } else {
                controlView(mBinding.tvForumGrowScoreSeniorPrivilegesHint, View.GONE);
            }
        }
        int normalPrivilegesSize = 0;
        // 显示下一级解锁的普通特权
        if (!isHintNormalPrivilegesModule) {
            normalPrivilegesSize = levelBean.getNextUnlockNormalPrivilege();
            if (!levelBean.isHighestLevel() && normalPrivilegesSize != 0) {
                controlView(mBinding.tvForumGrowScoreNormalPrivilegesHint, View.VISIBLE);
                SpanUtils hint = new SpanUtils();
                if (levelBean.isCurrentLevel()) {
                    hint.append("下一级");
                } else {
                    hint.append("此等级");
                }
                hint.setForegroundColor(0xFFA3A3A3).append("解锁" + normalPrivilegesSize + "项").setForegroundColor(0xFF353535);
                mBinding.tvForumGrowScoreNormalPrivilegesHint.setText(hint.create());
            } else {
                controlView(mBinding.tvForumGrowScoreNormalPrivilegesHint, View.GONE);
            }
        }
        // 计算总的解锁数量（高级特权 + 普通特权）
        return seniorPrivilegesSize + normalPrivilegesSize;
    }

    /**
     * 显示下级解锁相关信息的弹窗
     */
    private void showNextLevelUnLockDialog() {
        if (mCurrentLevelBean == null || mGrowScoreHomeInfo == null || mGrowScoreHomeInfo.getLevel() == null || mGrowScoreHomeInfo.getLevel().isEmpty()) {
            return;
        }
        ForumGrowScoreHomeInfo.LevelBean temp = null;
        // 如果是当前等级，就需要显示下一级的弹窗
        if (mCurrentLevelBean.isCurrentLevel()) {
            // 当前等级，显示下一等级的信息
            for (ForumGrowScoreHomeInfo.LevelBean levelBean : mGrowScoreHomeInfo.getLevel()) {
                if (levelBean.getReachGrowScore() > mCurrentLevelBean.getReachGrowScore()) {
                    temp = levelBean;
                    break;
                }
            }
        } else {
            // 非当前等级的情况，就只看自己的解锁信息
            temp = mCurrentLevelBean;
        }
        if (temp != null) {
            List<ForumGrowScoreHomeInfo.Privilege> mDialogPrivilegeList = new ArrayList<>();
            mDialogPrivilegeList.addAll(mGrowScoreHomeInfo.getNextLevelSeniorPrivileges(temp.getReachGrowScore()));
            mDialogPrivilegeList.addAll(mGrowScoreHomeInfo.getNextLevelNormalPrivileges(temp.getReachGrowScore()));
            ForumGrowScoreTargetLevelPrivilegeDialog.showDialog(getContext(), mDialogPrivilegeList, temp.getName(), temp.getIcon(), temp.getReachGrowScore());
        }
    }

    /**
     * 初始化等级勋章
     */
    private void initLevelMedalRecyclerView() {
        mLevelMedalAdapter = new XBaseAdapter(getContext());
        mLevelMedalAdapter.setItems(new ArrayList<>());
        mLevelMedalAdapter.register(ForumGrowScoreHomeInfo.LevelBean.class, ForumGrowScoreHomeMedalViewHolder.class);
        mLevelMedalAdapter.register(ForumGrowScoreHomeInfo.LevelBeanPlaceholder.class, ForumGrowScoreHomeMedalPlaceholderViewHolder.class);
        mBinding.rvForumGrowScoreLevel.setAdapter(mLevelMedalAdapter);
        mBinding.rvForumGrowScoreLevel.setNestedScrollingEnabled(false);
        ForumGrowScoreHomeMadelScrollHelper scrollHelper = new ForumGrowScoreHomeMadelScrollHelper(RecyclerView.HORIZONTAL, false, mBinding.rvForumGrowScoreLevel);
        mBinding.rvForumGrowScoreLevel.setLayoutManager(scrollHelper);
        // 绑定滑动事件
        scrollHelper
                .setScrollListener(new ForumGrowScoreHomeMadelScrollHelper.ScrollListener() {
                    @Override
                    public void onPageSelected(String leftImage, String currentImage, String rightImage, int position) {
                        mBinding.ivForumGrowScoreLevelLeftBg.setImageFromUrl(leftImage);
                        mBinding.ivForumGrowScoreLevelLeftBg.setAlpha(0f);
                        mBinding.ivForumGrowScoreLevelCurrentBg.setImageFromUrl(currentImage);
                        mBinding.ivForumGrowScoreLevelCurrentBg.setAlpha(1f);
                        mBinding.ivForumGrowScoreLevelRightBg.setImageFromUrl(rightImage);
                        mBinding.ivForumGrowScoreLevelRightBg.setAlpha(0f);
                        // 这里之所以减 1 ，是因为最左边有个空白的占位符 item ，导致计算的时候 position + 1了
                        if (mGrowScoreHomeInfo != null && mGrowScoreHomeInfo.getLevel() != null) {
                            // 显示解锁信息
                            showUnLockLevelInfo(mGrowScoreHomeInfo.getLevel().get(position - 1));
                            // 显示等级勋章
                            showLevelMedalInfo(mGrowScoreHomeInfo.getLevel().get(position - 1));
                        }
                    }

                    @Override
                    public void onScrolled(float leftAlpha, float currentAlpha, float rightAlpha) {
                        mBinding.ivForumGrowScoreLevelLeftBg.setAlpha(leftAlpha);
                        mBinding.ivForumGrowScoreLevelCurrentBg.setAlpha(currentAlpha);
                        mBinding.ivForumGrowScoreLevelRightBg.setAlpha(rightAlpha);
                    }
                });
    }

    /**
     * 初始化高级权限列表
     */
    private void initSeniorPrivilegesRecyclerView() {
        mSeniorPrivilegesAdapter = new XBaseAdapter(getContext());
        mSeniorPrivilegesAdapter.register(ForumGrowScoreHomeInfo.Privilege.class, ForumGrowScoreHomePrivilegesViewHolder.class);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), 4);
        gridLayoutManager.setRecycleChildrenOnDetach(true);
        mBinding.rvForumGrowScoreSeniorPrivileges.setRecycledViewPool(mRecycledViewPool);
        mBinding.rvForumGrowScoreSeniorPrivileges.setLayoutManager(gridLayoutManager);
        mBinding.rvForumGrowScoreSeniorPrivileges.setAdapter(mSeniorPrivilegesAdapter);
        mBinding.rvForumGrowScoreSeniorPrivileges.setHasFixedSize(true);
        mBinding.rvForumGrowScoreSeniorPrivileges.setNestedScrollingEnabled(false);
        // 监听数据变化
        getViewModel()
                .getGrowScoreHomeSeniorPrivileges()
                .observe(this, list -> {
                    if (list == null || list.isEmpty()) {
                        // 服务端下发的数据为空的时候，需要隐藏高级权限模块
                        hideSeniorPrivilegesView();
                        return;
                    }
                    showSeniorPrivilegesView(list.size());
                    mSeniorPrivilegesAdapter.setItems(list);
                    mSeniorPrivilegesAdapter.notifyDataSetChanged();
                });
    }

    /**
     * 隐藏高级权限模块
     */
    private void hideSeniorPrivilegesView() {
        isHintSeniorPrivilegesModule = true;
        controlView(mBinding.viewForumGrowScoreSeniorPrivilegesBg, View.GONE);
        controlView(mBinding.tvForumGrowScoreSeniorPrivilegesTitle, View.GONE);
        controlView(mBinding.tvForumGrowScoreSeniorPrivilegesContent, View.GONE);
        controlView(mBinding.tvForumGrowScoreSeniorPrivilegesHint, View.GONE);
        controlView(mBinding.rvForumGrowScoreSeniorPrivileges, View.GONE);
        controlView(mBinding.viewForumGrowScoreSeniorPrivilegesExpandBg, View.GONE);
        controlView(mBinding.tvForumGrowScoreSeniorPrivilegesScaling, View.GONE);
    }

    /**
     * 显示高级权限模块
     *
     * @param size 当前权限列表的个数
     */
    private void showSeniorPrivilegesView(int size) {
        isHintSeniorPrivilegesModule = false;
        controlView(mBinding.viewForumGrowScoreSeniorPrivilegesBg, View.VISIBLE);
        controlView(mBinding.tvForumGrowScoreSeniorPrivilegesTitle, View.VISIBLE);
        controlView(mBinding.tvForumGrowScoreSeniorPrivilegesContent, View.VISIBLE);
        controlView(mBinding.tvForumGrowScoreSeniorPrivilegesHint, View.VISIBLE);
        controlView(mBinding.rvForumGrowScoreSeniorPrivileges, View.VISIBLE);
        // 显示的时候需要判断，是否需要显示加载更多，根据当前权限的个数
        if (ForumGrowScoreHomeViewModel.EXPAND_CRITICAL_VALUE < size) {
            controlView(mBinding.viewForumGrowScoreSeniorPrivilegesExpandBg, View.VISIBLE);
            controlView(mBinding.tvForumGrowScoreSeniorPrivilegesScaling, View.VISIBLE);
        }
    }

    /**
     * 初始化普通权限列表
     */
    private void initNormalPrivilegesRecyclerView() {
        mNormalPrivilegesAdapter = new XBaseAdapter(getContext());
        mNormalPrivilegesAdapter.register(ForumGrowScoreHomeInfo.Privilege.class, ForumGrowScoreHomePrivilegesViewHolder.class);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), 4);
        gridLayoutManager.setRecycleChildrenOnDetach(true);
        mBinding.rvForumGrowScoreNormalPrivileges.setLayoutManager(gridLayoutManager);
        mBinding.rvForumGrowScoreNormalPrivileges.setRecycledViewPool(mRecycledViewPool);
        mBinding.rvForumGrowScoreNormalPrivileges.setAdapter(mNormalPrivilegesAdapter);
        mBinding.rvForumGrowScoreNormalPrivileges.setHasFixedSize(true);
        mBinding.rvForumGrowScoreNormalPrivileges.setNestedScrollingEnabled(false);
        // 监听数据变化
        getViewModel()
                .getGrowScoreHomeNormalPrivileges()
                .observe(this, list -> {
                    if (list == null || list.isEmpty()) {
                        // 服务端下发的数据为空的时候，需要隐藏普通权限模块
                        hideNormalPrivilegesView();
                        return;
                    }
                    showNormalPrivilegesView(list.size());
                    mNormalPrivilegesAdapter.setItems(list);
                    mNormalPrivilegesAdapter.notifyDataSetChanged();
                });
    }

    /**
     * 隐藏默认权限模块
     */
    private void hideNormalPrivilegesView() {
        isHintNormalPrivilegesModule = true;
        controlView(mBinding.viewForumGrowScoreNormalPrivilegesBg, View.GONE);
        controlView(mBinding.tvForumGrowScoreNormalPrivilegesTitle, View.GONE);
        controlView(mBinding.tvForumGrowScoreNormalPrivilegesContent, View.GONE);
        controlView(mBinding.tvForumGrowScoreNormalPrivilegesHint, View.GONE);
        controlView(mBinding.rvForumGrowScoreNormalPrivileges, View.GONE);
        controlView(mBinding.tvForumGrowScoreNormalPrivilegesScaling, View.GONE);
    }

    /**
     * 显示默认权限模块
     *
     * @param size 当前权限列表的个数
     */
    private void showNormalPrivilegesView(int size) {
        isHintNormalPrivilegesModule = false;
        controlView(mBinding.viewForumGrowScoreNormalPrivilegesBg, View.VISIBLE);
        controlView(mBinding.tvForumGrowScoreNormalPrivilegesTitle, View.VISIBLE);
        controlView(mBinding.tvForumGrowScoreNormalPrivilegesContent, View.VISIBLE);
        controlView(mBinding.tvForumGrowScoreNormalPrivilegesHint, View.VISIBLE);
        controlView(mBinding.rvForumGrowScoreNormalPrivileges, View.VISIBLE);
        // 显示的时候需要判断，是否需要显示加载更多，根据当前权限的个数
        if (ForumGrowScoreHomeViewModel.EXPAND_CRITICAL_VALUE < size) {
            controlView(mBinding.tvForumGrowScoreNormalPrivilegesScaling, View.VISIBLE);
        }
    }

    /**
     * 获取 viewModel
     *
     * @return
     */
    @NotNull
    private ForumGrowScoreHomeViewModel getViewModel() {
        return ViewModelProviders
                .of(this)
                .get(ForumGrowScoreHomeViewModel.class);
    }

    /**
     * 控制 view 显示隐藏
     *
     * @param view
     * @param visibility
     */
    private void controlView(View view, int visibility) {
        if (view != null && view.getVisibility() != visibility) {
            view.setVisibility(visibility);
        }
    }
}
