package com.xmhaibao.forum.helper;

import android.content.Context;
import android.text.Editable;
import android.text.Html;
import android.text.Spanned;
import android.text.style.TextAppearanceSpan;


import com.xmhaibao.forum.R;

import org.xml.sax.XMLReader;

/**
 * html处理文本标签高亮 
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/11/17.
 */

public class KeyTagHandler implements Html.TagHandler {

    private String key;
    private int sIndex = 0;
    private int eIndex = 0;
    private int index = 0;
    private Context context;

    public KeyTagHandler(Context context,String key) {
        this.context=context;
        this.key = key;

    }

    @Override
    public void handleTag(boolean opening, String tag, Editable output, XMLReader xmlReader) {
        if (tag.toLowerCase().equals(key)) {
            if (opening) {
                sIndex = output.length();
            } else {
                eIndex = output.length();
                output.setSpan(new TextAppearanceSpan(context.getApplicationContext(), R.style.text_forum_search_key)
                        , sIndex, eIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                index++;
            }
        }
    }
}
