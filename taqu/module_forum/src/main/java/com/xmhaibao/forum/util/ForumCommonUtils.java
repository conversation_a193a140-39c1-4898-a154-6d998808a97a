package com.xmhaibao.forum.util;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.ushengsheng.utils.AppNameUtils;
import com.xmhaibao.account.api.router.AccountPinsRouter;
import com.xmhaibao.call.api.bean.CallChannelInfo;
import com.xmhaibao.call.api.bean.CallHostInfo;
import com.xmhaibao.call.api.bean.DatingCallerInfo;
import com.xmhaibao.call.api.router.CallPinsRouter;
import com.xmhaibao.forum.api.bean.ForumHomeDisplayTabInfo;
import com.xmhaibao.forum.api.bean.ForumHomeTabInfo;
import com.xmhaibao.forum.api.bean.ForumMediaInfo;
import com.xmhaibao.forum.api.bean.FriendHomeListBean;
import com.xmhaibao.forum.api.bean.PostDisplayItem;
import com.xmhaibao.forum.api.bean.PostGifInfo;
import com.xmhaibao.forum.api.bean.PostImageInfo;
import com.xmhaibao.forum.api.bean.PostListBaseInfo;
import com.xmhaibao.forum.api.bean.PostListCommonInfo;
import com.xmhaibao.forum.api.bean.PostListHostInfo;
import com.xmhaibao.forum.api.bean.PostListPostInfo;
import com.xmhaibao.forum.api.constants.CirclePostListType;
import com.xmhaibao.forum.api.constants.PersonProfileFromSource;
import com.xmhaibao.forum.api.util.ForumPreferencesUtils;
import com.xmhaibao.forum.bean.ForumPostGridImageBean;
import com.xmhaibao.forum.bean.ForumReportTypeBean;
import com.xmhaibao.forum.bean.PostContentInfo;
import com.xmhaibao.forum.bean.PostHeaderInfo;
import com.xmhaibao.forum.bean.PostListHostQuCallChatRoomInfo;
import com.xmhaibao.forum.bean.PostListQuCallInfo;
import com.xmhaibao.forum.bean.PostListVoteInfo;
import com.xmhaibao.forum.constants.ForumKVKey;
import com.xmhaibao.forum.core.ForumProcess;
import com.xmhaibao.forum.driver.dialog.ForumDriverHintDialog;
import com.xmhaibao.forum.driver.model.getAccountBlackInfo;
import com.xmhaibao.forum.home.ForumHomeFragment4;
import com.xmhaibao.forum.repository.ForumRepository;
import com.xmhaibao.forum.router.ForumRouter;
import com.xmhaibao.gift.api.bean.MessageGiftPacketBean;
import com.xmhaibao.gift.api.bean.MessageLiveGiftInfo;
import com.xmhaibao.message.api.router.MessagePinsRouter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Random;
import java.util.Set;

import cn.taqu.lib.base.BaseApplication;
import cn.taqu.lib.base.common.AppAccountBean;
import cn.taqu.lib.base.common.XjbPremissionHelper;
import cn.taqu.lib.base.constants.CommonConstants;
import cn.taqu.lib.base.http.LifeCycleGsonCallBack;
import cn.taqu.lib.base.router.ARouterManager;
import cn.taqu.lib.base.utils.BaseSPUtils;
import cn.taqu.lib.base.utils.appdiff.AppDifferentiationUtils;
import cn.taqu.lib.base.xtracker.ForumTrackerOperate;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import hb.common.data.AccountHelper;
import hb.common.helper.ServerTime;
import hb.location.region.XRegionService;
import hb.utils.CollectionUtils;
import hb.utils.StringUtils;
import hb.utils.TimeUtils;
import hb.utils.ToastUtils;
import hb.utils.kvcache.KVCacheUtils;

/**
 * description:
 *
 * <AUTHOR>
 * @date 2019/8/13
 */
public class ForumCommonUtils {

    public static String mFromSource = "";
    public static String sNearTabName = ForumHomeFragment4.TAB_NAME_NEAR_HAS_PERMISSION;

    public static void addDeletePointWetTask(boolean isPointWet, final String postId, final String reviewId) {
        if (isPointWet) {
            ForumProcess.deleteReviewDianZan(postId, reviewId);
        } else {
            ForumProcess.addReviewDianZan(postId, reviewId);
        }
    }

    public static String getForumTabType() {
        ForumHomeDisplayTabInfo displayTabInfo = ForumPreferencesUtils.getForumTabInfo();
        if (displayTabInfo != null) {
            if (!checkIsNewUser()) {
                String tabType = displayTabInfo.getOldUserTab();
                return tabType;
            } else {
                String tabType = displayTabInfo.getNewUserTab();
                return tabType;
            }
        } else {
            if (!checkIsNewUser()) {
                return ForumHomeTabInfo.FORUM_HOME_TAB_TYPE_FOCUS;
            } else {
                return ForumHomeTabInfo.FORUM_HOME_TAB_TYPE_RECOMMEND;
            }
        }
    }


    /**
     * 社区帖子的拨打趣聊语音   (此处无验证是否可以拨打趣聊，需要服务器下发可以拨打的趣聊)
     *
     * @param context
     * @param callType 此处写死
     * @param info
     */
    public static void onCallToQuCallVoice(Context context, String callType, PostListHostQuCallChatRoomInfo info) {
        boolean isVideo = CallChannelInfo.TYPE_VIDEO.equals(callType);
        if (info == null) {
            return;
        }
        if (info.getAccount_uuid() != null && info.getAccount_uuid().equals(AccountHelper.getAccountUuid())) {
            ToastUtils.makeToast(context, "不能跟自己通话");
            return;
        }
        CallChannelInfo callChannelInfo = CallPinsRouter.imCallService().getStartCallChannelInfo(info.getAvatar(), info.getNickname(), info.getAccount_uuid(),
                true, 0, CallChannelInfo.TYPE_VIDEO.equals(callType), false, "");
        if (callChannelInfo != null) {
            callChannelInfo.setSource(CommonConstants.CALL_SOURCE_HALL);
            CallPinsRouter.imCallService().checkAndStartCall(context, callChannelInfo, true);
        }
    }

    /**
     * 社区帖子的拨打趣聊视频
     *
     * @param context
     * @param info
     */
    public static void callToQuCallVideo(Context context, PostListHostQuCallChatRoomInfo info) {
        if (info == null || info.getPostListQuCallInfo() == null) {
            return;
        }
        PostListQuCallInfo quCallInfo = info.getPostListQuCallInfo();
        if (StringUtils.isEmpty(quCallInfo.getVideoCover())) {
            ForumRouter.launchPersonalHomePageActivity(context, quCallInfo.getAccountUuid(), getPersonalSourceByTab(info));
            return;
        }
        ForumRepository.getDatingCallerInfo(quCallInfo.getAccountUuid(), AccountHelper.getTicketId())
                .execute(new LifeCycleGsonCallBack<DatingCallerInfo>(context) {

                    @Override
                    public void onStart(boolean isCache) {
                        super.onStart(isCache);
                    }

                    @Override
                    public void onLifeCycleFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {
                    }

                    @Override
                    public void onLifeCycleSuccess(boolean isCache, @Nullable DatingCallerInfo obj, @NonNull IResponseInfo response) {
                        CallHostInfo callHostInfo = datingCallerInfoConvertCallHostInfo(obj);
                        CallPinsRouter.launchPlayVideoHostActivity(callHostInfo);
                    }
                });
    }


    public static CallHostInfo datingCallerInfoConvertCallHostInfo(DatingCallerInfo info) {
        if (info == null) {
            return null;
        }
        CallHostInfo callHostInfo = new CallHostInfo();
        callHostInfo.setUuid(info.getAccountUuid());
        callHostInfo.setAge(info.getAge());
        callHostInfo.setAudioIntro(info.getAudioIntro());
        callHostInfo.setAvatar(info.getAvatar());
        callHostInfo.setStatus(info.getCallStatus() + "");
        callHostInfo.setCity(info.getCity());
        callHostInfo.setAudioTime(info.getDuration());
        callHostInfo.setImgList(info.getImgList());
        callHostInfo.setNickname(info.getNickName());
        callHostInfo.setPersonalProfile(info.getPersonalProfile());
        callHostInfo.setReviews(info.getReviews());
        callHostInfo.setSexType(info.getSexType() + "");
        callHostInfo.setVideoCover(info.getVideoCover());
        callHostInfo.setVideoIntro(info.getVideoIntro());
        callHostInfo.setVideoStatus(info.getVideoStatus());
        callHostInfo.setPrice(info.getPrice());
        callHostInfo.setVideoPrice(info.getVideoPrice());
        callHostInfo.doExtra(null);
        return callHostInfo;
    }


    /**
     * 将主播的location 转换成地点
     *
     * @param dbRegionService
     * @param info
     */
    public static String parseCity(XRegionService dbRegionService, PostListHostInfo info) {
        // 城市id
//        info.setCityId(location.optString("location"));
        String location = info.getLocation();
        if (dbRegionService != null) {
            String cityName = dbRegionService.getRegionInfoById(location).getRegionName();
            // 把位置城市添加到属性中
            if (StringUtils.isEmpty(cityName)) {
                cityName = "火星"; // 未获取到城市信息的，统一显示【火星】
            }
            cityName = cityName.replace("市", "");
            return cityName;
        }
        return "";
    }


    public static PostListHostQuCallChatRoomInfo convertToPostListHostQuCallChatRoomInfo(PostListPostInfo info) {
        if (info == null) {
            return null;
        }
        PostListHostQuCallChatRoomInfo mPostListHostQuCallChatRoomInfo = new PostListHostQuCallChatRoomInfo();
        mPostListHostQuCallChatRoomInfo.setAccount_uuid(info.getAccountId());
        mPostListHostQuCallChatRoomInfo.setIdentityIconList(info.getIdentityIconList());
        mPostListHostQuCallChatRoomInfo.setAvatar(info.getUserAvatar());
        mPostListHostQuCallChatRoomInfo.setCity(info.getCity());
        mPostListHostQuCallChatRoomInfo.setcCity(info.getcCity());
        mPostListHostQuCallChatRoomInfo.setGender_certification(info.getGender_certification());
        mPostListHostQuCallChatRoomInfo.setHost_info(info.getPostListHostInfo());
        mPostListHostQuCallChatRoomInfo.setNickname(info.getNickName());
        mPostListHostQuCallChatRoomInfo.setSex_type(info.getSexType());
        mPostListHostQuCallChatRoomInfo.setPostListType(info.getPostListType());
        mPostListHostQuCallChatRoomInfo.setAge(info.getAge());
        mPostListHostQuCallChatRoomInfo.setGrowScore(info.getGrowScore());
        mPostListHostQuCallChatRoomInfo.doExtra(null);
        if (mPostListHostQuCallChatRoomInfo.getHost_info() != null) {
            mPostListHostQuCallChatRoomInfo.getHost_info().doExtra(null);
        }
        return mPostListHostQuCallChatRoomInfo;
    }


    /**
     * 去重
     *
     * @param list
     */
    public static void distinct(List list) {
        Set set = new HashSet();
        List newList = new ArrayList();
        for (Iterator iter = list.iterator(); iter.hasNext(); ) {
            Object element = iter.next();
            if (set.add(element))
                newList.add(element);
        }
        list.clear();
        list.addAll(newList);
    }

    /**
     * 数字转k
     *
     * @param num
     * @return
     */
    public static String convertToK(float num) {
        if (num == 0) {
            return "  ";
        }
        if (Math.abs(num) < 1000) {
            return ((int) num) + "";
        } else {
            return BigDecimal.valueOf(num / 1000).setScale(1, BigDecimal.ROUND_HALF_DOWN).floatValue() + "k";
        }
    }

    /**
     * 私信或者打招呼
     */
    public static void sendMsg(Context context, String accountId) {
        if (!AccountHelper.isUserLogined()) {
            AccountPinsRouter.accountService().launchLogin();
            return;
        }
        if (!ForumRouter.forumPermissionService().checkMobileDriverLevel(context)) {
            return;
        }
        if (!XjbPremissionHelper.getInstance().isLetterEnable()) {
            if (AppDifferentiationUtils.isJimuCommunity()) {
                ForumRouter.forumPermissionService().showJimuLetterDriverScoreLack(context);
                return;
            }
            ForumDialogUtils.showDriverScoreLack(context);
            return;
        }
        if (accountId.equals(AccountHelper.getAccountUuid())) {
            ToastUtils.makeToast(BaseApplication.getInstance(), "不能向自己发私信哦~", true);
            return;
        }
        MessagePinsRouter.launchChatMessageActivity(accountId);
        ARouterManager.messageForBaseService().trackMessageDetailShow("other", accountId);
    }

    /**
     * 获取封禁信息，如果被封禁就弹框提示
     *
     * @param activity 需要弹出的dialog的页面
     */
    public static void requestCheckShowAccountBlackDialog(FragmentActivity activity) {
        ForumRepository.getAccountBlackInfo(AccountHelper.getTicketId())
                .execute(new LifeCycleGsonCallBack<getAccountBlackInfo>(activity) {

                    @Override
                    public void onLifeCycleFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {

                    }

                    @Override
                    public void onLifeCycleSuccess(boolean isCache, @Nullable getAccountBlackInfo obj, @NonNull IResponseInfo response) {
                        // 封禁需要提示
                        if (obj == null || obj.getIs_black() == null) {
                            return;
                        }
                        if (!obj.getIs_black().equals("0")) {
                            ForumDriverHintDialog.initBannedDialog(activity,
                                    obj.getAppeal_status(), Long.decode(obj.getBlack_time()));
                        }
                    }
                });
    }

    /**
     * 社区举报类型
     *
     * @return
     */
    public static ArrayList<ForumReportTypeBean> createForumReportType() {
        ArrayList<ForumReportTypeBean> list = new ArrayList<>();
        ForumReportTypeBean minor1 = new ForumReportTypeBean("该用户可能是未成年", ForumReportTypeBean.MINOR_TYPE_1);
        ForumReportTypeBean minor2 = new ForumReportTypeBean("涉嫌对未成年人实施违法行为", ForumReportTypeBean.MINOR_TYPE_2);
        ForumReportTypeBean minor3 = new ForumReportTypeBean("传播危害未成年人身心健康的内容", ForumReportTypeBean.MINOR_TYPE_3);
        ForumReportTypeBean sex = new ForumReportTypeBean("色情污秽或低俗内容", ForumReportTypeBean.SEX);
        ForumReportTypeBean ad = new ForumReportTypeBean("广告及垃圾信息", ForumReportTypeBean.AD);
        ForumReportTypeBean political = new ForumReportTypeBean("涉及政治", ForumReportTypeBean.POLITICAL);
        ForumReportTypeBean abuse = new ForumReportTypeBean("语言侮辱、脏话", ForumReportTypeBean.ABUSE);
        ForumReportTypeBean other = new ForumReportTypeBean("其他", ForumReportTypeBean.OTHER);
        list.add(minor1);
        list.add(minor2);
        list.add(minor3);
        list.add(sex);
        list.add(ad);
        list.add(political);
        list.add(abuse);
        list.add(other);
        return list;
    }

    public static String getEditHideRandomText() {
        Random random = new Random();
        int num = random.nextInt(11);
        String text;
        switch (num) {
            case 0:
                text = "他趣清清，口下留情（≧∇≦）";
                break;
            case 1:
                text = "贴小广告会被警察叔叔抓走哦╥﹏╥";
                break;
            case 2:
                text = "来卖萌，别来卖货(╯ω╰)";
                break;
            case 3:
                text = "不要让我看到少儿不宜(＾ｰ^)ノ";
                break;
            case 4:
                text = "我可爱，因为你文明（＾ν＾）";
                break;
            case 5:
                text = "凶我我会哭的呦∑(ﾟДﾟ）";
                break;
            case 6:
                text = "我有玻璃的心，最怕锋利的贱(つД`)ノ";
                break;
            case 7:
                text = "再刺激我我就要爆啦(´Д` )";
                break;
            case 8:
                text = "撸是你的事，脏就是你的错啦ಠ_ಠ";
                break;
            case 9:
                text = "有美文，有美女，还有文明的你( ^ω^ )";
                break;
            case 10:
                text = "随口一句我也可能哭出声音(╥﹏╥)";
                break;
            default:
                text = "他趣清清，口下留情（≧∇≦）";
                break;
        }
        return AppNameUtils.replaceAppName(text);
    }

    /**
     * 是否是新用户，首次打开应用3天内。
     *
     * @return true 新用户
     */
    public static boolean checkIsNewUser() {
        long openTime = BaseSPUtils.getFirstOpenAppTime();
        long currentTime = System.currentTimeMillis() / 1000;
        long difTime = currentTime - openTime;
        long threeDayTime = 3 * 24 * 60 * 60;
        return difTime <= threeDayTime;
    }

    /**
     * 是否是新注册用户， 新注册3天内。
     *
     * @return true 新用户
     */
    public static boolean checkIsNewRegisterUser() {
        if (!AccountHelper.isUserLogined()) {
            return false;
        }
        String createTime = AppAccountBean.get().getRegisterTime();
        if (StringUtils.isEmpty(createTime)) {
            return false;
        }
        long createTimeL = 0;
        try {
            createTimeL = Long.valueOf(createTime);
        } catch (Exception e) {
            return false;
        }
        long currentTime = System.currentTimeMillis() / 1000;
        long difTime = currentTime - createTimeL;
        long threeDayTime = 3 * 24 * 60 * 60;
        return difTime <= threeDayTime;
    }

    @NonNull
    public static FriendHomeListBean postListPostInfo2FriendFindFriendListBean(PostListPostInfo info) {
        if (info == null) {
            return null;
        }
        FriendHomeListBean friendHomeListBean = new FriendHomeListBean();
        ArrayList<FriendHomeListBean.ImgListBean> imgList = new ArrayList<>();

        friendHomeListBean.setAccountName(info.getName());
        friendHomeListBean.setAccountUuid(info.getAccountId());
        friendHomeListBean.setAge(info.getAge() + "");
        friendHomeListBean.setAvatar(info.getUserAvatar());
        friendHomeListBean.setDist(info.getDistance());
        friendHomeListBean.setGrowScore(info.getGrowScore());
        friendHomeListBean.setFaceCertification(info.getFaceCertification());
        friendHomeListBean.setProfileVerifyStatus(info.getProfileVerifyStatus());
        friendHomeListBean.setSexType(info.getSexType());
        friendHomeListBean.setImgList(imgList);
        if (CollectionUtils.isNotEmpty(info.getImgList())) {
            for (int i = 0; i < info.getImgList().size(); i++) {
                FriendHomeListBean.ImgListBean imgListBean = new FriendHomeListBean.ImgListBean();
                imgListBean.setPhotoUrl(info.getImgList().get(i).getUrl());
                imgList.add(imgListBean);
            }
        }
        return friendHomeListBean;
    }

    /**
     * 保存个人主页拨打引导时间和次数
     *
     * @param count
     */
    public static void saveGuideCountAndTime(int count) {
        KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
                .putLong(ForumKVKey.FORUM_PERSONAL_HOME_CALL_GUIDE_TIME, ServerTime.currentTimeMillis());
        KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
                .putInt(ForumKVKey.FORUM_PERSONAL_HOME_CALL_GUIDE_COUNT, count + 1);
    }


    /**
     * 通过礼物id查找skuid
     *
     * @param messageGiftPacketBean 个人的礼物背包信息
     * @param gid                   需要查找的对面礼物
     * @return 对应礼物的背包skuid
     */
    @Nullable
    public static String getSkuIdByGid(@Nullable MessageGiftPacketBean messageGiftPacketBean, @Nullable String gid) {
        if (messageGiftPacketBean == null || CollectionUtils.isEmpty(messageGiftPacketBean.getList()) || StringUtils.isEmpty(gid)) {
            return "";
        }
        List<MessageLiveGiftInfo> list = messageGiftPacketBean.getList();
        for (MessageLiveGiftInfo info : list) {
            if (info == null) {
                continue;
            }
            if (gid.equals(info.getGid())) {
                return info.getSkuId();
            }
        }
        return "";
    }

    /**
     * 帖子创建时间格式
     * @param time
     * @return
     */
    public static String getNewForumPostTime(String time) {
        StringBuffer dateStr = new StringBuffer();
        try {
            if ("0".equals(time) || StringUtils.isEmpty(time)) {
                return "";
            }
            Calendar myDate = Calendar.getInstance();
            myDate.setTime(new Date(Long.valueOf(time) * 1000));
            long realTimeInMillisByDiff = System.currentTimeMillis() + TimeUtils.LOCAL_DIFF_SERVER_SECONDS * 1000;
            long mytime = realTimeInMillisByDiff - myDate.getTimeInMillis();
            if (mytime < 0) {
                dateStr.append("刚刚");
            } else if (mytime / 1000 < 60) {
                dateStr.append("刚刚");
            } else if (mytime / 1000 / 60 < 60) {
                dateStr.append("刚刚");
            } else if (mytime / 1000 / 60 / 60 < 24) {
                dateStr.append(mytime / 1000 / 60 / 60).append("小时前");
            } else if (mytime / 1000 / 60 / 60 / 24 < 30) {
                dateStr.append(mytime / 1000 / 60 / 60 / 24).append("天前");
            } else {
                //具体的年月日
                dateStr.append(TimeUtils.millis2String(myDate.getTimeInMillis(), "yyyy-MM-dd"));
            }
        } catch (Exception e) {
        }
        return dateStr.toString();
    }
    /**
     * 表态人数格式化显示
     * @param num
     * @return 1w 以下显示具体数字  超过1w 则显示xxx.x万
     */
    public static String expressAttitudeCountFormat(String num) {
        int value = StringUtils.stringToInt(num);
        if (value < 10000) {
            return num;
        }
        float result = value / 10000.0F;
        return String.format("%sw", formatDecimal(result, 1));
    }

    /**
     * 不四舍五入保留小数点后两位的方法
     * @ maxDigits 最多保留的小数位
     */
    public static String formatDecimal (double value,int maxDigits){
        final DecimalFormat formatter = new DecimalFormat();
        formatter.setMaximumFractionDigits(maxDigits);
        formatter.setGroupingSize(0);

        formatter.setRoundingMode(RoundingMode.FLOOR);
        return formatter.format(value);
    }

    /**
     * 获取帖子类型 字符串
     *
     * @param postInfo
     * @return
     */
    public static String getPostTypeString(PostListBaseInfo postInfo) {
        int postType = getPostType(postInfo);
        return getPostTypeString(postType);
    }

    /**
     * 无类型
     */
    public static final int POST_TYPE_NONE = 0;
    /**
     * 投票帖
     */
    public static final int POST_TYPE_VOTE = 1;
    /**
     * 视频帖
     */
    public static final int POST_TYPE_VIDEO = 2;
    /**
     * 图片帖
     */
    public static final int POST_TYPE_IMAGE = 3;
    /**
     * 纯文字帖
     */
    public static final int POST_TYPE_TEXT = 4;

    /**
     * 获取帖子类型 字符串
     *
     * @param postType 为POST_TYPE_NONE时 返回空字符串
     * @return
     */
    public static String getPostTypeString(int postType) {
        if (postType == POST_TYPE_NONE) {
            return "";
        }
        if (postType == POST_TYPE_VOTE) {
            return "投票";
        } else if (postType == POST_TYPE_VIDEO) {
            return "视频帖";
        } else if (postType == POST_TYPE_IMAGE) {
            return "图片帖";
        } else {
            return "纯文字帖";
        }
    }
    /**
     * 获取帖子类型
     * @param postInfo
     * @return   1 投票帖 2 视频帖 3 图片帖  4 文字帖 ，0 参数为null时返回
     */
    public static int getPostType(PostListBaseInfo postInfo) {
        if (postInfo == null) {
            return  POST_TYPE_NONE;
        }
        if (postInfo instanceof PostListVoteInfo) {
            return POST_TYPE_VOTE;
        }
        if (postInfo instanceof PostListCommonInfo) {
            if (((PostListCommonInfo) postInfo).isVideoPost()) {
                return POST_TYPE_VIDEO;
            }

            if (CollectionUtils.isNotEmpty(((PostListCommonInfo) postInfo).getImgList())) {
                return POST_TYPE_IMAGE;
            }
        }

        return POST_TYPE_TEXT;

    }

    /**
     * 获取帖子类型
     * @param postInfo
     * @return 1 投票帖 2 视频帖 3 图片帖  4 文字帖 ，0 参数为null时返回
     */
    public static int getPostType(PostContentInfo postInfo) {
        if (postInfo == null) {
            return POST_TYPE_NONE;
        }
        PostHeaderInfo postHeaderInfo = postInfo.getPostHeaderInfo();
        if (postHeaderInfo.isVotePost()) {
            return POST_TYPE_VOTE;
        }
        ForumMediaInfo mediaInfo = postInfo.getMediaInfo();
        if (mediaInfo != null) {
            return POST_TYPE_VIDEO;
        }
        if (CollectionUtils.isNotEmpty(postInfo.getPostMultist())) {
            for (PostDisplayItem item : postInfo.getPostMultist()) {
                if (item instanceof PostImageInfo || item instanceof PostGifInfo|| item instanceof ForumPostGridImageBean) {
                    return POST_TYPE_IMAGE;

                }

            }
        }

        return POST_TYPE_TEXT;

    }

    /**
     * 根据广场列表类型获取个人主页来源
     * @param info 帖子信息
     * @return source
     */
    public static String getPersonalSourceByTab(PostListBaseInfo info) {
        if (info != null) {
            if (info.getPostListType() != CirclePostListType.PostListType.LIST_TYPE_IS_NEAR) {
                return PersonProfileFromSource.SOURCE_FORUM_NEAR;
            } else if (info.getPostListType() == CirclePostListType.PostListType.LIST_TYPE_IS_FOLLOW) {
                return PersonProfileFromSource.SOURCE_FORUM_FOLLOW;
            } else if (info.getPostListType() == CirclePostListType.PostListType.LIST_TYPE_IS_BEST_LIST) {
                return PersonProfileFromSource.SOURCE_FORUM_RECOMMEND;
            }
        }
        return PersonProfileFromSource.SOURCE_SQUARE;
    }

    /**
     * 根据广场列表类型、帖子获取个人主页来源 如：帖子详情页-推荐、帖子详情页-附近、帖子详情页-关注
     * @param operatePosition 来源 {@link ForumTrackerOperate}
     * @return source
     */
    public static String getPersonalSourceByPostTab(String operatePosition) {
        StringBuilder sb = new StringBuilder();
        sb.append(PersonProfileFromSource.SOURCE_POST);
        if (StringUtils.isNotEmpty(operatePosition)) {
            if (TextUtils.equals(ForumTrackerOperate.TAB_FORUM_HOME_FOLLOW, operatePosition)) {
                sb.append("-关注");
            } else if (TextUtils.equals(ForumTrackerOperate.TAB_FORUM_HOME_NEAR, operatePosition)) {
                sb.append("-").append(sNearTabName);
            } else if (TextUtils.equals(ForumTrackerOperate.TAB_FORUM_HOME_RECOMMEND, operatePosition)) {
                sb.append("-推荐");
            }
        }
        return sb.toString();
    }

}
