package com.xmhaibao.forum.helper;

import androidx.lifecycle.LifecycleOwner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSnapHelper;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.xmhaibao.forum.R;
import com.xmhaibao.forum.widget.ForumGrowScoreHomeRecyclerView;

import org.jetbrains.annotations.NotNull;

import cn.taqu.lib.base.lifecycle.XDefaultLifecycleObserver;
import hb.utils.SizeUtils;

/**
 * @author：杨浩 项目：taqu
 * 创建日期：2019-12-26
 * 功能简介 有趣值中心，勋章的滑动效果
 */
public class ForumGrowScoreHomeMadelScrollHelper extends LinearLayoutManager implements XDefaultLifecycleObserver {

    /**
     * 滑动效果所绑定的 view
     */
    @Nullable
    private ForumGrowScoreHomeRecyclerView mBindRecyclerView;

    /**
     * 单页滑动效果
     */
    @Nullable
    private LinearSnapHelperWrapper mPagerSnapHelper;

    /**
     * 水平最大滑动距离(一页的滑动距离)
     */
    private final int MAX_DOWN_X = SizeUtils.dp2px(125);

    /**
     * view 最大缩放比例
     */
    private final float BIG_VIEW_SCALE = 1;

    /**
     * view 最小缩放比例
     */
    private final float MIN_VIEW_SCALE = 0.8f;

    /**
     * 缩放比例最大差值（View 最大比例与最小比例的差值）
     */
    private final float SCALE_DIFF_MAX = BIG_VIEW_SCALE - MIN_VIEW_SCALE;

    /**
     * view 最大透明度
     */
    private final float BIG_VIEW_ALPHA = 1f;

    /**
     * view 最小透明度
     */
    private final float MIN_VIEW_ALPHA = 0.6f;

    /**
     * 透明度比例最大差值（View 最大比例与最小比例的差值）
     */
    private final float ALPHA_DIFF_MAX = BIG_VIEW_ALPHA - MIN_VIEW_ALPHA;

    /**
     * 当前显示的位置
     */
    private int mCurrentPosition = -1;

    /**
     * 总的偏移量
     */
    private int mCurrentItemOffset = -1;

    /**
     * 滑动回调
     */
    @Nullable
    private ScrollListener mScrollListener;

    /**
     * 用于判断每次滑动的方法，抬手置空
     */
    private int mScrollDirection;

    /**
     * @param recyclerView 需要绑定滑动事件的 view
     */
    public ForumGrowScoreHomeMadelScrollHelper(@RecyclerView.Orientation int orientation,
                                               boolean reverseLayout, ForumGrowScoreHomeRecyclerView recyclerView) {
        super(recyclerView.getContext(), orientation, reverseLayout);
        bindRecyclerView(recyclerView);
    }

    /**
     * @param recyclerView 需要绑定滑动事件的 view
     */
    public ForumGrowScoreHomeMadelScrollHelper bindRecyclerView(@NotNull ForumGrowScoreHomeRecyclerView recyclerView) {
        bindLifecycleObserver(recyclerView.getContext());
        mPagerSnapHelper = new LinearSnapHelperWrapper();
        recyclerView.addOnScrollListener(new ScrollWrapper());
        mBindRecyclerView = recyclerView;
        return this;
    }

    /**
     * 绑定 recyclerview
     */
    public void attachToRecyclerView() {
        if (mPagerSnapHelper != null && mBindRecyclerView != null) {
            mPagerSnapHelper.attachToRecyclerView(mBindRecyclerView);
        }
    }

    public void setScrollListener(ScrollListener scrollListener) {
        mScrollListener = scrollListener;
    }

    @Override
    public void scrollToPosition(int position) {
        super.scrollToPosition(position);
        mCurrentPosition = position;
    }

    @Override
    public void onDestroyDealWith(LifecycleOwner owner) {
        if (mBindRecyclerView != null) {
            mBindRecyclerView.clearOnScrollListeners();
            mBindRecyclerView = null;
        }
        if (mPagerSnapHelper != null) {
            mPagerSnapHelper.attachToRecyclerView(null);
            mPagerSnapHelper = null;
        }
        mScrollListener = null;
    }

    private class LinearSnapHelperWrapper extends LinearSnapHelper {

        @Override
        public int[] calculateDistanceToFinalSnap(@NonNull RecyclerView.LayoutManager layoutManager, @NonNull View targetView) {
            int[] temp = super.calculateDistanceToFinalSnap(layoutManager, targetView);
            // temp 代表了 x 和 y 轴固定会有俩
            if (temp != null && temp.length == 2) {
                // 同时为 0 的情况下，代表对其完成（用户真正的抬手了）
                if (temp[0] == 0 && temp[1] == 0) {
                    if (mScrollListener != null && mBindRecyclerView != null && mBindRecyclerView.getLayoutManager() != null && mBindRecyclerView.getAdapter() != null) {
                        int position = mBindRecyclerView.getChildLayoutPosition(targetView);
                        // 避免出现多次回调
                        if (mCurrentPosition == position) {
                            return temp;
                        }
                        mScrollDirection = 0;
                        mCurrentPosition = position;
                        View leftView = (position - 1) > 0 ? mBindRecyclerView.getLayoutManager().findViewByPosition(position - 1) : null;
                        View rightView = position < mBindRecyclerView.getAdapter().getItemCount() - 1 ? mBindRecyclerView.getLayoutManager().findViewByPosition(position + 1) : null;
                        // viewHolder 中保存了 tag
                        String leftImageUrl = "";
                        if (leftView != null && leftView.getTag() instanceof String) {
                            leftImageUrl = (String) leftView.getTag();
                        }
                        String currentImageUrl = "";
                        if (targetView.getTag() instanceof String) {
                            currentImageUrl = (String) targetView.getTag();
                        }
                        String rightImageUrl = "";
                        if (rightView != null && rightView.getTag() instanceof String) {
                            rightImageUrl = (String) rightView.getTag();
                        }
                        mScrollListener.onPageSelected(leftImageUrl, currentImageUrl, rightImageUrl, position);
                    }
                }
            }
            return temp;
        }
    }

    /**
     * 处理滑动放大、滑动颜色改变
     */
    private class ScrollWrapper extends RecyclerView.OnScrollListener {

        @Override
        public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
            // 矫正由于 scrollToPosition 导致的位置不齐
            initCurrentItemOffset(recyclerView);
            mScrollDirection += dx;
            mCurrentItemOffset += dx;
            if (mCurrentItemOffset < 0) {
                mCurrentItemOffset = 0;
            }
            if (dx == 0) {
                return;
            }
            dealWithScroll(recyclerView);
        }
    }

    /**
     * 处理滑动效果
     *
     * @param recyclerView
     */
    private void dealWithScroll(RecyclerView recyclerView) {
        // 确定当前的位置
        // 之所以要加一 因为第一个是占位符
        int position = mCurrentItemOffset / MAX_DOWN_X + 1;
        // 拿到需要放大的 view 与需要缩小的 view
        View currentView = findViewByPosition(position);
        if (currentView == null) {
            return;
        }
        View minView = currentView.findViewById(R.id.ivForumGrowScoreMedalBg);
        View bigView = position < recyclerView.getAdapter().getItemCount() - 1 ? findViewByPosition(position + 1) : null;
        if (bigView != null) {
            bigView = bigView.findViewById(R.id.ivForumGrowScoreMedalBg);
        }
        // 先计算当前移动的百分比 ( currentItemOffset % MAX_DOWN_X) / MAX_DOWN_X
        // 然后根据百分比计算缩放值
        float scale = SCALE_DIFF_MAX * ((mCurrentItemOffset % MAX_DOWN_X) / (float) MAX_DOWN_X);
        float alpha = ALPHA_DIFF_MAX * ((mCurrentItemOffset % MAX_DOWN_X) / (float) MAX_DOWN_X);
        // 平整数值
        scale = flatData(scale, SCALE_DIFF_MAX, 0);
        alpha = flatData(alpha, ALPHA_DIFF_MAX, 0);
        if (minView != null) {
            // 最大值 - 当前减少的值 = 显示值
            float minScale = BIG_VIEW_SCALE - scale;
            float minAlpha = BIG_VIEW_ALPHA - alpha;
            minView.setScaleX(minScale);
            minView.setScaleY(minScale);
            minView.setAlpha(minAlpha);
        }
        if (bigView != null) {
            // 最小值 + 当前增加的值 = 显示值
            float bigScale = MIN_VIEW_SCALE + scale;
            float bigAlpha = MIN_VIEW_ALPHA + alpha;
            bigView.setScaleX(bigScale);
            bigView.setScaleY(bigScale);
            bigView.setAlpha(bigAlpha);
        }
        if (mScrollListener != null) {
            float leftAlpha;
            float rightAlpha;
            float currentAlpha;
            // 判断方向
            // 如果是往左滑的话需要右边要慢慢显示
            // 要是是往右滑的话需要右边要慢慢显示
            if (mScrollDirection > 0) {
                rightAlpha = 1;
                leftAlpha = 0;
            } else {
                rightAlpha = 0;
                leftAlpha = 1;
            }
            currentAlpha = flatData(BIG_VIEW_ALPHA - (Math.abs(mScrollDirection) / (float) MAX_DOWN_X), BIG_VIEW_ALPHA, 0);
            mScrollListener.onScrolled(leftAlpha, currentAlpha, rightAlpha);
        }
    }

    /**
     * 矫正由于 scrollToPosition 导致的位置不齐
     *
     * @param recyclerView
     */
    private void initCurrentItemOffset(RecyclerView recyclerView) {
        if (mCurrentPosition != -1 && mCurrentItemOffset == -1 && recyclerView.getChildCount() != 0) {
            mCurrentItemOffset = MAX_DOWN_X * (mCurrentPosition);
        }
    }

    /**
     * 平整数据
     *
     * @param currentData 当前数据
     * @param maxData     当前数据的最大值
     * @param minData     当前数据的最小值
     */
    private float flatData(float currentData, float maxData, float minData) {
        if (currentData > maxData) {
            return maxData;
        }
        if (currentData < minData) {
            return maxData;
        }
        return currentData;
    }

    /**
     * 滑动回调，用于提供给外部改变图片透明度
     */
    public interface ScrollListener {

        /**
         * 当前发生了翻页，需要重置图片
         *
         * @param leftImage    左边的图片
         * @param currentImage 当前中间的图片
         * @param rightImage   右边的图片
         * @param position     当前的位置
         */
        public void onPageSelected(@NotNull String leftImage, @NotNull String currentImage, @NotNull String rightImage, @NotNull int position);

        /**
         * 滑动监听，用于改变透明度
         *
         * @param leftAlpha    左边控件的透明度
         * @param currentAlpha 当前控件的透明度
         * @param rightAlpha   右边控件的透明度
         */
        public void onScrolled(@NotNull float leftAlpha, @NotNull float currentAlpha, @NotNull float rightAlpha);
    }
}

