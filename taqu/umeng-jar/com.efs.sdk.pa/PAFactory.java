package com.efs.sdk.pa;

import android.content.Context;
import android.util.Log;
import com.efs.sdk.base.EfsReporter;
import com.efs.sdk.pa.config.ConfigManager;
import com.efs.sdk.pa.config.IEfsReporter;
import com.efs.sdk.pa.config.PackageLevel;
import java.util.HashMap;

/* loaded from: pasdk-2.1.159.umeng.jar:com/efs/sdk/pa/PAFactory.class */
public class PAFactory {
    static final ThreadLocal<PA> sThreadLocal = new ThreadLocal<>();
    private static final long DEFAULT_TIME_OUT_TIME = 2000;
    private static final long MAX_TIME_OUT_TIME = 4000;
    private static final long INVALID_TIME_OUT_TIME = 0;
    private static final String TAG = "pafactory";
    private IEfsReporter mReporterFactory;
    private EfsReporter mReporter;
    private ConfigManager mConfigManager;
    private String mSerial;
    private String mSver;
    private HashMap<String, String> mExtend;
    private Context mContext;
    private PATraceListener mTraceListener;
    private IPaClient mPaClient;
    private long mTimeOutTime;

    public synchronized PA getPaInstance() {
        PA pa = sThreadLocal.get();
        PA pa2 = pa;
        if (pa == null) {
            com.efs.sdk.pa.a.c cVar = new com.efs.sdk.pa.a.c(this.mConfigManager.enableTracer());
            pa2 = cVar;
            cVar.registerPAANRListener(this.mContext, new a(this), this.mTimeOutTime);
            sThreadLocal.set(pa2);
        }
        return pa2;
    }

    private PAFactory(Context context, PackageLevel packageLevel, IEfsReporter iEfsReporter, boolean z, String str, HashMap<String, String> map, String str2, long j, PATraceListener pATraceListener, IPaClient iPaClient) {
        this.mReporterFactory = iEfsReporter;
        this.mSerial = str;
        this.mExtend = map;
        this.mSver = str2;
        this.mContext = context;
        this.mTraceListener = pATraceListener;
        this.mPaClient = iPaClient;
        this.mTimeOutTime = j;
        this.mConfigManager = new ConfigManager(context, packageLevel, iEfsReporter, z);
    }

    public IPaClient getPaClient() {
        return this.mPaClient;
    }

    public PATraceListener getTraceListener() {
        return this.mTraceListener;
    }

    public ConfigManager getConfigManager() {
        return this.mConfigManager;
    }

    public String getSerial() {
        return this.mSerial;
    }

    public String getSver() {
        return this.mSver;
    }

    public HashMap<String, String> getExtend() {
        return this.mExtend;
    }

    public EfsReporter getReporter() {
        if (this.mReporter == null) {
            this.mReporter = this.mReporterFactory != null ? this.mReporterFactory.getReporter() : null;
        }
        return this.mReporter;
    }

    public Context getContext() {
        return this.mContext;
    }

    /* loaded from: pasdk-2.1.159.umeng.jar:com/efs/sdk/pa/PAFactory$Builder.class */
    public static class Builder {
        private PackageLevel a;
        private IEfsReporter b;
        private boolean c;
        private Context d;
        private String e;
        private HashMap<String, String> f;
        private String g;
        private long h = PAFactory.DEFAULT_TIME_OUT_TIME;
        private PATraceListener i;
        private IPaClient j;

        public Builder(Context context, IEfsReporter iEfsReporter) {
            if (context == null) {
                throw new RuntimeException("context Should Not null");
            }
            if (iEfsReporter == null) {
                throw new RuntimeException("reporter Should Not Empty");
            }
            this.b = iEfsReporter;
            this.d = context;
        }

        public Builder packageLevel(PackageLevel packageLevel) {
            this.a = packageLevel;
            return this;
        }

        public Builder isNewInstall(boolean z) {
            this.c = z;
            return this;
        }

        public Builder serial(String str) {
            this.e = str;
            return this;
        }

        public Builder extendLogInfo(HashMap<String, String> map) {
            this.f = map;
            return this;
        }

        public Builder sver(String str) {
            this.g = str;
            return this;
        }

        public Builder traceListener(PATraceListener pATraceListener) {
            this.i = pATraceListener;
            return this;
        }

        public Builder setPaClient(IPaClient iPaClient) {
            this.j = iPaClient;
            return this;
        }

        public Builder timeoutTime(long j) {
            if (j <= PAFactory.INVALID_TIME_OUT_TIME) {
                Log.w(PAFactory.TAG, "Timeout time is invalid, and the default value 2s will be used");
                this.h = PAFactory.DEFAULT_TIME_OUT_TIME;
            } else {
                if (j > PAFactory.MAX_TIME_OUT_TIME) {
                    Log.w(PAFactory.TAG, "Timeout time over 4s is not recommended, and the default value 2s will be used");
                    this.h = PAFactory.DEFAULT_TIME_OUT_TIME;
                    return this;
                }
                this.h = j;
            }
            return this;
        }

        public PAFactory build() {
            if (this.a != null) {
                return new PAFactory(this.d, this.a, this.b, this.c, this.e, this.f, this.g, this.h, this.i, this.j);
            }
            throw new RuntimeException(String.format("%s Should Not Null", ""));
        }
    }
}