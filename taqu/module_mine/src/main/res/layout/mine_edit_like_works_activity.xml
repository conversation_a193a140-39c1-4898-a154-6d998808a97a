<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/g7"
    tools:context=".activity.MineEditLikeWorksActivity">

    <View
        android:id="@+id/viewAddBg"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/mine_personal_add_like_label_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvAddTitle"
        style="@style/Text.T5_2.G1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/viewAddBg"
        app:layout_constraintStart_toStartOf="@+id/viewAddBg"
        app:layout_constraintTop_toTopOf="@+id/viewAddBg"
        tools:text="添加音乐" />

    <TextView
        style="@style/Text.T6.G1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:drawableEnd="@drawable/mine_personal_add_like_arrow_ic"
        android:gravity="center"
        android:text="添加"
        app:layout_constraintBottom_toBottomOf="@+id/viewAddBg"
        app:layout_constraintEnd_toEndOf="@+id/viewAddBg"
        app:layout_constraintTop_toTopOf="@+id/viewAddBg" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvSelectedList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:background="@color/white"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        app:layout_constraintBottom_toTopOf="@+id/btnTop"
        app:layout_constraintTop_toBottomOf="@+id/viewAddBg"
        app:layout_goneMarginBottom="0dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rvSelectedList" />

    <Button
        android:id="@+id/btnTop"
        style="@style/Btn1.Large"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginEnd="16dp"
        android:text="置顶"
        app:layout_constraintBottom_toBottomOf="@+id/btnDelete"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btnDelete" />

    <Button
        android:id="@+id/btnDelete"
        style="@style/Btn3.Large"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="20dp"
        android:text="删除"
        android:textColor="@color/g1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btnTop"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupBottomBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="btnDelete,btnTop" />
</androidx.constraintlayout.widget.ConstraintLayout>