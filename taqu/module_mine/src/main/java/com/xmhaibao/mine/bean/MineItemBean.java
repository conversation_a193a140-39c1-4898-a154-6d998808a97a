package com.xmhaibao.mine.bean;

import com.google.gson.annotations.SerializedName;
import com.xmhaibao.mine.api.bean.TransferIncomeBean;

import java.util.ArrayList;
import java.util.List;

import cn.taqu.lib.base.utils.appdiff.AppDifferentiationUtils;
import cn.taqu.lib.okhttp.model.IDoExtra;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import hb.common.helper.HostHelper;
import hb.utils.CollectionUtils;
import hb.utils.StringUtils;
import hb.utils.WebpUtils;

/**
 * 配配我的页面模块item数组
 *
 * <AUTHOR>
 * @date 2021-03-27
 */
public class MineItemBean implements IDoExtra {

    public static final String TYPE_LOOK_ME = "look_me";
    /**
     * 贵族
     */
    public static final String TYPE_VIP = "coin_shop";
    /**
     * 每日任务
     */
    public static final String TYPE_TASK = "daily_task";

    /**
     * 设置
     */
    public static final String TYPE_SETTING = "setting";

    /**
     * 奖励
     */
    public static final String TYPE_REWARD = "receive_award";

    /**
     * 充值优惠券
     */
    public static final String TYPE_RECHARGE_COUPON = "inpour_coupon";

    /**
     * 小秘密
     */
    public static final String TYPE_SECRET = "secret";

    @SerializedName("top_fix_list")
    private List<IconListBean> topFixList;

    @SerializedName("modules_list")
    private List<List<IconListBean>> modulesList;
    private List<Object> itemList = new ArrayList<>();

    /**
     * 是否有谁看过我item
     */
    private boolean hasLookMeItem;
    /**
     * 是否有每日任务
     */
    private boolean hasDailyTaskItem;
    /**
     * 是否有小秘密
     */
    private boolean hasSecretItem;


    public boolean isHasDailyTaskItem() {
        return hasDailyTaskItem;
    }

    public void setHasDailyTaskItem(boolean hasDailyTaskItem) {
        this.hasDailyTaskItem = hasDailyTaskItem;
    }

    public boolean isHasSecretItem() {
        return hasSecretItem;
    }

    public boolean isHasLookMeItem() {
        return hasLookMeItem;
    }

    public void setHasLookMeItem(boolean hasLookMeItem) {
        this.hasLookMeItem = hasLookMeItem;
    }

    public List<Object> getItemList() {
        return itemList;
    }

    public List<IconListBean> getTopFixList() {
        return topFixList;
    }

    public void setTopFixList(List<IconListBean> topFixList) {
        this.topFixList = topFixList;
    }

    public List<List<IconListBean>> getModulesList() {
        return modulesList;
    }

    public void setModulesList(List<List<IconListBean>> modulesList) {
        this.modulesList = modulesList;
    }

    @Override
    public void doExtra(IResponseInfo response) {
        if (CollectionUtils.isNotEmpty(topFixList)) {
            for (IconListBean bean : topFixList) {
                bean.setIconImg(WebpUtils.getWebpUrl_4_1(bean.getIconImg(), HostHelper.getInstance().getHostImg()));
                if (StringUtils.isNotEmpty(bean.getDarkIconImg())) {
                    bean.setDarkIconImg(HostHelper.getImageDefaultHost().getWebpUrl_4_1(bean.getDarkIconImg()));
                }
                bean.setSubIconImg(WebpUtils.getWebpUrl_4_1(bean.getSubIconImg(), HostHelper.getInstance().getHostImg()));
                //守护图片集合
                if (CollectionUtils.isNotEmpty(bean.getRankImgList())) {
                    for (int i = 0; i < bean.getRankImgList().size(); i++) {
                        final String avatar = WebpUtils.getWebpUrl_4_1(bean.getRankImgList().get(i), HostHelper.getInstance().getHostAvatar());
                        bean.getRankImgList().set(i, avatar);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(modulesList)) {
            boolean hasLookMeItem = false;
            for (int i = 0; i < modulesList.size(); i++) {
                List<IconListBean> iconList = modulesList.get(i);
                for (int j = 0; j < iconList.size(); j++) {
                    IconListBean bean = iconList.get(j);
                    bean.setIconImg(WebpUtils.getWebpUrl_4_1(bean.getIconImg(), HostHelper.getInstance().getHostImg()));
                    if (StringUtils.isNotEmpty(bean.getDarkIconImg())) {
                        bean.setDarkIconImg(HostHelper.getImageDefaultHost().getWebpUrl_4_1(bean.getDarkIconImg()));
                    }
                    if (j == 0) {
                        bean.setItemMode(iconList.size() == 1 ? IconListBean.MODE_ALL : IconListBean.MODE_UP);
                    } else if (j == iconList.size() - 1) {
                        bean.setItemMode(IconListBean.MODE_DOWN);
                    } else {
                        bean.setItemMode(IconListBean.MODE_MIDDLE);
                    }
                    if (bean.getIconType().equals(TYPE_LOOK_ME)) {
                        hasLookMeItem = true;
                    }else if (bean.getIconType().equals(TYPE_TASK)) {
                        hasDailyTaskItem = true;
                    } else if ("m=live&a=noble".equals(bean.getRelaction())) {
                        if (AppDifferentiationUtils.isLoveAndMarriage()) {
                            bean.iconName = "会员中心";
                            bean.relactionTitle = "";
                        }
                    } else if (bean.getIconType().equals(TYPE_SECRET)) {
                        hasSecretItem = true;
                    }
                    itemList.add(bean);
                }
            }
            this.hasLookMeItem = hasLookMeItem;
        }
    }

    public static class IconListBean {
        public static final int MODE_UP = 1;
        public static final int MODE_DOWN = 2;
        public static final int MODE_ALL = 4;
        public static final int MODE_MIDDLE = 0;

        /**
         *提现小气泡-不可提现
         */
        public transient static final String TYPE_BUBBLES_NONE = "0";

        /**
         * 提现小气泡-可提现
         */
        public transient static final String TYPE_BUBBLES_CAN_CASH_OUT = "1";

        /**
         * 提现小气泡-即将可提现
         */
        public transient static final String TYPE_BUBBLES_SOON_CASH_OUT = "2";


        /**
         * 守护
         */
        public transient static  final String TYPE_PROGUARD = "guard";

        /**
         * 家族
         */
        public transient static  final String TYPE_FAMILY = "family";

        /**
         * 收益
         */
        public transient static  final String TYPE_INTEGRAL = "integral";

        /**
         * 服务中心
         */
        public transient static  final String TYPE_SERVICE_CENTER = "serviceCenter";

        /**
         * 趣币充值
         */
        public transient static  final String TYPE_RECHARGE = "amount";

        /**
         * 他趣 VIP
         */
        public transient static  final String TYPE_MEMBERSHIP = "vip";


        /**
         * icon_img : index/71bc1ccd895613297b3b1cf154c6e3a3.png
         * icon_name : 任务
         * icon_type : live_open
         * relaction : m=live&a=open
         */

        @SerializedName("icon_img")
        private String iconImg;
        /**
         * 深色模式图标
         */
        @SerializedName("deep_color_icon_img")
        private String darkIconImg;
        @SerializedName("icon_name")
        private String iconName;
        @SerializedName("icon_type")
        private String iconType;
        private String relaction;
        @SerializedName("relaction_title")
        private String relactionTitle;
        /**
         * itemView的模式，上中下三种，用来处理圆角问题和分割线问题
         */
        private int itemMode;

        /**
         * 每日任务滚动数组
         */
        @SerializedName("scrollow_contents")
        private String[] tasks = new String[]{};

        /**
         * 副标题
         */
        @SerializedName("sub_icon_name")
        private String subIconName;

        /**
         * 图片
         */
        @SerializedName("sub_icon_img")
        private String subIconImg;

        /**
         * 守护图片地址列表
         */
        @SerializedName("rank_img_list")
        private ArrayList<String> rankImgList;

        /**
         * 总收益
         */
        @SerializedName("total_integral")
        private String totalIntegral;

        /**
         * 是否为公会，公会不展示收益 1:是 0:不是
         */
        @SerializedName("is_labour_union")
        private String isLabourUnion = "0";

        /**
         * 气泡文案 0 不展示  1可提现  2即将可提现
         */
        @SerializedName("show_pop_text")
        private String showPopText;

        /**
         * 我的蓝票lottie动画地址
         */
        @SerializedName("lottie_url")
        private String blueTicketLottieUrl;

        /**
         * 充值优惠菜单是否显示红点
         */
        @SerializedName("is_show_red_dot")
        private String isShowRedDot;

        //region VIP
        /**
         * 是否是 VIP
         */
        @SerializedName("is_vip")
        private String isMembership;

        /**
         * 副标题列表
         */
        @SerializedName("subtitle_list")
        private List<String> subtitleList;
        /**
         * 会员类型
         */
        @SerializedName("event_tracking_val")
        private String abCodeType;
        //endregion

        /**
         * 充值优惠菜单是否显示红点
         */
        public boolean isRechargeDiscountShowRedDot() {
            return StringUtils.equalsIgnoreCase(isShowRedDot, "1");
        }

        public void setRechargeDiscountShowRedDot(boolean isShow) {
            isShowRedDot = isShow ? "1" : "0";
        }

        public String getBlueTicketLottieUrl() {
            return blueTicketLottieUrl;
        }

        public void setBlueTicketLottieUrl(String blueTicketLottieUrl) {
            this.blueTicketLottieUrl = blueTicketLottieUrl;
        }

        public String getShowPopText() {
            return showPopText;
        }

        public void setShowPopText(String showPopText) {
            this.showPopText = showPopText;
        }

        public int getItemMode() {
            return itemMode;
        }

        public void setItemMode(int itemMode) {
            this.itemMode = itemMode;
        }

        public String getIconImg() {
            return iconImg;
        }

        public void setIconImg(String iconImg) {
            this.iconImg = iconImg;
        }

        public String getIconName() {
            return iconName;
        }

        public void setIconName(String iconName) {
            this.iconName = iconName;
        }

        public String getIconType() {
            return iconType;
        }

        public void setIconType(String iconType) {
            this.iconType = iconType;
        }

        public String getRelaction() {
            return relaction;
        }

        public void setRelaction(String relaction) {
            this.relaction = relaction;
        }

        public String getRelactionTitle() {
            return relactionTitle;
        }

        public void setRelactionTitle(String relactionTitle) {
            this.relactionTitle = relactionTitle;
        }

        public String getSubIconName() {
            return subIconName;
        }

        public void setSubIconName(String subIconName) {
            this.subIconName = subIconName;
        }

        public void setSubIconImg(String subIconImg) {
            this.subIconImg = subIconImg;
        }

        public String getSubIconImg() {
            return subIconImg;
        }

        public void setRankImgList(ArrayList<String> rankImgList) {
            this.rankImgList = rankImgList;
        }

        public ArrayList<String> getRankImgList() {
            return rankImgList;
        }

        public void setTotalIntegral(String totalIntegral) {
            this.totalIntegral = totalIntegral;
        }

        public String getTotalIntegral() {
            return totalIntegral;
        }

        public String[] getTasks() {
            return tasks;
        }

        public String getIsLabourUnion() {
            return isLabourUnion;
        }

        public void setIsLabourUnion(String isLabourUnion) {
            this.isLabourUnion = isLabourUnion;
        }

        public String getDarkIconImg() {
            return darkIconImg;
        }

        public void setDarkIconImg(String darkIconImg) {
            this.darkIconImg = darkIconImg;
        }

        /**
         * 【收益转积分】2023-06-01
         */
        @SerializedName("transfer_income_score")
        private TransferIncomeBean transferIncomeScore;

        public TransferIncomeBean getTransferIncomeScore() {
            return transferIncomeScore;
        }

        public void setTransferIncomeScore(TransferIncomeBean transferIncomeScore) {
            this.transferIncomeScore = transferIncomeScore;
        }


        public String getIsMembership() {
            return isMembership;
        }

        public void setIsMembership(String isMembership) {
            this.isMembership = isMembership;
        }

        public List<String> getSubtitleList() {
            return subtitleList;
        }

        public void setSubtitleList(List<String> subtitleList) {
            this.subtitleList = subtitleList;
        }


        public String getAbCodeType() {
            return abCodeType;
        }

        public void setAbCodeType(String abCodeType) {
            this.abCodeType = abCodeType;
        }
    }
}
