package com.xmhaibao.mine.dialog

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import com.xmhaibao.core.utils.UiHandler
import com.xmhaibao.mine.activity.MineActivity
import com.xmhaibao.mine.databinding.MineRealNameSuccessDialogBinding
import hb.utils.ActivityUtils
import hb.utils.SizeUtils
import hb.xstyle.xdialog.XLifecycleDialog

/**
 * description: 实名认证成功dialog
 *
 * <AUTHOR>
 * @date 2021/06/01 23:03
 */
class MineRealNameSuccessDialog(context: Context) : XLifecycleDialog(context) {


    private lateinit var binding: MineRealNameSuccessDialogBinding


    companion object {
        fun show() {
            UiHandler.postDelayed({
                var activity = ActivityUtils.getTopActivity()
                if (activity != null) {
                    MineRealNameSuccessDialog(activity).show()
                }
            },1500)


        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = MineRealNameSuccessDialogBinding.inflate(LayoutInflater.from(context))
        setContentView(binding.root)
        binding.root.layoutParams.width = SizeUtils.dp2px(288f)
        binding.root.layoutParams.height = SizeUtils.dp2px(306f)
        binding.tvOk.setOnClickListener { dismiss() }
    }


}