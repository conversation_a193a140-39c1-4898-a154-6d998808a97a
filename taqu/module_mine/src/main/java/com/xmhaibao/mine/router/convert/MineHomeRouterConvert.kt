package com.xmhaibao.mine.router.convert

import android.content.Context
import android.os.Bundle
import cn.taqu.lib.base.constants.NavigationTabs
import cn.taqu.lib.base.router.JustRouterConvert
import cn.taqu.lib.base.router.RouterLaunchOther
import cn.taqu.lib.base.utils.appdiff.AppDifferentiationUtils
import com.alibaba.android.arouter.facade.annotation.Route
import com.xmhaibao.mine.api.router.MinePinsRouter
import com.xmhaibao.mine.api.router.MineRouterPath

/**
 * 回到首页路由转换
 */
@Route(
    path = MineRouterPath.ROUTER_GROUP.plus(cn.taqu.lib.base.constants.PushConstants.PUSH_TYPE_ACTION_HOME),
    name = "回到首页"
)
class MineHomeRouterConvert : JustRouterConvert() {

    override fun navigation(args: Bundle?, context: Context?) {
        if (AppDifferentiationUtils.isRectification()) {
            MinePinsRouter.launchMineActivity()
        } else {
            RouterLaunchOther.launchNavigationActivity(NavigationTabs.TAB_TAG_MINE)
        }
    }
}