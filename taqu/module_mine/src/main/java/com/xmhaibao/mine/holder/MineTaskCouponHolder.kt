package com.xmhaibao.mine.holder

import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import cn.taqu.lib.base.router.RouterLaunch
import com.xmhaibao.mine.R
import com.xmhaibao.mine.bean.MineTaskCouponItemBean
import com.xmhaibao.mine.databinding.MineTaskCouponItemBinding
import com.xmhaibao.mine.tracker.MineTaskCenterTracker
import com.xmhaibao.mine.viewModel.MineTaskCenterViewModel
import hb.utils.ColorUtils
import hb.utils.Loger
import hb.utils.SizeUtils
import hb.xadapter.XBaseViewHolder
import hb.xstatic.mvvm.XViewModelProviders

/**
 * 我的-任务中心-优惠券任务holder
 *
 * <AUTHOR>
 * @date 2023-12-22
 */
class MineTaskCouponHolder(parent: ViewGroup?) :
    XBaseViewHolder<MineTaskCouponItemBean>(parent, R.layout.mine_task_coupon_item) {

    companion object{
        const val AUTH_TASK_KEY = "auth_task"
    }

    /**
     * binding
     */
    private val binding by lazy {
        MineTaskCouponItemBinding.bind(itemView).apply {
            tvCouponTaskSubTitle.setOnClickListener {
                RouterLaunch.dealJumpData(itemView.context, viewModel.mTaskBean.value?.coupon?.subTitleRelation)
            }
        }
    }
    private val mColorYellow600 = ColorUtils.getColor(cn.taqu.lib.base.R.color.TH_Yellow600)
    private val mColorGray990 = ColorUtils.getColor(cn.taqu.lib.base.R.color.TH_Gray990)
    private val mColorGray001 = ColorUtils.getColor(cn.taqu.lib.base.R.color.TH_Gray001)

    /**
     * 任务信息
     */
    private var itemBean: MineTaskCouponItemBean? = null

    /**
     * viewModel
     */
    private val viewModel by lazy {
        XViewModelProviders.getViewModel(
            itemView.context as LifecycleOwner,
            MineTaskCenterViewModel::class.java
        )
    }

    fun bindClickListener(l: View.OnClickListener) {
        binding.tvStatus.setOnClickListener(l)
    }

    override fun onBindView(item: MineTaskCouponItemBean?) {
        item?.run {
            itemBean = this
            binding.tvStatus.tag = item
            binding.ivIcon.setImageFromUrl(taskIcon)
            binding.tvName.text = taskName
            binding.tvRewardName.text = rewardName
            binding.tvStatus.shaper().solid(
                when {
                    isNotComplete -> mColorYellow600
                    isCompleteNoGet -> mColorGray990
                    else -> mColorYellow600
                }
            )
            binding.tvStatus.text = if (isNotComplete) "去完成" else "待领取"
            binding.tvStatus.setTextColor(
                when {
                    isNotComplete -> mColorGray990
                    isCompleteNoGet -> mColorGray001
                    else -> mColorYellow600
                }
            )

            //控制item背景圆角
            if (titleName?.isNotEmpty() == true) {
                binding.clItem.shaper().corners(SizeUtils.dp2px(8f).toFloat(), SizeUtils.dp2px(8f).toFloat(), 0f, 0f)
                binding.groupTopCorner.visibility = View.VISIBLE
                binding.tvMineTaskTitle.text = item.titleName
                binding.placeTopDivider.visibility = View.VISIBLE
                binding.tvCouponTaskSubTitle.visibility = View.VISIBLE
                binding.tvCouponTaskSubTitle.text = viewModel.mTaskBean.value?.coupon?.subTitle.orEmpty()
                binding.tvMineTaskTitle.text = viewModel.mTaskBean.value?.coupon?.title.orEmpty()
            } else {
                if (this.isLastItem) {
                    binding.clItem.shaper().corners(0f, 0f, SizeUtils.dp2px(8f).toFloat(), SizeUtils.dp2px(8f).toFloat())
                } else {
                    binding.clItem.shaper().corners(0f, 0f, 0f, 0f)
                }
                binding.groupTopCorner.visibility = View.GONE
                binding.tvMineTaskTitle.text = ""
                binding.placeTopDivider.visibility = View.GONE
                binding.tvCouponTaskSubTitle.visibility = View.GONE
                binding.tvCouponTaskSubTitle.text = ""
                binding.tvMineTaskTitle.text = ""
            }
        }
    }

    override fun onViewAttachedToWindow() {
        super.onViewAttachedToWindow()
        if (itemBean?.taskId?.isEmpty() == true) {
            //真人认证任务曝光埋点，"去认证"状态才去上报
            if (itemBean?.taskStatus == "0" && !viewModel.trackerExpList.contains(AUTH_TASK_KEY)) {
                viewModel.trackerExpList.add(AUTH_TASK_KEY)
                MineTaskCenterTracker.Instance.taskCenterTaskExp(itemBean?.taskId ?: "",viewModel.sourceValue.value)
            }
        } else {
            //其他任务曝光埋点
            if (!viewModel.trackerExpList.contains(itemBean?.taskId)) {
                viewModel.trackerExpList.add(itemBean?.taskId)
                MineTaskCenterTracker.Instance.taskCenterTaskExp(itemBean?.taskId ?: "",viewModel.sourceValue.value)
            }
        }
    }
}