package com.xmhaibao.mine.view

import android.animation.ObjectAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import cn.taqu.lib.base.model.VoiceSignInfo
import com.airbnb.lottie.LottieDrawable.*
import com.xmhaibao.mine.R
import com.xmhaibao.mine.constants.MineLottieAliasNames
import com.xmhaibao.mine.databinding.MinePersonalInfoVoiceSignViewBinding
import com.xmhaibao.urd.URDConstans
import com.xmhaibao.urd.URDManager
import com.xmhaibao.imganim.lottie.XLottiePlayer
import hb.audioplay.AudioPlayManager
import hb.audioplay.bean.AudioErrorInfo
import hb.audioplay.core.XAudioPlayStrategy
import hb.audioplay.exoplay.ExoAudioPlay
import hb.audioplay.inter.SimpleAudioPlayListener
import hb.utils.StringUtils


/**
 * 个人资料语音录制
 *
 * <AUTHOR>
 * @date 2020-11-06
 */
class MinePersonalInfoVoiceRecordView(context: Context, attrs: AttributeSet) :
    FrameLayout(context, attrs) {


    /**
     * 语音签名信息
     */
    private var mVoiceSignInfo: VoiceSignInfo? = null

    /**
     * 语音播放类
     */
    private var mAudioPlayStrategy: XAudioPlayStrategy? = null


    /**
     * 回调
     */
    private var mCallback: VoiceRecordCallback? = null

    /**
     * 是否正在播放
     */
    private var mIsPlaying: Boolean = false

    /**
     * 是否已经通过url播放
     */
    private var mIsSetVoiceUrl: Boolean = false

    /**
     * Lottie播放动画
     */
    private var mPlayLottieAnim: ObjectAnimator? = null

    /**
     * 关闭Lottie动画
     */
    private var mStopLottieAnim: ObjectAnimator? = null

    private val mBinding: MinePersonalInfoVoiceSignViewBinding by lazy {
        MinePersonalInfoVoiceSignViewBinding.inflate(LayoutInflater.from(context), this, true)
    }
    init {
        initView()
    }

    /**
     * 初始化播放控件
     */
    private fun initView() {
        mBinding.lottieVoice?.frame = 0
        mBinding.lottieVoice?.alpha = 0f
        mBinding.lottieVoice?.repeatCount = INFINITE
        mBinding.lottieVoice?.repeatMode = RESTART
        mAudioPlayStrategy = AudioPlayManager.getXAudioPlay(context, ExoAudioPlay())
        mAudioPlayStrategy?.setAudioPlayListener(object : SimpleAudioPlayListener() {
            override fun onAudioPlayError(errorInfo: AudioErrorInfo) {
                super.onAudioPlayError(errorInfo)
                mBinding.lottieVoice?.cancelAnimation()
                mBinding.lottieVoice?.frame = 0
            }

            override fun onAudioPlayComplete() {
                super.onAudioPlayComplete()
                reset()
            }

            override fun onAudioPlayProgress(currentTime: Long, totalTime: Long) {
                super.onAudioPlayProgress(currentTime, totalTime)
                mBinding.tvVoiceTime.text = "${((totalTime - currentTime) / 1000.0).toInt()}”"
            }
        })

        /**
         * 播放录音
         */
        mBinding.ivVoiceRecord.setOnClickListener {
            startPlay()
        }
    }

    /**
     * 初始化语音数据
     * @param voiceSignInfo 语音签名信息
     */
    fun initData(voiceSignInfo: VoiceSignInfo, callBack: VoiceRecordCallback) {
        this.mVoiceSignInfo = voiceSignInfo
        this.mCallback = callBack
        mBinding.ivVoiceRecord.setImageResource(R.drawable.mine_personal_info_voice_disable_ic)
        if (StringUtils.isEmpty(voiceSignInfo.voiceSignUrl)) {
           mBinding.tvVoiceTime.text = "0”"
           mBinding.ivEmptyBg.visibility = VISIBLE
           mBinding.groupVoice.visibility = GONE
        } else {

           mBinding.tvVoiceTime.text = "${voiceSignInfo.voiceDuration}”"
           mBinding.ivEmptyBg.visibility = GONE
           mBinding.groupVoice.visibility = VISIBLE
        }
        reset()
        //lottie不存在，下载资源
        if (!isSignLottieExist()) {
            // 使用别名获取资源
            val bean =
                URDManager.getInstance().getLottieByAliasName(
                    URDConstans.TYPE_LOTTIE,
                    MineLottieAliasNames.MINE_VOICE_SIGN_PLAY
                )
            URDManager.getInstance().loadRes(bean, null, null)
        }
    }

    /**
     * 签名lottie资源是否存在
     */
    private fun isSignLottieExist(): Boolean {
        return XLottiePlayer.checkResourceByAliasName(MineLottieAliasNames.MINE_VOICE_SIGN_PLAY)
    }

    /**
     * 点击播放按钮，当前正在播放，点击暂停
     */
    private fun startPlay() {

        when {
            mIsPlaying -> {
                stopPlay()
                return
            }
            mIsSetVoiceUrl -> {
                mAudioPlayStrategy?.rePlay()
                mBinding.ivVoiceRecord.setImageResource(R.drawable.mine_personal_info_void_suspend_ic)
                mIsPlaying = true
                playLottieAnim()
            }
            else -> {
                this.mVoiceSignInfo?.run {
                    if (StringUtils.isEmpty(voiceSignUrl) || voiceDuration == 0) {
                        //跳转到录制语音界面
                        startVoicePage()
                    } else {
                        mIsPlaying = true
                        mIsSetVoiceUrl = true
                        mBinding.ivVoiceRecord.setImageResource(R.drawable.mine_personal_info_void_suspend_ic)
                        mAudioPlayStrategy?.playUrl(voiceSignUrl)
                        playLottieAnim()
                    }
                }
            }
        }
    }

    /**
     * 播放Lottie动画
     */
    private fun playLottieAnim() {
        if (mPlayLottieAnim == null) {
            mPlayLottieAnim = ObjectAnimator.ofFloat(mBinding.lottieVoice, View.ALPHA, 0F, 1F)
            mPlayLottieAnim?.duration = 200
        }
        mPlayLottieAnim?.start()
        //资源存在，播放动效
        if (isSignLottieExist()) {
            XLottiePlayer.playByAlias(mBinding.lottieVoice, MineLottieAliasNames.MINE_VOICE_SIGN_PLAY, 0)
        }
    }

    /**
     * 关闭Lottie动画
     */
    private fun cancelLottieAnim() {
        if (mStopLottieAnim == null) {
            mStopLottieAnim = ObjectAnimator.ofFloat(mBinding.lottieVoice, View.ALPHA, 1F, 0F)
            mStopLottieAnim?.duration = 200
        }
        mStopLottieAnim?.start()
        mBinding.lottieVoice?.cancelAnimation()
    }

    /**
     * 停止播放
     */
    fun stopPlay(){
        if(mIsPlaying){
            mBinding.ivVoiceRecord.setImageResource(R.drawable.mine_personal_info_voice_disable_ic)
            mAudioPlayStrategy?.pause()
            mIsPlaying = false
            cancelLottieAnim()
        }
    }

    /**
     * 重置语音
     */
    private fun reset() {
        mBinding.ivVoiceRecord.setImageResource(R.drawable.mine_personal_info_voice_disable_ic)
        mAudioPlayStrategy?.pause()
        mVoiceSignInfo?.run {
            mBinding.lottieVoice?.cancelAnimation()
            mBinding.lottieVoice?.frame = 0
            mBinding.tvVoiceTime.text = "${this.voiceDuration}”"
        }
        mIsPlaying = false
        mIsSetVoiceUrl = false
    }


    /**
     * 跳转到语音界面
     */
    private fun startVoicePage() {
        mCallback?.run {
            voiceRecordClick()
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        destroy()
    }

    /**
     * 销毁
     */
    fun destroy() {
        mAudioPlayStrategy?.release()
        mBinding.lottieVoice?.cancelAnimation()
    }

    /**
     * 录音回调
     */
    interface VoiceRecordCallback {

        /**
         * 前去录音
         */
        fun voiceRecordClick()
    }

}