package com.xmhaibao.newfemalelove.dialog

import android.animation.Animator
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import cn.taqu.lib.base.utils.eventlog.AppTrackEventUtils
import com.xmhaibao.mine.R
import com.xmhaibao.mine.databinding.MineLoveCostBindWeChatDialogBinding
import com.xmhaibao.newfemalelove.bean.MineLoveCostBindSuccess
import com.xmhaibao.newfemalelove.bean.MineLoveCostPayment
import com.xmhaibao.newfemalelove.bean.MineLoveCostWeChatUnbind
import com.xmhaibao.newfemalelove.bean.MineWxWithdrawBean
import com.xmhaibao.mine.api.newfemalelove.tracker.MineLoveCostTracker
import hb.drawable.shape.extend.shape
import hb.utils.ColorUtils
import hb.utils.SizeUtils
import hb.utils.StringUtils
import hb.xstatic.mvvm.XBaseViewAction
import hb.xtoast.XToastUtils
import kale.sharelogin.LoginListener
import kale.sharelogin.OAuthUserInfo
import kale.sharelogin.ShareLoginLib
import kale.sharelogin.weixin.WeiXinPlatform
import kotlinx.coroutines.flow.collectLatest

/**
 * Description:  恋爱金绑定微信及打款中弹窗 弹窗
 * <AUTHOR>
 * @date 2022-12-19
 */

class MineLoveCostBindWeChatDialog : MineLoveCostBaseDialog<Any>() {

    companion object {
        private const val KEY_COST = "keyCost"

        /**
         *  伴生对象构建对象
         */
        fun newInstance(cost: MineWxWithdrawBean): MineLoveCostBindWeChatDialog {
            return MineLoveCostBindWeChatDialog().apply {
                arguments = Bundle().apply {
                    //这里传参
                    putParcelable(KEY_COST, cost)
                }
                //配置弹窗属性
                setConfig(
                    //居中显示
                    ConfigStyle.CENTER.config()
                        //可以取消
                        .setCanceledOnTouchOutside(false)
                        .setCancelable(false)
                        //背景蒙层透明度
                        .setDimAmount(0.9f)
                        //自定义左右Margin 
                        .setLeftAndRightMargin(76)

                )
            }
        }
    }

    private var wxWithdrawBean: MineWxWithdrawBean? = null


    //页面ViewBinding
    private lateinit var binding: MineLoveCostBindWeChatDialogBinding


    override fun onCreateContentView(): Any {
        binding = MineLoveCostBindWeChatDialogBinding.inflate(layoutInflater)
        return binding.root
    }

    /**
     *	初始化配置
     *	比initViews先执行
     */
    override fun initConfig(argumentBundle: Bundle) {
        //基础参数解析
        initArgs()
    }

    /**
     *	状态栏等UI的初始化操作
     */
    override fun initViews(rootView: View?) {
        //点击事件
        registerViewClick()
        binding.tvHighest.shape(
            bottomLeftRadius = SizeUtils.dp2px(2f) * 1f,
            topLeftRadius = SizeUtils.dp2px(11f) * 1f,
            topRightRadius = SizeUtils.dp2px(11f) * 1f,
            bottomRightRadius = SizeUtils.dp2px(11f) * 1f,
            solidColor = ColorUtils.getColor("#FFE8C7")
        )
        binding.tvPayment.shape(
            radius = SizeUtils.dp2px(9f) * 1f,
            solidColor = ColorUtils.getColor("#40C575")
        )
        binding.lottieAnimationView.addAnimatorListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(animation: Animator) {
                //ignore
            }

            override fun onAnimationEnd(animation: Animator) {
                //结束
                positiveDismiss()//主动结束 调用
            }

            override fun onAnimationCancel(animation: Animator) {
                //ignore

            }

            override fun onAnimationRepeat(animation: Animator) {
                //ignore

            }
        })
    }

    /**
     *	一些基础数据的初始化
     */
    override fun initData(savedInstanceState: Bundle?) {
        //初始化数据

        viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            viewModel.bindWechatStateFlow.collectLatest {
                binding.lottieAnimationView.isVisible = it is MineLoveCostPayment
                binding.groupAccount.isVisible = it !is MineLoveCostPayment

                when (it) {
                    is MineLoveCostBindSuccess -> {
                        binding.lottieAnimationView.pauseAnimation()
                        binding.tvBindAccount.setTextColor(ColorUtils.getColor(hb.xstyle.R.color.black))
                        binding.tvBindAccount.text = it.accountName
                        binding.tvPayment.text = "立即打款"
                    }
                    is MineLoveCostPayment -> {
                        binding.lottieAnimationView.playAnimation()
                    }
                    is MineLoveCostWeChatUnbind -> {
                        binding.lottieAnimationView.pauseAnimation()
                        binding.tvPayment.text = "绑定账号并打款"
                        binding.tvBindAccount.text = "待绑定"
                        binding.tvBindAccount.setTextColor(ColorUtils.getColor("#FF2056"))

                    }
                }
            }
        }
        viewModel.mActionLiveData.observe(viewLifecycleOwner){
            binding.loadingBar.isVisible = StringUtils.equalsIgnoreCase(it.state,XBaseViewAction.SHOW_LOADING)
        }
    }

    /**
     *  初始Bundle传参
     */
    private fun initArgs() {
        wxWithdrawBean = requireArguments().getParcelable(KEY_COST) ?: return
        wxWithdrawBean?.let {
            binding.tvCost.text = "¥".plus(it.amount)
            viewModel.updateWechatBindStatus(it.isBindWechat)
            binding.tvCost.isInvisible = it.isRandomAmount
            binding.tvRandom.isVisible = it.isRandomAmount
            binding.tvHighest.isInvisible = it.isRandomAmount
        }
    }

    override fun onStart() {
        super.onStart()
    }

    /**
     *  点击事件监听
     */
    private fun registerViewClick() {
        binding.tvThinkAgain.setOnClickListener {
            dismiss()
        }
        binding.tvPayment.setOnClickListener {
            MineLoveCostTracker.Instance.trackNewFemaleLoveCostBindAccountAndPaymentClick()
            bindWechat()
        }
    }

    private fun bindWechat() {
        activity ?: return
        val isWeChatInstalled = ShareLoginLib.isAppInstalled(
            activity,
            WeiXinPlatform::class.java
        )
        if (isWeChatInstalled.not()) {
            XToastUtils.show("请先安装微信")
            return
        }
        ShareLoginLib.doLogin(requireActivity(), WeiXinPlatform.LOGIN, object : LoginListener() {
            override fun onReceiveToken(
                accessToken: String, uId: String, expiresIn: Long,
                wholeData: String?
            ) {
                super.onReceiveToken(accessToken, uId, expiresIn, wholeData)
                // 注意：这里的uId是code
                viewModel.bindWechat(uId)
            }

            override fun onReceiveUserInfo(userInfo: OAuthUserInfo) {
                super.onReceiveUserInfo(userInfo)
            }

            override fun onError(errorMsg: String) {
                super.onError(errorMsg)
                XToastUtils.show(if (TextUtils.isEmpty(errorMsg)) "获取信息失败" else errorMsg)
            }

            override fun onComplete() {
                //ignore
            }
        })

    }

    fun setDismissCallback(runnable: Runnable){
        dismissBlock = {
            runnable.run()
        }
    }

}