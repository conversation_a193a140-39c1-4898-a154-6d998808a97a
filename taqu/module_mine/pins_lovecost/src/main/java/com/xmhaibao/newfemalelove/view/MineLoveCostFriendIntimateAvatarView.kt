package com.xmhaibao.newfemalelove.view

import android.content.Context
import android.util.AttributeSet
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.core.view.updatePaddingRelative
import com.xmhaibao.mine.R
import com.xmhaibao.newfemalelove.bean.MineLoveCostIntimateFriendsBean
import dp
import hb.common.helper.HostHelper
import hb.kotlin_extension.gone
import hb.kotlin_extension.invisible
import hb.kotlin_extension.noneSyncLazy
import hb.kotlin_extension.visible
import hb.utils.ColorUtils
import hb.utils.StringUtils
import hb.utils.WebpUtils
import hb.ximage.fresco.AvatarDraweeView

/**
 * @desc:新女日报朋友亲密度头像控件
 *
 * <AUTHOR>
 * @date 2023/3/13
 */
class MineLoveCostFriendIntimateAvatarView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {


    private val avatarFirst: AvatarDraweeView by noneSyncLazy {
        AvatarDraweeView(context).apply {
            layoutParams = LayoutParams(
                16.dp,
                16.dp
            )
            hierarchy.roundingParams?.let {
                it.borderColor = ColorUtils.getColor(hb.xstyle.R.color.white)
                it.borderWidth = 1.dp * 1f
            }
        }
    }

    private val avatarSecond: AvatarDraweeView by noneSyncLazy {
        AvatarDraweeView(context).apply {
            layoutParams = LayoutParams(
                16.dp,
                16.dp
            ).apply {
                marginStart = -(8.dp)
            }
            hierarchy.roundingParams?.let {
                it.borderColor = ColorUtils.getColor(hb.xstyle.R.color.white)
                it.borderWidth = 1.dp * 1f
            }
        }
    }

    private val avatarThree: AvatarDraweeView by noneSyncLazy {
        AvatarDraweeView(context).apply {
            layoutParams = LayoutParams(
                16.dp,
                16.dp
            ).apply {
                marginStart = -(8.dp)
            }
            hierarchy.roundingParams?.let {
                it.borderColor = ColorUtils.getColor(hb.xstyle.R.color.white)
                it.borderWidth = 1.dp * 1f
            }
        }
    }

    private val text: TextView by noneSyncLazy {
        TextView(context).apply {
            textSize = 10f
            setTextColor(ColorUtils.getColor(hb.xstyle.R.color.TH_Gray600))
            updatePaddingRelative(start = 2.dp)
        }
    }

    private val defaultIcon: ImageView by noneSyncLazy {
        ImageView(context).apply {
            layoutParams = LayoutParams(
                16.dp,
                16.dp
            )
            setImageResource(R.drawable.mine_love_cost_income_default_avatar_ic)
        }
    }

    init {
        orientation = HORIZONTAL
        addView(defaultIcon)
        addView(avatarFirst)
        addView(avatarSecond)
        addView(avatarThree)
        addView(text)
    }

    fun setData(bean: MineLoveCostIntimateFriendsBean?) {
        if (bean == null) {
            invisible()
            return
        }
        visible()
        text.text = bean.showText ?: "暂无"

        val firstAvatarUrl = bean.avatarList?.getOrNull(0)
        val secondAvatarUrl = bean.avatarList?.getOrNull(1)
        val threeAvatarUrl = bean.avatarList?.getOrNull(2)

        if (StringUtils.isNotEmpty(firstAvatarUrl)) {
            avatarFirst.setImageFromUrl(
                HostHelper.getAvatarHost()
                    .getWebpUrlWithSize(firstAvatarUrl, WebpUtils.VIEW_SIZE_120)
            )
            avatarFirst.visible()
        } else {
            avatarFirst.gone()
        }

        if (StringUtils.isNotEmpty(secondAvatarUrl)) {
            avatarSecond.setImageFromUrl(
                HostHelper.getAvatarHost()
                    .getWebpUrlWithSize(secondAvatarUrl, WebpUtils.VIEW_SIZE_120)
            )
            avatarSecond.visible()
        } else {
            avatarSecond.gone()
        }

        if (StringUtils.isNotEmpty(threeAvatarUrl)) {
            avatarThree.setImageFromUrl(
                HostHelper.getAvatarHost()
                    .getWebpUrlWithSize(threeAvatarUrl, WebpUtils.VIEW_SIZE_120)
            )
            avatarThree.visible()
        } else {
            avatarThree.gone()
        }

        defaultIcon.isVisible = bean.avatarList.isNullOrEmpty()
    }

}