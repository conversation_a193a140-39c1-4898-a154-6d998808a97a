package com.xmhaibao.newfemalelove.vm

import android.app.Application
import androidx.lifecycle.viewModelScope
import cn.taqu.lib.base.bean.UiState
import com.xmhaibao.account.api.constants.AccountPinsConstants
import com.xmhaibao.newfemalelove.MineNewFemaleLoveCostRepository
import com.xmhaibao.newfemalelove.bean.*
import hb.common.xstatic.viewmodel.BaseViewModel
import hb.xrequest.launchHttp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext

/**
 * @desc:新女恋爱金绑定微信弹窗的vm
 *
 * <AUTHOR>
 * @date 2022/12/19
 */
class MineLoveCostViewModel(application: Application) : BaseViewModel(application) {

    private val repository by lazy {
        MineNewFemaleLoveCostRepository()
    }

    private val bindWechatMutableStateFlow: MutableStateFlow<MineLoveCostWeChatStatusBean> =
        MutableStateFlow(
            MineLoveCostWeChatUnbind()
        )

    val bindWechatStateFlow: StateFlow<MineLoveCostWeChatStatusBean> = bindWechatMutableStateFlow


    private val newFemaleLoveCostMutableStateFlow: MutableStateFlow<UiState<MineNewFemaleLoveCostBean>> =
        MutableStateFlow(UiState.Empty)

    val newFemaleStateFlow: StateFlow<UiState<MineNewFemaleLoveCostBean>> =
        newFemaleLoveCostMutableStateFlow


    fun updateWechatBindStatus(isBindWechat: Boolean) {
        if (isBindWechat) {
            bindWechatMutableStateFlow.value = MineLoveCostPayment()
        } else {
            bindWechatMutableStateFlow.value = MineLoveCostWeChatUnbind()
        }
    }


    fun bindWechat(uid: String) {
        showLoadingBar()
        viewModelScope.launchHttp({
            repository.bindThirdLogin(uid, AccountPinsConstants.THIRD_ACCOUNT_TYPE_WEIXIN, null)
            hideLoadingBar()
            bindWechatMutableStateFlow.value = MineLoveCostPayment()
        }, {
            hideLoadingBar()
            false
        })
    }


    fun awardTask(
        taskKey: String,
        target: String,
        taskType: String
    ): StateFlow<UiState<MineLoveCostAwardTaskBean>> {
        showLoadingBar()
        val flow = MutableStateFlow<UiState<MineLoveCostAwardTaskBean>>(UiState.Empty)
        viewModelScope.launchHttp({
            val data = repository.requestAwardTask(taskKey, target, taskType)
            if (data != null) {
                flow.value = UiState.Success(data)
            }
            hideLoadingBar()
        }, {
            hideLoadingBar()
            false
        })
        return flow
    }


    fun requestLoveCostTask() {
        showLoadingBar()
        viewModelScope.launchHttp({
            val data = withContext(Dispatchers.IO) {
                val challenge = repository.getLoveCostChallengeTask(this)
                val eventDay = repository.getLoveCostEveryDayTask(this)
                val challengeTaskBean = challenge.await()
                val eventDayTaskBean = eventDay.await()
                if (challengeTaskBean == null || eventDayTaskBean == null){
                    return@withContext null
                }
                return@withContext MineNewFemaleLoveCostBean(challengeTaskBean, eventDayTaskBean)
            }
            if (data != null){
                newFemaleLoveCostMutableStateFlow.value = UiState.Success(data)
            }
            hideLoadingBar()
        },{
            hideLoadingBar()
            false
        })
    }

    fun updateLoveCostBean(bean:MineNewFemaleLoveCostBean){
        newFemaleLoveCostMutableStateFlow.value = UiState.Success(bean)
    }


}