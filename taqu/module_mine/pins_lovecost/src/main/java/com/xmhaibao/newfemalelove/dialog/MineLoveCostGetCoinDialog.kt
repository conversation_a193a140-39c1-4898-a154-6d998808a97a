package com.xmhaibao.newfemalelove.dialog

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.graphics.PointF
import android.os.Bundle
import android.view.View
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import androidx.core.animation.doOnEnd
import com.xmhaibao.base.widget.BezierListener
import com.xmhaibao.base.widget.SecondBezierEvaluator
import com.xmhaibao.mine.R
import com.xmhaibao.mine.databinding.MineLoveCostGetCoinDialogBinding
import com.xmhaibao.mine.api.newfemalelove.tracker.MineLoveCostTracker
import hb.drawable.shape.extend.shape
import hb.utils.*
import kotlin.math.roundToInt

/**
 * Description:  新女恋爱金获得金币的弹窗 弹窗
 * <AUTHOR>
 * @date 2022-12-19
 */

class MineLoveCostGetCoinDialog : MineLoveCostBaseDialog<Any>() {

    companion object {
        const val KEY_COST = "keyCost"

        private const val KEY_TASK_NAME = "keyTaskName"
        private const val KEY_COLLECTION_TYPE = "keyCollectionType"
        /**
         * 来源任务弹窗
         */
        const val KEY_FROM_TASK_DIALOG = "keyFromTaskDialog"

        /**
         *  伴生对象构建对象
         */
        fun newInstance(cost: String, isFromTaskDialog: Boolean,taskName:String,collectionType:String): MineLoveCostGetCoinDialog {
            return MineLoveCostGetCoinDialog().apply {
                arguments = Bundle().apply {
                    //这里传参
                    putString(KEY_COST, cost)
                    putBoolean(KEY_FROM_TASK_DIALOG, isFromTaskDialog)
                    putString(KEY_TASK_NAME, taskName)
                    putString(KEY_COLLECTION_TYPE, collectionType)
                }
                if (isFromTaskDialog){
                    setConfig(
                        //居中显示
                        ConfigStyle.BOTTOM.config()
                            //可以取消
                            .setCanceledOnTouchOutside(false)
                            .setCancelable(false)
                            //背景蒙层透明度
                            .setDimAmount(0f)
                            //自定义左右Margin
                            .setLeftAndRightMargin(0)
                            .setHeight(((ScreenUtils.getScreenHeight() * 0.82f).roundToInt()))

                    )
                }else{
                    setConfig(
                        //居中显示
                        ConfigStyle.FULL.config()
                            //可以取消
                            .setCanceledOnTouchOutside(false)
                            .setCancelable(false)
                            //背景蒙层透明度
                            .setDimAmount(0f)
                            //自定义左右Margin
                            .setLeftAndRightMargin(0)

                    )
                }
            }
        }
    }


    //页面ViewBinding
    private lateinit var mBinding: MineLoveCostGetCoinDialogBinding


    private var isFromTaskDialog: Boolean = false

    override fun onCreateContentView(): Any {
        mBinding = MineLoveCostGetCoinDialogBinding.inflate(layoutInflater)
        return mBinding.root
    }

    /**
     *	初始化配置
     *	比initViews先执行
     */
    override fun initConfig(argumentBundle: Bundle) {
        //基础参数解析
    }

    private var animationEndX = 0
    private var animationEndY = 0

    /**
     *	状态栏等UI的初始化操作
     */
    override fun initViews(rootView: View?) {

        mBinding.viewBg.shape(
            radius = SizeUtils.dp2px(16f) * 1f,
            solidColor = ColorUtils.getColor(hb.xstyle.R.color.black_alpha_80)
        )

    }

    /**
     *	一些基础数据的初始化
     */
    @SuppressLint("SetTextI18n")
    override fun initData(savedInstanceState: Bundle?) {
        //初始化数据
        val cost = requireArguments().getString(KEY_COST)
        mBinding.tvCost.text = "+${cost}元"
        isFromTaskDialog = requireArguments().getBoolean(KEY_FROM_TASK_DIALOG, false)


        val taskName = requireArguments().getString(KEY_TASK_NAME,"")
        val collectionType = requireArguments().getString(KEY_COLLECTION_TYPE,"")
        if (StringUtils.isNotEmpty(taskName) && StringUtils.isNotEmpty(collectionType)){
            MineLoveCostTracker.Instance.trackNewFemaleLoveCostPaymentAnimExposure(collectionType,taskName)
        }

        mBinding.root.post {
            val control = PointF()
            if (isFromTaskDialog) {
                //任务弹窗的XY点
                animationEndX = SizeUtils.dp2px(16f)
                animationEndY = SizeUtils.dp2px(159f)
//                animationEndY = (ScreenUtils.getScreenHeight() * 0.18f + SizeUtils.dp2px(150f)).roundToInt()
                control.x = mBinding.viewBg.left + (mBinding.viewBg.width / 4f)
                control.y = mBinding.viewBg.top * 1f - SizeUtils.dp2px(20f)
            } else {
                //他趣福利官的XY点
                animationEndX = ScreenUtils.getScreenWidth() - SizeUtils.dp2px(96f)
                animationEndY = ScreenUtils.getScreenHeight() - SizeUtils.dp2px(121f)
                control.x = mBinding.viewBg.right * 1f
                control.y = mBinding.viewBg.top + (mBinding.viewBg.height / 6f)
            }
            startAnimationToTarget(mBinding.ivAnimation4, control, 0)
            startAnimationToTarget(mBinding.ivAnimation3, control, 100)
            startAnimationToTarget(mBinding.ivAnimation2, control, 200)
            val lastAnimation = startAnimationToTarget(mBinding.ivAnimation1, control, 300)
            lastAnimation.doOnEnd {
                positiveDismiss()
            }
        }

    }


    private fun startAnimationToTarget(
        view: View, control: PointF, startDelay: Long
    ): Animator {
        val scaleX = ObjectAnimator.ofFloat(view, View.SCALE_X, 1f, 0f)
        val scaleY = ObjectAnimator.ofFloat(view, View.SCALE_Y, 1f, 0f)
        val startX = mBinding.ivAnimation1.x
        val startY = mBinding.ivAnimation1.y
        val endX = animationEndX * 1f
        val endY = animationEndY * 1f
        val startPointF = PointF(startX, startY)
        val endPointF = PointF(endX, endY)
        val translation = getAnimator(
            view, SecondBezierEvaluator(control), startPointF, endPointF
        )

        val animation = AnimatorSet().apply {
            duration = 500
            if (isFromTaskDialog) {
                val rotateAnimation = ObjectAnimator.ofFloat(view, View.ROTATION, 0f, -30f)
                playTogether(scaleX, scaleY, translation, rotateAnimation)
            } else {
                playTogether(scaleX, scaleY, translation)
            }
            this.startDelay = startDelay
            interpolator = AccelerateInterpolator()
        }
        animation.start()
        return animation
    }

    private fun getAnimator(
        view: View, evaluator: SecondBezierEvaluator, startPointF: PointF, endPointF: PointF
    ): ValueAnimator {
        val animator = ValueAnimator.ofObject(evaluator, startPointF, endPointF)
        animator.setTarget(view)
        animator.addUpdateListener(BezierListener(view))
        animator.interpolator = DecelerateInterpolator()
        return animator
    }


}