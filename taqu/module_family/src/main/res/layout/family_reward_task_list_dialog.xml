<?xml version="1.0" encoding="utf-8"?>
<hb.drawable.shape.view.HbConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:corner_top_left="16dp"
    app:solid="@color/white"
    app:corner_top_right="16dp">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scaleType="fitXY"
        android:src="@drawable/xdialog_cover_bg"
        app:layout_constraintTop_toTopOf="parent" />
    
    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="24dp"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="14dp"
        android:src="@drawable/xdialog_close_ic_normal"
        android:layout_height="24dp"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:text="任务列表"
        style="@style/Text.H3.B100"
        android:textStyle="bold"
        android:layout_marginTop="11dp"
        android:gravity="center_horizontal"
        />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvTaskList"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@id/ivClose"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="0dp"/>
    
    


</hb.drawable.shape.view.HbConstraintLayout>