<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <!--  背景图  -->
    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivBg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:placeholderImage="@color/transparent"
        app:roundedCornerRadius="18dp" />


    <!--  个人活跃 or 家族活跃艺术字标题  -->
    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivTitle"
        android:layout_width="wrap_content"
        android:layout_height="16dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="12dp"
        app:layout_constraintStart_toStartOf="@id/ivBg"
        app:layout_constraintTop_toTopOf="@id/ivBg"
        app:placeholderImage="@color/transparent" />

    <!--  渐变色文字-活跃值  -->
    <TextView
        android:id="@+id/tvFamilyActiveNum"
        android:layout_width="0dp"
        android:layout_height="20dp"
        android:layout_marginStart="4dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:textStyle="bold"
        android:textColor="#210D9A"
        android:textSize="@dimen/TH_FONT_B2"
        android:layout_marginTop="9.5dp"
        app:layout_constraintEnd_toStartOf="@id/tvFamilyActiveTips"
        app:layout_constraintStart_toEndOf="@+id/ivTitle"
        app:layout_constraintTop_toTopOf="@id/ivBg"
        tools:text="999999999" />


    <TextView
        android:id="@+id/tvFamilyActiveTips"
        style="@style/Text.N3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:textColor="@color/black_alpha_55"
        app:layout_constraintBottom_toBottomOf="@id/ivTitle"
        app:layout_constraintEnd_toEndOf="@id/ivBg"
        app:layout_constraintTop_toTopOf="@id/ivTitle"
        tools:text="家族活跃值" />

    <ViewStub
        android:id="@+id/viewStubTop"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:inflatedId="@+id/viewStubTop"
        app:layout_constraintEnd_toEndOf="@id/ivBg"
        app:layout_constraintStart_toStartOf="@id/ivBg"
        app:layout_constraintTop_toBottomOf="@id/tvFamilyActiveNum"
        />

    <com.xmhaibao.family.widget.progress.FamilyTaskProgress
        android:id="@+id/taskProgress"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginTop="17dp"
        app:layout_constraintEnd_toEndOf="@id/ivBg"
        app:layout_constraintStart_toStartOf="@id/ivBg"
        app:layout_constraintTop_toBottomOf="@id/viewStubTop" />


    <ViewStub
        android:id="@+id/viewStub"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@id/ivBg"
        app:layout_constraintStart_toStartOf="@id/ivBg"
        app:layout_constraintTop_toBottomOf="@id/taskProgress" />

</merge>