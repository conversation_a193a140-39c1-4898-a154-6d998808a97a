<?xml version="1.0" encoding="utf-8"?>
<hb.drawable.shape.view.HbConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:corner="16dp"
    app:solid="@color/TH_Navy002">

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivNotice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:placeholderImage="@drawable/message_family_notice_ic"
        app:viewAspectRatio="3.75" />

    <TextView
        android:id="@+id/tvNoticeTitle"
        style="@style/Text.H2.G990"
        android:layout_marginTop="9dp"
        android:text="家族公告"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivNotice" />

    <TextView
        android:id="@+id/tvNoticeContent"
        style="@style/Text.B2.G600"
        android:layout_width="match_parent"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="20dp"
        android:gravity="center_horizontal"
        android:lineSpacingExtra="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvNoticeTitle"
        tools:text="告家族公告家族公告家族公告家族公告家族公告家族公告家族公告家族公告家族公告家族公告" />

    <View
        android:id="@+id/viewDivider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="16dp"
        android:background="@color/TH_Gray150"
        app:layout_constraintTop_toBottomOf="@+id/tvNoticeContent" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvClose"
        style="@style/Text.B1.G990"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:gravity="center"
        android:text="关闭"
        android:textStyle="bold"
        app:corner="16dp"
        app:layout_constraintTop_toBottomOf="@+id/viewDivider"
        app:solid="@color/TH_Navy002" />


</hb.drawable.shape.view.HbConstraintLayout>