package com.xmhaihao.message.dialog;

import android.content.Context;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;

import com.ushengsheng.widget.BaseBottomDialog;
import com.xmhaibao.family.R;
import com.xmhaibao.family.databinding.MessageFamilyGiftDividedDialogBinding;

/**
 * @author：杨浩 项目：taqu
 * 创建日期：2020/7/16
 * 功能简介：家族礼物分成 dialog
 */
public class MessageFamilyGiftDividedDialog extends BaseBottomDialog {
    /**
     * 礼物分成内容
     */
    @NonNull
    private String mContent;

    /**
     * 礼物分成标题
     */
    @NonNull
    private String mTitle;

    private MessageFamilyGiftDividedDialogBinding mViewBinding;

    /**
     * 礼物分成弹窗
     *
     * @param context
     * @param title
     * @param content
     */
    public MessageFamilyGiftDividedDialog(@NonNull Context context, @NonNull String title, @NonNull String content) {
        super(context);
        mContent = content;
        mTitle = title;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.message_family_gift_divided_dialog;
    }

    @Override
    public void setContentView(int layoutResID) {
        mViewBinding = MessageFamilyGiftDividedDialogBinding.inflate(getLayoutInflater());
        super.setContentView(mViewBinding.getRoot());
    }

    private void initView() {
        mViewBinding.mTvFamilyGiftDividedTitle.setText(mTitle);
        mViewBinding.mTvFamilyGiftDividedContent.setText(mContent);
        mViewBinding.mBtnFamilyGiftDividedConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }
}
    