package com.xmhaihao.message.family

import android.view.ViewStub
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.xmhaibao.family.R
import com.xmhaibao.family.databinding.FamilyChatDanmuBinding
import com.xmhaihao.message.holder.Family520ActivityDanMuViewHolder
import hb.danmu.DanMaLayout
import hb.danmu.base.AbsDanMuBean

/**
 * 家族 弹幕辅助
 * viewStub 绑定 [R.layout.family_chat_danmu]
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
class FamilyChatDanMuHelper(
    private val vs: ViewStub,
    private val lifecycleOwner: LifecycleOwner,
    private val danMuListener: DanMaLayout.OnDanMaKuListener
) {

    private val mBinding: FamilyChatDanmuBinding by lazy {
        FamilyChatDanmuBinding.bind(vs.inflate())
    }


    /**
     * 添加520飘屏弹幕
     */
    fun add520ActivityDanMu(family520ActivityDanMuEvent: AbsDanMuBean) {
        playDanMu(family520ActivityDanMuEvent)
    }

    /**
     * 弹幕注册初始化
     */
    private fun initDanMu() {
        mBinding.danmuLayout.setOnDanMaKuListener(danMuListener)
        mBinding.danmuLayout.registerItem(Family520ActivityDanMuViewHolder::class.java, R.layout.family_lover_activity_danmu)
        lifecycleOwner.lifecycle.addObserver(object : DefaultLifecycleObserver{
            override fun onDestroy(owner: LifecycleOwner) {
                super.onDestroy(owner)
                mBinding.danmuLayout.destroy()
            }
        })
    }

    /**
     * 播放弹幕
     */
    private fun playDanMu(family520ActivityDanMuEvent: AbsDanMuBean) {
        if (!isInit()) {
            initDanMu()
        }
        mBinding.danmuLayout.addDanMu(family520ActivityDanMuEvent)
    }

    /**
     * 是否初始化过
     */
    private fun isInit(): Boolean {
        return vs.parent == null
    }


}