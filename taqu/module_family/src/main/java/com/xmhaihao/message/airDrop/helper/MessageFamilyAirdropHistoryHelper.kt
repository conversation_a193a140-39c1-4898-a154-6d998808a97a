package com.xmhaihao.message.airDrop.helper

import android.util.ArrayMap
import com.xmhaibao.message.api.constants.ImTypeConstants
import com.xmhaibao.xjson.XJson
import com.xmhaihao.message.airDrop.bean.MessageFamilyAirDropMsgBean
import com.xmhaihao.message.utils.MessageChatLog
import com.xmhaihao.message.utils.SpObjectUtils
import hb.common.data.AccountHelper
import hb.message.db.entity.HBMessageContentDO
import hb.utils.*
import hb.utils.kvcache.KVCacheUtils
import hb.xthread.XThreadPool
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.collections.ArrayList

/**
 * 空投状态
 *
 * <AUTHOR>
 * @date 2022-06-29
 */
class MessageFamilyAirdropHistoryHelper {

    companion object {

        private const val TAG = "MessageFamilyAirdropHistoryHelper"
        private const val RED_PACKET_INFO_SP = "ADSP"
        private const val MAX_RED_PACKET_INFO = 100

        /**
         * 记录所有的空投状态
         */
        val airDropStatusList = ArrayMap<String, Int>(64)

        fun updateAirDropStatus(airDropId: String?, status: Int) {
            airDropId?.run {
                airDropStatusList.put(airDropId, status)
            }
        }

        /**
         * 将红包领取记录写入本地
         *
         * @param redPacketList
         */
        fun saveLocalRedPacketOpenStatus(redPacketList: CopyOnWriteArrayList<MessageFamilyAirDropMsgBean?>?) {
            if (redPacketList == null) {
                return
            }
            //jdk7，CopyOnWriteArrayList无法sort 所以中转一下
            val tempList = mutableListOf<MessageFamilyAirDropMsgBean?>()
            tempList.addAll(redPacketList)
            kotlin.runCatching {
                //先去重
                distinctAndJustNeedOpen(tempList)
                //排序
                tempList.sortWith { o1, o2 ->
                    (o2?.createTime ?: 0).toInt() - (o1?.createTime ?: 0).toInt()
                }

                // 将list超过max的元素丢弃掉
                if (tempList.size > MAX_RED_PACKET_INFO) {
                    tempList.subList(MAX_RED_PACKET_INFO, tempList.size).clear()
                }
            }.onFailure {
                MessageChatLog.reportRedPacketOrAirdropSortError("2", tempList.size, it.message ?: "")
            }

            XThreadPool.IO().execute {
                val copyOnWriteArrayList = CopyOnWriteArrayList(tempList.filterNotNull())
                if (copyOnWriteArrayList.isNullOrEmpty()) {
                    KVCacheUtils.putString(getRedPacketSavePath(), null)
                } else {
                    val toJson = XJson.toJson(copyOnWriteArrayList)
                    KVCacheUtils.putString(getRedPacketSavePath(), toJson)
                }
            }
        }

        /**
         * 取出本地sp的红包领取记录
         *
         * @return
         */
        fun getLocalRedPacketOpenStatus(): CopyOnWriteArrayList<MessageFamilyAirDropMsgBean?>? {
            val string = KVCacheUtils.getString(getRedPacketSavePath(), "[]")
            val newList =
                XJson.fromJson<ArrayList<MessageFamilyAirDropMsgBean?>>(
                    string,
                    XJson.getListType(MessageFamilyAirDropMsgBean::class.java)
                )
            //新旧版兼容，先查看新版本内容，无则查看旧版本内容
            if (CollectionUtils.isEmpty(newList)) {
                val oldList =
                    SpObjectUtils.getObjFromSp<CopyOnWriteArrayList<MessageFamilyAirDropMsgBean?>>(
                        AppUtils.getApp(),
                        getRedPacketSavePath()
                    )
                return if (CollectionUtils.isEmpty(oldList)) {
                    CopyOnWriteArrayList()
                } else {
                    saveLocalRedPacketOpenStatus(oldList)
                    oldList
                }
            }
            //更新空投状态
            newList?.forEach {
                it?.run {
                    updateAirDropStatus(this.airDropId, this.receiveStatus)
                }
            }

            return CopyOnWriteArrayList(newList)
        }


        /**
         * 去重且只要有状态的红包
         *
         * @param list
         */
        private fun distinctAndJustNeedOpen(list: MutableList<MessageFamilyAirDropMsgBean?>) {
            val temp: List<MessageFamilyAirDropMsgBean?> = ArrayList(list)
            val newList: MutableList<MessageFamilyAirDropMsgBean> = ArrayList()
            for (i in temp.indices) {
                val element = temp[i]
                if (element == null || element.isUnKnow()) {
                    continue
                }
                if (!newList.contains(element)) {
                    newList.add(element)
                }
            }
            list.clear()
            list.addAll(newList)
        }


        /**
         * 调用时机：历史记录拉取后拦截
         *
         *
         * 查询列表 有则更新 无则加入
         * (ps：更新：将本地保存的开关状态，更新给即将用于列表展示的数据)
         *
         * @param messageContentList 请求回来的正在被拦截的消息记录
         * @param localRedPacketList 本地中保存的红包list
         */
        fun handleHistoryIntercept(
            messageContentList: List<HBMessageContentDO?>,
            localRedPacketList: CopyOnWriteArrayList<MessageFamilyAirDropMsgBean>,
        ) {
            Loger.d(TAG, "handleHistoryIntercept:start")
            distinctAndJustNeedOpen(localRedPacketList)
            findRedPacketMsgFromAll(messageContentList, object : OnFindRedPacketCallback {
                override fun findMessageChatFamilyRedPacketBean(
                    DO: HBMessageContentDO,
                    airDropBean: MessageFamilyAirDropMsgBean
                ) {
                    airDropBean.createTime = DO.createTime
                    for (bean in localRedPacketList) {
                        if (bean == null) {
                            continue
                        }
                        if (airDropBean.airDropId == bean.airDropId) {
                            //如果本地有同一个红包,用本地的状态去更新
                            airDropBean.receiveStatus = bean.receiveStatus
                            updateAirDropStatus(airDropBean.airDropId, airDropBean.receiveStatus)
                            return
                        }
                    }
                    //没有找到就加入本地
                    addLocalPacketListIfNotFind(airDropBean, localRedPacketList)
                }
            })
            Loger.d(TAG, "handleHistoryIntercept:end")
        }

        /**
         * 调用时机：列表每刷新一个消息(不管是手动调用updateMessageContent,还是新推过来的数据)
         *
         *
         * 查询列表 有则更新 无则加入
         * (ps：更新：将红包的开关状态，更新至本地存储)
         *
         * @param DO                 刷新的消息
         * @param localRedPacketList 会将红包消息插入localRedPacketList
         */
        fun updateLocalRedPacket(
            DO: HBMessageContentDO?,
            localRedPacketList: CopyOnWriteArrayList<MessageFamilyAirDropMsgBean>,
        ) {
            if (DO == null) {
                return
            }
            //红包
            if (ImTypeConstants.MESSAGE_AIRDROP == DO.contentType
            ) {
                val hbMessageContentBean =
                    DO.getHBMessageContentBeanDO(MessageFamilyAirDropMsgBean::class.java) ?: return
                hbMessageContentBean.createTime = DO.createTime
                for (localBean in localRedPacketList) {
                    if (localBean != null && hbMessageContentBean.airDropId == localBean.airDropId) {
                        //如果找得到相同红包id的
                        localBean.receiveStatus = hbMessageContentBean.receiveStatus
                        return
                    }
                }
                addLocalPacketListIfNotFind(hbMessageContentBean, localRedPacketList)
            }
        }

        /**
         * 如果本地没有这个数据，要加入本地
         *
         * @param hbMessageContentBean
         * @param localRedPacketList
         */
        private fun addLocalPacketListIfNotFind(
            hbMessageContentBean: MessageFamilyAirDropMsgBean,
            localRedPacketList: MutableList<MessageFamilyAirDropMsgBean>,
        ) {
            try {
                // 这个地方如果直接拿hbMessageContentBean，会导致因为，内存永远是同一个，diff界面永远不会刷新
                val clone = hbMessageContentBean.clone()
                localRedPacketList.add(clone)
                updateAirDropStatus(clone.airDropId, clone.receiveStatus)
            } catch (e: CloneNotSupportedException) {
                e.printStackTrace()
            }
        }


        /**
         * @param all                     从all里查红包p消息
         * @param onFindRedPacketCallback
         */
        fun findRedPacketMsgFromAll(
            all: List<HBMessageContentDO?>,
            onFindRedPacketCallback: OnFindRedPacketCallback?
        ) {
            for (DO in all) {
                if (DO == null) {
                    continue
                }
                //红包
                if (ImTypeConstants.MESSAGE_AIRDROP == DO.contentType
                ) {
                    val hbMessageContentBean =
                        DO.getHBMessageContentBeanDO(MessageFamilyAirDropMsgBean::class.java)
                            ?: continue
                    onFindRedPacketCallback?.findMessageChatFamilyRedPacketBean(
                        DO,
                        hbMessageContentBean
                    )
                    continue
                }
            }
        }


        private fun getRedPacketSavePath(): String {
            val path = StringBuilder()
            path.append(RED_PACKET_INFO_SP + AccountHelper.getAccountUuid())
            path.append(":newVersion")
            return path.toString()
        }


    }

    /**
     * 红包消息回调
     */
    interface OnFindRedPacketCallback {
        /**
         * 查找红包消息
         *
         * @param airDropBean
         */
        fun findMessageChatFamilyRedPacketBean(
            DO: HBMessageContentDO,
            airDropBean: MessageFamilyAirDropMsgBean
        )
    }
}