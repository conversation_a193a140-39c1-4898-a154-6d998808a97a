package com.xmhaihao.message.familyLevel

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.os.CountDownTimer
import android.view.*
import com.xmhaibao.family.bean.MessageFamilyLevelNotifyBean
import com.xmhaibao.family.databinding.MessageFamilyLevelNotifyDialogBinding
import hb.actionqueue.annotation.XActionQueue
import hb.xstyle.xdialog.XLifecycleDialog

/**
 * 【家族等级特权体系】https://o15vj1m4ie.feishu.cn/wiki/QAuVwMM5WiLSA0kxU8Hc1mCinI0
 * 【figma】https://www.figma.com/file/hiCums4LunEX46gkYOTfX5/%E7%A4%BE%E4%BA%A4%E8%BF%AD%E4%BB%A3(2023%E5%B9%B4)?node-id=101704-53763&t=ywmA8hjepavuFWYu-0
 *
 * 家族等级权益 等级变化通知弹窗
 *
 * <AUTHOR>
 * @since 2023-4-18
 * */
@XActionQueue(type = XActionQueue.TYPE_DIALOG , priority = 10)
class MessageFamilyLevelNotifyDialog(context: Context): XLifecycleDialog(context) {

    companion object {
        private const val DISMISS_COUNT_DOWN = 300_000L
        private const val SECOND_UNIT = 1_000L


    }

    private val binding by lazy {
        MessageFamilyLevelNotifyDialogBinding.inflate(layoutInflater)
    }

    private var data : MessageFamilyLevelNotifyBean? = null

    /**
     * 关闭弹窗倒计时
     */
    private var mDismissTimer: CountDownTimer? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        window?.setGravity(Gravity.TOP or Gravity.CENTER_HORIZONTAL)
        window?.setWindowAnimations(cn.taqu.lib.base.R.style.AnimationPopupFromTop)
        window?.decorView?.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        window?.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window?.statusBarColor = Color.TRANSPARENT
        window?.attributes?.apply {
            // Dialog 背景不变暗
            dimAmount = 0F
            width = WindowManager.LayoutParams.MATCH_PARENT
            height = WindowManager.LayoutParams.MATCH_PARENT
        }
        window?.navigationBarColor = Color.TRANSPARENT

        initViews()
    }

    private fun initViews() {
        binding.root.setOnClickListener {
            dismiss()
        }
        binding.ivBackground.setOnClickListener(null)

        data?.let {
            if(it.beans.isNotEmpty()) {
                binding.privilegeView.updateView(it.beans, 182F, 12F)
            }
            if (it.subTitleColor.isNotBlank()) {
                binding.tvSubTitle.setTextColor(Color.parseColor(it.subTitleColor))
            }
            binding.ivBackground.setImageFromUrl(it.backgroundUrl)
            binding.ivTitle.setImageFromUrl(it.titleUrl)
            binding.tvSubTitle.text = it.subTitle
            binding.ivLevelBadge.setImageFromUrl(it.badgeUrl)
        }
    }

    fun updateView(input: MessageFamilyLevelNotifyBean) {
        data = input
        if(input.beans.isNotEmpty()) {
            binding.privilegeView.updateView(input.beans, 182F, 12F)
        }
        if (input.subTitleColor.isNotBlank()) {
            binding.tvSubTitle.setTextColor(Color.parseColor(input.subTitleColor))
        }
        binding.ivBackground.setImageFromUrl(input.backgroundUrl)
        binding.ivTitle.setImageFromUrl(input.titleUrl)
        binding.tvSubTitle.text = input.subTitle
        initTasks()
    }

    private fun initTasks() {
        mDismissTimer?.cancel()
        // 弹窗展示3秒后dismiss
        mDismissTimer = object: CountDownTimer(DISMISS_COUNT_DOWN, SECOND_UNIT) {
            override fun onTick(millis: Long) {
                // ignore
            }

            override fun onFinish() {
                dismiss()
            }
        }
        mDismissTimer?.start()
        setOnDismissListener {
            mDismissTimer?.cancel()
            mDismissTimer = null
        }
    }

    override fun onStart() {
        super.onStart()
    }
}