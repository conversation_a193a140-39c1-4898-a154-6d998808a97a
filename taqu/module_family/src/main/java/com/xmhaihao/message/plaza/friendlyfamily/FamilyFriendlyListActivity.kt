package com.xmhaihao.message.plaza.friendlyfamily

import android.view.View
import android.widget.FrameLayout
import com.alibaba.android.arouter.facade.annotation.Route
import com.xmhaibao.message.api.router.FamilyRouterPath
import com.xmhaihao.message.fragment.MessageFamilyHomeFragment
import hb.common.xstatic.viewmodel.BaseViewModel
import hb.kotlin_extension.getCompatColor
import hb.kotlin_extension.noneSyncLazy
import hb.kotlin_extension.safeReplaceFragment
import hb.skin.support.SkinCompatManager
import hb.xstatic.mvvm.ui.XBaseMVVMActivity

/**
 * 家族友好列表页面
 
 * <AUTHOR>
 * @date 2023-11-27
 */
@Route(path = FamilyRouterPath.FAMILY_FRIEDNLY_LIST_ACTIVITY)
class FamilyFriendlyListActivity : XBaseMVVMActivity<BaseViewModel>() {

    companion object{
        /**
         * 友好家族
         */
        const val TYPE_FRIENDLY_LIST = "friendly_list"
    }


    private val rootView:FrameLayout by noneSyncLazy {
        FrameLayout(this).apply {
            id = View.generateViewId()
            setBackgroundColor(getCompatColor(cn.taqu.lib.base.R.color.TH_Navy001))
        }
    }



    override fun onCreateContentView(): Any {
        return rootView
    }

    override fun initViews() {
        super.initViews()
        setupToolbar("新人友好家族").setTextColor(cn.taqu.lib.base.R.color.TH_Gray990)
        val contentFragment = MessageFamilyHomeFragment.getInstance(TYPE_FRIENDLY_LIST, "")
        safeReplaceFragment(contentFragment,rootView.id)
    }

    override fun applySkin() {
        super.applySkin()
        rootView?.setBackgroundColor(getCompatColor(cn.taqu.lib.base.R.color.TH_Navy001))
    }

    override fun isApplySkin(): Boolean {
        return SkinCompatManager.getInstance().isEnableSkin
    }
}