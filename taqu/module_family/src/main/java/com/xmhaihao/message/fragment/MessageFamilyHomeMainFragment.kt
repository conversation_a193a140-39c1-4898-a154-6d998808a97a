package com.xmhaihao.message.fragment

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.NonNull
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import androidx.recyclerview.widget.RecyclerView.SCROLL_STATE_IDLE
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import cn.taqu.lib.base.callback.AppBarStateChangeListener
import cn.taqu.lib.base.constants.NavigationTabs
import cn.taqu.lib.base.event.EventNavigationTabChanged
import cn.taqu.lib.base.event.EventXjbLogin
import cn.taqu.lib.base.event.EventXjbUserLoginOut
import cn.taqu.lib.base.router.RouterLaunch
import cn.taqu.lib.base.utils.GrowingIOUtils
import cn.taqu.lib.base.utils.appdiff.AppDifferentiationUtils
import cn.taqu.lib.base.utils.eventlog.AppTrackEventUtils
import cn.taqu.lib.base.widget.SmoothScrollLinearLayoutManager
import cn.taqu.lib.base.xtracker.FamilyTracker
import cn.taqu.lib.okhttp.callback.GsonCallBack
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxItemDecoration
import com.google.android.material.appbar.AppBarLayout
import com.xmhaibao.family.R
import com.xmhaibao.family.api.router.FamilyPinsRouter
import com.xmhaibao.family.bean.FamilyTagInfo
import com.xmhaibao.family.bean.MessageFamilyHomeItemBean
import com.xmhaibao.family.bean.MessageFamilyHomeRecommendStatusBean
import com.xmhaibao.family.constants.JoinFamilyScene
import com.xmhaibao.family.databinding.MessageFamilyHomeMainFragmentBinding
import com.xmhaibao.family.databinding.MessageFamilyHomeMyLayoutBinding
import com.xmhaibao.family.dialog.FamilyTagDetailDialog
import com.xmhaibao.family.dialog.FamilyTagDetailEditDialog
import com.xmhaibao.family.event.FamilyDissolveEvent
import com.xmhaibao.family.event.MessageEventFamilyApplyJoinSuccess
import com.xmhaibao.family.holder.FamilyTagBaseViewHolder
import com.xmhaibao.family.holder.FamilyTagWallViewHolder
import com.xmhaibao.family.utils.FamilyLevelUpSwitchHelper
import com.xmhaibao.family.viewholder.FamilyRecommendFriendlyAdapter
import com.xmhaibao.family.widget.familytag.MaxLineFlexBoxLayoutManager
import com.xmhaibao.family.widget.familytag.provider.FamilyTagWallStyleProvider
import com.xmhaibao.message.api.constants.ImTypeConstants
import com.xmhaibao.message.api.event.MessageEventFamilyKickingMember
import com.xmhaibao.message.api.router.MessagePinsRouter
import com.xmhaibao.pins.module.api.databinding.FamilyHomeRecommendationBannerBinding
import com.xmhaihao.message.activity.MessageFamilyHomeActivity
import com.xmhaihao.message.bean.*
import com.xmhaihao.message.bean.MessageFamilyHomeRecommendBean.Companion.TYPE_B
import com.xmhaihao.message.bean.MessageFamilyHomeRecommendBean.Companion.TYPE_C
import com.xmhaihao.message.dialog.MessageFamilyHomeInviteJoinBDialog
import com.xmhaihao.message.dialog.MessageFamilyHomeInviteJoinCDialog
import com.xmhaihao.message.dialog.MessageFamilyHomeRecommendDialog
import com.xmhaihao.message.dialog.MessageFamilyIntroduceDialog
import com.xmhaihao.message.dialog.family.hall.FamilyHallRecommendDialog
import com.xmhaihao.message.dialog.family.hall.FamilyHallWeekRankDialog
import com.xmhaihao.message.event.EventMessageFamilyCreateSuccess
import com.xmhaihao.message.helper.MessageChatFamilyRedPacketHelper
import com.xmhaihao.message.helper.MessageFamilyHelper
import com.xmhaihao.message.helper.family.inforeach.FamilyInformationReachHelper
import com.xmhaihao.message.router.MessageRouter
import com.xmhaihao.message.utils.FamilyCommonUtils
import com.xmhaihao.message.utils.MessageFamilyUtils
import com.xmhaihao.message.viewModel.MessageFamilyHallViewModel
import dp
import hb.common.data.AccountHelper
import hb.common.xstatic.fragment.BaseFragment
import hb.drawable.shape.shape.ShapeBuilder
import hb.kotlin_extension.delayLaunch
import hb.kotlin_extension.safeAction
import hb.location.XLocation
import hb.skin.support.SkinCompatManager
import hb.skin.support.widget.SkinCompatSupportable
import hb.utils.ActivityUtils
import hb.utils.AppUtils
import hb.utils.BarUtils
import hb.utils.CollectionUtils
import hb.utils.ColorUtils
import hb.utils.ScreenUtils
import hb.utils.SizeUtils
import hb.utils.StringUtils
import hb.xadapter.XBaseAdapter
import hb.xbanner.http.XBannerBean
import hb.ximage.fresco.BaseDraweeView
import hb.xstatic.core.view.XToolbarLayout
import hb.xstyle.xdialog.XLoadingDialog
import hb.xtoast.XToastUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch


/**
 * 家族大厅修改成fragment，支持tab切换
 * 家族广场
 *
 * <AUTHOR>
 * @date 2021-03-09
 */
class MessageFamilyHomeMainFragment : BaseFragment() {
    /**
     * Tab列表
     */
    private val mTabList = mutableListOf<FamilyHomeTabItemBean>()

    private val messageFamilyHallViewModel by viewModels<MessageFamilyHallViewModel>()
    private var mFragmentAdapter: MessageFamilyFragmentAdapter? = null

    /**
     * 自己的家族
     */
    private var mMyFamilyView: ConstraintLayout? = null

    /**
     * 家族推荐邀请弹框（对照版本a）
     */
    private var recommendDialog: MessageFamilyHomeRecommendDialog? = null

    /**
     * 家族推荐邀请弹框（实验版本b）
     */
    private var mRecommendBDialog: MessageFamilyHomeInviteJoinBDialog? = null

    /**
     * 家族推荐邀请弹框（实验版本c）
     */
    private var mRecommendCDialog: MessageFamilyHomeInviteJoinCDialog? = null

    /**
     * 家族推荐邀请弹框,新版本
     */
    private var newRecommendFamilyDialog: FamilyHallRecommendDialog? = null

    private lateinit var mBinding: MessageFamilyHomeMainFragmentBinding

    /**
     * 家族新人友好推荐banner
     * @see getFamilyRecommendBinding()
     */
    private lateinit var mVsFamilyRecommendBannerBinding:FamilyHomeRecommendationBannerBinding

    /**
     * 我的家族模块 binding
     * 家族广场-已加入家族-我的家族顶部视图
     */
    private var mVsMyFamilyBinding: MessageFamilyHomeMyLayoutBinding? = null

    /**
     * 推荐家族列表是否隐藏
     * appbarLayout 折叠 算隐藏
     * onPause 算隐藏
     */
    var mRecommendRvHide = false

    /**
     * 新人友好家族推荐adapter
     */
    var mRecommendAdapter: FamilyRecommendFriendlyAdapter? = null

    /**
     * 新人友好家族 最后展示的家族位置
     */
    var mLastShowRecommendFamilyPosition:Int? = null


    /**
     * 上下文
     */
   var mContext:Context? = null

    /**
     * appbar 是否折叠状态
     */
    var mAppbarIsCollapsed=false

    /**
     * 家族信息触达帮助类
     */
    private var informationReachHelper: FamilyInformationReachHelper?  =null


    private val mHandler = Handler(Looper.getMainLooper())

    /** 上次是否有定位权限 */
    private var prevHasLocationPermission = false

    companion object {
        /**
         * 外部传进来的tab列表KEY
         */
        const val TAB_LIST = "TAB_LIST"

        fun getInstance(list: List<FamilyHomeTabItemBean>) =
            MessageFamilyHomeMainFragment().apply {
                val bundle = Bundle().apply {
                    putSerializable(TAB_LIST, ArrayList(list))
                }
                arguments = bundle
            }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mContext = context
    }
    override fun onCreateContentView(): Any {
        mBinding = MessageFamilyHomeMainFragmentBinding.inflate(layoutInflater)
        return mBinding.root
    }

    /***
     * 弹窗宽高缩放比例
     */
    private var scale = 1f

    override fun initViews() {
        super.initViews()
        pageTitle = "家族广场主页"
        val list = arguments?.getSerializable(TAB_LIST) as? List<FamilyHomeTabItemBean>?
        list?.let {
            mTabList.addAll(list)
        }
        val requestMap = hashMapOf<String, String>().apply {
            put("ticket_id", AccountHelper.getTicketId())
        }
        mBinding.bannerViewFamilyHome.setRequestParam(requestMap)
        mBinding.bannerViewFamilyHome.bindLifecycle(viewLifecycleOwner)
        initToolbarLayout()
        initFragment()
        initTabLayout()
        initListener()
        scale = ScreenUtils.getScreenWidth().toFloat() / SizeUtils.dp2px(375f)

        // 先看服务端是否有下发默认选中tab
        // 没下发的话走默认逻辑，登录后-选中定位tab
        var defaultSelectPosition = mTabList.indexOfFirst { it.isDefaultTab() }
        //默认定位到附近tab
        if (AccountHelper.isUserLogined() && defaultSelectPosition < 0) {
            defaultSelectPosition = mTabList.indexOfFirst {
                it.type == MessageFamilyHallViewModel.TAB_LOCATION
            }
        }
        mBinding.mVpFamilyHomeMain.currentItem = defaultSelectPosition
    }

    /**
     * 初始化标题栏 即使不知道是否有我的家族默认也要默认初始化
     */
    private fun initToolbarLayout() {
        XToolbarLayout.immerseStatusBar(activity?.window, true)
        var title = "家族广场"
        if (AppDifferentiationUtils.isPeiPeiApp()) {
            title = "家族群聊"
        }
        mBinding.clToolbar.tvTitle.text = title
        mBinding.clToolbar.ivBack.setOnClickListener {
            activity?.finish()
        }
        mBinding.clToolbar.ivSearch.setOnClickListener {
            activity?.run {
                MessagePinsRouter.launchMessageFamilySearchActivity(this, false, true)
            }
        }
        mBinding.clToolbar.root.layoutParams.height =
            SizeUtils.dp2px(44f) + BarUtils.getStatusBarHeight()
        mBinding.clToolbar.root.setPadding(0, BarUtils.getStatusBarHeight(), 0, 0)
        val contentParams = mBinding.clContent.layoutParams as ConstraintLayout.LayoutParams
        contentParams.topMargin = BarUtils.getStatusBarHeight() + SizeUtils.dp2px(44f)
    }

    /**
     * 初始化标题栏 当知道我是否有家族后 重新调整ui
     */
    private fun initToolbarWithFamilyData(hasMyFamily: Boolean) {
        if(hasMyFamily){
            mBinding.clToolbar.ivCreateFamily.visibility = View.GONE
            val contentParams = mBinding.clContent.layoutParams as ConstraintLayout.LayoutParams
            contentParams.topMargin = 0
            if(mMyFamilyView != null){
                val mIvFamilyHomeMyCover = mMyFamilyView!!.findViewById<View>(R.id.mIvFamilyHomeMyCover)
                (mIvFamilyHomeMyCover.layoutParams as? ViewGroup.MarginLayoutParams)?.topMargin =
                    BarUtils.getStatusBarHeight() + SizeUtils.dp2px(44f + 12f)
                mBinding.flTooBar.minimumHeight = SizeUtils.dp2px(44f) + BarUtils.getStatusBarHeight()
            }
        }else{
            mBinding.flTooBar.minimumHeight = 0
            mBinding.clToolbar.ivCreateFamily.visibility = View.VISIBLE
            mBinding.clToolbar.ivCreateFamily.setOnClickListener {
                FamilyCommonUtils.requestCheckCanCreateFamily()
            }

            val contentParams = mBinding.clContent.layoutParams as ConstraintLayout.LayoutParams
            contentParams.topMargin = BarUtils.getStatusBarHeight() + SizeUtils.dp2px(44f)
            //没有家族时状态栏字体颜色设置
            activity?.let {
                val isApplySkin= it is SkinCompatSupportable && !SkinCompatManager.getInstance().isSkinDark
                BarUtils.setNavBarLightMode(it, isApplySkin)
            }
        }
        setSkinToolbarColor(hasMyFamily)
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        messageFamilyHallViewModel.mMyFamilyData.observe(this, Observer<MessageFamilyHomeBean> {
            if (it == null || it.uuid.isNullOrEmpty()) {
                //隐藏家族入口
                mMyFamilyView?.visibility = View.GONE
                initToolbarWithFamilyData(false)
                messageFamilyHallViewModel.requestFamilyRecruitPackageNum()
                // 无家族时，请求是否展示家族推荐Banner 以及 是否展示家族玩法介绍弹窗
                messageFamilyHallViewModel.getFamilyIntroduceInfo()

                // 未加入家族-请求新人友好家族推荐
                messageFamilyHallViewModel.getFamilyRecommendFamily()

                //没家族情况下，也不展示Banner
                setAdBannerVisible(false)

                updateMyselfFamilyInfo(null)
            } else {
                setMyFamilyView(it)
                val ivAlphaBg = mMyFamilyView?.findViewById<View>(R.id.ivAlphaBg)
                //有家族隐藏领红包加入家族入口
                mBinding.clFamilyRecruitPackage.visibility = View.GONE
                // 有家族展示 正常广告Banner
                messageFamilyHallViewModel.mFamilyWelcomeBannerLiveData.postValue(
                    MessageFamilyWelcomeBannerBean())
                initToolbarWithFamilyData(true)

                (activity as? MessageFamilyHomeActivity)?.safeAction {
                    if (informationReachHelper == null) {
                        informationReachHelper = FamilyInformationReachHelper(this)
                    }
                    informationReachHelper?.requestFamilyHallInformation(it.uuid)
                }
                // 隐藏新人友好家族推荐
                getFamilyRecommendBinding(false)?.let { binding->
                    binding.root.isVisible = false
                    recommendScroll(false)
                }
                setAdBannerVisible(true)

            }
        })

        //关闭当前界面
        messageFamilyHallViewModel.mNeedFinishView.observe(this){
            activity?.run {
                if(it && !isFinishing) {
                    finish()
                }
            }
        }

        messageFamilyHallViewModel.mRecruitRedPackage.observe(this, Observer {
            val number = if (it.redPackageNumber.isEmpty()) "0" else it.redPackageNumber
            mBinding.clFamilyRecruitPackage.visibility = if (number.toInt() == 0) View.GONE else View.VISIBLE
            mBinding.tvRecruitNumber.text = String.format("%s个红包\n待领取", number)
        })

        messageFamilyHallViewModel.requestMyFamilyDada()
        messageFamilyHallViewModel.mFamilyWelcomeBannerLiveData.observe(this) { bean ->
            // 家族推荐Banner 和广告Banner只能展示一个
            if (bean.avatar.isNotBlank()) {
                setAdBannerVisible(false)
                mBinding.ivWelcomeFamilyBanner.visibility = View.VISIBLE
                mBinding.ivWelcomeFamilyBanner.setImageFromUrl(bean.avatar)
                mBinding.ivWelcomeFamilyBanner.setOnClickListener {
                    RouterLaunch.dealJumpData(context, bean.relation)
                }
            } else {
                setAdBannerVisible(true)
                mBinding.ivWelcomeFamilyBanner.visibility = View.GONE
            }
        }
        messageFamilyHallViewModel.mFamilyIntroduceDialogLiveData.observe(this) {
            context?.let { context ->
                // 数据有效切未加入过家族
                if(it.content.isNotBlank() && !it.isUserJoinedFamily()) {
                    MessageFamilyIntroduceDialog(context, it).apply {
                        setOnDismissListener {
                            // 客户端写死， 家族推荐窗在家族玩法介绍窗后 1min 展示
                            mHandler.postDelayed({
                                initRecommendData()
                            }, 60 * 1000)
                        }
                    }.show()
                } else {
                    // 若未配置家族玩法介绍窗，则直接展示家族推荐窗
                    initRecommendData()
                }
            }
        }
        messageFamilyHallViewModel.mFamilyHallWeekInfo.observe(this) {
            XLoadingDialog.hideLoadingbar()
            if (it != null) {
                FamilyHallWeekRankDialog.showDialog(messageFamilyHallViewModel, it, childFragmentManager)
            }
        }
        initRecommendFamilyView()
    }


    /**
     * 监听家族友好推荐数据
     */
    private fun initRecommendFamilyView() {
        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
            messageFamilyHallViewModel.recommendFamilyFlow.collectLatest {
                getFamilyRecommendBinding(!it.isNullOrEmpty())?.let { binding->
                    binding.tvMore.setOnClickListener {
                        // 点击跳转更多友好家族列表
                        FamilyTracker.trackFamilySquareFriendBannerClk("","更多按钮")
                        MessagePinsRouter.launchFamilyFriendlyListActivity()
                    }
                    if (binding.rvRecommend.adapter == null) {
                        // 设置滚动速率
                        binding.rvRecommend.layoutManager =
                            SmoothScrollLinearLayoutManager(context, 30F).apply {
                                orientation = LinearLayoutManager.HORIZONTAL
                            }
                        mRecommendAdapter = FamilyRecommendFriendlyAdapter(
                            itemJoinClick = { familyRecommendBean ->
                                messageFamilyHallViewModel.requestApplyJoin(
                                    familyUuid = familyRecommendBean.familyUuid,
                                    isAdminRecommend = null,
                                    isRecommend = JoinFamilyScene.FAMILY_FRIEND_BANNER.value,
                                    requestRecommendFamily = false,
                                    toast = "申请已发送，等待通过"
                                )
                            }, itemClick = {
                                // 点击跳转家族详情
                                FamilyPinsRouter.launchMessageFamilyDetailActivity(it)
                            }, bindLastPosition = { position ->
                                // 实际曝光和缓存view会差5个
                                val offsetPosition = position - 4
                                if (mLastShowRecommendFamilyPosition ?: 0 < offsetPosition) {
                                    mLastShowRecommendFamilyPosition = offsetPosition
                                }
                            })
                        binding.rvRecommend.adapter = mRecommendAdapter
                        binding.rvRecommend.addOnScrollListener(object : OnScrollListener() {
                            var job: Job? = null
                            override fun onScrollStateChanged(
                                recyclerView: RecyclerView,
                                newState: Int
                            ) {
                                super.onScrollStateChanged(recyclerView, newState)
                                job?.cancel()
                                if(newState == SCROLL_STATE_IDLE){
                                    // 停止滚动了，继续轮播滚动
                                    job = delayLaunch(500){
                                        if(!mRecommendRvHide){
                                            binding.rvRecommend.smoothScrollToPosition(Int.MAX_VALUE-1)
                                        }
                                    }
                                }
                            }
                        })
                    }
                    if (it != null) {
                        mRecommendAdapter?.setDatas(it)
                        // 实现可以往回滚的假象
                        val maxIntCenter = (Int.MAX_VALUE - 1) / 2
                        val fromPosition = maxIntCenter % it.size
                        binding.rvRecommend.scrollToPosition(maxIntCenter - fromPosition)
                        binding.root.isVisible = true
                    } else {
                        // 不显示友好家族
                        binding.root.isVisible = false
                    }

                }
            }
        }
    }


    private fun initTabLayout() {
        prevHasLocationPermission = XLocation.hasLocationPermission(AppUtils.getApp())
        mBinding.mTbFamilyHomeRank.run {
            setViewPager(mBinding.mVpFamilyHomeMain)
            indicatorHeight = 0
            dividerColor = Color.TRANSPARENT
            textSize = SizeUtils.sp2px(15f)
            setIsSelectScale(true)
            setBackgroundResource(hb.xstyle.R.color.transparent)

            setSelectedTextBold(true)
            tabPaddingLeftRight = SizeUtils.dp2px(8f)
            setSkinForTabColor()
            setIndicatorWidth(SizeUtils.dp2px(5f))
            indicatorHeight = SizeUtils.dp2px(3f)
            setIndicatorRadius(SizeUtils.dp2px(1f))
            setLineBottomPadding(SizeUtils.dp2px(6f))
            setOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                override fun onPageScrollStateChanged(state: Int) {
                }

                override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                }

                override fun onPageSelected(position: Int) {
                    mTabList.getOrNull(position)?.let {
                        AppTrackEventUtils.trackFamilySquareListExposure(it.trackName)
                    }
                }
            })

        }
    }

    private fun initFragment() {
        mFragmentAdapter = MessageFamilyFragmentAdapter(childFragmentManager)

        mBinding.mVpFamilyHomeMain.offscreenPageLimit = 3
        mBinding.mVpFamilyHomeMain.adapter = mFragmentAdapter
    }

    private fun initListener() {
        mBinding.mAbMessageHome.addOnOffsetChangedListener(AppBarLayout.OnOffsetChangedListener { _, verticalOffset ->
            if (!isAdded || isDetached) {
                return@OnOffsetChangedListener
            }
            val enabled = verticalOffset >= 0
            childFragmentManager.fragments.forEach {
                if (it is MessageFamilyHomeNewFragment) {
                    it.setRefreshEnabled(enabled)
                }
            }

            val verticalOffsetAbs = Math.abs(verticalOffset)
            if(StringUtils.isNotEmpty(messageFamilyHallViewModel.mMyFamilyData.value?.uuid)){
                //有家族
                if(verticalOffsetAbs <= 0){
                    mBinding.clToolbar.ivBack.setImageResource(R.drawable.message_family_home_back_w0_ic)
                    mBinding.clToolbar.ivSearch.setImageResource(R.drawable.message_family_home_search_white_ic)
                    mBinding.clToolbar.root.setBackgroundColor(ColorUtils.getColor(hb.xstyle.R.color.transparent))
                    mBinding.clToolbar.tvTitle.setTextColor(ColorUtils.getColor(hb.xstyle.R.color.white))
                    mBinding.clToolbar.root.alpha = 1f
                }else{
                    mBinding.clToolbar.ivBack.setImageResource(R.drawable.message_family_home_back_black_ic)
                    mBinding.clToolbar.ivSearch.setImageResource(R.drawable.message_family_home_search_black_ic)
                    mContext?.run {
                        mBinding.clToolbar.root.setBackgroundColor(ContextCompat.getColor(this, hb.xstyle.R.color.TH_Navy001))
                        mBinding.clToolbar.tvTitle.setTextColor(ContextCompat.getColor(this, hb.xstyle.R.color.TH_Gray990))
                    }
                    val parentHeight = mBinding.flTooBar.measuredHeight - SizeUtils.dp2px(44f) - BarUtils.getStatusBarHeight()
                    val alpha = (parentHeight - verticalOffsetAbs).toFloat() / parentHeight
                    mBinding.clToolbar.root.alpha = 1f - alpha
                }
            }
            if (verticalOffsetAbs > 300) {
                recommendScroll(false)
            } else {
                recommendScroll(true)
            }

        })
        //状态栏字体颜色设置
        mBinding.mAbMessageHome.addOnOffsetChangedListener(object: AppBarStateChangeListener(){
            override fun onStateChanged(appBarLayout: AppBarLayout?, state: State?) {

                activity?.let {
                    val isApplySkin= it is SkinCompatSupportable &&  !SkinCompatManager.getInstance().isSkinDark
                    when (state) {
                        State.EXPANDED->{
                            //展开时状态栏字体显示白色 不受换肤设置影响
                            BarUtils.setNavBarLightMode(it, false)
                        }
                        State.COLLAPSED->{
                            mAppbarIsCollapsed=true
                            BarUtils.setNavBarLightMode(it, isApplySkin)
                        }
                        else ->{
                            mAppbarIsCollapsed=false
                        }
                    }
                }
            }

            override fun onOffsetChanged1(appBarLayout: AppBarLayout?, i: Int) {
                //Not yet implemented
            }

        })


        /*
        * 跳转到招募红包列表
        */
        mBinding.ivBall.setOnClickListener {
            MessageRouter.launchMessageFamilyRecruitListActivity()
        }

        /**
         * 排行榜入口
         */
        mBinding.ivRankEntrance.setOnClickListener {
            XLoadingDialog.showLoadingbar(context)
            messageFamilyHallViewModel.getHallWeekInfo()
        }
    }

    /**
     * 设置家族推荐弹框信息
     */
    private fun initRecommendData() {
        // 延时操作，可能存在Activity切换的情况
        if(ActivityUtils.getTopActivity() !is MessageFamilyHomeActivity) {
            return
        }
        if (newRecommendFamilyDialog?.isShowing == true
            || mRecommendCDialog?.isShowing == true
            || mRecommendBDialog?.isShowing == true) {
            return
        }
        messageFamilyHallViewModel.requestGetRecommendFamily()
    }

    override fun initCompleted() {
        super.initCompleted()
        observerRecommendFamily()
    }
    private fun observerRecommendFamily(){
        messageFamilyHallViewModel.mRecommendFamilyData.observe(viewLifecycleOwner) { recommendBean ->
            if (recommendBean?.recommendInfo?.isNewFamilyRecommendType() == true){
                activity?.safeAction {
                    newRecommendFamilyDialog?.dismiss()
                    newRecommendFamilyDialog = FamilyHallRecommendDialog(
                        this,
                        recommendBean.recommendInfo!!,
                        object : MessageFamilyHomeInviteJoinBDialog.OnDialogClickCallback {
                            override fun acceptInvite() {
                                AppTrackEventUtils.trackFamilyRecomPopClick(
                                    familyUuid = recommendBean.recommendInfo?.uuid,
                                    scene = "家族广场",
                                    tagList = recommendBean.recommendInfo?.getTagIdList()
                                )
                                joinFamily(recommendBean.recommendInfo)
                            }
                        })
                    newRecommendFamilyDialog?.show()
                    AppTrackEventUtils.trackFamilyRecomPopExps(
                        familyUuid = recommendBean.recommendInfo?.uuid,
                        scene = "家族广场",
                        tagList = recommendBean.recommendInfo?.getTagIdList()
                    )

                }
            }else if (StringUtils.equalsIgnoreCase(TYPE_B, recommendBean.recommendInfo?.inviteType)) {
                if (mRecommendBDialog == null) {
                    context?.run {
                        mRecommendBDialog = MessageFamilyHomeInviteJoinBDialog(this, recommendBean,
                            object : MessageFamilyHomeInviteJoinBDialog.OnDialogClickCallback {
                                override fun acceptInvite() {
                                    AppTrackEventUtils.trackFamilyRecomPopClick(
                                        familyUuid = recommendBean.recommendInfo?.uuid,
                                        scene = "家族广场",
                                        tagList = recommendBean.recommendInfo?.getTagIdList()
                                    )
                                    recommendBean?.run {
                                        joinFamily(this.recommendInfo)
                                    }
                                }
                            })
                        mRecommendBDialog?.show()
                        AppTrackEventUtils.trackFamilyRecomPopExps(
                            familyUuid = recommendBean.recommendInfo?.uuid,
                            scene = "家族广场",
                            tagList = recommendBean.recommendInfo?.getTagIdList(),
                        )
                    }

                }
            } else if (StringUtils.equalsIgnoreCase(
                    TYPE_C,
                    recommendBean.recommendInfo?.inviteType
                )
            ) {
                context?.run {
                    mRecommendCDialog = MessageFamilyHomeInviteJoinCDialog(this, recommendBean,
                        object : MessageFamilyHomeInviteJoinCDialog.OnDialogClickCallback {
                            override fun acceptInvite() {
                                AppTrackEventUtils.trackFamilyRecomPopClick(
                                    familyUuid =  recommendBean.recommendInfo?.uuid,
                                    scene = "家族广场",
                                    tagList = recommendBean.recommendInfo?.getTagIdList()
                                )
                                recommendBean?.run {
                                    joinFamily(this.recommendInfo)
                                }
                            }
                        })
                    mRecommendCDialog?.show()
                    AppTrackEventUtils.trackFamilyRecomPopExps(
                        familyUuid = recommendBean.recommendInfo?.uuid,
                        scene = "家族广场",
                        tagList = recommendBean.recommendInfo?.getTagIdList()
                    )
                }
            } else {
                when {
                    StringUtils.equalsIgnoreCase(recommendBean.status, "2") -> {
                        /*
                         * 显示家族推荐申请弹窗
                         */
                        context?.run {
                            if (recommendDialog == null) {
                                recommendDialog = MessageFamilyHomeRecommendDialog(this,
                                    object :
                                        MessageFamilyHomeRecommendDialog.OnDialogClickCallback {
                                        override fun acceptInvite() {
                                            AppTrackEventUtils.trackFamilyRecomPopClick(
                                                familyUuid = recommendBean.recommendInfo?.uuid,
                                                scene = "家族广场",
                                                tagList = recommendBean.recommendInfo?.getTagIdList()
                                            )
                                            recommendBean?.run {
                                                joinFamily(this.recommendInfo)
                                            }
                                        }

                                    }, recommendBean.recommendInfo
                                )
                                recommendDialog?.show()
                                AppTrackEventUtils.trackFamilyRecomPopExps(
                                    familyUuid = recommendBean.recommendInfo?.uuid,
                                    scene = "家族广场",
                                    tagList = recommendBean.recommendInfo?.getTagIdList()
                                )
                            }

                        }

                    }

                    StringUtils.equalsIgnoreCase(recommendBean.status, "1") -> {
                        /*
                         * 显示红包弹窗
                         */
                        recommendBean.recommendInfo?.run {
                            MessageChatFamilyRedPacketHelper.clickRedPacket(
                                activity,
                                worldFamilyUuid,
                                redPackageId,
                                ImTypeConstants.MESSAGE_FAMILY_RECRUIT_RED_PACKET,
                                true,
                                true,
                                "",
                                ""
                            ) {
                            }
                        }
                    }

                    else -> {
                        //不处理
                    }
                }
            }
        }
    }

    /**
     * 请求加入家族
     */
    private fun joinFamily(recommendInfo: MessageFamilyHomeRecommendBean.MessageFamilyHomeRecommendItem?) {
        MessageFamilyHelper.requestJoin(recommendInfo?.uuid, "", JoinFamilyScene.RECOMMEND.value,recommendInfo?.isAdminRecommend,
            object : GsonCallBack<MessageFamilyHomeRecommendStatusBean>() {
                override fun onSuccess(isCache: Boolean, obj: MessageFamilyHomeRecommendStatusBean?,
                                       response: IResponseInfo<out IResponseInfo<*>>
                ) {
                    obj?.run {
                        if (StringUtils.equalsIgnoreCase(applyStatus, "1") || StringUtils.equalsIgnoreCase(applyStatus, "3")) {
                            onEventMainThread(MessageEventFamilyApplyJoinSuccess(recommendInfo?.uuid, MessageFamilyHomeItemBean.STATUS_CAN_JOIN))
                            //直接进入家族
                            recommendInfo?.conversationId?.run {
                                XToastUtils.show("您已成功加入家族")
                                FamilyPinsRouter.familyService().launchMessageFamilyChatActivity(this)
                                GrowingIOUtils.trackMessageFamilyShow("家族大厅邀请",recommendInfo?.uuid)
                            }

                        } else {
                            XToastUtils.show("已接受邀请，等待对方确认")
                        }
                        recommendDialog?.dismiss()
                    }

                }

                override fun onFailure(isServiceFailure: Boolean, response: IResponseInfo<out IResponseInfo<*>>) {
                    XToastUtils.show(response.responseMsg)
                }

            })
    }


    /**
     * 我的家族
     * @param bean MessageFamilyHomeBean
     */
    private fun setMyFamilyView(bean: MessageFamilyHomeBean) {
        if (mBinding.mVsFamilyHomeMyFamily.parent != null && mMyFamilyView == null) {
            mMyFamilyView = mBinding.mVsFamilyHomeMyFamily.inflate() as? ConstraintLayout?
        }
        mBinding.clToolbar.ivCreateFamily.visibility = View.GONE
        mMyFamilyView?.visibility = View.VISIBLE

        mMyFamilyView?.let {
            mVsMyFamilyBinding = MessageFamilyHomeMyLayoutBinding.bind(it)
        }
        val mIvFamilyHomeMyCover = mMyFamilyView?.findViewById<BaseDraweeView>(R.id.mIvFamilyHomeMyCover)
        val mIvFamilyHomeFrame = mMyFamilyView?.findViewById<BaseDraweeView>(R.id.mIvFamilyHomeFrame)
        val mTvFamilyHomeMyManifesto = mMyFamilyView?.findViewById<TextView>(R.id.mTvFamilyHomeMyManifesto)
        val mTvFamilyHomeMyName = mMyFamilyView?.findViewById<TextView>(R.id.mTvFamilyHomeMyName)
        val mTvFamilyHomeMyPrestige = mMyFamilyView?.findViewById<TextView>(R.id.mTvFamilyHomeMyPrestige)
        val mTvFamilyHomeMyPrestigeKey = mMyFamilyView?.findViewById<TextView>(R.id.mTvFamilyHomeMyPrestigeKey)
        val mTvFamilyHomeMyMemberNum = mMyFamilyView?.findViewById<TextView>(R.id.mTvFamilyHomeMyMemberNum)
        val tvHomeMyLevel = mMyFamilyView?.findViewById<TextView>(R.id.tvHomeMyLevel)
        val ivHomeMyLevel = mMyFamilyView?.findViewById<BaseDraweeView>(R.id.ivHomeMyLevel)
        val tvFamilyMasterName = mMyFamilyView?.findViewById<TextView>(R.id.tvFamilyMasterName)
        val ivAlphaBg = mMyFamilyView?.findViewById<BaseDraweeView>(R.id.ivAlphaBg)
        val ivLevelBg = mMyFamilyView?.findViewById<BaseDraweeView>(R.id.ivLevelBg)

        FamilyCommonUtils.updateMessageFamilyConversation(bean.conversationId, bean.uuid,
                bean.name, bean.image, bean.createTime, bean.role)

        mIvFamilyHomeMyCover?.setImageFromUrl(bean.image)
        if(FamilyLevelUpSwitchHelper.isUseNewVersion) {
            mIvFamilyHomeFrame?.setImageFromUrl(bean.honourFrameUrl)
        }
        mTvFamilyHomeMyManifesto?.text = bean.manifesto
        mTvFamilyHomeMyName?.text = bean.name
        mTvFamilyHomeMyPrestige?.text =
            MessageFamilyUtils.getPrestigeStringOfW(bean.prestige)
        "总${if(FamilyLevelUpSwitchHelper.isUseNewVersion) {"荣誉"} else {"威望"}}".also { mTvFamilyHomeMyPrestigeKey?.text = it }
        mTvFamilyHomeMyMemberNum?.text = bean.memberNum

        tvHomeMyLevel?.text = "Lv.${bean.level}"
        ivHomeMyLevel?.setImageFromUrl(bean.levelIcon)

        if (bean.hotChatInfo != null) {
            mVsMyFamilyBinding?.ivHotChat?.isVisible = true
            mVsMyFamilyBinding?.ivHotChat?.setImageFromUrl(bean.hotChatInfo?.hotChatPic)
            mVsMyFamilyBinding?.ivHotChat?.setOnClickListener {
                bean.hotChatInfo?.showHotChatDetails(requireContext())
            }
        } else {
            mVsMyFamilyBinding?.ivHotChat?.isVisible = false
        }

        // 更多标签点击->标签详情页
        mVsMyFamilyBinding?.tvTagMore?.setOnClickListener {
            bean.uuid?.let { familyUuid ->
                FamilyTagDetailDialog(it.context, familyUuid,
                    listener = object :FamilyTagDetailEditDialog.FamilyTagSaveSuccessListener{
                        override fun onSuccess() {
                            // 标签页编辑完成，刷新当前家族信息
                            messageFamilyHallViewModel.requestMyFamilyDada()
                        }
                    }).show()
            }
        }

        if(StringUtils.isEmpty(bean.levelColorIcon)) {
            ivLevelBg?.isVisible = false
            tvHomeMyLevel?.background = ShapeBuilder()
                .corner(SizeUtils.dp2px(8F).toFloat())
                .solid(ContextCompat.getColor(mContext!!,hb.xstyle.R.color.black_alpha_30))
                .create()
        }else {
            tvHomeMyLevel?.post {
                ivLevelBg?.isVisible = true
                val tvLevel = tvHomeMyLevel.width
                val params = ivLevelBg?.layoutParams?.run {
                    this.width = tvLevel
                    this
                }
                ivLevelBg?.layoutParams = params
                ivLevelBg?.setImageFromUrl(bean.levelColorIcon)
            }
        }

        if (CollectionUtils.isNotEmpty(bean.memberList) && bean.memberList?.get(0) != null) {
            tvFamilyMasterName?.text = bean.memberList!![0]!!.nickname
        }
        ivAlphaBg?.setImageBlurFromUrl(bean.image, 18)

        mMyFamilyView?.setOnClickListener {
            bean.conversationId?.let { conversation ->
                GrowingIOUtils.trackMessageFamilyShow("其他", bean.uuid)
                FamilyPinsRouter.familyService().launchMessageFamilyChatActivity(conversation)
            }
        }
        updateMyselfFamilyInfo(bean.uuid)
        updateMyFamilyTagInfo(bean.familyTagList)
    }


    /**
     * 更新我的家族-标签信息
     */
    private fun updateMyFamilyTagInfo(familyTagList: MutableList<FamilyTagInfo>?) {
        mVsMyFamilyBinding?.apply {
            if(rvFamilyTagList.adapter == null){
                val layoutManager = MaxLineFlexBoxLayoutManager(root.context).apply {
                    setFixMaxLine(2)
                }
                val adapter = XBaseAdapter(root.context)
                val styleProvider = FamilyTagWallStyleProvider()
                layoutManager.flexDirection = FlexDirection.ROW
                layoutManager.flexWrap = FlexWrap.WRAP
                rvFamilyTagList.layoutManager = layoutManager
                // 设置间距
                rvFamilyTagList.addItemDecoration(FlexboxItemDecoration(root.context).apply {
                    setDrawable(ShapeBuilder().size(0, SizeUtils.dp2px(9f)).create())
                })
                rvFamilyTagList.adapter = adapter
                adapter.register(FamilyTagInfo::class.java, FamilyTagWallViewHolder::class.java)
                adapter.setOnViewHolderCreatedListener {
                    (it as? FamilyTagWallViewHolder)?.let {
                        it.styleProvider = styleProvider
                        it.isForceLight = true
                    }
                }
            }

            // 标签为空则不展示标签墙视图
            groupTag.isVisible = !familyTagList.isNullOrEmpty()

            (rvFamilyTagList.adapter as? XBaseAdapter)?.apply {
                items = familyTagList?: listOf<FamilyTagInfo>()
                notifyDataSetChanged()
            }
        }


    }

    /**
     * 更新加入的家族信息
     */
    private fun updateMyselfFamilyInfo(familyUuid: String?) {
        childFragmentManager.fragments.forEach {
            if (it is MessageFamilyHomeNewFragment) {
                it.updateMyselfFamilyInfo(familyUuid)
            }
        }
    }

    /**
     * 新人友好家族推荐区域是否已经初始化
     * @param needInit 是否需要初始化
     */
    private fun getFamilyRecommendBinding(needInit: Boolean): FamilyHomeRecommendationBannerBinding? {
        if (needInit && !::mVsFamilyRecommendBannerBinding.isInitialized) {
            mVsFamilyRecommendBannerBinding =
                FamilyHomeRecommendationBannerBinding.bind(mBinding.vsFamilyHomeRecommend.inflate())
            return mVsFamilyRecommendBannerBinding
        } else if (::mVsFamilyRecommendBannerBinding.isInitialized) {
            return mVsFamilyRecommendBannerBinding
        }
        return null
    }

    /**
     * 推荐列表滚动
     * @param scroll 是滚动还是暂停
     */
    private fun recommendScroll(scroll: Boolean) {
        if (scroll) {
            mRecommendRvHide = false
            getFamilyRecommendBinding(false)?.apply {
                if (root.isVisible) {
                    rvRecommend?.smoothScrollToPosition(Int.MAX_VALUE - 1)
                } else {
                    // 隐藏就不触发滚动了
                    mRecommendRvHide = true
                    rvRecommend?.stopScroll()
                }
            }
        } else {
            mRecommendRvHide = true
            getFamilyRecommendBinding(false)?.rvRecommend?.stopScroll()
        }
    }

    private fun refreshAllData() {
        messageFamilyHallViewModel.requestMyFamilyDada()
        childFragmentManager.fragments.forEach {
            if (it is MessageFamilyHomeNewFragment) {
                it.refreshData()
            }
        }
    }

    /**
     * 保存最后展示的推荐家族位置
     */
    private fun saveLastShowRecommendFamilyPosition() {
        mLastShowRecommendFamilyPosition?.let {
            messageFamilyHallViewModel.saveLastRecommendFamilyUuid(mRecommendAdapter?.getCurrentData(it)?.familyUuid)
        }
    }

    fun onEventMainThread(event: EventMessageFamilyCreateSuccess) {
        activity?.finish()
    }

    fun onEventMainThread(event: EventXjbLogin) {
        refreshAllData()
    }

    fun onEventMainThread(event: EventXjbUserLoginOut) {
        refreshAllData()
    }

    fun onEventMainThread(event: FamilyDissolveEvent) {
        refreshAllData()
    }


    fun onEventMainThread(event: MessageEventFamilyApplyJoinSuccess) {
        if (StringUtils.equalsIgnoreCase(event.status, MessageFamilyHomeItemBean.STATUS_CAN_JOIN)
                || StringUtils.equalsIgnoreCase(event.status, MessageFamilyHomeItemBean.STATUS_EXIT)) {
            messageFamilyHallViewModel.requestMyFamilyDada()
        } else {
            childFragmentManager.fragments.forEach {
                if (it is MessageFamilyHomeNewFragment) {
                    it.setApplyJoin(event)
                }
            }
        }
    }

    fun onEventMainThread(event: EventNavigationTabChanged) {
        if (NavigationTabs.TAB_TAG_MESSAGE_FAMILY == event.tabId) {
            refreshAllData()
        }
    }

    override fun onResume() {
        super.onResume()
        recommendScroll(true)

        // 比对当前定位权限是否变更，变更过则更新Tab
        val hasLocationPermission = XLocation.hasLocationPermission(AppUtils.getApp())
        if (prevHasLocationPermission != hasLocationPermission) {
            prevHasLocationPermission = hasLocationPermission
            mBinding.mTbFamilyHomeRank.notifyDataSetChanged()
        }
    }
    override fun onPause() {
        super.onPause()
        recommendScroll(false)
    }

    override fun onStop() {
        super.onStop()
        saveLastShowRecommendFamilyPosition()
    }

    override fun onDestroy() {
        super.onDestroy()
        mHandler.removeCallbacksAndMessages(null)
        getFamilyRecommendBinding(false)?.let {
            it.rvRecommend.clearOnScrollListeners()
            it.rvRecommend.stopScroll()
        }
        mFragmentAdapter = null
    }

    private inner class MessageFamilyFragmentAdapter(@NonNull fm: FragmentManager) :
        FragmentStatePagerAdapter(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {
        override fun getItem(position: Int): Fragment {
            return MessageFamilyHomeNewFragment.getInstance(mTabList[position])
        }

        override fun getPageTitle(position: Int): CharSequence? {
            val tabItem = mTabList[position]
            if (MessageFamilyHallViewModel.TAB_LOCATION == tabItem.type && !prevHasLocationPermission) {
                // 没有开启定位权限时，【定位】Tab固定显示为【附近】
                return "附近"
            }
            return mTabList[position].name
        }

        override fun getCount() = mTabList.size
    }

    override fun isApplyEventBus(): Boolean {
        return true
    }

    override fun isShowToolbar(): Boolean {
        return false
    }


    fun onEventMainThread(event: MessageEventFamilyKickingMember) {
        refreshAllData()
    }

    fun applySkin() {
        val familyData = messageFamilyHallViewModel.mMyFamilyData.value
        val hasFamily = familyData != null && StringUtils.isNotEmpty(familyData.uuid)

        setSkinToolbarColor(hasFamily)
        setSkinForTabColor()
        activity?.let {
            if (hasFamily && !mAppbarIsCollapsed) {
                //有家族 且appbar未完全折叠起来时 状态栏显示白色字体
                BarUtils.setNavBarLightMode(it, false)
            } else {
                BarUtils.setNavBarLightMode(it, !SkinCompatManager.getInstance().isSkinDark)

            }
        }
        childFragmentManager.fragments.forEach {
            if (it is MessageFamilyHomeNewFragment) {
                it.applySkin()
            }
        }
    }

    /**
     * toolbar 换肤
     */
    private fun setSkinToolbarColor(hasMyFamily: Boolean) {
        if(!this::mBinding.isInitialized){
            return
        }
        if (hasMyFamily) {
            mBinding.clToolbar.ivBack.setImageResource(R.drawable.message_family_home_back_w0_ic)
            mBinding.clToolbar.ivSearch.setImageResource(R.drawable.message_family_home_search_white_ic)
            mBinding.clToolbar.root.setBackgroundColor(ColorUtils.getColor(hb.xstyle.R.color.transparent))
            mBinding.clToolbar.tvTitle.setTextColor(ColorUtils.getColor(hb.xstyle.R.color.white))
        } else {
            mBinding.clToolbar.ivBack.setImageResource(R.drawable.message_family_home_back_black_ic)
            mBinding.clToolbar.ivSearch.setImageResource(R.drawable.message_family_home_search_black_ic)
            mContext?.let {
                mBinding.clToolbar.root.setBackgroundColor(ContextCompat.getColor( it, hb.xstyle.R.color.TH_Navy001))
                mBinding.clToolbar.tvTitle.setTextColor(ContextCompat.getColor(it,hb.xstyle.R.color.TH_Gray990))
            }
        }


    }

    /**
     * 设置广告Banner的Visibility
     * */
    private fun setAdBannerVisible(isVisible: Boolean) {
        mBinding.bannerViewFamilyHome.visibility = if(isVisible) {
            View.VISIBLE
        } else {
            View.GONE
        }
//        val params = mBinding.bannerViewFamilyHome.layoutParams
        mBinding.bannerViewFamilyHome.layoutParams = mBinding.bannerViewFamilyHome.layoutParams.apply {
            width = if (isVisible) {
                ScreenUtils.getAppScreenWidth()-32.dp
            } else {
                0
            }
            height = if(isVisible) {
                (SizeUtils.dp2px(56f) * scale).toInt()
            } else {
                0
            }
        }


        checkBannerTracker()
    }

    /**
     * 更新监测是否需要上报埋点
     */
    private fun checkBannerTracker() {
        //下面逻辑只有当banner可见才会调用
        if (mBinding.bannerViewFamilyHome.isVisible) {
            mBinding.bannerViewFamilyHome.setOnPageChangeListener(object :
                ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    val list: List<Any?> = mBinding.bannerViewFamilyHome.data ?: return
                    if (list.size <= position || position < 0) {
                        return
                    }
                    val item = list[position] as? XBannerBean ?: return
                    FamilyTracker.trackFamilyAdSquareBannerExp(
                        messageFamilyHallViewModel.mMyFamilyData.value?.uuid,
                        item.bannerUrl,
                        item.relationUrl
                    )
                }
            })
            mBinding.bannerViewFamilyHome.setOnClickBannerListener { ibanner->
                FamilyTracker.trackFamilyAdSquareBannerClk(
                    messageFamilyHallViewModel.mMyFamilyData.value?.uuid,
                    ibanner.bannerUrl,
                    ibanner.relationUrl)
                false
            }
        } else {
            mBinding.bannerViewFamilyHome.setOnPageChangeListener(null)
        }
    }

    /**
     * tab 换肤
     */
    private fun setSkinForTabColor() {
        mBinding.mTbFamilyHomeRank.run {
            setSelectedTextColor(ContextCompat.getColor(mContentView.context, hb.xstyle.R.color.TH_Gray990))
            textColor = ContextCompat.getColor(mContentView.context, hb.xstyle.R.color.TH_Gray600)
            indicatorColor = ContextCompat.getColor(mContentView.context, hb.xstyle.R.color.TH_Gray990)
        }
    }
}
