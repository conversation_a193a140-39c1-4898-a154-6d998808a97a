package com.xmhaibao.family.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import hb.common.helper.HostHelper

/**
 * 表情回复引导
 *
 * <AUTHOR>
 * @date 2024/06/06
 */
class FamilyEmojiGuideBean : IDoExtra {

    /** 第一步引导图片 */
    @SerializedName("step_first")
    var stepFirstUrl: String = ""

    /** 第二步引导图片 */
    @SerializedName("step_second")
    var stepSecondUrl: String = ""

    /** 第三步引导图片 */
    @SerializedName("step_third")
    var stepThirdUrl: String = ""

    /** 引导过期时间 */
    @SerializedName("guide_expired")
    var guideExpired: Long = 0L

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        stepFirstUrl = HostHelper.getImageDefaultHost().getWebpUrlFull(stepFirstUrl)
        stepSecondUrl = HostHelper.getImageDefaultHost().getWebpUrlFull(stepSecondUrl)
        stepThirdUrl = HostHelper.getImageDefaultHost().getWebpUrlFull(stepThirdUrl)
    }
}