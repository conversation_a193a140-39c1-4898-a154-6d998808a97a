package com.xmhaibao.family.router.convert

import android.content.Context
import android.os.Bundle
import cn.taqu.lib.base.router.JustRouterConvert
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.xmhaibao.family.api.router.FamilyPinsRouter
import com.xmhaibao.family.api.router.FamilyRouterPath
import com.xmhaibao.hbchat.bean.constant.MessageConstants

/**
 * 家族邀请弹窗列表 路由转换
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Route(path = FamilyRouterPath.FAMILY_INVITE_DIALOG, name = "家族邀请弹窗")
class FamilyInviteConvert : JustRouterConvert() {

    @Autowired(name = "family_uuid", required = true, desc = "家族uuid", testValue = "eedwu9i80u")
    @JvmField
    var familyUuid: String? = null

    @Autowired
    @JvmField
    var source: String? = null


    override fun navigation(args: Bundle?, context: Context?) {
        FamilyPinsRouter.messageFamilyService().showInviteMemberDialog(
            MessageConstants.MESSAGE_SEND_TYPE_ACTIVE_BOX,
            if (source.isNullOrEmpty()) "宝箱任务" else source
        )
    }
}