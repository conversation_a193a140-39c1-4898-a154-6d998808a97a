package com.xmhaibao.family.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import hb.common.helper.HostHelper

class FamilyPlazaDidiDanMuItemBean : IDoExtra {
    @SerializedName("left_avatar")
    var leftAvatar: String? = ""

    @SerializedName("right_avatar")
    var rightAvatar: String? = ""

    @SerializedName("content")
    var content: String = ""

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        leftAvatar = HostHelper.getAvatarHost().getWebpUrl_4_1(leftAvatar)
        rightAvatar = HostHelper.getAvatarHost().getWebpUrl_4_1(rightAvatar)
    }
}