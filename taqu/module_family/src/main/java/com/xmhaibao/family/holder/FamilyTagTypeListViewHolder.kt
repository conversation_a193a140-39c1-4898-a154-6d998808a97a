package com.xmhaibao.family.holder

import android.annotation.SuppressLint
import android.view.ViewGroup
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxItemDecoration
import com.google.android.flexbox.FlexboxLayoutManager
import com.xmhaibao.family.R
import com.xmhaibao.family.databinding.FamilyTagTypeListItemBinding
import com.xmhaibao.family.bean.FamilyTagInfo
import com.xmhaibao.family.model.FamilyTagTypeListBean
import com.xmhaibao.family.widget.familytag.provider.FamilyTagWallStyleProvider
import hb.drawable.shape.shape.ShapeBuilder
import hb.kotlin_extension.gone
import hb.kotlin_extension.isNotNull
import hb.kotlin_extension.visible
import hb.utils.SizeUtils
import hb.xadapter.XBaseAdapter
import hb.xadapter.XBaseViewHolder

/**
 * 家族-标签详情-每个标签类型列表
 * <AUTHOR>
 * @date 2024/6/20
 */
class FamilyTagTypeListViewHolder(parent: ViewGroup) :
    XBaseViewHolder<FamilyTagTypeListBean>(parent, R.layout.family_tag_type_list_item) {

    val binding by lazy {
        FamilyTagTypeListItemBinding.bind(itemView)
    }
    val adapter by lazy {
        XBaseAdapter(parent.context)
    }

    init {
        val layoutManager = FlexboxLayoutManager(itemView.context)
        layoutManager.flexDirection = FlexDirection.ROW
        layoutManager.flexWrap = FlexWrap.WRAP

        binding.recyclerViewTag.layoutManager = layoutManager
        binding.recyclerViewTag.addItemDecoration(FlexboxItemDecoration(itemView.context).apply {
            setDrawable(ShapeBuilder().size(0, SizeUtils.dp2px(8f)).create())
        })
        adapter.register(FamilyTagInfo::class.java, FamilyTagWallViewHolder::class.java)
        val styleProvider = FamilyTagWallStyleProvider()
        adapter.setOnViewHolderCreatedListener {
            if (it is FamilyTagWallViewHolder) {
                it.styleProvider = styleProvider
            }
        }
        binding.recyclerViewTag.adapter = adapter
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onBindView(item: FamilyTagTypeListBean?) {
        item?.run {
            binding.tvTitle.text = item.title
            if (subTitle.isNotNull() && subTitle.isNotEmpty()) {
                binding.tvSubTitle.text = item.subTitle
                binding.tvSubTitle.visible()
            } else {
                binding.tvSubTitle.gone()
            }
            adapter.items = item.tagList
            adapter.notifyDataSetChanged()
        }
    }

}