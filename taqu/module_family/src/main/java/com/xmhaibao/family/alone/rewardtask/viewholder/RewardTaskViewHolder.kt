package com.xmhaibao.family.alone.rewardtask.viewholder

import android.view.ViewGroup
import cn.taqu.lib.base.router.RouterLaunch
import com.xmhaibao.family.R
import com.xmhaibao.family.alone.rewardtask.model.TaskBean
import com.xmhaibao.family.databinding.FamilyRewardTaskItemBinding
import hb.common.helper.ServerTime
import hb.xadapter.XBaseViewHolder
import hb.xtoast.XToastUtils
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit
import kotlin.math.max

/**
 * 悬赏任务列表 viewHolder
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
class RewardTaskViewHolder(parent: ViewGroup) :
    XBaseViewHolder<TaskBean>(parent, com.xmhaibao.family.R.layout.family_reward_task_item) {

    val mBinding by lazy {
        FamilyRewardTaskItemBinding.bind(itemView)
    }

    /**
     * 倒计时任务
     */
    var mCountDownJob: Job? = null

    /**
     * 任务数据
     */
    var mTaskBean: TaskBean? = null

    /**
     * 剩余时间结束回调
     */
    var onRemindFinish: (() -> Unit)? = null

    /**
     * 关闭弹窗
     */
    var onCloseDialog: (() -> Unit)? = null

    override fun onBindView(item: TaskBean) {
        mTaskBean = item
        mBinding.ivTaskIcon.setImageFromUrl(item.icon)
        mBinding.tvPrice.text = item.giftPrice
        mBinding.tvTaskName.text = item.taskName
        mBinding.tvResidual.text = item.surplusSpan()
        mBinding.ivPublisherAvatar.setImageFromUrl(item.offerOdAvatar)
        mBinding.tvPublisherName.text = item.offerOdName
        mBinding.btnComplete.setImageFromResource(
            if (item.isComplete()) com.xmhaibao.family.R.drawable.family_reward_task_finished_btn else com.xmhaibao.family.R.drawable.family_reward_task_unfinished_btn
        )

        mBinding.btnComplete.setOnClickListener {
            if (!item.isComplete()) {
                RouterLaunch.dealJumpData(itemView.context, item.relation)
                onCloseDialog?.invoke()
            } else if(item.isCompleteNew()){
                XToastUtils.show("今日已经完成过该任务了，请明日再来")
            }
        }

    }

    /**
     * 开始倒计时
     */
    private fun startCountDown() {
        if (mCountDownJob?.isActive == true) return
        mCountDownJob = MainScope().launch {
            while (isActive) {
                mTaskBean?.let {
                    val remainingTime = max(it.expireTime - ServerTime.currentTimeMillis(), 0)
                    mBinding.tvCountDownTimer.text = formatMillisToTime(remainingTime)
                    if(remainingTime == 0L) {
                        // 重新刷新数据
                        onRemindFinish?.invoke()
                        // 直接切空页面，并没有走detachedWindow，手动cancel一下
                        cancel()
                    }
                }
                delay(1000)
            }
        }
    }

    override fun onViewAttachedToWindow() {
        super.onViewAttachedToWindow()
        startCountDown()
    }

    override fun onViewDetachedFromWindow() {
        super.onViewDetachedFromWindow()
        mCountDownJob?.cancel()
    }

    private fun formatMillisToTime(millis: Long): String {
        val hours = TimeUnit.MILLISECONDS.toHours(millis)
        val minutes = TimeUnit.MILLISECONDS.toMinutes(millis) % 60
        val seconds = TimeUnit.MILLISECONDS.toSeconds(millis) % 60
        return String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }
}