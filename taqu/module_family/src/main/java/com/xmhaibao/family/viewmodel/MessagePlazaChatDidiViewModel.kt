package com.xmhaibao.family.viewmodel

import android.app.Application
import androidx.lifecycle.viewModelScope
import cn.taqu.lib.base.bean.UiState
import cn.taqu.lib.okhttp.exception.BaseException
import com.xmhaibao.family.bean.FamilyPlazaDidiDanMuListBean
import com.xmhaibao.family.bean.FamilyPlazaDidiGoResultBean
import com.xmhaibao.family.repository.FamilyDidiRepository
import hb.common.xstatic.HttpParams
import hb.common.xstatic.viewmodel.BaseViewModel
import hb.xrequest.RequestException
import hb.xrequest.awaitNullable
import hb.xrequest.launchHttp
import hb.xtoast.XToastUtils
import kotlinx.coroutines.flow.MutableStateFlow

/**
 * 聊天广场-滴滴ViewModel
 * <AUTHOR>
 * @date 2024/8/39
 */
class MessagePlazaChatDidiViewModel(application: Application) : BaseViewModel(application) {
    val danMuListFlow = MutableStateFlow<UiState<FamilyPlazaDidiDanMuListBean>>(UiState.Empty)
    val didiGoResultFlow = MutableStateFlow<UiState<FamilyPlazaDidiGoResultBean>>(UiState.Empty)

    private val familyDidiRepository by lazy {
        FamilyDidiRepository()
    }

    fun getPlazaDidiListParams(): HttpParams {
        return familyDidiRepository.getPlazaDidiList().httpParams
    }

    fun getPlazaDanMuList() {
        viewModelScope.launchHttp({
            val familyPlazaDidiDanMuListBean =
                familyDidiRepository.getPlazaDanMuList().awaitNullable()
            if (familyPlazaDidiDanMuListBean == null) {
                danMuListFlow.emit(UiState.Failed(RequestException()))
            } else {
                danMuListFlow.emit(UiState.Success(familyPlazaDidiDanMuListBean))
            }
        }) {
            danMuListFlow.value = UiState.Failed(it)
            return@launchHttp true
        }
    }

    fun didiGo(targetAccountUuid: String,
               name: String) {
        viewModelScope.launchHttp({
            val familyPlazaDidiGoResultBean =
                familyDidiRepository.didiGo(targetAccountUuid).awaitNullable()
            if (familyPlazaDidiGoResultBean == null) {
                didiGoResultFlow.emit(UiState.Failed(RequestException()))
            } else {
                familyPlazaDidiGoResultBean.targetAccountUuid = targetAccountUuid
                didiGoResultFlow.emit(UiState.Success(familyPlazaDidiGoResultBean))

                XToastUtils.show("恭喜，你和${name}成为密友")
            }
        }) {
            if (it.code == BaseException.CODE_OTHER_ERROR) {
                didiGoResultFlow.value = UiState.Success(FamilyPlazaDidiGoResultBean().apply {
                    this.targetAccountUuid = targetAccountUuid
                })
            } else {
                didiGoResultFlow.value = UiState.Failed(it)
            }
            return@launchHttp false
        }
    }
}