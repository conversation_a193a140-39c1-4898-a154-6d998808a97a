package com.xmhaibao.game.dialog

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import com.xmhaibao.family.databinding.GameEntranceTicketReciverSuccessDialogBinding
import com.xmhaibao.game.model.GameFreeTicketResultBean
import dp
import hb.kotlin_extension.noneSyncLazy
import hb.utils.ScreenUtils
import hb.xstyle.xdialog.XLifecycleDialog

/**
 * @desc:游戏房领取门票成功弹窗
 *
 * <AUTHOR>
 * @date 2023/11/18
 */
class GameTicketReceiveSuccessDialog(context:Context,private val info: GameFreeTicketResultBean): XLifecycleDialog(context){

    private val binding:GameEntranceTicketReciverSuccessDialogBinding by noneSyncLazy {
        GameEntranceTicketReciverSuccessDialogBinding.inflate(layoutInflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        binding.tvTitle.text = info.title
        binding.tvSubTitle.text = info.desc
        binding.tvConfirm.setOnClickListener {
            dismiss()
        }
    }

    override fun onStart() {
        super.onStart()
        window?.setGravity(Gravity.CENTER)
    }

}