package com.xmhaibao.game.viewmodel

import android.app.Application
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.viewModelScope
import com.xmhaibao.game.helper.GameRoomCoreHelper
import com.xmhaibao.game.model.GameRoomState
import com.xmhaibao.game.model.sud.TeamScore
import hb.common.xstatic.viewmodel.BaseViewModel
import hb.game.api.HBGameHelper
import hb.kotlin_extension.isNotNull
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

/**
 * 消消消游戏界面ViewModel
 *
 * <AUTHOR>
 * @date 2023-12-17
 */
class GamePageViewModel(application: Application) : BaseViewModel(application) {


    private val _scoreFlow: MutableStateFlow<Long> = MutableStateFlow(0)
    val scoreFlow: StateFlow<Long> = _scoreFlow

    private val _targetScoreFlow: MutableStateFlow<Long> = MutableStateFlow(0)
    val targetScoreFlow: StateFlow<Long> = _targetScoreFlow

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        viewModelScope.launch {
            if (HBGameHelper.getGameViewModel() is XSudGameViewModel) {
                (HBGameHelper.getGameViewModel() as XSudGameViewModel).sourceFlow.combine(
                    GameRoomCoreHelper.getViewModel(GameRoomViewModel::class.java).gameRoomStateFlow
                ) { source, gameRoomState ->
                    if (gameRoomState is GameRoomState.Playing) {
                        // 当前是游戏中状态
                        var myTeamScore = 0L
                        var otherTeamScore = 0L
                        source?.scores?.forEach {
                            if (it.uid.equals(gameRoomState.ownerUuid) || it.uid.equals(
                                    gameRoomState.teammateUuid
                                )
                            ) {
                                // 己方分数
                                myTeamScore += it.score
                            } else {
                                otherTeamScore += it.score
                            }
                        }
                        return@combine TeamScore(myTeamScore = myTeamScore, enemyTeamScore = otherTeamScore)
                    }
                    null
                }.collectLatest {
                    it?.let {
                        _scoreFlow.value = it.myTeamScore
                        _targetScoreFlow.value = it.enemyTeamScore
                    }
                }
            }
        }
    }


    fun updateScore(score: Long) {
        _scoreFlow.value = score
    }

    fun updateTargetScore(score: Long) {
        _targetScoreFlow.value = score
    }
}