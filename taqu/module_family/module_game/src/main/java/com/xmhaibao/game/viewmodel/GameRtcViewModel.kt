package com.xmhaibao.game.viewmodel

import android.app.Application
import android.os.Bundle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.viewModelScope
import com.tencent.trtc.TRTCCloudDef
import com.tencent.trtc.TRTCCloudListener
import com.xmhaibao.game.constants.GameConstants
import com.xmhaibao.game.constants.RtcPublishStatus
import com.xmhaibao.game.constants.RtcRole
import com.xmhaibao.game.constants.RtcRoomState
import com.xmhaibao.game.helper.GameRoomCoreHelper
import com.xmhaibao.game.helper.GameTracker
import com.xmhaibao.game.helper.trtc.GameTRtcHelper
import com.xmhaibao.game.model.GameTRtcConfigBean
import com.xmhaibao.game.model.RtcUserVolumeBean
import com.xmhaibao.game.repository.GameTRtcRepository
import hb.common.xstatic.viewmodel.BaseViewModel
import hb.utils.Loger
import hb.utils.ToastUtils
import hb.xrequest.launchHttp
import hb.xtoast.XToastUtils
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.supervisorScope
import java.util.ArrayList

/**
 * 游戏房 Rtc ViewModel
 *
 * <AUTHOR>
 * @date 2023-11-21
 */
class GameRtcViewModel(application: Application) : BaseViewModel(application) {
    companion object {
        const val TAG = "GameRtcViewModel"

        /**
         * 成功code
         */
        const val SUCCESS_CODE = 0
    }

    /**
     * 麦克风状态
     */
    private val _microphoneStateFlow: MutableStateFlow<Boolean> = MutableStateFlow(false)
    val microphoneFlow: Flow<Boolean> = _microphoneStateFlow

    /**
     * 用户麦克风声音音量
     */
    private val _userVolumeFlow: MutableStateFlow<ArrayList<TRTCCloudDef.TRTCVolumeInfo>> =
        MutableStateFlow(arrayListOf())

    @FlowPreview
    val userVolumeFlow: Flow<MutableList<RtcUserVolumeBean>> = _userVolumeFlow.map {
        it.map { volumeInfo ->
            RtcUserVolumeBean(
                userId = volumeInfo.userId,
                volume = volumeInfo.volume
            )
        }.toMutableList()
    }

    /**
     * 是否已成功加入房间
     */
    private var isEnterRoom: RtcRoomState = RtcRoomState.Init

    /**
     * 当前角色
     * 只有客户端能直接操作角色，已客户端角色为准
     */
    private var mCurrentRole = RtcRole.Audience

    /**
     * 旁路推流id
     */
    private var streamId: String? = null

    /**
     * TRtc监听器
     */
    private val mTRtcCloudListener = object : TRTCCloudListener() {
        override fun onEnterRoom(result: Long) {
            super.onEnterRoom(result)
            if (result >= 0) {
                isEnterRoom = RtcRoomState.Entered
            }
        }

        override fun onExitRoom(reason: Int) {
            super.onExitRoom(reason)
            isEnterRoom = RtcRoomState.Exited
        }

        override fun onSwitchRole(errCode: Int, errMsg: String?) {
            super.onSwitchRole(errCode, errMsg)
            if (errCode >= 0) {
                mCurrentRole.switchSuccess()
            }
        }

        override fun onStartPublishCDNStream(err: Int, errMsg: String?) {
            super.onStartPublishCDNStream(err, errMsg)
            if (err == SUCCESS_CODE) {
                // 开始推流成功
                reportPublish(streamId, RtcPublishStatus.Publishing)
            }
        }

        override fun onStopPublishCDNStream(err: Int, errMsg: String?) {
            super.onStopPublishCDNStream(err, errMsg)
            if (err == SUCCESS_CODE) {
                // 开始推流成功
                reportPublish(streamId, RtcPublishStatus.PublishEnd)
            }
        }

        override fun onUserVoiceVolume(
            userVolumes: ArrayList<TRTCCloudDef.TRTCVolumeInfo>?,
            totalVolume: Int
        ) {
            super.onUserVoiceVolume(userVolumes, totalVolume)
            userVolumes?.let {
                _userVolumeFlow.value = it
            }
        }

        override fun onError(errCode: Int, errMsg: String?, extraInfo: Bundle?) {
            super.onError(errCode, errMsg, extraInfo)
            if (errCode < 0 && mCurrentRole == RtcRole.Anchor) {
                // 当前是主播，但是rtc报错
                XToastUtils.show("你的麦克风已掉线")
                switchRole(RtcRole.Audience) {
                    mCurrentRole = RtcRole.Audience
                }
            }
        }
    }

    /**
     * 加入房间
     */
    fun joinRoom() {
        GameTRtcHelper.addTRtcCloudListener(mTRtcCloudListener)
        viewModelScope.launch {
            joinTRtcRoom(GameRoomCoreHelper.currentRoom())
        }

        viewModelScope.launch {
            // 监听 rtc风控事件
            GameRoomCoreHelper.getViewModel(GameImViewModel::class.java).rtcRiskEventFlow.collectLatest {
                if (it?.isMute() == true) {
                    // 静音处罚
                    closeMicrophone()
                }
                if (it?.isCloseMic() == true) {
                    // 闭麦处罚
                    closeMicrophone(true)
                }
            }
        }
    }


    fun isOpenMicrophone(): Boolean {
        return _microphoneStateFlow.value
    }

    /**
     * 切换麦克风状态
     */
    fun switchMicrophone() {
        val isOpen = !_microphoneStateFlow.value
        if (isOpen) {
            // 打开麦克风
            openMicrophone()
        } else {
            // 关闭麦克风
            closeMicrophone()
        }
    }

    /**
     * 拉取TRtc token
     * @param roomId 房间id
     * @param role 角色 主播: 用于首次上麦  观众: 默认角色
     */
    private suspend fun fetchTRtcToken(
        roomId: String?, role: RtcRole = RtcRole.Audience
    ): GameTRtcConfigBean? {
        if (roomId.isNullOrEmpty()) {
            Loger.e(TAG, "拉取TRtc配置失败，RoomId为空")
            return null
        }
        var response: GameTRtcConfigBean? = null
        viewModelScope.launchHttp({
            response = GameTRtcRepository.fetTRtcEnterRoomConfig(roomId, role = role)
        }) {
            Loger.e(TAG, "拉取TRtc配置失败 ${it.responseMsg}")
            return@launchHttp false
        }.join()
        return response
    }

    /**
     * 加入 TRtc 房间
     */
    private suspend fun joinTRtcRoom(roomId: String?): Boolean {
        var isSuccess = false
        supervisorScope {
            launchHttp({
                val enterBean: GameTRtcConfigBean? = fetchTRtcToken(roomId)
                enterBean?.runCatching {
                    GameTRtcHelper.joinTRtcRoom(this)
                    isSuccess = true
                }?.onFailure {
                    ToastUtils.show(it.message)
                }
            }) {
                // 进房失败，重置麦克风状态
                ToastUtils.show(it.responseMsg)
                return@launchHttp true
            }.join()
        }
        return isSuccess
    }

    /**
     * 关闭麦克风流程
     * @param isDownMicrophone  是否是下麦，用于判断是否是强制下麦。true:强制下麦(触发风控) false:静音
     */
    private fun closeMicrophone(isDownMicrophone: Boolean = false) {
        if (isDownMicrophone) {
            // 切换到观众身份，并关闭旁路推流
            mCurrentRole = RtcRole.Audience
            switchRole(mCurrentRole) {
                switchMicrophoneSuccess(false)
            }
        } else {
            // 走静音流程
            GameTRtcHelper.switchMute(true)
            _microphoneStateFlow.value = false
            switchMicrophoneSuccess(false)
        }
    }

    /**
     * 打开麦克风流程
     */
    private fun openMicrophone() {
        // 先置为 true
        _microphoneStateFlow.value = true
        when (isEnterRoom) {
            RtcRoomState.Entered -> {
                // 已经加入房间，切换角色 or 解除静音
                if (mCurrentRole.isAnchor()) {
                    // 已经是主播，解除静音
                    GameTRtcHelper.switchMute(false)
                    _microphoneStateFlow.value = true
                    switchMicrophoneSuccess(true)
                } else {
                    // 切换至主播角色
                    switchRole(RtcRole.Anchor) {
                        switchMicrophoneSuccess(true)
                        mCurrentRole = RtcRole.Anchor
                    }
                }
            }

            else -> {
                // 未加入房间，尝试加入房间并切换角色至主播
                viewModelScope.launch {
                    val isJoin = joinTRtcRoom(GameRoomCoreHelper.currentRoom())
                    if (isJoin) {
                        switchRole(RtcRole.Anchor) {
                            switchMicrophoneSuccess(true)
                            mCurrentRole = RtcRole.Anchor
                        }
                    } else {
                        // 进房失败
                        _microphoneStateFlow.value = false
                    }
                }
            }
        }


    }

    /**
     * 切换角色
     * @param role 角色信息
     * @param switchCall 切换调用 -> 切换至主播会有异步情况，需要请求鉴权信息
     */
    private fun switchRole(role: RtcRole, switchCall: () -> Unit = {}) {
        when (role) {
            RtcRole.Anchor -> {
                // 主播
                viewModelScope.launchHttp({
                    val enterBean: GameTRtcConfigBean? =
                        fetchTRtcToken(GameRoomCoreHelper.currentRoom(), RtcRole.Anchor)
                    if (enterBean != null) {
                        streamId = enterBean.streamId
                        GameTRtcHelper.switchRole(isAnchor = true, enterBean)
                        switchCall()
                    } else {
                        _microphoneStateFlow.value = false
                    }
                })
            }

            RtcRole.Audience -> {
                // 观众
                GameTRtcHelper.switchRole(isAnchor = false)
                _microphoneStateFlow.value = false
                switchCall()
            }
        }
    }

    /**
     * 切换麦克风真正成功
     * - 切换麦克风状态时，会先切换ui状态，然后走角色切换等一系列操作，等真正切换成功以后弹出提示
     */
    private fun switchMicrophoneSuccess(isOpen: Boolean) {
        val microphoneTips = if (isOpen) "麦克风已开启" else "麦克风已关闭"
        XToastUtils.show(microphoneTips)
    }


    /**
     * 上报旁路推流
     */
    private fun reportPublish(streamId: String?, rtcPublishStatus: RtcPublishStatus) {
        viewModelScope.launch {
            var isSuccess = false
            repeat(3) {
                // 请求失败 重试3次，确保成功
                launchHttp({
                    GameTRtcRepository.reportTRtcAudioStreamPublishStatus(
                        roomUuid = GameRoomCoreHelper.currentRoom() ?: "",
                        streamId = streamId ?: "",
                        status = rtcPublishStatus
                    )
                    isSuccess = true
                }) {
                    Loger.d(
                        TAG,
                        "上报旁路推流失败 ${it.responseMsg} status:${rtcPublishStatus.statusValue}"
                    )
                    return@launchHttp false
                }.join()
                if (isSuccess) {
                    return@launch
                }
                delay(1000)
            }
        }
    }


    override fun onCleared() {
        super.onCleared()
        GameTRtcHelper.removeTRtcCloudListener(mTRtcCloudListener)
        GameTRtcHelper.leaveRoom()
    }

}