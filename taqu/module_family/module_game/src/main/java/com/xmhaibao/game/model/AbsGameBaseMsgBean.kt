package com.xmhaibao.game.model

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import hb.common.helper.HostHelper

/**
 * 消息基类
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
abstract class AbsGameBaseMsgBean : IDoExtra {

    @SerializedName("uuid")
    var uuid: String? = ""

    @SerializedName("avatar")
    var avatar: String? = ""

    @SerializedName("name")
    var name: String? = ""

    /**
     * 0系统 1用户发送（用于客户端 过滤是否自己发送）
     */
    @SerializedName("is_system")
    var systemMsg: String? = ""


    fun isSystemMsg(): Boolean {
        return "1" == systemMsg
    }

    fun isUserMsg(): Boolean {
        return "0" == systemMsg
    }

    fun setUserMsg() {
        systemMsg = "0"
    }

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        avatar = HostHelper.getAvatarHost().getWebpUrl_2_1(avatar)
    }

}