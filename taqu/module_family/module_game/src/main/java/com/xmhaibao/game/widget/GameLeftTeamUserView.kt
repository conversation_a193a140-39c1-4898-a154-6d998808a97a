package com.xmhaibao.game.widget

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintSet
import com.airbnb.lottie.LottieAnimationView
import com.xmhaibao.family.R
import com.xmhaibao.family.databinding.GameLeftUserLayoutBinding
import hb.drawable.shape.view.HbTextView
import hb.drawable.shape.view.HbView
import hb.utils.ColorUtils
import hb.utils.ScreenUtils
import hb.ximage.fresco.AvatarDraweeView
import hb.ximage.fresco.BaseDraweeView
import kotlin.math.roundToInt

/**
 * 游戏组队房间中，角色信息View --左侧
 *
 * <AUTHOR>
 * @date 2023-11-18
 */
class GameLeftTeamUserView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AbsGameTeamUserView(context, attrs, defStyleAttr) {

    private val view = LayoutInflater.from(context).inflate(R.layout.game_left_user_layout, this)
    private val binding: GameLeftUserLayoutBinding = GameLeftUserLayoutBinding.bind(view)

    init {
        getIvUser().setPlaceholderImage(ColorDrawable(ColorUtils.getColor(cn.taqu.lib.base.R.color.translucent)))
        getIvPlatform().setPlaceholderImage(ColorDrawable(ColorUtils.getColor(cn.taqu.lib.base.R.color.translucent)))
    }

    override fun getIvUser(): BaseDraweeView {
        return binding.ivUser
    }

    override fun getIvPlatform(): BaseDraweeView {
        return binding.ivPlatform
    }

    override fun getIvAvatar(): AvatarDraweeView {
        return binding.ivAvatar
    }

    override fun getTvHost(): HbTextView {
        return binding.tvHost
    }

    override fun getTvName(): TextView {
        return binding.tvName
    }


    override fun getReadyIv(): BaseDraweeView {
        return binding.ivReady
    }

    override fun getNullPlayerTv(): TextView {
        return binding.tvNullPlayer
    }

    override fun getAvatarBg(): View {
        return binding.bgUser
    }

    override fun getLottieVoice(): LottieAnimationView {
        return binding.lottieVoice
    }

    override fun getVoiceBg(): BaseDraweeView {
        return binding.ivVoiceBg
    }


}