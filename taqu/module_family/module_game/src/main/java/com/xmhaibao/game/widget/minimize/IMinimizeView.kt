package com.xmhaibao.game.widget.minimize

import android.view.View

/**
 * 最小化窗口视图接口
 *
 * <AUTHOR>
 * @date 2023-12-13
 */
interface IMinimizeView {

    /**
     * 获取当前视图的 X 轴位置
     */
    fun getLocationX(): Float

    /**
     * 获取当前视图的 Y 轴位置
     */
    fun getLocationY(): Float

    /**
     * 设置当前视图的 位置
     */
    fun changeViewLocation(x: Float, y: Float)

    /**
     * 获取当前视图
     */
    fun getView(): View

    /**
     * 释放资源
     */
    fun release()
}