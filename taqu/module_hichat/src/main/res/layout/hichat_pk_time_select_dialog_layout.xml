<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <hb.drawable.shape.view.HbView
        android:id="@+id/bgView"
        android:layout_width="0dp"
        android:layout_height="366dp"
        app:corner_top_left="16dp"
        app:corner_top_right="18dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:solid="@color/TH_Navy002" />

    <TextView
        android:id="@+id/tvCancel"
        android:layout_width="38dp"
        android:layout_height="30dp"
        android:layout_marginStart="7dp"
        android:layout_marginTop="7dp"
        android:gravity="center"
        android:text="取消"
        android:textColor="@color/TH_Gray600"
        android:textSize="@dimen/TH_FONT_B2"
        app:layout_constraintStart_toStartOf="@+id/bgView"
        app:layout_constraintTop_toTopOf="@+id/bgView" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="11dp"
        android:gravity="center"
        android:text="选择本轮PK时间"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_H3"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/bgView"
        app:layout_constraintStart_toStartOf="@+id/bgView"
        app:layout_constraintTop_toTopOf="@+id/bgView" />

    <TextView
        android:id="@+id/tvConfirm"
        android:layout_width="38dp"
        android:layout_height="30dp"
        android:layout_marginTop="7dp"
        android:layout_marginEnd="7dp"
        android:gravity="center"
        android:text="确定"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_B2"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/bgView"
        app:layout_constraintTop_toTopOf="@+id/bgView" />

    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="43.5dp"
        android:layout_marginEnd="12dp"
        android:background="@color/TH_Gray200"
        app:layout_constraintEnd_toEndOf="@+id/bgView"
        app:layout_constraintStart_toStartOf="@+id/bgView"
        app:layout_constraintTop_toTopOf="@+id/bgView" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvSelectTime"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="66dp"
        android:layout_marginEnd="12dp"
        android:gravity="center"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_B2"
        app:corner="45dp"
        app:layout_constraintEnd_toEndOf="@+id/bgView"
        app:layout_constraintStart_toStartOf="@+id/bgView"
        app:layout_constraintTop_toTopOf="@+id/bgView"
        app:solid="@color/TH_Navy110"
        tools:text="35分钟" />

    <cn.taqu.lib.base.widget.timepicker.view.WheelView
        android:id="@+id/minutePicker"
        android:layout_width="45dp"
        android:layout_height="200dp"
        android:layout_marginTop="12dp"
        app:layout_constraintEnd_toEndOf="@+id/bgView"
        app:layout_constraintStart_toStartOf="@+id/bgView"
        app:layout_constraintTop_toBottomOf="@+id/tvSelectTime"
        app:wheelview_textSize="@dimen/TH_FONT_D2" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="200dp"
        android:layout_marginTop="12dp"
        android:paddingTop="40dp"
        android:text="分钟"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_B2"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@+id/tvSelectTime"
        app:layout_constraintStart_toEndOf="@+id/minutePicker"/>


</androidx.constraintlayout.widget.ConstraintLayout>