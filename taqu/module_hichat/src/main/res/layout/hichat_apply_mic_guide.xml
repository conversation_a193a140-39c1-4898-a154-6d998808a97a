<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    tools:background="@color/black_alpha_50"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout"
    tools:ignore="MissingDefaultResource">


    <hb.drawable.shape.view.HbView
        app:layout_constraintTop_toTopOf="@+id/ivBg"
        app:layout_constraintEnd_toEndOf="@+id/ivBg"
        app:layout_constraintBottom_toBottomOf="@+id/ivBg"
        app:layout_constraintStart_toStartOf="@+id/ivBg"
        app:solid="@color/white"
        app:corner="12dp"
        android:layout_width="0dp"
        android:layout_height="0dp"/>

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivBg"
        android:layout_marginTop="8dp"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginStart="13dp"
        android:layout_marginEnd="13dp"
        app:actualImageScaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:placeholderImage="@color/transparent"
        app:roundedCornerRadius="12dp" />

    <hb.ximage.fresco.BaseDraweeView
        app:placeholderImage="@color/transparent"
        app:layout_constraintBottom_toBottomOf="@+id/ivBg"
        app:layout_constraintTop_toTopOf="@+id/ivBg"
        app:layout_constraintStart_toStartOf="@+id/ivBg"
        android:id="@+id/userAvatar"
        android:layout_marginStart="12dp"
        android:layout_width="40dp"
        android:layout_height="40dp"/>



    <TextView
        android:textStyle="bold"
        android:id="@+id/tvTips"
        android:layout_width="0dp"
        app:layout_constraintEnd_toStartOf="@id/btnAssist"
        android:layout_marginEnd="12dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/ivBg"
        app:layout_constraintBottom_toBottomOf="@+id/ivBg"
        app:layout_constraintStart_toEndOf="@+id/userAvatar"
        android:layout_marginStart="12dp"
        android:textSize="@dimen/TH_FONT_B3"
        android:textColor="@color/TH_Gray990"
        tools:text="上麦聊天，与大家一起互动起来吧～" />

    <TextView
        android:id="@+id/btnAssist"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/xstyle_btn_a_m_background"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:drawablePadding="2dp"
        android:text="申请上麦"
        android:textColor="@color/TH_Black100"
        android:textSize="@dimen/TH_FONT_B3"
        app:layout_constraintBottom_toBottomOf="@+id/ivBg"
        app:layout_constraintEnd_toEndOf="@+id/ivBg"
        app:layout_constraintTop_toTopOf="@+id/ivBg" />




    <ImageView
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/ivMicApplyGuideClose"
        android:layout_width="28dp"
        android:layout_height="16dp"
        android:paddingStart="6dp"
        android:paddingEnd="6dp"
        android:src="@drawable/hichat_apply_tips_close_ic"
        app:layout_constraintEnd_toEndOf="parent"/>

    <cn.taqu.lib.base.widget.CircleProgressBar
        android:id="@+id/pbMicApplyTimer"
        android:layout_width="16dp"
        android:layout_height="16dp"
        app:cpb_progress_bg_color="@color/TH_White100"
        app:cpb_progress_color="@color/transparent"
        app:cpb_progress_start_degree="-90"
        app:cpb_progress_width="1dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivMicApplyGuideClose"
        app:layout_constraintEnd_toEndOf="@+id/ivMicApplyGuideClose"
        app:layout_constraintStart_toStartOf="@+id/ivMicApplyGuideClose"
        app:layout_constraintTop_toTopOf="@+id/ivMicApplyGuideClose"
        tools:cpb_progress="40"
        tools:visibility="visible" />

</merge>