<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <hb.drawable.shape.view.HbView
        android:id="@+id/viewBg"
        android:layout_width="272dp"
        android:layout_height="0dp"
        android:layout_marginTop="86dp"
        android:layout_marginBottom="11dp"
        android:maxHeight="176dp"
        app:corner="23dp"
        app:gradient_color_end="#FF54A7"
        app:gradient_color_start="#5B31D9"
        app:gradient_linear_orientation="tl_br"
        app:layout_constraintBottom_toTopOf="@+id/ivClose"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivTop"
        app:stroke_color="@color/white_alpha_35"
        app:stroke_width="1dp" />

    <ImageView
        android:layout_width="139dp"
        android:layout_height="27dp"
        android:layout_marginTop="208dp"
        android:src="@drawable/hichat_pk_result_line_ic"
        app:layout_constraintEnd_toEndOf="@+id/ivTop"
        app:layout_constraintStart_toStartOf="@+id/ivTop"
        app:layout_constraintTop_toTopOf="@+id/ivTop" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="154dp"
        android:textColor="@color/white"
        android:textSize="@dimen/TH_FONT_H3"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/ivTop"
        app:layout_constraintStart_toStartOf="@+id/ivTop"
        app:layout_constraintTop_toTopOf="@+id/ivTop"
        tools:text="恭喜红方获胜！" />

    <TextView
        android:id="@+id/tvStar"
        android:layout_width="wrap_content"
        android:layout_height="62dp"
        android:layout_marginTop="169dp"
        android:drawableLeft="@drawable/hichat_star_pk_result_ic"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="44sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/ivTop"
        app:layout_constraintStart_toStartOf="@+id/ivTop"
        app:layout_constraintTop_toTopOf="@+id/ivTop"
        tools:text="23042" />

    <TextView
        android:id="@+id/tvStarHint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="230dp"
        android:text="本轮获得星星数"
        android:textColor="@color/white"
        android:textSize="@dimen/TH_FONT_B4"
        app:layout_constraintEnd_toEndOf="@+id/ivTop"
        app:layout_constraintStart_toStartOf="@+id/ivTop"
        app:layout_constraintTop_toTopOf="@+id/ivTop" />

    <hb.drawable.shape.view.HbRecycleView
        android:id="@+id/recyclerView"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginTop="255dp"
        android:maxHeight="176dp"
        app:layout_constraintHeight_default="wrap"
        app:layout_constraintHeight_max="176dp"
        android:padding="12dp"
        app:corner="16dp"
        app:layout_constraintEnd_toEndOf="@+id/ivTop"
        app:layout_constraintStart_toStartOf="@+id/ivTop"
        app:layout_constraintTop_toTopOf="@+id/ivTop"
        app:solid="@color/white_alpha_30"
        app:stroke_color="@color/white_alpha_30"
        app:stroke_width="1dp" />


    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivSpot"
        android:layout_width="272dp"
        android:layout_height="168dp"
        android:layout_marginTop="1dp"
        app:layout_constraintEnd_toEndOf="@+id/viewBg"
        app:layout_constraintStart_toStartOf="@+id/viewBg"
        app:layout_constraintTop_toTopOf="@+id/viewBg"
        app:placeholderImage="@color/alpha"
        app:roundTopLeft="true"
        app:roundTopRight="true"
        app:roundedCornerRadius="23dp" />


    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivTop"
        android:layout_width="270dp"
        android:layout_height="223dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:placeholderImage="@color/alpha" />


    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_marginTop="19dp"
        android:padding="5dp"
        android:src="@drawable/mybean_recharge_first_opt_close_ic"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/recyclerView" />


</androidx.constraintlayout.widget.ConstraintLayout>