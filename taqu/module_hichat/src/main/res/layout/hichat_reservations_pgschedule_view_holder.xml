<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="124dp"
    android:layout_marginStart="19dp"
    android:layout_marginEnd="12dp">

    <View
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:background="@color/TH_Gray300"
        app:layout_constraintStart_toStartOf="parent" />

    <hb.drawable.shape.view.HbView
        android:id="@+id/bgView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="9dp"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp"
        app:corner="8dp"
        app:solid="@color/TH_Navy001" />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivCover"
        android:layout_width="74dp"
        android:layout_height="100dp"
        android:layout_marginStart="8dp"
        app:layout_constraintBottom_toBottomOf="@+id/bgView"
        app:layout_constraintStart_toStartOf="@+id/bgView"
        app:layout_constraintTop_toTopOf="@+id/bgView"
        app:placeholderImage="@color/alpha"
        app:roundedCornerRadius="4dp" />


    <hb.drawable.shape.view.HbView
        android:id="@+id/bgCoverBottom"
        android:layout_width="74dp"
        android:layout_height="17dp"
        app:corner_bottom_left="4dp"
        app:corner_bottom_right="4dp"
        app:gradient_color_end="#b2000000"
        app:gradient_color_start="#00000000"
        app:gradient_linear_orientation="top_bottom"
        app:layout_constraintBottom_toBottomOf="@+id/ivCover"
        app:layout_constraintEnd_toEndOf="@+id/ivCover"
        app:layout_constraintStart_toStartOf="@+id/ivCover" />

    <hb.drawable.shape.view.HbView
        android:id="@+id/hintView"
        android:layout_width="4dp"
        android:layout_height="4dp"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="2dp"
        app:corner="4dp"
        app:layout_constraintEnd_toStartOf="@+id/tvState"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@+id/bgCoverBottom"
        app:layout_constraintTop_toTopOf="@+id/bgCoverBottom"
        app:solid="@color/TH_Green600" />

    <TextView
        android:id="@+id/tvState"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="进行中"
        android:textColor="@color/TH_Navy001"
        android:textSize="@dimen/t8"
        app:layout_constraintBottom_toBottomOf="@+id/bgCoverBottom"
        app:layout_constraintEnd_toEndOf="@+id/bgCoverBottom"
        app:layout_constraintStart_toEndOf="@+id/hintView"
        app:layout_constraintTop_toTopOf="@+id/bgCoverBottom" />


    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvType"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingStart="2dp"
        android:paddingEnd="2dp"
        android:maxWidth="64dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginTop="2dp"
        android:layout_marginStart="2dp"
        android:textColor="@color/TH_Navy001_Normal"
        android:textSize="8sp"
        app:corner="2dp"
        app:layout_constraintStart_toStartOf="@+id/ivCover"
        app:layout_constraintTop_toTopOf="@+id/ivCover"
        app:solid="@color/black_alpha_70"
        tools:text="唱歌K歌" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_B1"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/bgView"
        app:layout_constraintStart_toEndOf="@+id/ivCover"
        app:layout_constraintTop_toTopOf="@+id/bgView"
        tools:text="节目名称节目名称节目名称节目名称节目名称目名称节目名称目名称节目名称目名称节目名称目名称节目名称" />

    <TextView
        android:id="@+id/tvTime"
        android:layout_width="wrap_content"
        android:layout_height="15dp"
        android:layout_marginBottom="7dp"
        android:gravity="center_vertical"
        android:textColor="@color/TH_Gray600"
        android:textSize="@dimen/TH_FONT_N1"
        app:layout_constraintStart_toStartOf="@+id/tvTitle"
        app:layout_constraintBottom_toTopOf="@+id/avatarListView"
        tools:text="15:00-16:00" />

    <cn.taqu.lib.base.widget.AvatarListView
        android:id="@+id/avatarListView"
        android:layout_width="80dp"
        android:layout_height="24dp"
        android:layout_marginBottom="12dp"
        app:avatarRadius="12dp"
        app:layout_constraintStart_toStartOf="@+id/tvTime"
        app:layout_constraintBottom_toBottomOf="@+id/bgView"
        app:leftMargin="4dp" />

    <TextView
        android:id="@+id/tvCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/TH_Orange600"
        android:textSize="@dimen/TH_FONT_N1"
        app:layout_constraintBottom_toBottomOf="@+id/tvCountSuffix"
        app:layout_constraintEnd_toStartOf="@+id/tvCountSuffix"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@+id/btnOption"
        tools:text="9999999" />

    <TextView
        android:id="@+id/tvCountSuffix"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:textColor="@color/TH_Gray600"
        android:textSize="@dimen/TH_FONT_N2"
        app:layout_constraintBottom_toBottomOf="@+id/bgView"
        app:layout_constraintEnd_toEndOf="@+id/btnOption"
        app:layout_constraintStart_toEndOf="@+id/tvCount"
        tools:text="人围观中" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/btnOption"
        android:layout_width="92dp"
        android:layout_height="32dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="12dp"
        android:gravity="center"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_N1"
        android:textStyle="bold"
        app:corner="16dp"
        app:layout_constraintBottom_toTopOf="@+id/holder"
        app:layout_constraintEnd_toEndOf="@+id/bgView"
        app:solid="@color/TH_Yellow600"
        tools:text="去开启" />

    <View
        android:id="@+id/holder"
        android:layout_width="1dp"
        android:layout_height="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/bgView"
        app:layout_constraintEnd_toEndOf="@+id/bgView" />

</androidx.constraintlayout.widget.ConstraintLayout>