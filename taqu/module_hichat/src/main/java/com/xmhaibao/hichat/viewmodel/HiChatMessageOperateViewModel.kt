package com.xmhaibao.hichat.viewmodel

import android.app.Application
import androidx.lifecycle.viewModelScope
import com.xmhaibao.hichat.bean.message.HiChatMessageBean
import com.xmhaibao.hichat.bean.message.HiChatMessageVoiceBean
import com.xmhaibao.hichat.constant.HiChatMessageConstant
import com.xmhaibao.hichat.helper.HiChatRoomCoreHelper
import com.xmhaibao.hichat.repository.HiChatRepository
import hb.common.xstatic.viewmodel.BaseViewModel
import hb.xrequest.launchHttp
import hb.xstyle.xdialog.XLoadingDialog
import hb.xthread.XThreadPool
import kotlinx.coroutines.Job
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * 畅聊圈消息操作 - 相关转发
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
class HiChatMessageOperateViewModel(application: Application) : BaseViewModel(application) {

    sealed class VoiceToTextIntent(val msgId: String) {
        object NONE : VoiceToTextIntent(msgId = "-1")

        /**
         * 加载中
         */
        class Loading(msgId: String) : VoiceToTextIntent(msgId = msgId)

        /**
         * 转换成功
         */
        class TransformSuccess(
            msgId: String, val transText: String
        ) : VoiceToTextIntent(msgId = msgId)
    }

    private val repository by lazy {
        HiChatRepository()
    }

    private val _voice2TextFlow = MutableStateFlow<VoiceToTextIntent>(VoiceToTextIntent.NONE)
    val voice2TextFlow: Flow<VoiceToTextIntent> = _voice2TextFlow

    /**
     * 语音转文字 任务
     */
    private var mTransJob: Job? = null

    private var mIsCancel = false

    /**
     * 消息相关ViewModel
     */
    private val imViewModel by lazy {
        HiChatRoomCoreHelper.getViewModel(HiChatRoomSocketViewModel::class.java)
    }


    /**
     * 进行音频转文字
     */
    fun voiceToText(message: HiChatMessageBean?) {
        if (message?.contentType != HiChatMessageConstant.CONTENT_TYPE_VOICE) return
        mTransJob?.cancel()
        mTransJob = viewModelScope.launch(XThreadPool.IO().get().asCoroutineDispatcher()) {
            voiceToText(message, imViewModel.currentMessageList())
        }
        mTransJob?.invokeOnCompletion {
            _voice2TextFlow.value = VoiceToTextIntent.NONE
            mIsCancel = false
        }
    }

    /**
     * 取消语音转文字
     */
    fun cancelVoiceToText() {
        // 当前如果有正在转的，不取消，而是取消自动下一次的动作
        mIsCancel = true
//        mTransJob?.cancel()
    }

    private suspend fun voiceToText(
        message: HiChatMessageBean,
        messageList: List<HiChatMessageBean>?
    ) {
        if (mIsCancel) {
            mTransJob?.cancel()
            return
        }
        // 开始转文字
        _voice2TextFlow.value = VoiceToTextIntent.Loading(message.msgId)
        // 如果已经有转换过，直接返回转换过的文字
        val content = message.contentInfo as? HiChatMessageVoiceBean
        val transTxt: String? = transVoice2Text(content, message.msgId)
        // 是否是未读语音
        val isUnPlayed = content?.isUnPlay()
        content?.voiceText = transTxt
        content?.isShowVoiceTxt = !transTxt.isNullOrEmpty()
        content?.updatePlayed()
        _voice2TextFlow.value = VoiceToTextIntent.TransformSuccess(
            msgId = message.msgId,
            transText = transTxt ?: ""
        )
        delay(20)
        if (transTxt == null) {
            // 转换失败，不继续转文字
            return
        }
        // 如果当前是未播语音，则继续转换
        if (isUnPlayed == true) {
            // 查看下一条语音消息是不是未读，如果是，则继续转文字。
            val nextMessage = messageList?.getOrNull(messageList.indexOf(message) + 1)
            if (nextMessage != null) {
                if (nextMessage.contentType != HiChatMessageConstant.CONTENT_TYPE_VOICE) return
                val nextContent = nextMessage.contentInfo as? HiChatMessageVoiceBean
                if (nextContent?.isUnPlay() == true) {
                    // 继续转文字
                    voiceToText(nextMessage, messageList)
                }
            } else {
                // 如果当前语音消息是列表中最后一条，则再取一次快照，判断当前消息在新的快照下一条语音消息是否未读
                val newMessageList = imViewModel.currentMessageList()
                val newNextMessage = newMessageList.getOrNull(newMessageList.indexOf(message) + 1)
                if (newNextMessage != null && newNextMessage.contentType == HiChatMessageConstant.CONTENT_TYPE_VOICE) {
                    val newNextContent = newNextMessage.contentInfo as? HiChatMessageVoiceBean
                    if (newNextContent?.isUnPlay() == true) {
                        // 继续转文字
                        voiceToText(newNextMessage, newMessageList)
                    }
                }
            }
        }
    }


    /**
     * 获取下一条播放语音消息
     */
    fun getAutoPlayNextVoiceMessage(messageBean: HiChatMessageBean?): HiChatMessageBean? {
        if (messageBean?.contentType != HiChatMessageConstant.CONTENT_TYPE_VOICE) return null
        val messageList = imViewModel.currentMessageList()
        val nextMessage = messageList.getOrNull(messageList.indexOf(messageBean) + 1)
        if (nextMessage != null && nextMessage.contentType == HiChatMessageConstant.CONTENT_TYPE_VOICE) {
            val nextContent = nextMessage.contentInfo as? HiChatMessageVoiceBean
            if (nextContent?.isUnPlay() == true) {
                // 继续播放
                return nextMessage
            }
        }

        return null
    }


    private suspend fun transVoice2Text(
        messageContent: HiChatMessageVoiceBean?,
        msgId: String?
    ): String? {
        var transText: String? = null
        viewModelScope.launchHttp({
            delay(1000)
            transText = if (messageContent?.voiceText?.isNotEmpty() == true) {
                messageContent.voiceText
            } else {
                val hiChatId = HiChatRoomCoreHelper.currentRoomUuid()
                val response = repository.getVoiceText(hiChatId, msgId)
                response?.messageTxt
            }
        }) {
            _voice2TextFlow.value = VoiceToTextIntent.NONE
            return@launchHttp false
        }.join()

        return transText
    }
}