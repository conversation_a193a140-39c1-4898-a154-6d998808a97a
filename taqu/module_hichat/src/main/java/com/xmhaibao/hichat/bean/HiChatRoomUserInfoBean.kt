package com.xmhaibao.hichat.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import com.xmhaibao.imganim.bean.XImageAnimAvatarDressInfoBean
import hb.common.helper.HostHelper
import hb.xadapter.diffutil.XDiffUtilBean

/**
 *
 * 畅聊圈用户模型bean

 * <AUTHOR>
 * @date 2025-01-20
 */
open class HiChatRoomUserInfoBean : IDoExtra, XDiffUtilBean {
    @SerializedName("avatar")
    var avatar: String? = null

    @SerializedName("name")
    var name: String? = null

    @SerializedName("uuid")
    var uuid: String? = null

    @SerializedName("sex_type")
    var sexType: String? = null

    @SerializedName("age")
    var age: String? = null

    /**
     * 当前用户麦序
     */
    @SerializedName("index")
    var index: String? = null

    /**
     * 当前用户麦序
     */
    @SerializedName("mic_style")
    var micStyle: HiChatMicStyleBean? = null

    /**
     * 头像框
     */
    @SerializedName("avatar_frame_info")
    var avatarFrameInfo: XImageAnimAvatarDressInfoBean? = null

    /**
     * 是否在麦上
     * 0: 不在麦上
     * 1: 在麦上
     */
    @SerializedName("on_mic_seat")
    var onMicSeat: String? = "0"


    /**
     * 用户角色
     * @see com.xmhaibao.hichat.constant.HiChatRoleConstant
     */
    var role: String? = null

    /**
     * 用户标签
     */
    @SerializedName("tag_icon")
    var tagIcon: String? = null


    /**
     * 用户是否被选中
     * - 礼物面板使用
     */
    var isSelected = false


    /**
     * 当前是否在麦上
     */
    fun isOnMicSeat(): Boolean {
        return onMicSeat == "1"
    }


    override fun equals(other: Any?): Boolean {
        if (other is HiChatRoomUserInfoBean) {
            return other.diffContent == diffContent
        }
        return false
    }

    override fun hashCode(): Int {
        return diffContent.hashCode()
    }

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        avatar = HostHelper.getAvatarHost().getWebpUrl_4_1(avatar)
        avatarFrameInfo?.doExtra(response)
        micStyle?.doExtra(response)
        tagIcon = HostHelper.getImageDefaultHost().getWebpUrl_3_1(tagIcon)
    }

    override fun getDiffId(): String {
       return "$uuid"
    }

    override fun getDiffContent(): String {
        return "${uuid}:${index}:${role}:${avatar}:${avatarFrameInfo?.lottieUrl}:${onMicSeat}:${micStyle?.hexColor}:${tagIcon}"
    }
}