package com.xmhaibao.hichat.widget

import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView

/**
 * pk结果 GridLayoutManager
 * 实现最后一行居中效果
 * <AUTHOR>
 * @date 2025/7/21
 */
class HiChatPkResultGridLayoutManager(
    private val spanCount: Int, // 每行列数（如5）
    private val horizontalSpacing: Int, // item水平间距（dp转px）
    private val verticalSpacing: Int //  item垂直间距（dp转px）
) : RecyclerView.LayoutManager() {

    private var itemWidth = 0 // item宽度（px）
    private var itemHeight = 0 // item高度（px）
    private var totalRows = 0 // 总行数
    private var verticalOffset = 0 // 垂直滚动偏移量

    override fun generateDefaultLayoutParams(): RecyclerView.LayoutParams {
        return RecyclerView.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onMeasure(
        recycler: RecyclerView.Recycler,
        state: RecyclerView.State,
        widthSpec: Int,
        heightSpec: Int
    ) {
        super.onMeasure(recycler, state, widthSpec, heightSpec)
        if (state.itemCount == 0) {
            setMeasuredDimension(0, 0)
            return
        }

        // 测量第一个item的大小
        val firstChild = recycler.getViewForPosition(0)
        measureChild(firstChild, widthSpec, heightSpec)
        itemWidth = firstChild.measuredWidth
        itemHeight = firstChild.measuredHeight

        // 计算总行数
        totalRows = (state.itemCount + spanCount - 1) / spanCount

        // 计算宽度
        val width = when (View.MeasureSpec.getMode(widthSpec)) {
            View.MeasureSpec.EXACTLY -> View.MeasureSpec.getSize(widthSpec)
            else -> paddingLeft + paddingRight + spanCount * itemWidth + (spanCount - 1) * horizontalSpacing
        }

        // 计算高度
        val height = when (View.MeasureSpec.getMode(heightSpec)) {
            View.MeasureSpec.EXACTLY -> View.MeasureSpec.getSize(heightSpec)
            else -> paddingTop + paddingBottom + totalRows * itemHeight + (totalRows - 1) * verticalSpacing
        }

        // 设置测量大小
        setMeasuredDimension(width, height)
    }

    override fun onLayoutChildren(recycler: RecyclerView.Recycler, state: RecyclerView.State) {
        if (state.itemCount == 0) {
            detachAndScrapAttachedViews(recycler)
            return
        }

        // 回收所有可见item（复用优化）
        detachAndScrapAttachedViews(recycler)

        // 遍历所有item，计算位置并布局
        for (position in 0 until state.itemCount) {
            val view = recycler.getViewForPosition(position)
            addView(view)
            measureChild(view, 0, 0) // 再次测量（确保item大小正确）

            // 计算行号和列号
            val row = position / spanCount
            val column = position % spanCount

            // 计算左边距（核心：最后一行居中）
            val left = if (row == totalRows - 1) {
                val remain = state.itemCount % spanCount
                val currentSpan = if (remain == 0) spanCount else remain
                val lastRowWidth = currentSpan * itemWidth + (currentSpan - 1) * horizontalSpacing
                val recyclerViewWidth = width - paddingLeft - paddingRight
                val offset = (recyclerViewWidth - lastRowWidth) / 2 // 居中偏移量
                paddingLeft + offset + column * (itemWidth + horizontalSpacing)
            } else {
                // 前面的行：左对齐
                paddingLeft + column * (itemWidth + horizontalSpacing)
            }

            // 计算上边距（行号×行高 + 垂直间距）
            val top = paddingTop + row * (itemHeight + verticalSpacing)

            // 布局item（left, top, right, bottom）
            layoutDecorated(view, left, top, left + itemWidth, top + itemHeight)
        }
    }

    // 支持垂直滚动
    override fun canScrollVertically(): Boolean = true

    override fun scrollVerticallyBy(
        dy: Int,
        recycler: RecyclerView.Recycler,
        state: RecyclerView.State
    ): Int {
        if (childCount == 0 || dy == 0) return 0

        val availableSpace = height - paddingTop - paddingBottom
        val totalHeight = totalRows * (itemHeight + verticalSpacing) - verticalSpacing
        val maxScroll = totalHeight - availableSpace

        var newVerticalOffset = verticalOffset + dy
        newVerticalOffset = newVerticalOffset.coerceIn(0, maxScroll)

        val delta = newVerticalOffset - verticalOffset
        verticalOffset = newVerticalOffset

        offsetChildrenVertical(-delta)
        return delta
    }
}