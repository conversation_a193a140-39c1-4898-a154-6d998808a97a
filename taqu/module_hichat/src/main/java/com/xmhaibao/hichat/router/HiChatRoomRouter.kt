package com.xmhaibao.hichat.router

import cn.taqu.lib.base.constants.PushConstants
import com.alibaba.android.arouter.launcher.ARouter

/**
 *
 * 聊天室路由页面

 * <AUTHOR>
 * @date 2025-01-21
 */
object HiChatRoomRouter {

    /**
     * 搜索页面
     */
    fun launchHiChatSearch(source: String?) {
        ARouter.getInstance()
            .build(HiChatRouterPath.HI_CHAT_ROOM_SEARCH)
            .withString(PushConstants.PUSH_TYPE_SOURCE, source)
            .navigation()
    }


    /**
     * 大厅页面
     */
    fun launchHiChatHall() {
        ARouter.getInstance()
            .build(HiChatRouterPath.HI_CHAT_ROOM_HALL)
            .navigation()
    }

    /**
     * 节目单列表大厅页面
     */
    fun launchHiChatPgScheduleHall() {
        ARouter.getInstance()
            .build(HiChatRouterPath.HI_CHAT_PG_SCHEDULE_HALL)
            .navigation()
    }

    /**
     * 节目单列表-我的预约
     */
    fun launchHiChatPgScheduleMyMyReservations() {
        ARouter.getInstance()
            .build(HiChatRouterPath.HI_CHAT_PG_SCHEDULE_MY_RESERVATIONS)
            .navigation()
    }


    /**
     * 创建房间
     */
    fun launchHiChatRoomCreate(source: String?) {
        ARouter.getInstance()
            .build(HiChatRouterPath.HI_CHAT_ROOM_CREATE)
            .withString(PushConstants.PUSH_TYPE_SOURCE, source)
            .navigation()
    }
}