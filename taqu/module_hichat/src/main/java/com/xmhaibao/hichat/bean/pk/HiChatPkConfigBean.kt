package com.xmhaibao.hichat.bean.pk

import com.google.gson.annotations.SerializedName


/**
 * 畅聊圈 pk配置模型
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
data class HiChatPkConfigBean(
    /**
     * 单次延长的时长
     */
    @SerializedName("delay_time")
    var delayTime: String? = "", // 10
    /**
     * 多人模式 人数上限
     */
    @SerializedName("pk_multiple_member_limit")
    var pkMultipleMemberLimit: String? = "", // 20
    /**
     * 组队模式 人数上限
     */
    @SerializedName("pk_team_member_limit")
    var pkTeamMemberLimit: String? = "", // 10
    /**
     * pk设置单局时长类别 配置下发
     */
    @SerializedName("round_time_list")
    var roundTimeList: List<String>? = listOf()
) {
    companion object {
        /**
         * 获取默认配置
         */
        fun getDefaultConfig(): HiChatPkConfigBean {
            return HiChatPkConfigBean(
                delayTime = "10",
                pkMultipleMemberLimit = "20",
                pkTeamMemberLimit = "10",
                roundTimeList = listOf("20", "25", "30", "35", "40", "45","50")
            )
        }
    }
}
