package com.xmhaibao.hichat.viewholder.member

import android.view.View
import android.view.ViewGroup
import androidx.annotation.NonNull
import androidx.core.view.isVisible
import cn.taqu.lib.base.utils.SexTypeUtils
import com.xmhaibao.hichat.R
import com.xmhaibao.hichat.bean.HiChatRoomUserInfoBean
import com.xmhaibao.hichat.constant.HiChatRoleConstant
import com.xmhaibao.hichat.databinding.HichatSendUserSelectItemBinding
import hb.utils.ColorUtils
import hb.utils.StringUtils
import hb.xadapter.XBaseViewHolder

/**
 * 畅聊圈选择在线成员viewHolder
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
class HiChatSendUserSelectItemViewHolder(@NonNull parent: ViewGroup) :
    XBaseViewHolder<HiChatRoomUserInfoBean>(parent, R.layout.hichat_send_user_select_item) {
    private val mBinding = HichatSendUserSelectItemBinding.bind(itemView)
    private var mItemData: HiChatRoomUserInfoBean? = null

    var mUserClickCallBack: ((HiChatRoomUserInfoBean?) -> Unit)? = null

    /**
     * 是否多选
     */
    var isMultiChoice: Boolean = false

    init {
        itemView.setOnClickListener {
            mUserClickCallBack?.invoke(mItemData)
        }
    }
    /**
     * 圈主标识背景色
     */
    private val masterBgSeatColor = ColorUtils.getColor(hb.xstyle.R.color.TH_Purple600)
    /**
     * 管理标识背景色
     */
    private val managerBgSeatColor = ColorUtils.getColor(hb.xstyle.R.color.TH_Blue600)


    override fun onBindView(item: HiChatRoomUserInfoBean?) {
        mItemData = item
        mBinding.apply {
            if (HiChatRoleConstant.isMaster(item?.role)) {
                tvMasterTag.isVisible = true
                tvMasterTag.text = "圈主"
                tvMasterTag.shaper().solid(masterBgSeatColor)
            } else if (HiChatRoleConstant.isManager(item?.role)) {
                tvMasterTag.isVisible = true
                tvMasterTag.text = "管理"
                tvMasterTag.shaper().solid(managerBgSeatColor)
            } else {
                tvMasterTag.isVisible = false
            }
            tvNickName.text = item?.name
            userAvatar.setImageFromUrl(item?.avatar)
            val sexRes = if (SexTypeUtils.isSexTypeBoy(item?.sexType)) {
                cn.taqu.lib.base.R.drawable.ic_sex_male
            } else {
                cn.taqu.lib.base.R.drawable.ic_sex_new_female
            }
            tvSexAndAge.setCompoundDrawablesRelativeWithIntrinsicBounds(sexRes, 0, 0, 0)
            tvSexAndAge.text = if (StringUtils.stringToInt(item?.age) == 0) {
                ""
            } else {
                "·".plus(item?.age)
            }
            setSelectView()
        }
    }

    /**
     * 多选按钮
     */
    private fun setSelectView() {
        if (isMultiChoice) {
            mItemData?.let {
                mBinding.ivChoice.visibility = View.VISIBLE
                if (it.isSelected) {
                    //选中
                    mBinding.ivChoice.setImageResource(R.drawable.hichat_16_checked)
                } else {
                    mBinding.ivChoice.setImageResource(R.drawable.hichat_16_check_n)
                }
            }
        } else {
            mBinding.ivChoice.visibility = View.GONE
        }
    }

}