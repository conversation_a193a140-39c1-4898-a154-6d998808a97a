package com.xmhaibao.hichat.viewholder.message.slot

import android.view.View
import android.view.ViewGroup
import android.view.View.OnLongClickListener
import androidx.core.view.children
import com.xmhaibao.hichat.R
import com.xmhaibao.hichat.bean.message.HiChatMessageBean
import com.xmhaibao.hichat.bean.message.HiChatMessageVoiceBean
import com.xmhaibao.hichat.bean.message.IHiChatMessageCopy
import com.xmhaibao.hichat.bean.message.IHiChatReplyMessage
import com.xmhaibao.hichat.constant.HiChatMessageConstant
import com.xmhaibao.hichat.helper.HiChatRoomCoreHelper
import com.xmhaibao.hichat.track.HiChatRoomTrack
import com.xmhaibao.hichat.utils.hiChatMessageTypeStr
import com.xmhaibao.hichat.viewholder.message.HiChatMessageSlotType
import com.xmhaibao.hichat.viewholder.message.IHiChatMessageProvider
import com.xmhaibao.hichat.viewholder.message.options.HiChatOperatePopWindow
import com.xmhaibao.hichat.viewholder.message.options.OperateType
import hb.common.data.AccountHelper
import hb.kotlin_extension.noneSyncLazy
import hb.logupload.XLogUpload
import hb.utils.AppUtils
import hb.utils.ClipboardManagerUtils
import hb.xadapter.XBaseViewHolder
import hb.xtoast.XToastUtils

/**
 * 畅聊圈消息- 长按功能菜单插槽
 *
 * <AUTHOR>
 * @date 2025-03-31
 */
class HiChatMessageOptionSlot(private val slotType: HiChatMessageSlotType.LongPressMenu) :
    IHiChatMessageProvider<HiChatMessageSlotType.LongPressMenu> {

    /**
     * 语音转文字回调
     */
    var onVoiceTextCallback: ((HiChatMessageBean) -> Unit)? = null

    /**
     * at操作回调
     * @param accountUuid 账号uuid
     * @param accountName 账号名称
     */
    var onAtOptionCallback: ((String, String) -> Unit)? = null

    /**
     * 消息回复操作回调
     */
    var onReplyOptionCallback: ((HiChatMessageBean) -> Unit)? = null

    val mTracker by noneSyncLazy { HiChatRoomTrack() }

    override fun onAttachToWindow(itemView: View) {
        // do-nothing
    }

    override fun onDetachFromWindow(itemView: View) {
    }

    override fun onBindContentView(
        holder: XBaseViewHolder<*>?,
        itemView: View,
        messageBean: HiChatMessageBean
    ) {
        itemView.findViewById<View>(R.id.messageContentLayer)?.let { contentView ->
            // 遍历子View存在点击事件并且可点击且没有长按事件，则补充长按事件。
            // 不然会存在，设置了点击事件的区域会无法触发 contentView 的长按。

            // 设置父View的长按事件
            val longClickListener = OnLongClickListener { v ->
                val options = buildOperateTypes(messageBean)
                if (options.isEmpty()) return@OnLongClickListener false
                val operateWindow =
                    HiChatOperatePopWindow(itemView.context, options) { operateType ->
                        handleOperateAction(itemView, operateType, messageBean)
                    }
                operateWindow.show(contentView, false)
                return@OnLongClickListener true
            }
            contentView.setOnLongClickListener(longClickListener)
            // 遍历子View并设置长按事件
            setupLongClickForChildren(contentView, longClickListener)
        }
    }

    /**
     * 为子View设置长按事件
     * @param view 父View
     * @param longClickListener 需要设置的长按事件
     */
    private fun setupLongClickForChildren(view: View, longClickListener: OnLongClickListener) {
        if (view is ViewGroup) {
            view.children.forEach {
                setupLongClickForChildren(it, longClickListener)
            }
        } else {
            // 如果子View有点击事件且没有长按事件，则设置长按事件
            try {
                if (view.isClickable && view.hasOnClickListeners()) {
                    view.setOnLongClickListener(longClickListener)
                }
            } catch (e: Exception) {
                e.printStackTrace()
                // hasOnClickListeners 这个方法不知道是不是新api，这里拦截看看
                XLogUpload.newBuilder("HiChatOptionError")
                    .text1(e.message)
                    .post()
            }

        }
    }

    /**
     * 构建操作类型
     * @param messageBean HiChatMessageBean 消息Bean
     */
    private fun buildOperateTypes(messageBean: HiChatMessageBean): List<OperateType> {
        val operateSet = mutableSetOf<OperateType>()

        when (messageBean.contentType) {
            HiChatMessageConstant.CONTENT_TYPE_NORMAL_TEXT -> {
                // 文本消息，因为有系统本地代发，没有 msgId，本地插入的。
                // 这种消息不需要长按操作
                if (messageBean.msgId.isNotEmpty()) {
                    // 文本消息 at、回复、复制
                    operateSet.add(OperateType.AT)
                    operateSet.add(OperateType.COPY)
                }
            }

            HiChatMessageConstant.CONTENT_TYPE_IMAGE -> {
                // 图片消息 at、回复
                operateSet.add(OperateType.AT)
            }

            HiChatMessageConstant.CONTENT_TYPE_VOICE -> {
                // 语音条消息:@TA、回复、转文字；
                operateSet.add(OperateType.AT)
                operateSet.add(OperateType.CONVERT_TO_TEXT)
            }

            HiChatMessageConstant.CONTENT_TYPE_GIFT -> {
                // 礼物消息：@TA、回复
                operateSet.add(OperateType.AT)
            }
        }

        // 支持回复的消息类型 补充回复功能
        if (messageBean.contentInfo is IHiChatReplyMessage) {
            operateSet.add(OperateType.REPLY)
        }

        if (messageBean.accountInfo?.accountUuid == AccountHelper.getAccountUuid()) {
            // 如果是自己发送的消息
            // 不应该存在 at
            operateSet.remove(OperateType.AT)
        }

        // 消息发送失败或没有消息Id的，不能进行回复
        if (!messageBean.isSendSuccess() || messageBean.msgId.isEmpty()) {
            operateSet.remove(OperateType.REPLY)
        }

        // 没有消息id不能进行转文字
        // 已经转过文字的语音消息，不能进行转文字
        if (messageBean.msgId.isEmpty() || (messageBean.contentInfo as? HiChatMessageVoiceBean)?.isShowVoiceTxt == true) {
            operateSet.remove(OperateType.CONVERT_TO_TEXT)
        }

        return operateSet.toList().sortedBy { it.sort }
    }


    /**
     * 操作行为处理函数
     *
     * @param operateType 操作类型
     * @param messageBean 消息对象
     */
    private fun handleOperateAction(
        itemView: View,
        operateType: OperateType,
        messageBean: HiChatMessageBean
    ) {
        mTracker.trackMessageLongOptionClk(HiChatRoomCoreHelper.currentRoomUuid(),messageBean.contentType.hiChatMessageTypeStr(), operateType.text)
        when (operateType) {
            OperateType.AT -> {
                // 处理@操作
                val accountInfo = messageBean.accountInfo
                if (accountInfo?.accountName.isNullOrEmpty() || accountInfo!!.accountUuid.isEmpty()) {
                    return
                }
                onAtOptionCallback?.invoke(accountInfo!!.accountName, accountInfo!!.accountUuid)
            }

            OperateType.REPLY -> {
                // 处理回复操作
                onReplyOptionCallback?.invoke(messageBean)
            }

            OperateType.COPY -> {
                // 处理复制操作
                try {
                    val copyContent = messageBean.contentInfo as? IHiChatMessageCopy
                    ClipboardManagerUtils.copyText(
                        AppUtils.getApp(),
                        copyContent?.copyContent() ?: ""
                    )
                    XToastUtils.show("复制成功");
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            OperateType.CONVERT_TO_TEXT -> {
                onVoiceTextCallback?.invoke(messageBean)
            }
        }
    }

    override fun isActive(): Boolean {
        return true
    }

    override fun slotType(): HiChatMessageSlotType.LongPressMenu {
        return slotType
    }
}