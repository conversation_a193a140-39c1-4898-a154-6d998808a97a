package com.xmhaibao.hichat.repository

import cn.taqu.lib.base.api.UrlBase
import com.xmhaibao.hichat.bean.HiChatFunListResponseBean
import com.xmhaibao.hichat.bean.pk.HiChatPKResponseBean
import com.xmhaibao.hichat.bean.pk.HiChatPkConfigBean
import hb.common.xstatic.HttpParams
import hb.xrequest.XRequest
import hb.xrequest.awaitNullable

/**
 * 畅聊圈趣味玩法Repository
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
class HiChatFunGameRepository : UrlBase() {

    /**
     * 获取趣味玩法列表
     */
    suspend fun getFunGameList(hiChatUuid: String?): HiChatFunListResponseBean? {
        if (hiChatUuid.isNullOrEmpty()) return null
        val httpParams = HttpParams.newBuilder().get(API_TAQU_HICHAT.plus("/Entrance/funList"))
            .needTicketId(true).params("hichat_uuid", hiChatUuid).build()
        return XRequest.newRequest<HiChatFunListResponseBean>(httpParams).awaitNullable()
    }

    /**
     * 创建PK
     * 1-组队pk 2-多人pk
     */
    suspend fun createPk(hiChatUuid: String?, pkMode: String): Any? {
        if (hiChatUuid.isNullOrEmpty()) return null
        val httpParams =
            HttpParams.newBuilder().post(API_TAQU_HICHAT.plus("/Pk/create")).needTicketId(true)
                .params("type", pkMode).params("hichat_uuid", hiChatUuid).build()
        return XRequest.newRequest<Any?>(httpParams).awaitNullable()
    }


    /**
     * 拉取pk信息
     */
    suspend fun fetchPkInfo(hiChatUuid: String?): HiChatPKResponseBean? {
        if (hiChatUuid.isNullOrEmpty()) return null
        return XRequest.get(API_TAQU_HICHAT.plus("/Pk/getInfo")).needTicketId(true)
            .params("hichat_uuid", hiChatUuid).asRequest<HiChatPKResponseBean?>().awaitNullable()
    }


    /**
     * PK 添加成员
     *
     * @param hiChatUuid 畅聊圈uuid
     * @param teamType 队伍类型 1-蓝队 2-红队 0-多人pk
     * @param targetUuid 目标用户uuid  - 多个半角逗号隔开，eg: a,b,c
     */
    suspend fun addPkMember(hiChatUuid: String?, teamType: String?, targetUuid: String?): Any? {
        if (hiChatUuid.isNullOrEmpty()) return null
        return XRequest.post(API_TAQU_HICHAT.plus("/Pk/addMember")).needTicketId(true)
            .params("hichat_uuid", hiChatUuid).params("team_type", teamType)
            .params("uuid", targetUuid).asRequest<Any?>().awaitNullable()
    }


    /**
     * 重开一轮pk
     */
    suspend fun resetPk(hiChatUuid: String?): Any? {
        if (hiChatUuid.isNullOrEmpty()) return null
        return XRequest.post(API_TAQU_HICHAT.plus("/Pk/reset")).needTicketId(true)
            .params("hichat_uuid", hiChatUuid).asRequest<Any?>().awaitNullable()
    }

    /**
     * 开始pk
     */
    suspend fun startPk(hiChatUuid: String?): Any? {
        if (hiChatUuid.isNullOrEmpty()) return null
        return XRequest.post(API_TAQU_HICHAT.plus("/Pk/start")).needTicketId(true)
            .params("hichat_uuid", hiChatUuid).asRequest<Any?>().awaitNullable()
    }

    /**
     * 结束本轮pk
     * @param roundId 场次id
     */
    suspend fun endPk(hiChatUuid: String?, roundId: String?): Any? {
        if (hiChatUuid.isNullOrEmpty() || roundId.isNullOrEmpty()) return null
        return XRequest.post(API_TAQU_HICHAT.plus("/Pk/stop")).needTicketId(true)
            .params("hichat_uuid", hiChatUuid).params("round_id", roundId).asRequest<Any?>()
            .awaitNullable()
    }

    /**
     * 获取PK设置配置
     */
    suspend fun getPkSettingConfig(hiChatUuid: String?): HiChatPkConfigBean? {
        if (hiChatUuid.isNullOrEmpty()) return null
        return XRequest.get(API_TAQU_HICHAT.plus("/Pk/getConfig")).needTicketId(true)
            .params("hichat_uuid", hiChatUuid).asRequest<HiChatPkConfigBean?>().awaitNullable()
    }

    /**
     * 关闭PK活动
     */
    suspend fun closePk(hiChatUuid: String?): Any? {
        if (hiChatUuid.isNullOrEmpty()) return null
        return XRequest.post(API_TAQU_HICHAT.plus("/Entrance/closePk")).needTicketId(true)
            .params("hichat_uuid", hiChatUuid).asRequest<Any?>().awaitNullable()
    }

    /**
     * 设置pk时间
     */
    suspend fun setPkTime(hiChatUuid: String?, roundTime: String?): Any? {
        if (hiChatUuid.isNullOrEmpty() || roundTime.isNullOrEmpty()) return null
        return XRequest.post(API_TAQU_HICHAT.plus("/Pk/setPkTime")).needTicketId(true)
            .params("hichat_uuid", hiChatUuid).params("time", roundTime).asRequest<Any?>()
            .awaitNullable()
    }


    /**
     * 延长PK时间
     */
    suspend fun delayPkTime(hiChatUuid: String?, delayTime: String?): Any? {
        if (hiChatUuid.isNullOrEmpty() || delayTime.isNullOrEmpty()) return null
        return XRequest.post(API_TAQU_HICHAT.plus("/Pk/delayTime")).needTicketId(true)
            .params("hichat_uuid", hiChatUuid).params("time", delayTime).asRequest<Any?>()
            .awaitNullable()
    }

}
