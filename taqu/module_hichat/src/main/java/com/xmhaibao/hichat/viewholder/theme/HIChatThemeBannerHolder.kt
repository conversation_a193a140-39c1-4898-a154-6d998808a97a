package com.xmhaibao.hichat.viewholder.theme

import android.graphics.drawable.BitmapDrawable
import android.view.View
import android.view.ViewGroup
import com.xmhaibao.hichat.R
import com.xmhaibao.hichat.bean.HiChatRoomThemeExtendItemBean
import com.xmhaibao.hichat.bean.HiChatThemeBean
import com.xmhaibao.hichat.databinding.HichatThemeExtendsBannerItemBinding
import hb.convenientbanner.holder.Holder
import hb.utils.ColorUtils
import hb.utils.SizeUtils
import hb.utils.SpanUtils

/**
 *
 * 主题延展轮播
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
class HIChatThemeBannerHolder(
    private val themeInfo: HiChatThemeBean?,
    val parent: ViewGroup?) : Holder<HiChatRoomThemeExtendItemBean>(parent, R.layout.hichat_theme_extends_banner_item) {

    private val binding = HichatThemeExtendsBannerItemBinding.bind(itemView)

    override fun initView(itemView: View?) {
        binding.tvHiChatThemeName.setTextColor(ColorUtils.getColorByRGBA(themeInfo?.themeColor))
    }

    override fun bindData(data: HiChatRoomThemeExtendItemBean?, position: Int, pageSize: Int) {
        val span = SpanUtils()
        //设置图片宽高
        themeInfo?.smallIconBitmap?.let {
            val bitmapDrawable = BitmapDrawable(itemView.resources, themeInfo.smallIconBitmap)
            span.appendImage(bitmapDrawable, SpanUtils.ALIGN_BASELINE).append(" ")
            bitmapDrawable.setBounds(0, 0, SizeUtils.dp2px(12.6f), SizeUtils.dp2px(12.6f))
        }
        binding.tvHiChatThemeName.text = span.append("方向${position+1}：").append(data?.extendContent ?: "").create()
    }
}