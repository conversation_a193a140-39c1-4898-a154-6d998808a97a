package com.xmhaibao.hichat.dialog.pk

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import com.xmhaibao.hichat.R
import com.xmhaibao.hichat.bean.pk.HiChatPkResultBean
import com.xmhaibao.hichat.databinding.HichatMultiPlayerPkResultDialogLayoutBinding
import com.xmhaibao.hichat.databinding.HichatTeamPkResultDialogLayoutBinding
import dp
import hb.kotlin_extension.safeAction
import hb.utils.ActivityUtils
import hb.xstyle.xdialog.XLifecycleDialog

/**
 * 多人pk结果
 *
 * <AUTHOR>
 * @date 2025/7/21
 */
class HiChatMultiplayerPkResultDialog (
    context: Context,
    private val result: HiChatPkResultBean?
) : XLifecycleDialog(context) {

    companion object {
        fun showDialog(
            context: Context?, result: HiChatPkResultBean?,
        ) {
            ActivityUtils.getActivity(context)?.safeAction {
                HiChatMultiplayerPkResultDialog(
                    this, result
                ).show()
            }
        }
    }

    private val mBinding = HichatMultiPlayerPkResultDialogLayoutBinding.inflate(layoutInflater)


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        window?.apply {
            window?.apply {
                setLayout(272.dp, ViewGroup.LayoutParams.WRAP_CONTENT)
                setGravity(Gravity.CENTER)
            }
        }
        initView()
    }

    private fun initView() {
        mBinding.ivSpot.setImageFromResource(R.drawable.hichat_pk_result_bg)
        mBinding.ivTop.setImageFromResource(R.drawable.hichat_pk_result_success_top_ic)
        mBinding.ivClose.setOnClickListener { dismiss() }
        result?.let {
            val item = it.members?.getOrNull(0)
            item?.apply {
                mBinding.tvName.text = nickname
                mBinding.ivAvatar.setImageFromUrl(avatar)
                mBinding.tvStar.text = starCount
            }
        }
    }


}