package com.xmhaibao.hichat.viewmodel

import android.app.Application
import com.xmhaibao.hichat.router.HiChatPinsRouter
import hb.common.xstatic.viewmodel.BaseViewModel

/**
 * 聊天广场 畅聊圈ViewModel
 *
 * <AUTHOR>
 * @date 2025/7/29
 */
class HiChatHallPlazaViewModel(application: Application) : BaseViewModel(application) {

    /**
     * 是否展示畅聊圈推荐入口
     */
    private var isShowHiChatRecommend = true

    /**
     * 展示畅聊圈推荐入口
     */
    fun showHiChatRecommendDialog() {
        if (isShowHiChatRecommend) {
            isShowHiChatRecommend = false
            HiChatPinsRouter.hiChatService().showHiChatRecommendDialog("聊天广场-畅聊圈tab")
        }
    }
}