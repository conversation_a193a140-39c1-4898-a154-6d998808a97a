package com.xmhaibao.hichat.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.xmhaibao.hichat.R
import com.xmhaibao.hichat.databinding.HichatThemeRecommendRelpyDialogLayoutBinding
import com.xmhaibao.hichat.helper.HiChatRoomCoreHelper
import com.xmhaibao.hichat.repository.HiChatThemeExtentsRepository
import com.xmhaibao.hichat.track.HiChatRoomTrack
import com.xmhaibao.hichat.viewholder.HiChatThemeRecommendReplyViewHolder
import com.xmhaibao.hichat.viewmodel.HiChatRoomSocketViewModel
import hb.kotlin_extension.noneSyncLazy
import hb.kotlin_extension.viewScope
import hb.utils.ActivityUtils
import hb.utils.ColorUtils
import hb.utils.SpanUtils
import hb.xadapter.XBaseAdapter
import hb.xrequest.launchHttp
import hb.xstyle.xdialog.XLifecycleDialog
import hb.xstyle.xdialog.XLoadingDialog
import hb.xtoast.XToastUtils

/**
 * 畅聊圈延展主题推荐回复弹窗
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
class HiChatThemeRecommendReplyDialog(
    context: Context,
    private val themeExtend: String?
) : XLifecycleDialog(context) {

    companion object {
        fun showDialog(
            context: Context?, themeExtend: String?
        ) {
            ActivityUtils.getActivity(context)?.apply {
                if (this.isFinishing) {
                    return
                }
                HiChatThemeRecommendReplyDialog(this, themeExtend).show()
            }
        }
    }

    private val span by noneSyncLazy {
        SpanUtils()
            .append("暂无推荐内容，")
            .setForegroundColor(ColorUtils.getColor(hb.xstyle.R.color.TH_Black50))
            .append("点击刷新重试")
            .setForegroundColor(ColorUtils.getColor("#8170FF"))
            .create()
    }

    private val mThemeExtentsRepository by noneSyncLazy {
        HiChatThemeExtentsRepository()
    }

    private val mBinding = HichatThemeRecommendRelpyDialogLayoutBinding.inflate(layoutInflater)
    private var mAdapter: XBaseAdapter? = null
    private val socketViewModel: HiChatRoomSocketViewModel by lazy {
        HiChatRoomCoreHelper.getViewModel(HiChatRoomSocketViewModel::class.java)
    }
    private val hiChatRoomTrack by noneSyncLazy { HiChatRoomTrack() }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
        window?.apply {
            setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            setGravity(Gravity.BOTTOM)
            setWindowAnimations(com.ushengsheng.widget.R.style.PopupAnimation)
        }

        initView()
        refreshData()
    }

    private fun initView() {
        mBinding.ivTopBg.setImageFromResource(R.drawable.hichat_theme_reply_top_bg)
        mBinding.tvContent.text = themeExtend
        mBinding.tvHint.setOnClickListener {
            refreshData()
        }
        mBinding.tvChange.setOnClickListener {
            hiChatRoomTrack.aiReplyClk(HiChatRoomCoreHelper.currentRoomUuid(), clkName = "换一批")
            refreshData()
        }
        mBinding.ivChange.setOnClickListener {
            hiChatRoomTrack.aiReplyClk(HiChatRoomCoreHelper.currentRoomUuid(), clkName = "换一批")
            refreshData()
        }
        mAdapter = XBaseAdapter(context)
        mAdapter?.register(String::class.java, HiChatThemeRecommendReplyViewHolder::class.java)
        mAdapter?.setOnItemClickListener { view, position ->
            val size = mAdapter?.items?.size ?: 0
            if (position < 0 || position >= size) {
                return@setOnItemClickListener
            }
            hiChatRoomTrack.aiReplyClk(HiChatRoomCoreHelper.currentRoomUuid(), clkName = "推荐内容")
            mAdapter?.items?.get(position)?.let { reply ->
                replyTheme(reply.toString())
            }
        }
        mBinding.recyclerView.layoutManager = LinearLayoutManager(context)
        mBinding.recyclerView.adapter = mAdapter
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
        hiChatRoomTrack.aiReplyExp(HiChatRoomCoreHelper.currentRoomUuid())
    }


    @SuppressLint("SetTextI18n")
    private fun refreshData() {
        val hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid()
        if (hiChatUuid.isEmpty()) {
            showErrorHint()
            return
        }
        mBinding.tvHint.text = "内容加载中..."
        mAdapter?.items = emptyList<Any>()
        mAdapter?.notifyDataSetChanged()
        mBinding.tvHint.isClickable = false
        mBinding.root.viewScope.launchHttp({
            val result = mThemeExtentsRepository.getThemeRecReplys(hiChatUuid, themeExtend)
            result?.replyList?.takeIf { it.isNotEmpty() }?.let { replyList ->
                mBinding.tvHint.text = ""
                mAdapter?.items = replyList
                mAdapter?.notifyDataSetChanged()
            } ?: run {
                showErrorHint()
            }
        }){
            showErrorHint()
            return@launchHttp false
        }
    }

    private fun replyTheme(replyContent: String?) {
        val hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid()
        if (hiChatUuid.isEmpty()) {
            return
        }
        ActivityUtils.getActivity(context)?.apply {
            if (this is LifecycleOwner) {
                XLoadingDialog.showLoadingbar(this)
                lifecycleScope.launchHttp({
                   val result =  mThemeExtentsRepository.themeRecReply(hiChatUuid, themeExtend, replyContent)
                    XLoadingDialog.hideLoadingbar()
                    result?.let {
                        socketViewModel.updateMessage(arrayListOf(it), false)
                    }
                    dismiss()
                }, {
                    XLoadingDialog.hideLoadingbar()
                    if (it.responseStatus == "theme_reply_abnormal") {
                        XToastUtils.show(it.responseMsg)
                        dismiss()
                        return@launchHttp true
                    }
                    return@launchHttp false
                })
            }
        }
    }

    /**
     * 展示错误提示
     */
    private fun showErrorHint() {
        mBinding.tvHint.isVisible = true
        mBinding.tvHint.text = span
        mBinding.tvHint.isClickable = true
    }
}