package com.xmhaibao.hichat.dialog.star

import android.view.ViewGroup
import androidx.core.graphics.drawable.toDrawable
import androidx.core.view.isVisible
import com.xmhaibao.family.widget.download2Bitmap
import com.xmhaibao.family.widget.toNinePatchDrawable
import com.xmhaibao.hichat.R
import com.xmhaibao.hichat.bean.StarSkinBean
import com.xmhaibao.hichat.databinding.HichatStarPageItemBinding
import com.xmhaibao.hichat.dialog.star.StarBreathView.HeartBeatListener
import dp
import hb.kotlin_extension.viewScope
import hb.utils.ImageUtils
import hb.xadapter.XBaseViewHolder
import hb.xthread.XThreadPool
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 星星页面 ViewHolder
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
class StarPageViewHolder(parent: ViewGroup) :
    XBaseViewHolder<StarSkinBean>(parent, R.layout.hichat_star_page_item) {

    private val binding by lazy {
        HichatStarPageItemBinding.bind(itemView)
    }

    /**
     * 心跳监听器
     */
    var heartBeatListener: HeartBeatListener? = null

    override fun onBindView(item: StarSkinBean?) {
        item?.let { star ->
            // 设置图片
            binding.ivStarImage.setImageFromUrl(star.skinUrl)
            
            // 根据是否选中来控制显示
            if (star.isSelected) {
                // 显示呼吸动画，隐藏图片
                binding.root.viewScope.launch {
                    val drawable = withContext(XThreadPool.Image().get().asCoroutineDispatcher()) {
                        item.skinUrl.download2Bitmap()?.let {
                            ImageUtils.scale(it,100.dp,100.dp)
                        }?.toDrawable(itemView.resources)
                    }
                    binding.starBreathView.isVisible = true
                    binding.ivStarImage.isVisible = false
                    binding.starBreathView.setupDrawable(drawable)
                }

                // 设置心跳监听器
                binding.starBreathView.heartBeatListener = heartBeatListener
            } else {
                // 显示图片，隐藏呼吸动画
                binding.ivStarImage.isVisible = true
                binding.starBreathView.isVisible = false
                
                // 清除心跳监听器
                binding.starBreathView.heartBeatListener = null
            }
        }
    }

    /**
     * 停止星星触摸
     */
    fun stopTouch() {
        binding.starBreathView.stopTouch()
    }
}
