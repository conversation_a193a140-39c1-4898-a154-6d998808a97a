package com.xmhaibao.hichat.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import cn.taqu.lib.okhttp.OkHttpUtils
import com.xmhaibao.hichat.bean.HiChatOnlineUserBean
import com.xmhaibao.hichat.bean.HiChatRoomUserInfoBean
import com.xmhaibao.hichat.databinding.HichatSendUserSelectDialogBinding
import com.xmhaibao.hichat.repository.HiChatRepository
import com.xmhaibao.hichat.viewholder.member.HiChatSendUserSelectItemViewHolder
import com.xmhaibao.hichat.viewholder.member.HiChatSendUserTopItemViewHolder
import com.xmhaibao.hichat.widget.HiChatEmptyView
import hb.common.data.AccountHelper
import hb.common.helper.OkHttpDns
import hb.kotlin_extension.noneSyncLazy
import hb.utils.ActivityUtils
import hb.utils.SizeUtils
import hb.xadapter.XBaseAdapter
import hb.xstatic.list4page.IListHttpDataSource
import hb.xstatic.list4page.XListHttpDataSource
import hb.xstatic.list4page.XListPage
import hb.xstatic.list4page.XListResultDispose
import hb.xstyle.xdialog.XLifecycleDialog
import hb.xtoast.XToastUtils


/**
 * 小星星送给用户选择弹窗
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
class HiChatSendUserSelectDialog(
    private val context: Context,
    private val mHiChatUuid: String?,
    private val mTitle: String?,
    /**
     * 弹窗类型 用于区分网络请求
     */
    private val mType: String,
    /**
     * 单选模式下 是否自动关闭
     */
    private var autoDismiss: Boolean,
    /**
     * 预选中用户
     */
    private var preselectUuids: List<String>?,
    /**
     * 预剔除用户
     */
    private var preEliminateUuids: MutableList<String>? = null,
    /**
     * 最大选中数量
     */
    private var maxSelectCount: Int = 1,
    /**
     * 取消回调
     */
    private var cancelCallBack: (() -> Unit)? = null,
    /**
     * 选中结果回调
     */
    private var mSelectCallBack: ((MutableList<HiChatRoomUserInfoBean>) -> Unit)?,

    ) : XLifecycleDialog(context) {

    companion object {
        const val TYPE_PK = "PK"
        const val TYPE_COMMON = "common"

        fun showDialog(
            context: Context?, hiChatUuid: String?, title: String?,
            /** 弹窗类型 用于区分网络请求 **/
            type: String = TYPE_COMMON,
            /** 单选模式下 选择完用户 是否自动关闭 **/
            autoDismiss: Boolean = true,
            /** 预选中用户 **/
            preselectUuids: List<String>? = null,
            /** 预剔除用户 **/
            preEliminateUuids: List<String>? = null,
            /** 最大选中数量 **/
            maxSelectCount: Int = 1,
            /** 取消结果回调 **/
            cancelCallBack: (() -> Unit)? = null,
            /** 选中结果回调 **/
            selectCallBack: ((MutableList<HiChatRoomUserInfoBean>) -> Unit)?,
        ): HiChatSendUserSelectDialog? {
            var dialog: HiChatSendUserSelectDialog? = null
            ActivityUtils.getActivity(context)?.apply {
                if (this.isFinishing) {
                    return null
                }
                dialog = HiChatSendUserSelectDialog(
                    context = this,
                    mHiChatUuid = hiChatUuid,
                    mTitle = title,
                    mType = type,
                    autoDismiss = autoDismiss,
                    preselectUuids = preselectUuids,
                    preEliminateUuids = preEliminateUuids?.toMutableList(),
                    maxSelectCount = maxSelectCount,
                    cancelCallBack = cancelCallBack,
                    mSelectCallBack = selectCallBack
                )
                dialog?.show()
            }
            return dialog
        }
    }

    private val tag = "HiChatSendUserSelectDialog"
    private val mHiChatRepository by noneSyncLazy {
        HiChatRepository()
    }

    /**
     * 选中用户个数
     */
    private var selectCount = 0

    /**
     * 用户回传的选中用户列表
     */
    private var mSelectList: MutableList<HiChatRoomUserInfoBean> = mutableListOf()
    private var isMultiChoice: Boolean = false


    private val mBinding = HichatSendUserSelectDialogBinding.inflate(layoutInflater)
    private var xListPage: XListPage? = null

    /**
     * 顶部列表adapter
     */
    private var topAdapter: XBaseAdapter? = null

    @SuppressLint("NotifyDataSetChanged")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
        window?.apply {
            setLayout(ViewGroup.LayoutParams.MATCH_PARENT, SizeUtils.dp2px(569f))
            setGravity(Gravity.BOTTOM)
            setWindowAnimations(com.ushengsheng.widget.R.style.PopupAnimation)
        }
        // 判断是否是多选
        isMultiChoice = maxSelectCount > 1
        if (mType == TYPE_COMMON) {
            // 通用模式下，需要剔除当前用户
            if (preEliminateUuids == null) {
                preEliminateUuids = mutableListOf()
            }
            preEliminateUuids?.add(AccountHelper.getAccountUuid())
        }
        initView()
        topAdapter = XBaseAdapter(context)
        topAdapter?.let {
            it.items = mSelectList
            it.register(
                HiChatRoomUserInfoBean::class.java,
                HiChatSendUserTopItemViewHolder::class.java
            )
            it.setOnViewHolderCreatedListener { holder ->
                if (holder is HiChatSendUserTopItemViewHolder) {
                    holder.mUserClickCallBack = { userInfo ->
                        // 取消选择的用户
                        val result = mSelectList.remove(userInfo)
                        if (result) {
                            userInfo?.isSelected = false
                            selectCount--
                            topAdapter?.notifyDataSetChanged()
                            xListPage?.adapter?.notifyDataSetChanged()
                            showTitle()
                        }
                    }
                }
            }
        }
        mBinding.topRecyclerView.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = topAdapter
        }
    }

    private fun initView() {
        mBinding.apply {
            showTitle()
            ivBack.setOnClickListener {
                cancelCallBack?.invoke()
                if (autoDismiss) {
                    dismiss()
                }
            }
            tvConfirm.setOnClickListener {
                mSelectCallBack?.invoke(mSelectList.asReversed())
                dismiss()
            }
            xListPage = XListPage.create(context)
                .setDataSource(initDataSource())
                .setOnViewHolderCreatedListener {
                    if (it is HiChatSendUserSelectItemViewHolder) {
                        it.isMultiChoice = isMultiChoice
                        it.mUserClickCallBack = { userInfo ->
                            handleItemSelect(userInfo)
                        }
                    }
                }
                .registerItem(
                    HiChatRoomUserInfoBean::class.java,
                    HiChatSendUserSelectItemViewHolder::class.java
                )
                .setEmptyView(
                    HiChatEmptyView(context).apply {
                        setText("暂无可选的用户")
                        setTextColor(
                            ContextCompat.getColor(
                                context,
                                cn.taqu.lib.base.R.color.TH_Gray990
                            )
                        )
                    },
                    FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                )
                .attachToView(mBinding.flContent)
        }
    }

    @SuppressLint("SetTextI18n", "NotifyDataSetChanged")
    private fun showTitle() {
        if (isMultiChoice) {
            mBinding.topRecyclerView.isVisible = mSelectList.isNotEmpty()
            topAdapter?.notifyDataSetChanged()
            xListPage?.adapter?.notifyDataSetChanged()
            mBinding.tvTitle.text = mTitle + "（${selectCount}/${maxSelectCount}）"
        } else {
            mBinding.tvTitle.text = mTitle
        }
    }

    /**
     * 处理item选中事件
     */
    private fun handleItemSelect(userInfo: HiChatRoomUserInfoBean?) {
        if (isMultiChoice) {
            handleMultiSelect(userInfo)
            return
        }
        userInfo?.let {
            mSelectList.add(it)
            mSelectCallBack?.invoke(mSelectList)
        }
        if (autoDismiss) {
            dismiss()
        }
    }

    /**
     * 处理多选事件
     */
    @SuppressLint("NotifyDataSetChanged")
    private fun handleMultiSelect(userInfo: HiChatRoomUserInfoBean?) {
        userInfo?.let {
            if (it.isSelected) {
                // 如果当前处于选中状态，需要取消选中
                selectCount--
                it.isSelected = false
                mSelectList.remove(userInfo)
            } else {
                // 如果当前处于未选中状态
                if (selectCount >= maxSelectCount) {
                    // 如果已选中数量大于等于最大选中数量，需要提示用户
                    XToastUtils.show("最多只能选择${maxSelectCount}个用户")
                    return
                }
                selectCount++
                it.isSelected = true
                mSelectList.add(0, userInfo)
            }
            showTitle()
        }
    }


    /**
     * 初始化数据源
     */
    private fun initDataSource(): IListHttpDataSource<HiChatOnlineUserBean?> {
        return object : XListHttpDataSource<HiChatOnlineUserBean>() {}.apply {
            setHttpParams(
                if (mType == TYPE_PK) {
                    mHiChatRepository.featPkUserList(mHiChatUuid).httpParams.apply {
                        tag = <EMAIL>
                    }
                } else {
                    mHiChatRepository.featOnlineUser(mHiChatUuid).httpParams.apply {
                        tag = <EMAIL>
                    }
                }
            )
            setResultDispose(object : XListResultDispose<HiChatOnlineUserBean>() {
                override fun onHanldeSuccess(
                    isCache: Boolean, result: HiChatOnlineUserBean?, vararg objs: Any?,
                ) {
                    if (isFirstPage) {
                        preselectUuids?.let {
                            // 如果有预选中用户，并且用户手动选择用户列表也存在，以用户手动选择为准
                            if (mSelectList.isNotEmpty()) {
                                preselectUuids = emptyList()
                            }
                        }
                    }
                    val iterator = result?.userList?.iterator()
                    while (iterator?.hasNext() == true) {
                        val next = iterator.next()
                        //过滤掉待剔除用户
                        var isRemoved = false
                        preEliminateUuids?.forEach { uuid ->
                            if (next.uuid == uuid) {
                                iterator.remove()
                                isRemoved = true
                                return@forEach
                            }
                        }
                        if (isRemoved) {
                            continue
                        }
                        if (isFirstPage) {
                            mSelectList.forEach {
                                if (it.uuid == next.uuid) {
                                    next.isSelected = true
                                    return@forEach
                                }
                            }
                        }
                        // 处理预选中用户
                        preselectUuids?.forEach { uuid ->
                            if (uuid == next.uuid) {
                                next.isSelected = true
                                if (!mSelectList.any { it.uuid == next.uuid }) {
                                    selectCount++
                                    mSelectList.add(0, next)
                                }
                                return@forEach
                            }
                        }
                    }
                    if (result?.userList.isNullOrEmpty()) {
                        isLoadMoreble = false
                        isAutoLoadMore = false
                        xListPage?.adapter?.setLoadMoreHide()
                        return
                    }
                    super.onHanldeSuccess(isCache, result, *objs)
                    if (list.isNotEmpty()) {
                        // 遍历反选，如果选中的用户不存在列表中，则需要移除
                        val template = mutableListOf<HiChatRoomUserInfoBean>()
                        mSelectList.forEach { item ->
                            val exist = list.any {
                                (it as? HiChatRoomUserInfoBean)?.uuid == item.uuid
                            }
                            if (!exist) {
                                selectCount--
                                template.add(item)
                            }
                        }
                        if (template.isNotEmpty()) {
                            mSelectList.removeAll(template)
                        }
                    }
                    showTitle()
                }

                override fun onHandleFailure(vararg objs: Any?) {
                    super.onHandleFailure(*objs)
                }
            }
            )
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        OkHttpUtils.getInstance().cancelTag(tag)
    }
}