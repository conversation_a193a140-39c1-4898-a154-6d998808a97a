package com.xmhaibao.hichat.dialog.star

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.animation.ValueAnimator.AnimatorUpdateListener
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.core.content.ContextCompat
import com.xmhaibao.hichat.R
import hb.utils.Loger
import hb.utils.SizeUtils
import androidx.core.view.isVisible

class StarBreathView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    companion object{
        /**
         * 长按送
         */
        const val LONG_CLK = 1

        /**
         * 单击送
         */
        const val CLK = 0
    }

    // 心跳回调接口
    interface HeartBeatListener {
        fun onHeartBeat(x: Float, y: Float, type: Int)

        /**
         * 行为结束
         * 如： 点击行为结束，长按行为结束
         */
        fun onHeartBeatEnd(type: Int)
    }

    var heartBeatListener: HeartBeatListener? = null

    // 图像和动画属性
    private var heartDrawable: Drawable? = null
    private var scale = 1.0f
    private var breatheAnimator: ValueAnimator? = null
    private var clickAnimator: ValueAnimator? = null
    private var longPressAnimator: ValueAnimator? = null

    // 状态控制标志
    private var isLongPress = false
    private var isTouchDown = false
    private var isMoveOutside = false
    private var isBreathingActive = false
    // 是否中断触摸
    private var isInterceptTouch = false

    // 动画参数
    private val SCALE_FACTOR = 1.15f
    private val BREATHE_DURATION = 400L
    private val CLICK_DURATION = 200L
    private val LONG_PRESS_DURATION = 100L

    private val mAnimatorUpdateListener = AnimatorUpdateListener { animation ->
        if (!isShowOnWindow()) {
            animation.cancel()
            cancelAnimations()
        }
        scale = animation.animatedValue as Float
        invalidate()
    }

    private var touchViewSize = SizeUtils.dp2px(118f)

    fun setupDrawable(drawable: Drawable?) {
        // 获取心形图标资源
        heartDrawable = drawable
    }

    private fun setupBreatheAnimation() {
        // 确保之前的动画已经停止
        breatheAnimator?.cancel()
        breatheAnimator = null

        // 标记呼吸动画已激活
        isBreathingActive = true

        // 判断当前scale是否在呼吸范围的极值附近
        if (scale >= 1f && scale < SCALE_FACTOR) {
            // 当前值在中间，先平滑过渡到最大值
            createInitialAnimation()
        } else {
            // 当前值接近最小值或最大值，直接开始标准呼吸循环
            createStandardBreathingAnimation()
        }
    }

    // 创建初始过渡动画，从当前scale平滑过渡到最大值
    private fun createInitialAnimation() {
        val startScale = scale // 使用当前scale作为起始值
        val endScale = SCALE_FACTOR

        // 计算动画时长，根据距离调整
        val distanceRatio = (endScale - startScale) / (SCALE_FACTOR - 1.0f)
        val adjustedDuration = (BREATHE_DURATION * distanceRatio).toLong().coerceAtLeast(50)
        breatheAnimator?.cancel()
        breatheAnimator = ValueAnimator.ofFloat(startScale, endScale).apply {
            duration = adjustedDuration
            interpolator = LinearInterpolator()
            addUpdateListener(mAnimatorUpdateListener)

            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    if (!isShowOnWindow()) return

                    // 初始过渡动画完成后，开始标准的呼吸循环
                    if (!isLongPress && !isTouchDown && isBreathingActive) {
                        createStandardBreathingAnimation() // true表示从最大值开始
                    }
                }
            })
        }
        if(!isShowOnWindow()) return
        breatheAnimator?.start()
    }

    // 创建标准呼吸循环动画，完整的1.0f到SCALE_FACTOR变化
    private fun createStandardBreathingAnimation() {
        // 取消之前的动画
        breatheAnimator?.cancel()
        // 如果是从最大值开始，先创建缩小动画
        breatheAnimator = ValueAnimator.ofFloat(SCALE_FACTOR, 1.0f).apply {
            duration = BREATHE_DURATION
            interpolator = LinearInterpolator()
            repeatMode = ValueAnimator.REVERSE
            repeatCount = ValueAnimator.INFINITE
            addUpdateListener(mAnimatorUpdateListener)

            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    if (!isShowOnWindow()) return
                }
            })
        }
        if(!isShowOnWindow()) return
        breatheAnimator?.start()
    }

    private fun setupClickAnimation() {
        // 立即取消并停止呼吸动画
        cancelAnimations()
        isBreathingActive = false

        clickAnimator = ValueAnimator.ofFloat(1.0f, SCALE_FACTOR).apply {
            duration = CLICK_DURATION
            interpolator = LinearInterpolator()
            addUpdateListener(mAnimatorUpdateListener)

            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    super.onAnimationStart(animation)
                    if (!isShowOnWindow()) return
                    triggerHeartBeat(clkType = CLK)
                    heartBeatListener?.onHeartBeatEnd(CLK)
                }

                override fun onAnimationEnd(animation: Animator) {
                    if (!isShowOnWindow()) return
                    // 放大结束后开始缩小
                    // 如果当前是放大，则需要回到缩小状态
                    if (!isLongPress) {
                        setupBreatheAnimation()
                    }
                }
            })
        }
        if(!isShowOnWindow()) return
        clickAnimator?.start()
    }

    private fun setupLongPressAnimation() {
        // 确保所有其他动画都已经停止
        cancelAnimations()
        isBreathingActive = false

        // 设置长按连续动画
        longPressAnimator = ValueAnimator.ofFloat(1.0f, SCALE_FACTOR).apply {
            duration = LONG_PRESS_DURATION
            interpolator = LinearInterpolator()
            addUpdateListener(mAnimatorUpdateListener)

            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    if (!isShowOnWindow()) return
                    // 在放大接近最大值时触发回调
                    if (scale >= SCALE_FACTOR * 0.98f) {
                        triggerHeartBeat(clkType = LONG_CLK)
                    }
                    // 如果仍处于长按状态，继续执行长按动画
                    if (isLongPress && isInterceptTouch == false) {
                        longPressAnimator?.reverse()
                    } else {
                        heartBeatListener?.onHeartBeatEnd(LONG_CLK)
                        // 否则恢复呼吸动画
                        isBreathingActive = true
                        setupBreatheAnimation()
                    }
                }
            })
        }

        if(!isShowOnWindow()) return
        longPressAnimator?.start()
    }

    // 触发心跳回调
    private fun triggerHeartBeat(clkType: Int) {
        heartBeatListener?.let {
            val centerX = x + width / 2
            val centerY = y + height / 2
            it.onHeartBeat(centerX, centerY, clkType)
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        // 设置View的大小，默认使用heart_icon的大小
//        var width = heartDrawable?.intrinsicWidth ?: 0
//        var height = heartDrawable?.intrinsicHeight ?: 0
//
//        width = resolveSize(width, widthMeasureSpec)
//        height = resolveSize(height, heightMeasureSpec)
//
//        setMeasuredDimension(width, height)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        heartDrawable?.let { drawable ->
            val width = touchViewSize
            val height = touchViewSize


            // 计算缩放后的尺寸
            val scaledWidth = (width * scale).toInt()
            val scaledHeight = (height * scale).toInt()

            // 计算绘制位置，使图标居中
            val left = (measuredWidth - scaledWidth) / 2
            val top = (measuredHeight - scaledHeight) / 2

            // 设置drawable的绘制范围
            drawable.setBounds(left, top, left + scaledWidth, top + scaledHeight)

            // 绘制drawable
            drawable.draw(canvas)
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 记录按下状态
                isTouchDown = true
                isMoveOutside = false
                isLongPress = false
                isBreathingActive = false
                isInterceptTouch = false

                // 停止呼吸动画
                breatheAnimator?.takeIf { it.isRunning }?.cancel()

                // 添加长按判断
                postDelayed(longPressRunnable, 500)
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                // 检查是否移出了控件范围
                val isOutside = event.x < 0 || event.x > width ||
                        event.y < 0 || event.y > height

                // 只有当之前不是在外面，而现在在外面时，才进行处理
                if (!isMoveOutside && isOutside) {
                    isMoveOutside = true
                    removeCallbacks(longPressRunnable)
                    isLongPress = false

                    // 取消所有动画并恢复呼吸动画
                    cancelAnimations()
                    isTouchDown = false
                    isBreathingActive = true
                    setupBreatheAnimation()
                } else if (isMoveOutside && !isOutside) {
                    // 如果之前在外面，现在又移回来了，恢复触摸状态
                    isMoveOutside = false
                    isTouchDown = true
                    isBreathingActive = false

                    // 停止呼吸动画
                    breatheAnimator?.takeIf { it.isRunning }?.cancel()
                }
                return true
            }

            MotionEvent.ACTION_UP -> {
                // 手指抬起，取消长按判断
                removeCallbacks(longPressRunnable)
                isTouchDown = false

                // 如果不是长按状态且没有移出控件范围，执行点击动画
                if (!isLongPress && !isMoveOutside) {
                    setupClickAnimation()
                } else if (isLongPress) {
                    // 如果是长按状态，停止长按动画
                    isLongPress = false
                    // 长按动画会在自己的结束回调中处理恢复呼吸动画
                } else if (isMoveOutside) {
                    // 如果已经移出范围，则不需要重新启动呼吸动画，避免抖动
                    // 只需确保状态正确
                    isMoveOutside = false
                }
                return true
            }

            MotionEvent.ACTION_CANCEL -> {
                // 触摸取消，重置所有状态
                removeCallbacks(longPressRunnable)
                isLongPress = false
                isTouchDown = false
                isMoveOutside = false

                // 恢复呼吸动画
                cancelAnimations()
                isBreathingActive = true
                setupBreatheAnimation()
                return true
            }
        }
        return super.onTouchEvent(event)
    }

    private val longPressRunnable = Runnable {
        isLongPress = true
        // 停止任何正在运行的动画
        cancelAnimations()
        // 开始长按动画
        setupLongPressAnimation()
    }

    private fun cancelAnimations() {
        breatheAnimator?.takeIf { it.isRunning }?.cancel()
        clickAnimator?.takeIf { it.isRunning }?.cancel()
        longPressAnimator?.takeIf { it.isRunning }?.cancel()
    }

    /**
     * 中断本次触摸
     */
    fun stopTouch() {
        isInterceptTouch = true
    }

    override fun setVisibility(visibility: Int) {
        super.setVisibility(visibility)
        if (visibility == VISIBLE) {
            setupBreatheAnimation()
        } else {
            cancelAnimations()
        }
    }

    private fun isShowOnWindow(): Boolean{
        return isAttachedToWindow && isVisible
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        setupBreatheAnimation()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 释放资源
        cancelAnimations()

    }
} 