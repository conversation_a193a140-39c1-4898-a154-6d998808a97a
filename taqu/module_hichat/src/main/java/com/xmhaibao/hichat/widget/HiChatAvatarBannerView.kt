package com.xmhaibao.hichat.widget

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import com.facebook.drawee.generic.GenericDraweeHierarchyBuilder
import com.facebook.drawee.generic.RoundingParams
import com.xmhaibao.hichat.R
import dp
import hb.kotlin_extension.viewScope
import hb.utils.ColorUtils
import hb.ximage.fresco.AvatarDraweeView
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 * 封装头像轮播控件
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
class HiChatAvatarBannerView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val avatarList = mutableListOf<String>()
    private var avatarSize = 48.dp  // 头像大小（dp）
    private var overlap = 8.dp  // 重叠宽度

    // 头像索引（循环递增）用于展示最右侧出现的头像控件的展示内容
    private var avatarIndex: Int = 0
    private var mAnimatorSet: AnimatorSet? = null
    private val mInterpolator: LinearInterpolator = LinearInterpolator()
    private var mBorderWidth: Int = 1.dp
    private var mBorderColor: Int = ColorUtils.getColor(android.R.color.white)
    private var animationDuration: Long = 300L
    private var staticDuration: Long = 1000L

    /**
     * 轮播个数限制，最多展示4个头像，超过则进行轮播展示
     */
    private var carouselLimit: Int = 4


    init {
        // 读取XML属性
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.HiChatAvatarBannerView)
        avatarSize = typedArray.getDimensionPixelSize(
            R.styleable.HiChatAvatarBannerView_avatarSize,
            avatarSize
        )
        overlap =
            typedArray.getDimensionPixelSize(R.styleable.HiChatAvatarBannerView_overlap, overlap)
        animationDuration = typedArray.getInt(
            R.styleable.HiChatAvatarBannerView_animationDuration,
            animationDuration.toInt()
        ).toLong()
        staticDuration = typedArray.getInt(
            R.styleable.HiChatAvatarBannerView_staticDuration,
            staticDuration.toInt()
        ).toLong()
        mBorderWidth = typedArray.getDimensionPixelSize(
            R.styleable.HiChatAvatarBannerView_borderWidth,
            mBorderWidth
        )
        carouselLimit = typedArray.getDimensionPixelSize(
            R.styleable.HiChatAvatarBannerView_carouselLimit,
            carouselLimit
        )
        mBorderColor =
            typedArray.getColor(R.styleable.HiChatAvatarBannerView_borderColor, mBorderColor)
        typedArray.recycle()
        layoutDirection = View.LAYOUT_DIRECTION_RTL
    }


    /**
     * 设置轮播头像数据
     */
    fun startCarousel(list: MutableList<String>? = null) {
        list?.let {
            // 因为是从右侧添加，为了和服务端顺序一致，需要先将数据逆序
            it.reverse()
            avatarList.clear()
            avatarList.addAll(list)
            avatarIndex = avatarList.size - 1
        }
        // 添加初始头像
        var size = avatarList.size
        // 如果头像列表超过限制，需要限制个数
        if (avatarList.size >= carouselLimit) {
            size = carouselLimit
            layoutParams.width = avatarSize + (avatarSize - overlap) * (size - 1)
        }
        repeat(size) { createAvatar(it, it) }
        startCarousel()
    }

    /**
     * 添加一个头像到容器最右侧（初始或轮播新增）
     */
    private fun createAvatar(viewIndex: Int, avatarIndex: Int) {
        val avatar = AvatarDraweeView(context).apply {
            val marginEnd = if (viewIndex > 0) (avatarSize - overlap) * viewIndex else 0
            layoutParams = LayoutParams(avatarSize, avatarSize).apply {
                setMargins(0, 0, marginEnd, 0)  // 右边距=间距
            }
            val hierarchy = GenericDraweeHierarchyBuilder(context.resources)
            val roundingParams = RoundingParams().apply {
                borderColor = mBorderColor
                borderWidth = mBorderWidth.toFloat()
            }
            hierarchy.roundingParams = roundingParams
            setHierarchy(hierarchy.build())
            setImageFromUrl(avatarList[avatarIndex])
            setRoundAsCircle(true)
        }
        addView(avatar, viewIndex)
    }

    private fun createAnimatorSet() {
        mAnimatorSet?.cancel()
        mAnimatorSet = AnimatorSet()

        // 1. 最左侧头像：缩小+透明消失
        val leftAvatar = getChildAt(childCount - 1)
        val disappearAnim: ObjectAnimator = ObjectAnimator.ofPropertyValuesHolder(
            leftAvatar,
            PropertyValuesHolder.ofFloat(SCALE_X, 1f, 0f),
            PropertyValuesHolder.ofFloat(SCALE_Y, 1f, 0f),
            PropertyValuesHolder.ofFloat(ALPHA, 1f, 0f)
        ).setDuration(animationDuration)
        disappearAnim.interpolator = mInterpolator
        disappearAnim.addListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(animation: Animator) {
                // 添加新头像
                createAvatar(0, avatarIndex)

                playAppearAnimation()  // 执行新头像出现动画
            }

            override fun onAnimationEnd(animation: Animator) {
                removeView(leftAvatar)
                avatarIndex = (avatarIndex - 1) % avatarList.size
                if (avatarIndex < 0) {
                    avatarIndex = avatarList.size - 1
                }
            }

            override fun onAnimationCancel(animation: Animator) {

            }

            override fun onAnimationRepeat(animation: Animator) {

            }
        })

        // 2. 右侧3个头像：向左平移（填补左侧空位）
        val rightAvatars = mutableListOf<View>()
        for (i in 0 until childCount - 1) {
            rightAvatars.add(getChildAt(i))
        }
        val tranXAnimSet = AnimatorSet().apply {
            val animators = rightAvatars.map {
                ObjectAnimator.ofFloat(
                    it, TRANSLATION_X, it.translationX - (avatarSize - overlap).toFloat()
                )
            }
            playTogether(animators)
            duration = animationDuration
            interpolator = mInterpolator
        }
        mAnimatorSet?.playTogether(disappearAnim, tranXAnimSet)
        mAnimatorSet?.startDelay = staticDuration
        mAnimatorSet?.start()
    }

    /**
     * 启动轮播（用Coroutine实现定时循环）
     */
    private fun startCarousel() {
        if (avatarList.size > carouselLimit) {
            viewScope.launch {
                while (isActive) {
                    createAnimatorSet()
                    delay(staticDuration + animationDuration)  // 轮播间隔
                }
            }
        }
    }


    /**
     * 执行新头像出现动画（放大+显示）
     */
    private fun playAppearAnimation() {
        val newAvatar = getChildAt(0)
        val appearAnim: ObjectAnimator = ObjectAnimator.ofPropertyValuesHolder(
            newAvatar,
            PropertyValuesHolder.ofFloat(SCALE_X, 0f, 1f),
            PropertyValuesHolder.ofFloat(SCALE_Y, 0f, 1f),
            PropertyValuesHolder.ofFloat(ALPHA, 0f, 1f)
        ).setDuration(animationDuration)
        appearAnim.interpolator = mInterpolator
        appearAnim.start()
    }


    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mAnimatorSet?.cancel()
    }

}