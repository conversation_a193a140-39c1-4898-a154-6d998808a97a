package com.xmhaibao.hichat.startup

import android.content.Context
import cn.taqu.lib.base.startup.BaseStartupAlias
import cn.taqu.lib.base.startup.stage.IAppHomeRenderAfter2sIdle
import com.xmhaibao.family.notification.FamilyInAppNotificationManager
import com.xmhaibao.hichat.notification.HiChatPgStartNotificationBinder
import hb.utils.Loger
import hb.xstartup.core.StartupNode
import hb.xstartup.core.initializer.Initializer

/**
 * 畅聊圈通用启动器
 *
 * <AUTHOR>
 * @date 2025-06-14
 */
@StartupNode(
    name = HiChatStartUpAlias.HICHAT_NORMAL_START_UP,
    tags = [BaseStartupAlias.TAG_CHECK_SECURITY],
    dependencies = [BaseStartupAlias.BASE],
    priority = 11
)
class HiChatNormalStartUp : Initializer, IAppHomeRenderAfter2sIdle {
    override fun initialize(context: Context?) {
        // do-nothing
    }

    override fun onAppHomeRenderAfterIdleOnWork() {
    }

    override fun onAppHomeRenderAfterIdleOnMain() {
        // 注册畅聊圈应用内通知
        FamilyInAppNotificationManager.registerBinder(
            HiChatPgStartNotificationBinder.NOTIFICATION_ID,
            HiChatPgStartNotificationBinder()
        )
    }
}