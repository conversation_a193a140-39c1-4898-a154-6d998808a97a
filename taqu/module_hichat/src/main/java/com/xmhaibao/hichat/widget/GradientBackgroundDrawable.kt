package com.xmhaibao.hichat.widget

import android.graphics.*
import android.graphics.drawable.Drawable

/**
 * 渐变背景 Drawable
 * 用于创建带透明度的渐变背景，可以让下层内容穿透
 */
class GradientBackgroundDrawable(
    private val cornerRadius: Float = 36f,
    private val startColor: Int = Color.parseColor("#B2460031"),
    private val endColor: Int = Color.parseColor("#B2000631")
) : Drawable() {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val rectF = RectF()
    
    override fun draw(canvas: Canvas) {
        val bounds = bounds
        if (bounds.isEmpty) return

        val width = bounds.width().toFloat()
        val height = bounds.height().toFloat()
        
        // 创建渐变着色器，角度 315 度
        val gradient = LinearGradient(
            0f, 0f, width, height,
            startColor, endColor,
            Shader.TileMode.CLAMP
        )
        paint.shader = gradient
        paint.style = Paint.Style.FILL

        // 绘制圆角矩形背景
        rectF.set(0f, 0f, width, height)
        canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, paint)
    }

    override fun setAlpha(alpha: Int) {
        paint.alpha = alpha
        invalidateSelf()
    }

    override fun setColorFilter(colorFilter: ColorFilter?) {
        paint.colorFilter = colorFilter
        invalidateSelf()
    }

    override fun getOpacity(): Int {
        return PixelFormat.TRANSLUCENT
    }
}
