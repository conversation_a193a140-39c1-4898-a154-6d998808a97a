package com.xmhaibao.hichat.widget.pk

import android.content.Context
import android.graphics.drawable.LayerDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import cn.taqu.lib.base.router.RouterLaunch
import com.xmhaibao.hichat.R
import com.xmhaibao.hichat.bean.HiChatPkUserBean
import com.xmhaibao.hichat.bean.pk.HiChatPKResponseBean
import com.xmhaibao.hichat.bean.pk.HiChatPkTeamType
import com.xmhaibao.hichat.bean.pk.PKStatus
import com.xmhaibao.hichat.databinding.HichatIncludePkHeaderBinding
import com.xmhaibao.hichat.databinding.HichatViewTeamPkBinding
import com.xmhaibao.hichat.helper.HiChatRoomCoreHelper
import com.xmhaibao.hichat.widget.GradientBorderDrawable
import com.xmhaibao.imganim.lottie.XLottiePlayer
import dp
import hb.utils.ColorUtils
import hb.utils.Loger

/**
 * 组队pk视图
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
class HiChatTeamPKView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : HiChatBasePKView(context, attrs, defStyleAttr) {
    companion object {
        /**
         * 当前锁定的uuid
         */
        private var mBlueTeamLockUuid: String? = null
        private var mRedTeamLockUuid: String? = null
        private var mLockRoundId: String? = null
    }

    private val binding = HichatViewTeamPkBinding.inflate(LayoutInflater.from(context), this)
    private val headerBinding = HichatIncludePkHeaderBinding.bind(this)

    private val blueTeamUsers = mutableListOf<HiChatPkUserBean>()
    private val redTeamUsers = mutableListOf<HiChatPkUserBean>()

    private lateinit var blueTeamExpandedAdapter: HiChatPKUserAdapter
    private lateinit var redTeamExpandedAdapter: HiChatPKUserAdapter
    private lateinit var blueTeamCollapsedAdapter: HiChatPKUserAdapter
    private lateinit var redTeamCollapsedAdapter: HiChatPKUserAdapter


    init {
        // 设置渐变背景
        val borderDrawable = GradientBorderDrawable(
            borderWidth = 1.dp,
            cornerRadius = 12.dp,
            startColor = ColorUtils.getColor("#FF5DBE"),
            endColor = ColorUtils.getColor("42C0FF"),
            gradientAngle = 135f
        )
        val layerDrawable = LayerDrawable(arrayOf(ContextCompat.getDrawable(context,R.drawable.hichat_pk_bg_gradient), borderDrawable))
        background = layerDrawable
        setupViews()
        binding.btnToggleExpand.setOnClickListener {
            val isExpanded = binding.blueTeamExpanded.root.isVisible
            mTracker.hiChatPKPageClk(
                hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid(),
                clkName = if (isExpanded) "收起" else "展开"
            )
            toggleExpand()
        }
        binding.lottieLightning.repeatCount = Int.MAX_VALUE
        // 设置初始状态为展开
        setExpanded(true)

        // 设置进度变化监听器
        binding.progressBar.setOnProgressChangeListener(object :
            HiChatTeamPKProgressBar.OnProgressChangeListener {
            override fun onProgressChanged(
                blueScore: Int, redScore: Int, blueWidth: Float, totalWidth: Float
            ) {
                updateScoreDisplay(blueScore, redScore)
                if (pkStatus == PKStatus.IN_PROGRESS) {
                    // 进行中更新闪电位置
                    updateLightningPosition(blueWidth, totalWidth)
                }

            }
        })
    }

    override fun onMaxTeamSizeChanged() {
        updateAddButtonVisibility()
    }


    private fun setupViews() {
        // 展开适配器（不再需要添加按钮回调，因为按钮现在是固定的）
        blueTeamExpandedAdapter =
            HiChatPKUserAdapter(isExpanded = true, isTeamPK = true, pkAdapterCallback = this)
        redTeamExpandedAdapter =
            HiChatPKUserAdapter(isExpanded = true, isTeamPK = true, pkAdapterCallback = this)

        // 收起适配器（不再需要添加按钮回调，因为按钮现在是固定的）
        blueTeamCollapsedAdapter =
            HiChatPKUserAdapter(isExpanded = false, isTeamPK = true, pkAdapterCallback = this)
        redTeamCollapsedAdapter =
            HiChatPKUserAdapter(isExpanded = false, isTeamPK = true, pkAdapterCallback = this)

        // 设置展开recycleView属性
        binding.blueTeamExpanded.rvUsers.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = blueTeamExpandedAdapter
            // 完全禁用所有动画，包括插入、移除、变化和位置移动动画
//            itemAnimator = null
        }
        binding.redTeamExpanded.rvUsers.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = redTeamExpandedAdapter
            // 完全禁用所有动画，包括插入、移除、变化和位置移动动画
//            itemAnimator = null
        }

        // 设置收起 recycleView 属性
        binding.blueTeamCollapsed.rvUsers.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = blueTeamCollapsedAdapter
            // 完全禁用所有动画，包括插入、移除、变化和位置移动动画
//            itemAnimator = null
        }
        binding.redTeamCollapsed.rvUsers.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = redTeamCollapsedAdapter
            // 完全禁用所有动画，包括插入、移除、变化和位置移动动画
//            itemAnimator = null
        }

        // 设置标题
        headerBinding.ivTitle.setImageResource(R.drawable.hichat_pk_team_ic)

        // 自定义队伍名称和背景
        setupTeamAppearance()

        // 初始化列表提交
        updateLists()
    }


    private fun updateLists(isStructuralChange: Boolean = false) {

        // 更新蓝队锁定状态并标记变化
        blueTeamUsers.forEachIndexed { index, user ->
            user.hasChanged = false
            val newLockState = user.uuid == mBlueTeamLockUuid
            if (user.isLock != newLockState) {
                user.isLock = newLockState
                user.hasChanged = true
            }
            user.rank = index + 1
        }

        // 更新红队锁定状态并标记变化
        redTeamUsers.forEachIndexed { index, user ->
            user.hasChanged = false
            val newLockState = user.uuid == mRedTeamLockUuid
            if (user.isLock != newLockState) {
                user.isLock = newLockState
                user.hasChanged = true
            }
            user.rank = index + 1
        }

        // 总是传递列表的副本给适配器
        blueTeamExpandedAdapter.submitList(ArrayList(blueTeamUsers), isStructuralChange)
        redTeamExpandedAdapter.submitList(ArrayList(redTeamUsers), isStructuralChange)
        blueTeamCollapsedAdapter.submitList(ArrayList(blueTeamUsers), isStructuralChange)
        redTeamCollapsedAdapter.submitList(ArrayList(redTeamUsers), isStructuralChange)

        // 更新添加按钮的显示状态
        updateAddButtonVisibility()
    }

    /**
     * 更新添加按钮的显示状态
     * 展开状态：列表为空时显示中间，有用户时显示顶部，满员时隐藏
     * 收起状态：只在PK未开始且队伍未满时显示
     */
    private fun updateAddButtonVisibility() {
        // 使用基类的方法检查是否应该显示添加按钮
        val showBlueAddButton = shouldShowAddButton(blueTeamUsers.size)
        val showRedAddButton = shouldShowAddButton(redTeamUsers.size)

        // 展开状态的添加按钮逻辑
        updateExpandedAddButton(
            binding.blueTeamExpanded.ivAddMember,
            binding.blueTeamExpanded.rvUsers,
            binding.blueTeamExpanded.tvEmptyTips,
            showBlueAddButton,
            blueTeamUsers.isEmpty()
        )
        updateExpandedAddButton(
            binding.redTeamExpanded.ivAddMember,
            binding.redTeamExpanded.rvUsers,
            binding.redTeamExpanded.tvEmptyTips,
            showRedAddButton,
            redTeamUsers.isEmpty()
        )

        // 收起状态的添加按钮逻辑
        updateCollapsedAddButton(
            binding.blueTeamCollapsed.ivAddMember,
            tvEmptyTips = binding.blueTeamCollapsed.tvEmptyTips,
            showBlueAddButton,
            isEmpty = blueTeamUsers.isEmpty()
        )
        updateCollapsedAddButton(
            binding.redTeamCollapsed.ivAddMember,
            tvEmptyTips = binding.redTeamCollapsed.tvEmptyTips,
            showRedAddButton,
            isEmpty = redTeamUsers.isEmpty()
        )
    }

    /**
     * 更新分数显示
     */
    private fun updateScoreDisplay(blueScore: Int, redScore: Int) {
        binding.tvScoreLeft.text = blueScore.toString()
        binding.tvScoreRight.text = redScore.toString()
    }

    /**
     * 更新闪电位置到进度条相交处
     */
    private fun updateLightningPosition(blueWidth: Float, totalWidth: Float) {
        val lightningLayoutParams =
            binding.lottieLightning.layoutParams as? ConstraintLayout.LayoutParams
        lightningLayoutParams?.let {
            val lottieWidth =
                if (binding.lottieLightning.width == 0) 22.dp else binding.lottieLightning.width
            lightningLayoutParams.leftMargin =
                (blueWidth - lottieWidth / 2).toInt()
            binding.lottieLightning.layoutParams = lightningLayoutParams
        }

    }


    private fun toggleExpand() {
        val isExpanded = binding.blueTeamExpanded.root.isVisible
        setExpanded(!isExpanded)
    }

    override fun setExpanded(isExpanded: Boolean) {
        if (isExpanded) {
            // 显示展开状态的视图
            binding.blueTeamExpanded.root.visibility = VISIBLE
            binding.redTeamExpanded.root.visibility = VISIBLE
            // 隐藏收起状态的视图
            binding.blueTeamCollapsed.root.visibility = GONE
            binding.redTeamCollapsed.root.visibility = GONE
            binding.btnToggleExpand.text = "收起"
            binding.btnToggleExpand.setCompoundDrawablesWithIntrinsicBounds(
                cn.taqu.lib.base.R.drawable.ic_arrow_up_white_16, 0, 0, 0
            )

        } else {
            // 隐藏展开状态的视图
            binding.blueTeamExpanded.root.visibility = GONE
            binding.redTeamExpanded.root.visibility = GONE
            // 显示收起状态的视图
            binding.blueTeamCollapsed.root.visibility = VISIBLE
            binding.redTeamCollapsed.root.visibility = VISIBLE
            binding.btnToggleExpand.text = "展开"
            binding.btnToggleExpand.setCompoundDrawablesWithIntrinsicBounds(
                cn.taqu.lib.base.R.drawable.ic_arrow_down_white_16, 0, 0, 0
            )
        }

        // 控制添加按钮的显示（只在PK未开始且队伍未满时显示）
        updateAddButtonVisibility()
    }

    /**
     * 设置团队外观（名称和背景）
     */
    private fun setupTeamAppearance() {
        // 设置蓝方背景（右侧圆角）
        binding.blueTeamExpanded.root.setBackgroundResource(R.drawable.hichat_pk_bg_blue_team)
        binding.blueTeamCollapsed.root.setBackgroundResource(R.drawable.hichat_pk_bg_blue_team)

        // 设置红方背景（左侧圆角）
        binding.redTeamExpanded.root.setBackgroundResource(R.drawable.hichat_pk_bg_red_team)
        binding.redTeamCollapsed.root.setBackgroundResource(R.drawable.hichat_pk_bg_red_team)
    }

    /**
     * 绑定组队PK数据
     */
    override fun bindPKData(pkData: HiChatPKResponseBean) {
        super.bindPKData(pkData)

        // 只处理组队PK数据
        if (!pkData.isTeamPK()) return

        val teamPK = pkData.teamPk ?: return

        headerBinding.ivHelp.setOnClickListener {
            RouterLaunch.dealJumpData(context, pkData.rule)
        }

        // 更新蓝方队伍数据
        val blueMembers =
            teamPK.blueTeam?.members ?: emptyList()

        // 更新红方队伍数据
        val redMembers =
            teamPK.redTeam?.members ?: emptyList()

        // 更新内部数据
        blueTeamUsers.clear()
        blueTeamUsers.addAll(blueMembers)
        redTeamUsers.clear()
        redTeamUsers.addAll(redMembers)

        // 更新适配器
        updateLists()
        // 更新进度条
        binding.progressBar.updateProgress(
            teamPK.blueTeam?.totalCount ?: 0, teamPK.redTeam?.totalCount ?: 0
        )

        binding.tvBottomTip.isVisible = pkStatus == PKStatus.IN_PROGRESS

        // 更新添加按钮显示状态
        updateAddButtonVisibility()

        bindActionButtonStatus(
            btnStart = headerBinding.btnStart,
            btnTimeSettings = headerBinding.btnTimeSettings,
            isTeamPk = true
        )

        // 展开样式
        bindAddButtonStatus(
            btnAdd = binding.blueTeamExpanded.ivAddMember,
            tvEmptyTips = binding.blueTeamExpanded.tvEmptyTips,
            currentUsers = blueTeamUsers,
            excludeUsers = redTeamUsers,
            isExpanded = true,
            teamType = HiChatPkTeamType.BLUE_TEAM,
        )

        bindAddButtonStatus(
            btnAdd = binding.redTeamExpanded.ivAddMember,
            tvEmptyTips = binding.redTeamExpanded.tvEmptyTips,
            currentUsers = redTeamUsers,
            excludeUsers = blueTeamUsers,
            isExpanded = true,
            teamType = HiChatPkTeamType.RED_TEAM,
        )
        // 收起样式
        bindAddButtonStatus(
            btnAdd = binding.blueTeamCollapsed.ivAddMember,
            tvEmptyTips = binding.blueTeamCollapsed.tvEmptyTips,
            currentUsers = blueTeamUsers,
            excludeUsers = redTeamUsers,
            isExpanded = false,
            teamType = HiChatPkTeamType.BLUE_TEAM,
        )
        bindAddButtonStatus(
            btnAdd = binding.redTeamCollapsed.ivAddMember,
            tvEmptyTips = binding.redTeamCollapsed.tvEmptyTips,
            currentUsers = redTeamUsers,
            excludeUsers = blueTeamUsers,
            isExpanded = false,
            teamType = HiChatPkTeamType.RED_TEAM,
        )

        updateRemainTime(binding.tvTimer, pkData.remainTime?.toLongOrNull() ?: -1)
    }

    override fun onRoundIdChanged(mRoundId: String?) {
        if (mLockRoundId != mRoundId) {
            mBlueTeamLockUuid = null
            mRedTeamLockUuid = null
        }

    }

    /**
     * PK状态变化处理
     */
    override fun onPKStatusChanged(pkStatus: String) {
        super.onPKStatusChanged(pkStatus)

        if (pkStatus == PKStatus.IN_PROGRESS) {
            // PK开始
            headerBinding.btnStart.visibility = GONE
            headerBinding.btnTimeSettings.visibility = GONE


            // 设置进度条为PK开始状态
            binding.progressBar.setPkStarted(true)

            // 显示闪电
            XLottiePlayer.playByAssets(binding.lottieLightning, "lottie/hichat_pk_lightning/")
        } else {
            // PK结束
            headerBinding.btnStart.visibility = VISIBLE
            headerBinding.btnTimeSettings.visibility = VISIBLE

            // 设置进度条为PK结束状态
            binding.progressBar.setPkStarted(false)

            // 显示计时器，隐藏闪电
            binding.lottieLightning.isVisible = false
        }

        // 更新适配器状态
        blueTeamExpandedAdapter.pkStatus = pkStatus
        redTeamExpandedAdapter.pkStatus = pkStatus
        blueTeamCollapsedAdapter.pkStatus = pkStatus
        redTeamCollapsedAdapter.pkStatus = pkStatus

        // 更新添加按钮显示状态
        updateAddButtonVisibility()
    }

    override fun onUserLockClick(uuid: String, isLock: Boolean) {
        super.onUserLockClick(uuid, isLock)
        // 看下uuid是蓝队还是红队
        val isBlueTeam = blueTeamUsers.find { it.uuid == uuid }
        val isRedTeam = redTeamUsers.find { it.uuid == uuid }
        if (isBlueTeam != null) {
            mBlueTeamLockUuid = if (isLock) uuid else null
        } else if (isRedTeam != null) {
            mRedTeamLockUuid = if (isLock) uuid else null
        }
        updateLists()
    }
}