package com.xmhaibao.hichat.fragment

import HiChatRoomHallTabListFragment
import android.graphics.Typeface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.view.ViewGroup.MarginLayoutParams
import android.widget.TextView
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.viewpager.widget.ViewPager
import cn.taqu.lib.base.bean.UiState.Companion.doSuccess
import cn.taqu.lib.base.constants.PushConstants
import cn.taqu.lib.base.router.ARouterManager
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayout.OnTabSelectedListener
import com.xmhaibao.chatroom.api.bean.ChatRoomAppConfigBean
import com.xmhaibao.hichat.R
import com.xmhaibao.hichat.activity.HiChatRoomHallActivity
import com.xmhaibao.hichat.adapter.HiChatHallPageAdapter
import com.xmhaibao.hichat.bean.HiChatRoomCategoryInfoBean
import com.xmhaibao.hichat.databinding.HichatHallFragmentBinding
import com.xmhaibao.hichat.databinding.HichatHallRightTitleLayoutBinding
import com.xmhaibao.hichat.helper.pgshedule.HiChatHallPgScheduleHelper
import com.xmhaibao.hichat.router.HiChatPinsRouter
import com.xmhaibao.hichat.router.HiChatRoomRouter
import com.xmhaibao.hichat.track.HiChatRoomTrack
import com.xmhaibao.hichat.viewmodel.HiChatHallViewModel
import com.xmhaibao.live.api.interf.ILiveAndChatAction
import hb.common.data.AccountHelper
import hb.common.xstatic.fragment.BaseMVVMFragment
import hb.kotlin_extension.getCompatColor
import hb.kotlin_extension.getCompatDrawable
import hb.kotlin_extension.noneSyncLazy
import hb.skin.support.SkinCompatManager
import hb.utils.Loger
import hb.utils.SizeUtils
import hb.xstatic.core.skin.event.XSkinEventBean
import hb.xstyle.xdialog.XDialogUtils
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 畅聊圈 - -大厅
 *
 * <AUTHOR>
 * @date 2025/01/20
 */
class HiChatRoomHallFragment : BaseMVVMFragment<HiChatHallViewModel>(),
    ILiveAndChatAction,IAutoRefresh {
    companion object {

        const val CREATE_SOURCE = "create_Source"

        //畅聊圈大厅
        const val HI_CHAT_HALL = "hi_chat_hall"

        //娱乐大厅
        const val RECREATION_HALL = "recreation_hall"

        //搜索页面
        const val HI_CHAT_SEARCH = "hi_chat_search"

        //聊天广场
        const val HI_CHAT_PLAZA_HOME = "hi_chat_plaza_home"

        /**
         * @param createSource 来源
         */
        fun newInstance(createSource: String?, trackSource: String?) =
            HiChatRoomHallFragment().apply {
                val bundle = Bundle()
                bundle.putString(CREATE_SOURCE, createSource)
                bundle.putString(PushConstants.PUSH_TYPE_SOURCE, trackSource)
                this.arguments = bundle
            }
    }

    private val mBinding: HichatHallFragmentBinding by noneSyncLazy {
        HichatHallFragmentBinding.inflate(LayoutInflater.from(context))
    }

    private val mTitleBinding: HichatHallRightTitleLayoutBinding by noneSyncLazy {
        val binding = HichatHallRightTitleLayoutBinding.inflate(LayoutInflater.from(context))
        binding.root.setOnClickListener {
            mHiChatRoomTrack.hiChatHallClick(mTrackSource,"搜索")
            HiChatRoomRouter.launchHiChatSearch(mTrackSource)
        }
        binding
    }
    private var mPagerAdapter: HiChatHallPageAdapter? = null
    private var mIsInit = false
    private var mCreateSource: String? = null
    private var mTrackSource: String? = null
    private var mCreateHiChatRoomBtnView: View? = null
    private val mHiChatRoomTrack by noneSyncLazy { HiChatRoomTrack() }
    private val pgScheduleHelper by noneSyncLazy { HiChatHallPgScheduleHelper(this,mBinding.vsPgScheduleEnterView) }
    private var isChildCanRefresh = true

    override fun onCreateContentView(): Any {
        return mBinding.root
    }

    override fun isApplyLazyLoad(): Boolean {
        return true
    }

    override fun initViews() {
        super.initViews()
        mCreateSource = arguments?.getString(CREATE_SOURCE, "")
        mTrackSource  = arguments?.getString(PushConstants.PUSH_TYPE_SOURCE)
        pageTitle = if (mCreateSource == RECREATION_HALL) {
            "首页娱乐-畅聊圈大厅"
        } else {
            "畅聊圈大厅"
        }
        mHiChatRoomTrack.hiChatHallExp(mTrackSource)
        pgScheduleHelper.trackSource = mTrackSource
        mBinding.appbar.addOnOffsetChangedListener { _: AppBarLayout?, verticalOffset: Int ->
            val tempState = verticalOffset >= 0
            if (isChildCanRefresh != tempState) {
                isChildCanRefresh = tempState
                (getCurFragment() as? HiChatRoomHallTabListFragment)?.setRefreshEnable(isChildCanRefresh)
            }
        }
    }


    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (isResumed) {
            if (!isHidden) {
                // 切换 交友和娱乐等底部tab 的时候，会触发 onHiddenChanged，但是不会 onResume
                // 如果非当前选中 不会 isResumed，
                // 在这里进行判断 仅在当前选中（isResumed）的时候调用
                onRealResume()
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        lifecycleScope.launchWhenResumed {
            baseViewModel.tabData.collectLatest {
                it.doSuccess { tabList ->
                    initTabPageInfo(tabList)
                }
            }
        }
        lifecycleScope.launchWhenResumed {
            baseViewModel.hallConfigData.collectLatest {
                it.doSuccess {config->
                    if (config?.canCreateHiChat == "1") {
                        if (mCreateHiChatRoomBtnView == null) {
                            if (mCreateSource == RECREATION_HALL) {
                                mBinding.btnCreateHiChat.updateLayoutParams<LayoutParams> {
                                    width = SizeUtils.dp2px(144f)
                                }
                            }
                            mCreateHiChatRoomBtnView = mBinding.btnCreateHiChat.inflate()
                            mCreateHiChatRoomBtnView?.setOnClickListener {
                                onCreateRoomClick("")
                            }
                        }
                    }
                }
            }
        }

        initEverQuitRoomData()
    }

    /**
     * 初始化应用异常退出-畅聊圈未退出场景数据
     */
    private fun initEverQuitRoomData() {
        viewLifecycleOwner.lifecycleScope.launch {
            baseViewModel.everQuitRoomData.collectLatest {
                it.doSuccess { data ->
                    Loger.i(TAG,"获取到上次异常退出的畅聊圈ID ${data.hiChatUuid}")
                    // 弹窗提示
                    XDialogUtils.show(context,"提示",data.title,"确定",{ dialog,_ ->
                        dialog.dismiss()
                        HiChatPinsRouter.hiChatService().launchHiChat(data.hiChatUuid, "")
                    },"取消",{ dialog,_->
                        dialog.dismiss()
                    })
                }
            }
        }
    }

    private fun initTabPageInfo(tabList: MutableList<HiChatRoomCategoryInfoBean>?) {
        if (mPagerAdapter != null) {
            // 已经设置过 tab 以及 PageAdapter
            return
        }
        tabList?.apply {
            mBinding.vpRoomList.offscreenPageLimit = 1
            mPagerAdapter = HiChatHallPageAdapter(childFragmentManager).apply {
                setData(tabList, mCreateSource, mTrackSource)
            }
            mBinding.vpRoomList.adapter = mPagerAdapter
            mBinding.tabLayout.setupWithViewPager(mBinding.vpRoomList)
            mBinding.tabLayout.addOnTabSelectedListener(object : OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    updateTabText(tab, true)
                    mHiChatRoomTrack.hiChatHallClick(mTrackSource,"分类tab")
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {
                    updateTabText(tab, false)
                }

                override fun onTabReselected(tab: TabLayout.Tab?) {

                }

            })
            for (i in 0 until mBinding.tabLayout.tabCount) {
                val tabAt = mBinding.tabLayout.getTabAt(i)
                tabAt?.view?.let {
                    it.updateLayoutParams<MarginLayoutParams> {
                        marginEnd = SizeUtils.dp2px(8f)
                    }
                    it.background = context?.getCompatDrawable(R.drawable.hichat_hall_tab_item_bg)
                }
            }
            mBinding.vpRoomList.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                override fun onPageScrolled(
                    position: Int,
                    positionOffset: Float,
                    positionOffsetPixels: Int
                ) {
                }

                override fun onPageSelected(position: Int) {
                    val size = mPagerAdapter?.count ?: 0
                    if (position < 0 || position >= size) {
                        return
                    }
                    val verticalOffset = mBinding.appbar.top
                    isChildCanRefresh = verticalOffset >= 0
                    val fragment = mPagerAdapter?.getItem(position)
                    (fragment as? HiChatRoomHallTabListFragment)?.setRefreshEnable(isChildCanRefresh)
                }

                override fun onPageScrollStateChanged(state: Int) {
                }
            })
        }

    }

    private fun updateTabText(tab: TabLayout.Tab?, isSelect: Boolean) {
        val textView = tab?.view?.getChildAt(1) as? TextView
        textView?.apply {
            typeface = Typeface.defaultFromStyle(if (isSelect) Typeface.BOLD else Typeface.NORMAL)
        }
    }

    fun onEventMainThread(event: XSkinEventBean) {
        if (mContentView != null) {
            SkinCompatManager.getInstance().refreshView(mContentView)
        }
        context?.apply {
            mBinding.tabLayout.setTabTextColors(
                getCompatColor(hb.xstyle.R.color.TH_Gray600),
                getCompatColor(hb.xstyle.R.color.TH_Gray990)
            )
            for (i in 0 until mBinding.tabLayout.tabCount) {
                val tabAt = mBinding.tabLayout.getTabAt(i) ?: break
                tabAt.view.background = getCompatDrawable(R.drawable.hichat_hall_tab_item_bg)
            }
        }
    }


    private fun getCurFragment(): Fragment? =
        mPagerAdapter?.getItem(mBinding.vpRoomList.currentItem)

    override fun isApplyEventBus(): Boolean {
        return true
    }

    override fun onCreateRoomClick(position: String?) {
        super.onCreateRoomClick(position)
        HiChatRoomRouter.launchHiChatRoomCreate(mTrackSource)
        mHiChatRoomTrack.hiChatHallClick(mTrackSource, "创建房间")
    }

    override fun getRightTopView(): View? {
        if (ChatRoomAppConfigBean.getAppConfig().isChatroomRecreationTabLazyOptClose) {
            mTitleBinding.ivHiChatSearch.parent?.let {
                if (it is ViewGroup) {
                    it.removeView(mTitleBinding.ivHiChatSearch)
                }
            }
        }
        return mTitleBinding.ivHiChatSearch
    }


    override fun refreshData() {
        super.refreshData()
        (getCurFragment() as? HiChatRoomHallTabListFragment)?.refreshData()
    }


    override fun onResume() {
        super.onResume()
        Loger.i(TAG, "onResume")
        // 等tab 切换的时候，会触发 onResume，但是不会触发 onHiddenChanged
        if (!isHidden) {
            pgScheduleHelper.requestSquareList()
            onRealResume()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        pgScheduleHelper.release()
    }

    /**
     * 真实onResume
     */
    private fun onRealResume() {
        Loger.i(TAG, "onRealResume")
        if (!mIsInit) {
            // 懒加载
            mIsInit = true
            baseViewModel.getTabData()
            baseViewModel.getHiChatHallConfig()
            baseViewModel.getLastExitRoomData()
            showHiChatRecommendDialog()
        }
    }

    /**
     * 刷新节目单
     */
    override fun autoRefreshPgSchedule() {
        pgScheduleHelper.requestSquareList()
    }

    /**
     * 展示推荐畅聊圈
     */
    private fun showHiChatRecommendDialog() {
        if (requireActivity() is HiChatRoomHallActivity) {
            HiChatPinsRouter.hiChatService().showHiChatRecommendDialog("交友-畅聊圈大厅")
        }
    }

    override fun onPageSelected() {
        super.onPageSelected()
        if (ARouterManager.appService().isNavigationActivity(requireActivity())) {
            HiChatPinsRouter.hiChatService().showHiChatRecommendDialog("娱乐-畅聊圈tab")
        }
    }
}