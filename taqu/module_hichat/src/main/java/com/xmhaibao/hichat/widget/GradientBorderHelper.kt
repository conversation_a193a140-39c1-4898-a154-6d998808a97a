package com.xmhaibao.hichat.widget

import android.graphics.Color
import android.graphics.drawable.LayerDrawable
import android.view.View

/**
 * 渐变边框辅助类
 * 用于快速应用渐变边框效果
 */
object GradientBorderHelper {

    /**
     * 为 View 设置渐变边框 + 透明度渐变背景
     * @param view 目标 View
     * @param borderWidth 边框宽度，默认 3f
     * @param cornerRadius 圆角半径，默认 36f
     * @param borderStartColor 边框起始颜色
     * @param borderEndColor 边框结束颜色
     * @param bgStartColor 背景起始颜色（带透明度）
     * @param bgEndColor 背景结束颜色（带透明度）
     */
    fun applyPkGradientBorder(
        view: View,
        borderWidth: Float = 3f,
        cornerRadius: Float = 36f,
        borderStartColor: String = "#FF5DBE",
        borderEndColor: String = "#42C0FF",
        bgStartColor: String = "#B2460031",
        bgEndColor: String = "#B2000631"
    ) {
        // 创建边框 Drawable
        val borderDrawable = GradientBorderDrawable(
            borderWidth = borderWidth,
            cornerRadius = cornerRadius,
            startColor = Color.parseColor(borderStartColor),
            endColor = Color.parseColor(borderEndColor)
        )

        // 创建背景 Drawable
        val bgDrawable = GradientBackgroundDrawable(
            cornerRadius = cornerRadius,
            startColor = Color.parseColor(bgStartColor),
            endColor = Color.parseColor(bgEndColor)
        )

        // 组合两个 Drawable
        val layerDrawable = LayerDrawable(arrayOf(bgDrawable, borderDrawable))
        
        view.background = layerDrawable
    }

    /**
     * 只设置渐变边框，不设置背景
     */
    fun applyGradientBorderOnly(
        view: View,
        borderWidth: Float = 3f,
        cornerRadius: Float = 36f,
        startColor: String = "#FF5DBE",
        endColor: String = "#42C0FF"
    ) {
        val borderDrawable = GradientBorderDrawable(
            borderWidth = borderWidth,
            cornerRadius = cornerRadius,
            startColor = Color.parseColor(startColor),
            endColor = Color.parseColor(endColor)
        )
        
        view.background = borderDrawable
    }
}
