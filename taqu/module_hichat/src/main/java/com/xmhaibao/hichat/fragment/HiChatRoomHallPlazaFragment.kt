package com.xmhaibao.hichat.fragment

import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.FrameLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.taqu.lib.base.bean.UiState.Companion.doSuccess
import cn.taqu.lib.base.constants.PushConstants
import cn.taqu.lib.base.widget.Item_decoration.GridSpaceItemDecoration
import cn.taqu.lib.base.xstatic.view.XStaticRefreshLayout
import com.xmhaibao.chatroom.api.utils.RecreationLogUploadUtil
import com.xmhaibao.family.intf.IMessagePlazaChatFragment
import com.xmhaibao.hichat.R
import com.xmhaibao.hichat.bean.HiChatRoomHallListItemBean
import com.xmhaibao.hichat.helper.pgshedule.HiChatHallPgScheduleHelper
import com.xmhaibao.hichat.router.HiChatPinsRouter
import com.xmhaibao.hichat.track.HiChatRoomTrack
import com.xmhaibao.hichat.viewholder.hall.HiChatHallListItemViewHolder
import com.xmhaibao.hichat.viewmodel.HiChatHallPlazaViewModel
import com.xmhaibao.hichat.viewmodel.HiChatHallTabListViewModel
import com.xmhaibao.hichat.viewmodel.HiChatHallViewModel
import dp
import hb.common.xstatic.fragment.BaseMVVMFragment
import hb.kotlin_extension.noneSyncLazy
import hb.skin.support.SkinCompatManager
import hb.utils.Loger
import hb.xstatic.core.skin.event.XSkinEventBean
import hb.xstatic.list4page.XListPage
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 * 聊天广场 畅聊圈tab
 *
 * <AUTHOR>
 * @date 2025/5/22
 */
class HiChatRoomHallPlazaFragment : BaseMVVMFragment<HiChatHallTabListViewModel>(),
    IMessagePlazaChatFragment {
    companion object {

        /**
         * @param tabId 畅聊圈tabId
         */
        fun newInstance(tabId: String?) =
            HiChatRoomHallPlazaFragment().apply {
                val bundle = Bundle()
                bundle.putString(PushConstants.PUSH_VALUE_TABID, tabId)
                this.arguments = bundle
            }
    }

    private val rootId = View.generateViewId()
    private var mTabId: String? = null
    private val hiChatHallViewModel by viewModels<HiChatHallViewModel>()
    private val plazaViewModel by activityViewModels<HiChatHallPlazaViewModel>()

    private val mCreateSource = HiChatRoomHallFragment.HI_CHAT_PLAZA_HOME
    private val mTrackSource = "聊天广场"
    private var autoRefreshTime = 0L
    private var onPauseTime = 0L
    private var mStayRefreshJob: Job?=null
    private var mIsRealResume = false
    private val mHiChatRoomTrack by noneSyncLazy { HiChatRoomTrack() }
    private var mListPage: XListPage? = null
    private var pgScheduleHelper: HiChatHallPgScheduleHelper? = null

    override fun onCreateContentView(): Any {
        return FrameLayout(requireContext()).apply {
            setPadding(12.dp, 0, 12.dp, 0)
            id = rootId
        }
    }

    override fun initViews() {
        super.initViews()
        mTabId = arguments?.getString(PushConstants.PUSH_VALUE_TABID)
        pageTitle = "聊天广场-列表"
        baseViewModel.putParam(tabId = mTabId, createSource = mCreateSource)

        lifecycleScope.launchWhenResumed {
            hiChatHallViewModel.hallConfigData.collectLatest {
                it.doSuccess { config ->
                    autoRefreshTime = config?.autoRefreshTime ?: 0L
                    if (mIsRealResume) {
                        checkStayRefresh(mListPage?.recyclerView?.scrollState == RecyclerView.SCROLL_STATE_IDLE)
                    }
                }
            }
        }

    }


    private fun loadData() {
        if (mContentView == null) {
            RecreationLogUploadUtil.uploadContentViewNpeLog("HiChatRoomHallPlazaFragment")
            return
        }
        context?.let { context ->
            mListPage = XListPage.create(this)
                .setLayoutManager(GridLayoutManager(context, 2))
                .setEmptyView(
                    cn.taqu.lib.base.R.drawable.base_new_empty_new_ic,
                    "暂无进行中的畅聊圈"
                )
                .setRefreshLayoutSetter {
                    if (it is XStaticRefreshLayout) {
                        it.disableWhenHorizontalMove(true)
                    }
                }
                .setOnViewHolderCreatedListener { holder ->
                    if (holder is HiChatHallListItemViewHolder) {
                        lifecycle.addObserver(holder)
                        holder.mTabId = mTabId
                        holder.mItemClickCallBack = {
                            it?.apply {
                                mHiChatRoomTrack.hiChatHallClick(
                                    mTrackSource, "列表点击", hichatUuid, onlineCount
                                )
                                hichatUuid?.apply {
                                    HiChatPinsRouter.hiChatService().launchHiChat(this, mTrackSource)
                                }
                            }
                        }
                    }
                }
                .registerItem(
                    HiChatRoomHallListItemBean::class.java,
                    HiChatHallListItemViewHolder::class.java
                )
                .attachToView(findViewById(rootId))
            mListPage?.bindViewModel(this, baseViewModel)

            mListPage?.recyclerView?.addItemDecoration(
                GridSpaceItemDecoration.Builder(context)
                    .color(Color.TRANSPARENT)
                    .gridSize(7f.dp.toInt(), 7f.dp.toInt())
                    .setIncludeEdge(false)
                    .setHasHeaderView(true)
                    .build()
            )

            // 动态创建 ViewStub
            val viewStub = ViewStub(requireContext()).apply {
                layoutResource = R.layout.hichat_hall_pgschedule_enter_view_layout
                inflatedId = View.generateViewId()
                val params = FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                layoutParams = params
            }

            val frameLayout = FrameLayout(requireContext()).apply {
                val params = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    1
                )
                layoutParams = params
            }
            frameLayout.addView(viewStub)
            mListPage?.adapter?.addHeaderView(frameLayout)
            mListPage?.refreshLayout?.setOnRefreshListenerX {
                pgScheduleHelper?.requestSquareList()
                mListPage?.loadData()
            }
            pgScheduleHelper = HiChatHallPgScheduleHelper(owner = this, pgScheduleStub = viewStub, parentLayout = frameLayout) {
                mListPage?.setEnableRefresh(it)
            }
        }
    }

    override fun getFragment(): Fragment {
        return this
    }

    override fun onResume() {
        super.onResume()
        // 等tab 切换的时候，会触发 onResume，但是不会触发 onHiddenChanged
        if (!isHidden) {
            onRealResume()
        }
    }

    override fun onPause() {
        super.onPause()
        onRealPause()
    }

    /**
     * 真实onResume
     */
    private fun onRealResume() {
        Loger.i(TAG, "onRealResume:$baseViewModel")
        mIsRealResume = true
        if (mListPage == null) {
            loadData()
            hiChatHallViewModel.getHiChatHallConfig()
            pgScheduleHelper?.requestSquareList()
            plazaViewModel.showHiChatRecommendDialog()
        } else {
            checkLeaveRefresh()
        }
        checkStayRefresh(true)
    }

    /**
     * 真实onPause
     */
    private fun onRealPause() {
        mIsRealResume = false
        onPauseTime = System.currentTimeMillis()
        checkStayRefresh(false)
    }

    fun onEventMainThread(event: XSkinEventBean) {
        if (mContentView != null) {
            SkinCompatManager.getInstance().refreshView(mContentView)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        checkStayRefresh(false)
        pgScheduleHelper?.release()
    }

    /**
     * 检测离开n分钟，是否需要自动刷新tab
     */
    private fun checkLeaveRefresh() {
        if (autoRefreshTime > 0) {
            if (onPauseTime <= 0) {
                return
            }
            val leaveTime = System.currentTimeMillis() - onPauseTime
            if (leaveTime > autoRefreshTime * 1000) {
                mListPage?.loadData()
                pgScheduleHelper?.requestSquareList()
            }
        }
    }


    /**
     * 检测停留n分钟是否需要触发下拉刷新
     */
    private fun checkStayRefresh(isStay: Boolean) {
        mStayRefreshJob?.takeIf { it.isActive }?.cancel()
        if (isStay && autoRefreshTime > 0) {
            mStayRefreshJob = lifecycleScope.launch {
                while (isActive) {
                    delay(autoRefreshTime * 1000)
                    mListPage?.loadData()
                    pgScheduleHelper?.requestSquareList()
                }
            }
        }
    }
}