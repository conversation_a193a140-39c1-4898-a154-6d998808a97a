package com.xmhaibao.hichat.fragment

import android.os.Bundle
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.lifecycle.lifecycleScope
import cn.taqu.lib.base.bean.UiState
import com.xmhaibao.hichat.bean.HiChatOnMicUserBean
import com.xmhaibao.hichat.bean.HiChatOnMicUserListBean
import com.xmhaibao.hichat.dialog.HiChatRoomUserCardDialog
import com.xmhaibao.hichat.helper.HiChatRoomCoreHelper
import com.xmhaibao.hichat.track.HiChatRoomTrack
import com.xmhaibao.hichat.viewholder.mic.HiChatOnMicViewHolder
import com.xmhaibao.hichat.viewmodel.HiChatMicViewModel
import com.xmhaibao.hichat.widget.HiChatEmptyView
import hb.common.xstatic.fragment.BaseFragment
import hb.kotlin_extension.noneSyncLazy
import hb.utils.ColorUtils
import hb.xstatic.list4page.IListDataSource
import hb.xstatic.list4page.XListHttpDataSource
import hb.xstatic.list4page.XListPage
import hb.xstatic.mvvm.XViewModelProviders
import hb.xstyle.xdialog.XDialog
import kotlinx.coroutines.flow.collectLatest

class HiChatOnMicListFragment : BaseFragment() {
    private lateinit var xListPage: XListPage

    private val hiChatMicViewModel: HiChatMicViewModel by lazy {
        HiChatRoomCoreHelper.getViewModel(HiChatMicViewModel::class.java)
    }

    private val hiChatRoomTrack by noneSyncLazy { HiChatRoomTrack() }

    override fun onCreateContentView(): Any {
        return FrameLayout(requireContext())
    }

    override fun initViews() {
        super.initViews()

        xListPage = XListPage.create(this)
            .setDataSource(initDataSource())
            .attachToView(mContentView as ViewGroup)
            .registerItem(HiChatOnMicUserBean::class.java, HiChatOnMicViewHolder::class.java)
            .setOnViewHolderCreatedListener {
                if (it is HiChatOnMicViewHolder) {
                    it.onMicActionListener = object : HiChatOnMicViewHolder.OnMicActionListener {
                        override fun viewUserInfo(hiChatOnMicUserBean: HiChatOnMicUserBean) {
                            HiChatRoomUserCardDialog.showDialog(context,
                                hiChatOnMicUserBean.uuid,
                                HiChatRoomCoreHelper.currentRoomUuid())
                        }

                        override fun openMic(hiChatOnMicUserBean: HiChatOnMicUserBean) {
                            hiChatRoomTrack.hiChatMicManage(HiChatRoomCoreHelper.currentRoomUuid(), "解除禁麦")
                            hiChatMicViewModel.openMic(hiChatOnMicUserBean.uuid!!, hiChatOnMicUserBean.name!!)
                        }

                        override fun closeMic(hiChatOnMicUserBean: HiChatOnMicUserBean) {
                            hiChatRoomTrack.hiChatMicManage(HiChatRoomCoreHelper.currentRoomUuid(), "禁麦")
                            hiChatMicViewModel.closeMic(hiChatOnMicUserBean.uuid!!, hiChatOnMicUserBean.name!!)
                        }

                        override fun offMic(hiChatOnMicUserBean: HiChatOnMicUserBean) {
                            XDialog.newBuilder(context)
                                .type(XDialog.TYPE_PROMPT)
                                .title("提示")
                                .message("是否将 ${hiChatOnMicUserBean.name} 抱下麦?")
                                .negativeButton("否") { dialog, _ ->
                                    dialog.dismiss()
                                }
                                .positiveButton("是") { dialog, _ ->
                                    dialog.dismiss()
                                    hiChatRoomTrack.hiChatMicManage(HiChatRoomCoreHelper.currentRoomUuid(), "抱Ta下麦")
                                    hiChatMicViewModel.offMic(hiChatOnMicUserBean.uuid!!, hiChatOnMicUserBean.name!!)
                                }
                                .show()
                        }
                    }
                }
            }
            .setEnableRefresh(true)
            .setEnableLoadMore(false)
            .setLoadDataOnAttachView(false)
            .setEmptyView(HiChatEmptyView(requireContext()).apply {
                setTextColor(ColorUtils.getColor(hb.xstyle.R.color.TH_Gray990_Normal))
                setText("暂无上麦用户")
            }, FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT))
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        lifecycleScope.launchWhenCreated {
            hiChatMicViewModel.openMicFlow.collect {
                if (it is UiState.Success) {
                    val targetUuid = it.data
                    val index = xListPage.adapter.items.indexOfFirst { item ->
                        if (item is HiChatOnMicUserBean) {
                            return@indexOfFirst item.uuid == targetUuid
                        }
                        return@indexOfFirst false
                    }
                    if (index >= 0) {
                        val hiChatOnMicUserBean =
                            xListPage.adapter.items[index] as HiChatOnMicUserBean
                        hiChatOnMicUserBean.updateMicOpened()
                        xListPage.adapter.notifyItemChanged(index)
                    }
                }
            }
        }
        lifecycleScope.launchWhenCreated {
            hiChatMicViewModel.closeMicFlow.collect {
                if (it is UiState.Success) {
                    val targetUuid = it.data
                    val index = xListPage.adapter.items.indexOfFirst { item ->
                        if (item is HiChatOnMicUserBean) {
                            return@indexOfFirst item.uuid == targetUuid
                        }
                        return@indexOfFirst false
                    }
                    if (index >= 0) {
                        val hiChatOnMicUserBean =
                            xListPage.adapter.items[index] as HiChatOnMicUserBean
                        hiChatOnMicUserBean.updateMicClosed()
                        xListPage.adapter.notifyItemChanged(index)
                    }
                }
            }
        }
        lifecycleScope.launchWhenCreated {
            hiChatMicViewModel.offMicFlow.collect {
                if (it is UiState.Success) {
                    val targetUuid = it.data
                    val index = xListPage.adapter.items.indexOfFirst { item ->
                        if (item is HiChatOnMicUserBean) {
                            return@indexOfFirst item.uuid == targetUuid
                        }
                        return@indexOfFirst false
                    }
                    if (index >= 0) {
                        xListPage.adapter.items.removeAt(index)
                        if (xListPage.adapter.items.isEmpty()) {
                            xListPage.adapter.notifyDataSetChanged()
                        } else {
                            xListPage.adapter.notifyItemRemoved(index)
                        }
                    }
                }
            }
        }
        lifecycleScope.launchWhenCreated {
            hiChatMicViewModel.agreeApplyMicFlow.collectLatest {
                if (it is UiState.Success) {
                    xListPage.loadData()
                }
            }
        }

        xListPage.loadData()
    }

    private fun initDataSource(): IListDataSource<HiChatOnMicUserListBean> {
        return object : XListHttpDataSource<HiChatOnMicUserListBean>() {
        }.apply {
            setHttpParams(hiChatMicViewModel.getOnMicList().httpParams)
        }
    }
}