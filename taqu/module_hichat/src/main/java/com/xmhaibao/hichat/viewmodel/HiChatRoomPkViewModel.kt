package com.xmhaibao.hichat.viewmodel

import android.app.Application
import androidx.lifecycle.viewModelScope
import com.xmhaibao.hichat.bean.pk.HiChatPKResponseBean
import com.xmhaibao.hichat.bean.pk.HiChatPkConfigBean
import com.xmhaibao.hichat.bean.pk.HiChatPkNoticeBean
import com.xmhaibao.hichat.helper.HiChatRoomCoreHelper
import com.xmhaibao.hichat.repository.HiChatFunGameRepository
import hb.common.xstatic.viewmodel.BaseViewModel
import hb.kotlin_extension.noneSyncLazy
import hb.xrequest.launchHttp
import hb.xtoast.XToastUtils
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * 畅聊圈pk viewmodel
 *
 * <AUTHOR>
 * @date 2025/7/18
 */
class HiChatRoomPkViewModel(application: Application) : BaseViewModel(application) {

    private val mRepository by noneSyncLazy {
        HiChatFunGameRepository()
    }

    /**
     * pk通知（开始、结束）
     */
    private val _pkNoticeFlow = MutableSharedFlow<HiChatPkNoticeBean?>()
    val pkNoticeFlow: Flow<HiChatPkNoticeBean?> = _pkNoticeFlow

    /**
     * pk配置
     */
    private val _pkConfigFlow =
        MutableStateFlow<HiChatPkConfigBean>(HiChatPkConfigBean.getDefaultConfig())
    val pkConfigFlow: Flow<HiChatPkConfigBean> = _pkConfigFlow

    /**
     * pk信息
     */
    private val _pkInfoFlow = MutableStateFlow<HiChatPKResponseBean?>(null)
    val pkInfoFlow: Flow<HiChatPKResponseBean?> = _pkInfoFlow

    /**
     * 轮询PK信息接口 job
     */
    private var mPollingPkJob: Job? = null

    /**
     * 分发数据
     */
    fun dispatchNoticeEvent(analysis: HiChatPkNoticeBean?) {
        viewModelScope.launch {
            analysis?.let {
                _pkNoticeFlow.emit(it)
            }
        }
    }


    /**
     * 拉取pk配置
     */
    fun fetchPkConfig() {
        viewModelScope.launchHttp({
            val pkConfig = mRepository.getPkSettingConfig(HiChatRoomCoreHelper.currentRoomUuid())
            pkConfig?.let {
                _pkConfigFlow.value = it
            }
        }) {
            return@launchHttp true
        }
    }

    /**
     * 触发pk信息变化通知
     */
    fun noticePkInfoChange(hash: String? = "") {
        if (_pkInfoFlow.value?.hash == hash) return
        startPollingPkInfo()
    }

    /**
     * 获取当前pk剩余时间
     */
    fun currentPkRemainingTime(): Long {
        return _pkInfoFlow.value?.remainTime?.toLongOrNull() ?: 0
    }

    /**
     * 获取当前pk配置
     */
    fun currentPkConfig(): HiChatPkConfigBean {
        return _pkConfigFlow.value
    }

    private fun startPollingPkInfo() {
        mPollingPkJob?.cancel()
        mPollingPkJob = viewModelScope.launch {
            repeat(Int.MAX_VALUE) {
                var delayTime = 5000L
                launchHttp({
                    val response =
                        mRepository.fetchPkInfo(hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid())
                    response?.let {
                        if (it.isPKStarted()) {
                            // 进行中3s轮询，其他状态5s轮询
                            delayTime = 3000L
                        }

                        // 玩法未开启，结束轮询
                        if (it.isNoOpen()) {
                            _pkInfoFlow.value = null
                            mPollingPkJob?.cancel()
                            return@launchHttp
                        }
                        _pkInfoFlow.value = it
                    }
                }) {
                    delayTime = 3000L
                    return@launchHttp true
                }.join()
                delay(delayTime)
            }
        }
    }


    /**
     * 添加PK成员
     */
    fun addPkMember(teamType: String, users: String) {
        viewModelScope.launchHttp({
            mRepository.addPkMember(HiChatRoomCoreHelper.currentRoomUuid(), teamType, users)
        })
    }


    /**
     * 开始pk
     */
    fun startPk() {
        viewModelScope.launchHttp({
            mRepository.startPk(HiChatRoomCoreHelper.currentRoomUuid())
        })
    }

    /**
     * pk重开
     */
    fun resetPk() {
        viewModelScope.launchHttp({
            mRepository.resetPk(HiChatRoomCoreHelper.currentRoomUuid())
        })
    }


    /**
     * 结束本轮pk
     */
    fun endPk(roundId: String?, isTeamPk: Boolean) {
        viewModelScope.launchHttp({
            val response =
                mRepository.endPk(HiChatRoomCoreHelper.currentRoomUuid(), roundId = roundId)
            response?.let {
                XToastUtils.show("${if (isTeamPk) "组队" else "多人"}PK已结束")
            }
        })
    }


    /**
     * pk入口-关闭pk
     */
    fun closePk() {
        viewModelScope.launchHttp({
            mRepository.closePk(HiChatRoomCoreHelper.currentRoomUuid())
        })
    }

    /**
     * 设置pk时间
     */
    fun setPkTime(roundTime: String?) {
        viewModelScope.launchHttp({
            mRepository.setPkTime(HiChatRoomCoreHelper.currentRoomUuid(), roundTime)
        })
    }

    /**
     * 延长pk时间
     */
    fun delayPkTime(time: String?) {
        viewModelScope.launchHttp({
            val response = mRepository.delayPkTime(HiChatRoomCoreHelper.currentRoomUuid(), time)
            response?.let {
                XToastUtils.show("已增加${time}分钟")
            }
        })
    }
}