package com.xmhaibao.hichat.widget.pk

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.xmhaibao.hichat.R
import com.xmhaibao.hichat.bean.HiChatPkUserBean
import com.xmhaibao.hichat.bean.pk.HiChatPKResponseBean
import com.xmhaibao.hichat.bean.pk.HiChatPkTeamType
import com.xmhaibao.hichat.bean.pk.PKStatus
import com.xmhaibao.hichat.constant.HiChatRoleConstant
import com.xmhaibao.hichat.dialog.HiChatSendUserSelectDialog
import com.xmhaibao.hichat.helper.HiChatRoomCoreHelper
import com.xmhaibao.hichat.track.HiChatPkTrack
import com.xmhaibao.hichat.viewmodel.HiChatIntentViewModel
import com.xmhaibao.hichat.viewmodel.HiChatRoomNavigation
import com.xmhaibao.hichat.widget.pk.HiChatPKUserAdapter.PkAdapterCallBack
import dp
import hb.common.data.AccountHelper
import hb.drawable.shape.view.HbTextView
import hb.kotlin_extension.countDownCoroutines
import hb.kotlin_extension.isNull
import hb.kotlin_extension.join
import hb.kotlin_extension.viewScope
import hb.utils.ColorUtils
import hb.ximage.fresco.BaseDraweeView
import hb.xtoast.XToastUtils
import kotlinx.coroutines.Job

/**
 * PK视图的基类，封装公共的PK逻辑
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
abstract class HiChatBasePKView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr), PkAdapterCallBack {

    // 可配置的最大人数，默认为10人
    var maxTeamSize: Int = 10
        set(value) {
            field = if (value > 0) value else 10
            // 当设置新的最大人数时，更新按钮显示状态
            onMaxTeamSizeChanged()
        }

    /**
     * 当前pk状态
     * 0-未开启 1开启未开始-准备中 2-PK进行中 3-已结束
     */
    var pkStatus = PKStatus.NOT_STARTED

    /**
     * 当前用户身份
     */
    var userRole = HiChatRoleConstant.NORMAL

    /**
     * 当前场次id
     */
    var mRoundId: String? = null

    /**
     * 埋点上报
     */
    var mTracker = HiChatPkTrack()

    /**
     * 当最大人数改变时的回调，子类可以重写来更新UI
     */
    protected open fun onMaxTeamSizeChanged() {
        // 默认实现为空，子类可以重写
    }

    /**
     * 黄色背景色
     */
    private val yellowBgSeatColor = ColorUtils.getColor(hb.xstyle.R.color.TH_Yellow600)

    /**
     * 白色背景色
     */
    private val whiteBgSeatColor = ColorUtils.getColor(hb.xstyle.R.color.TH_Gray001)

    // PK状态变化监听器
    private var pkStatusListener: PKStatusListener? = null

    // ViewModel引用
//    protected var pkViewModel: PKViewModel? = null

    interface PKStatusListener {
        fun onPKStarted()
        fun onPKEnded(roundId: String?,isTeamPk: Boolean)

        /**
         * 重开一轮
         */
        fun onPkReset()

        /**
         * 添加成员
         * @param teamType 1-蓝队 2-红队 0-多人pk
         * @param users 成员uuid列表
         */
        fun addMember(teamType: String, users: String)

        /**
         * 选择pk时长
         */
        fun onPkTimeSelect()

        /**
         * 延长PK时间
         */
        fun onPkDelay()
    }

    /**
     * 设置PK状态监听器
     */
    fun setPKStatusListener(listener: PKStatusListener?) {
        pkStatusListener = listener
    }

    /**
     * 检查是否可以添加更多用户
     */
    protected fun canAddMoreUsers(currentSize: Int): Boolean {
        return isPKPreparing() && HiChatRoleConstant.isMasterOrManager(userRole)
//                && currentSize < maxTeamSize
    }

    /**
     * 添加参赛成员
     * @param teamType 1-蓝队 2-红队 0-多人pk
     */
    private fun addMembers(
        teamType: String,
        currentTeamUsers: List<HiChatPkUserBean>,
        excludeUsers: List<HiChatPkUserBean>,
        title: String
    ) {
        val preSelectUuids =
            currentTeamUsers.filter { it.uuid.isNullOrEmpty().not() }.map { it.uuid!! }
        val excludeUsers = excludeUsers.filter { it.uuid.isNullOrEmpty().not() }.map { it.uuid!! }
        HiChatSendUserSelectDialog.showDialog(
            context = context,
            hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid(),
            preselectUuids = preSelectUuids,
            maxSelectCount = maxTeamSize,
            title = title,
            preEliminateUuids = excludeUsers,
            type = HiChatSendUserSelectDialog.TYPE_PK
        ) { users ->
            pkStatusListener?.addMember(teamType, users.map { it.uuid }.join(","))
        }
    }


    /**
     * 检查PK是否已开始（公共访问方法）
     */
    private fun isPKStarted(): Boolean = pkStatus == PKStatus.IN_PROGRESS

    /**
     * 准备中
     */
    private fun isPKPreparing(): Boolean = pkStatus == PKStatus.PREPARING

    /**
     * pk已结束
     */
    private fun isPkEnd(): Boolean = pkStatus == PKStatus.ENDED


    /**
     * 展开/收起的公共逻辑
     */
    protected fun toggleExpandCommon(
        isCurrentlyExpanded: Boolean, onSetExpanded: (Boolean) -> Unit
    ) {
        onSetExpanded(!isCurrentlyExpanded)
    }

    /**
     * 设置展开状态的公共逻辑
     */
    protected fun setExpandedCommon(
        isExpanded: Boolean,
        expandedView: android.view.View,
        collapsedView: android.view.View,
        toggleButton: android.widget.TextView
    ) {
        if (isExpanded) {
            expandedView.visibility = VISIBLE
            collapsedView.visibility = GONE
            toggleButton.text = "收起"
            toggleButton.setCompoundDrawablesWithIntrinsicBounds(
                cn.taqu.lib.base.R.drawable.ic_arrow_up_white_16, 0, 0, 0
            )
        } else {
            expandedView.visibility = GONE
            collapsedView.visibility = VISIBLE
            toggleButton.text = "展开"
            toggleButton.setCompoundDrawablesWithIntrinsicBounds(
                cn.taqu.lib.base.R.drawable.ic_arrow_down_white_16, 0, 0, 0
            )
        }
    }

    /**
     * 更新添加按钮可见性的公共逻辑
     */
    protected fun shouldShowAddButton(currentSize: Int): Boolean {
        // 准备中，且为管理员，有添加按钮 。  成员且当前size为0 要展示空按钮
        return isPKPreparing() && (HiChatRoleConstant.isMasterOrManager(userRole) || (!HiChatRoleConstant.isMasterOrManager(
            userRole
        ) && currentSize == 0))
    }

    /**
     * 绑定PK数据，子类应该重写此方法来处理数据变化
     */
    open fun bindPKData(pkData: HiChatPKResponseBean) {
        // 更新PK状态
        val wasPkStatus = pkStatus
        pkStatus = pkData.status ?: PKStatus.PREPARING
        userRole = pkData.role
        val wasRoundId = mRoundId
        mRoundId = pkData.roundId

        // 如果状态发生变化，触发状态变化处理
        if (wasPkStatus != pkStatus) {
            onPKStatusChanged(pkStatus)
        }
        // 如果场次id发生变化，触发场次变化处理
        if (wasRoundId != mRoundId) {
            onRoundIdChanged(mRoundId)
        }
    }

    /**
     * 场次变化
     */
    abstract fun onRoundIdChanged(mRoundId: String?)

    /**
     * PK状态变化时的处理，子类可以重写
     */
    protected open fun onPKStatusChanged(pkStatus: String) {
        // 默认实现为空，子类可以重写
    }

    /**
     * 操作按钮状态绑定
     * 时间设置按钮、开始(结束)按钮
     * @param btnStart 开始按钮
     * @param btnTimeSettings 时间设置按钮
     */
    protected fun bindActionButtonStatus(
        btnStart: HbTextView, btnTimeSettings: HbTextView,isTeamPk: Boolean
    ) {
        val isManager = HiChatRoleConstant.isMasterOrManager(userRole)
        btnStart.isVisible = isManager
        btnTimeSettings.isVisible = isManager
        if (isPKStarted()) {
            btnStart.text = "结束本轮"
            btnStart.shaper().solid(whiteBgSeatColor)
            btnTimeSettings.text = "延长时间"
            btnStart.setOnClickListener {
                mTracker.hiChatPKPageClk(
                    hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid(),
                    clkName = "结束本轮"
                )
                pkStatusListener?.onPKEnded(mRoundId, isTeamPk = isTeamPk)
            }
            btnTimeSettings.setOnClickListener {
                mTracker.hiChatPKPageClk(
                    hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid(),
                    clkName = "延长时间"
                )
                pkStatusListener?.onPkDelay()
            }
        }
        if (isPKPreparing()) {
            btnStart.text = "开始"
            btnStart.shaper().solid(yellowBgSeatColor)
            btnTimeSettings.text = "时间设置"
            btnStart.setOnClickListener {
                mTracker.hiChatPKPageClk(
                    hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid(),
                    clkName = "开始"
                )
                pkStatusListener?.onPKStarted()
            }
            btnTimeSettings.setOnClickListener {
                mTracker.hiChatPKPageClk(
                    hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid(),
                    clkName = "时间设置"
                )
                pkStatusListener?.onPkTimeSelect()
            }
        }
        if (isPkEnd()) {
            btnStart.text = "再来一轮"
            btnStart.shaper().solid(whiteBgSeatColor)
            // pk结束不展示时间设置
            btnTimeSettings.isVisible = false
            btnStart.setOnClickListener { pkStatusListener?.onPkReset() }
        }
    }

    /**
     * 添加、非管理空 按钮状态绑定
     * @param btnAdd 添加按钮
     * @param currentUsers 当前队伍人员
     * @param isExpanded 是否展开
     */
    protected fun bindAddButtonStatus(
        btnAdd: BaseDraweeView,
        tvEmptyTips: TextView,
        currentUsers: List<HiChatPkUserBean>,
        excludeUsers: List<HiChatPkUserBean> = emptyList(),
        isExpanded: Boolean,
        teamType: String,
    ) {
        tvEmptyTips.text = ""
        val isManager = HiChatRoleConstant.isMasterOrManager(userRole)
        var title = ""
        if (teamType == HiChatPkTeamType.BLUE_TEAM) {
            btnAdd.setImageFromResource(if (isManager) R.drawable.hichat_pk_add_member_blue_ic else R.drawable.hichat_pk_empty_blue_seat)
            title = "添加到蓝方"
            if (isExpanded) {
                tvEmptyTips.text = "还未添加队员\n每方最多可添加${maxTeamSize}人"
            }
            if (!isManager) {
                tvEmptyTips.text = "虚位以待"
            }
        }

        if (teamType == HiChatPkTeamType.RED_TEAM) {
            btnAdd.setImageFromResource(if (isManager) R.drawable.hichat_pk_add_member_red_ic else R.drawable.hichat_pk_empty_red_seat)
            title = "添加到红方"
            if (isExpanded) {
                tvEmptyTips.text = "还未添加队员\n每方最多可添加${maxTeamSize}人"
            }
            if (!isManager) {
                tvEmptyTips.text = "虚位以待"
            }
        }

        if (teamType == HiChatPkTeamType.MULTI_PLAYER) {
            btnAdd.setImageFromResource(if (isManager) R.drawable.hichat_pk_add_member_blue_ic else R.drawable.hichat_pk_empty_blue_seat)
            title = "添加到多人PK"
            if (isExpanded) {
                tvEmptyTips.text = "还未添加人员\n最多可添加${maxTeamSize}人"
            }
            if (!isManager) {
                tvEmptyTips.text = "虚位以待"
            }
        }

        btnAdd.setOnClickListener {
            if (canAddMoreUsers(currentUsers.size)) {
                mTracker.hiChatPKPageClk(
                    hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid(),
                    clkName = "添加"
                )
                addMembers(
                    teamType = teamType,
                    currentTeamUsers = currentUsers,
                    excludeUsers = excludeUsers,
                    title = title
                )
            }
        }

    }


    /**
     * 更新展开状态的添加按钮位置和显示
     *
     * @param addButton 添加按钮视图
     * @param recyclerView 对应的RecyclerView
     * @param shouldShow 是否应该显示按钮
     * @param isEmpty 列表是否为空
     */
    protected fun updateExpandedAddButton(
        addButton: android.widget.ImageView,
        recyclerView: androidx.recyclerview.widget.RecyclerView,
        tvEmptyTips: TextView,
        shouldShow: Boolean,
        isEmpty: Boolean
    ) {
        tvEmptyTips.isVisible = isEmpty
        if (shouldShow) {
            addButton.visibility = VISIBLE
            val buttonLayoutParams = addButton.layoutParams as ConstraintLayout.LayoutParams
            val recyclerLayoutParams = recyclerView.layoutParams as ConstraintLayout.LayoutParams
            if (isEmpty) {
                // 列表为空时，按钮居中显示，RecyclerView保持margin
                buttonLayoutParams.topMargin = 0.dp
                buttonLayoutParams.bottomToTop = tvEmptyTips.id
            } else {
                // 有用户时，按钮显示在顶部，RecyclerView保持margin
                buttonLayoutParams.bottomToTop = View.NO_ID
                buttonLayoutParams.topMargin = 8.dp
                recyclerLayoutParams.topMargin = 48.dp
            }

            addButton.layoutParams = buttonLayoutParams
            recyclerView.layoutParams = recyclerLayoutParams
        } else {
            // 不显示添加按钮（PK开始或满员），RecyclerView顶部对齐
            addButton.visibility = GONE
            val recyclerLayoutParams = recyclerView.layoutParams as ConstraintLayout.LayoutParams
            recyclerLayoutParams.topMargin = 0
            recyclerView.layoutParams = recyclerLayoutParams
        }
    }

    /**
     * 更新收起状态的添加按钮位置和显示
     *
     * @param addButton 添加按钮视图
     * @param recyclerView 对应的RecyclerView
     * @param shouldShow 是否应该显示按钮
     */
    protected fun updateCollapsedAddButton(
        addButton: android.widget.ImageView,
        tvEmptyTips: TextView,
        shouldShow: Boolean,
        isEmpty: Boolean
    ) {
        addButton.isVisible = isEmpty || shouldShow
        tvEmptyTips.isVisible = isEmpty
    }


    /**
     * 倒计时job
     */
    var mCountDownJob: Job? = null

    /**
     * 更新剩余时间
     * @param tvRemainTime 剩余时间TextView
     * @param remainTime 剩余时间
     */
    protected fun updateRemainTime(tvRemainTime: TextView, remainTime: Long) {
        if (isPKStarted()) {
            mCountDownJob?.cancel()
            mCountDownJob = countDownCoroutines(
                remainTime.toInt(),
                gapTime = 1 * 1000,
                scope = viewScope,
                onTick = { tick ->
                    tvRemainTime.text = "PK中 ${time2StrFormat(tick.toLong())}"
                },
                onStart = {
                    tvRemainTime.setTextColor(
                        ContextCompat.getColor(
                            context, hb.xstyle.R.color.TH_Yellow600
                        )
                    )
                    tvRemainTime.text = "PK中 ${time2StrFormat(remainTime)}"
                },
                onFinish = {

                })

        } else {
            // 其他状态不需要倒计时
            mCountDownJob?.cancel()
            if (isPkEnd()) {
                tvRemainTime.text = "已结束"
                tvRemainTime.setTextColor(
                    ContextCompat.getColor(
                        context, hb.xstyle.R.color.TH_White55
                    )
                )
            } else if (isPKPreparing()) {
                // 准备中
                tvRemainTime.text = "未开始 ${time2StrFormat(remainTime)}"
                tvRemainTime.setTextColor(
                    ContextCompat.getColor(
                        context, hb.xstyle.R.color.TH_White100
                    )
                )
            }
        }
    }

    open fun setExpanded(isExpanded: Boolean) {}


    /**
     * 将剩余秒数转为 "mm:ss" 分钟秒数的格式
     */
    private fun time2StrFormat(time: Long): String {
        return if (time <= 0) {
            "00:00"
        } else {
            val minute = time / 60
            val second = time % 60
            "${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}"
        }
    }

    override fun onUserClick(uuid: String, isOnMic: Boolean) {
        // 用户头像点击
        // 当状态为进行中时，弹出星星送礼
        if (isPKStarted()) {
            if (uuid == AccountHelper.getAccountUuid()) {
                //
                XToastUtils.show("无法给自己赠送星星")
                return
            }
            if (!isOnMic) {
                XToastUtils.show("该用户已退出pk玩法，无法赠送星星")
                return
            }
            // 弹出送礼面板
            HiChatRoomCoreHelper.getViewModel(HiChatIntentViewModel::class.java).navigateTo(
                HiChatRoomNavigation.StarMainPage(targetUuid = uuid, sendFrom = "1")
            )
        } else {
            // 当状态为准备中、结束时，弹出用户资料卡
            HiChatRoomCoreHelper.getViewModel(HiChatIntentViewModel::class.java).navigateTo(
                HiChatRoomNavigation.ViewAccount(accountUuid = uuid)
            )
        }
    }

    override fun onUserLockClick(uuid: String, isLock: Boolean) {
        mTracker.hiChatPKPageClk(
            hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid(),
            clkName = "锁"
        )
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        mTracker.hiChatPKPageExp(hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid())
    }
}
