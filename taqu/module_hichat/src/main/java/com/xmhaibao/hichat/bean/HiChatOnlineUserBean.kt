package com.xmhaibao.hichat.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import hb.xstatic.mvp.interf.IListBean

/**
 * 畅聊圈-在线用户列表
 *
 * <AUTHOR>
 * @date 2025-02-05
 */
data class HiChatOnlineUserBean(
    /**
     * 在线用户列表
     */
    @SerializedName("list")
    var userList: MutableList<HiChatRoomUserInfoBean> = mutableListOf(),
    /**
     * 当前用户信息
     */
    @SerializedName("account_info")
    var accountInfo: HiChatRoomUserInfoBean? = null,
    /**
     * 在线总人数
     */
    @SerializedName("total_count")
    var totalCount: String? = null,

    /**
     * 在线用户数据hash
     */
    @SerializedName("atf_uuid_hash")
    var atfUuidHash:String?=null
) : IListBean<HiChatRoomUserInfoBean>, IDoExtra {
    override fun getList(): MutableList<HiChatRoomUserInfoBean> {
        return userList
    }

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        userList?.forEachIndexed { index, itemInfo ->
            itemInfo.index = "${index + 1}"
            itemInfo.doExtra(response)
        }
        accountInfo?.doExtra(response)
    }
}