package com.xmhaibao.hichat.block.room

import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.xmhaibao.hichat.bean.pk.HiChatPkNoticeBean
import com.xmhaibao.hichat.bean.pk.HiChatPkResultBean
import com.xmhaibao.hichat.block.service.IMessagePanelBlockService
import com.xmhaibao.hichat.block.service.OnInputChangeListener
import com.xmhaibao.hichat.constant.HiChatRoleConstant
import com.xmhaibao.hichat.databinding.HichatActivityGameLayoutBinding
import com.xmhaibao.hichat.dialog.pk.HiChatPkTimeSelectDialog
import com.xmhaibao.hichat.dialog.pk.HiChatTeamPkResultDialog
import com.xmhaibao.hichat.dialog.pk.HiChatMultiplayerPkResultDialog
import com.xmhaibao.hichat.dialog.pk.HiChatPkStartDialog
import com.xmhaibao.hichat.helper.HiChatRoomCoreHelper
import com.xmhaibao.hichat.strategy.HiChatFunGameStrategy
import com.xmhaibao.hichat.viewmodel.HiChatRoomOnlineMemberViewModel
import com.xmhaibao.hichat.viewmodel.HiChatRoomPkViewModel
import com.xmhaibao.hichat.viewmodel.HiChatRoomSocketViewModel
import com.xmhaibao.hichat.widget.pk.HiChatBasePKView
import com.xmhaibao.hichat.widget.pk.HiChatPKViewManager
import hb.utils.Loger
import hb.xblock.activityViewModels
import hb.xblockframework.framework.base.UIBlock
import hb.xblockframework.framework.join.IBlockContext
import hb.xstyle.xdialog.XDialogUtils
import hb.xblockframework.framework.utils.blockService
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

/**
 * 活动游戏 Block
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
class ActivityGameBlock(blockContext: IBlockContext) : UIBlock(blockContext),
    OnInputChangeListener {

    private val mBinding by lazy {
        HichatActivityGameLayoutBinding.inflate(LayoutInflater.from(context))
    }

    private val mSocketImViewModel by lazy {
        HiChatRoomCoreHelper.getViewModel(HiChatRoomSocketViewModel::class.java)
    }

    private val mOnlineMemberViewModel by lazy {
        HiChatRoomCoreHelper.getViewModel(HiChatRoomOnlineMemberViewModel::class.java)
    }

    private val mHiChatPkViewModel by activityViewModels<HiChatRoomPkViewModel>()

    /** 消息底部面板模块api */
    private val messagePanelBlockService: IMessagePanelBlockService? by blockService()

    /**
     * PK视图管理器
     * 懒加载调用 [pkViewManager]
     *
     */
    private var mPkViewManager: HiChatPKViewManager? = null

    /**
     * 主题服务
     */
    private val themeBlockService: IHiChatThemeBlockService? by blockService()

    override fun onCreateContentView(parent: View?): Any {
        return mBinding.root
    }

    override fun onRegister() {
        super.onRegister()
        lifecycleScope.launch {
            mSocketImViewModel.imEventFlow.collect {
                when (it.event) {
                    "hichat_pk_notice" -> {
                        mHiChatPkViewModel.dispatchNoticeEvent(HiChatPkNoticeBean.analysis(*(it.args ?: emptyArray())))
                    }

                    "hichat_pk_info_change" -> {
                        val hash = it.args?.getOrNull(0).toString()
                        mHiChatPkViewModel.noticePkInfoChange(hash)
                    }
                }
            }
        }

        lifecycleScope.launch {
            mHiChatPkViewModel.pkNoticeFlow.collectLatest { noticeBean ->
                noticeBean?.takeIf { it.isPkStart() }?.let {
                    HiChatPkStartDialog(context = context).show()
                }
                noticeBean?.takeIf { it.isPkEnd() }?.let {
                    handlePkResult(it.result)
                }
            }
        }
        observerPkInfo()
    }

    private fun observerPkInfo() {
        lifecycleScope.launch {
            mHiChatPkViewModel.pkInfoFlow.combine(mOnlineMemberViewModel.loginUserRoleFlow) { pkInfo, role ->
                pkInfo?.role = role
                pkInfo
            }.collectLatest { pkInfo ->
                if (pkInfo != null) {
                    pkInfo.let {
                        pkViewManager(true)?.bindPkData(pkInfo)
                    }
                    // 重置ai推荐回复可显示状态，这里需要确认用户角色
                    themeBlockService?.resetReplyPopWindowState(!HiChatRoleConstant.isMasterOrManager(pkInfo.role))
                } else {
                    //pk信息为空
                    pkViewManager()?.cleanup()
                    // 重置ai推荐回复可显示状态
                    themeBlockService?.resetReplyPopWindowState(true)
                }
            }
        }

        lifecycleScope.launch {
            mHiChatPkViewModel.pkConfigFlow.collectLatest {
                pkViewManager(true)?.pkConfigBean = it
            }
        }
    }

    private fun handlePkResult(result: HiChatPkResultBean?) {
        result?.let {
            if (it.isPkMultiPlayer()) {
                HiChatMultiplayerPkResultDialog.showDialog(context, it)
            } else if (it.isPkTeam()) {
                HiChatTeamPkResultDialog.showDialog(context, it)
            }
        }
    }


    private fun pkViewManager(forceInit: Boolean = false): HiChatPKViewManager? {
        if (forceInit && mPkViewManager == null) {
            activeIfNeed()
            mPkViewManager = HiChatPKViewManager(context = context, container = mBinding.groupPk)
            mPkViewManager?.setPKStatusListener(object : HiChatBasePKView.PKStatusListener {
                override fun onPKStarted() {
                    // PK开始点击
                    mHiChatPkViewModel.startPk()
                }

                override fun onPKEnded(roundId: String?,isTeamPk: Boolean) {
                    // PK结束点击
                    XDialogUtils.show(
                        context, "提示", "PK进行中，是否提前结束", "是",
                        { dialog, _ ->
                            mHiChatPkViewModel.endPk(roundId, isTeamPk = isTeamPk)
                            dialog?.dismiss()
                        }, "再想想", { dialog, _ ->
                            dialog?.dismiss()
                        })
                }

                override fun onPkReset() {
                    // 重开一轮点击
                    mHiChatPkViewModel.resetPk()
                }

                override fun addMember(teamType: String, users: String) {
                    // 处理添加成员
                    mHiChatPkViewModel.addPkMember(teamType, users)
                }

                override fun onPkTimeSelect() {
                    HiChatPkTimeSelectDialog.showDialog(
                        context = context,
                        roundTimeList = mHiChatPkViewModel.currentPkConfig().roundTimeList
                            ?: emptyList()
                    ) { selectTime ->
                        mHiChatPkViewModel.setPkTime(selectTime)
                    }
                }

                override fun onPkDelay() {
                    val delayTime = mHiChatPkViewModel.currentPkConfig().delayTime?.toIntOrNull() ?: 10
                    XDialogUtils.show(
                        context, "提示", "是否增加${delayTime}分钟?", "是",
                        { dialog, _ ->
                            mHiChatPkViewModel.delayPkTime(delayTime.toString())
                            dialog?.dismiss()
                        }, "再想想", { dialog, _ ->
                            dialog?.dismiss()
                        })
                }
            })
        }
        return mPkViewManager
    }

    override fun initViews() {
        super.initViews()
        messagePanelBlockService?.registerInputChangeListener(this@ActivityGameBlock)
        Loger.i("tag",messagePanelBlockService)
    }

    override fun onDestroy() {
        messagePanelBlockService?.unRegisterInputChangeListener(this@ActivityGameBlock)
        super.onDestroy()

    }

    override fun onInputChange(isShow: Boolean) {
        if(isShow){
            // 打开输入面板，收起pk视图
            pkViewManager()?.collapse()
        }
    }

}