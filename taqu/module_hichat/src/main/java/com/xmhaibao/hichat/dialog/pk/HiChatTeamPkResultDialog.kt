package com.xmhaibao.hichat.dialog.pk

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import com.xmhaibao.hichat.R
import com.xmhaibao.hichat.bean.pk.HiChatPkResultBean
import com.xmhaibao.hichat.bean.pk.HiChatPkResultUserItem
import com.xmhaibao.hichat.databinding.HichatTeamPkResultDialogLayoutBinding
import com.xmhaibao.hichat.viewholder.HiChatPkResultItemViewHolder
import com.xmhaibao.hichat.widget.HiChatPkResultGridLayoutManager
import dp
import hb.kotlin_extension.safeAction
import hb.utils.ActivityUtils
import hb.xadapter.XBaseAdapter
import hb.xstyle.xdialog.XLifecycleDialog

/**
 * 组队pk 结果
 *
 * <AUTHOR>
 * @date 2025/7/18
 */
class HiChatTeamPkResultDialog(
    context: Context,
    private val result: HiChatPkResultBean?
) : XLifecycleDialog(context) {

    companion object {
        fun showDialog(
            context: Context?, result: HiChatPkResultBean?,
        ) {
            ActivityUtils.getActivity(context)?.safeAction {
                HiChatTeamPkResultDialog(
                    this, result
                ).show()
            }
        }
    }

    private val mBinding = HichatTeamPkResultDialogLayoutBinding.inflate(layoutInflater)
    private val spanCount = 5 // 列数

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        window?.apply {
            window?.apply {
                setLayout(272.dp, ViewGroup.LayoutParams.WRAP_CONTENT)
                setGravity(Gravity.CENTER)
            }
        }
        initView()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initView() {
        mBinding.ivSpot.setImageFromResource(R.drawable.hichat_pk_result_bg)
        mBinding.ivTop.setImageFromResource(R.drawable.hichat_pk_result_success_top_ic)
        mBinding.ivClose.setOnClickListener { dismiss() }
        val mAdapter = XBaseAdapter(mBinding.recyclerView.context).apply {
            register(HiChatPkResultUserItem::class.java, HiChatPkResultItemViewHolder::class.java)
        }
        mBinding.recyclerView.apply {
            layoutManager = HiChatPkResultGridLayoutManager(spanCount, 8.dp ,16.dp)
            adapter = mAdapter
        }
        result?.let {
            mBinding.tvTitle.text = it.title
            mBinding.tvStar.text = it.totalCount
            it.members?.apply {
                mAdapter.items = this
            }
            mAdapter.notifyDataSetChanged()
        }

    }
}