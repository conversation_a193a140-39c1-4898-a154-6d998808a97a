package com.xmhaibao.hichat.dialog.star

import android.content.DialogInterface
import android.graphics.LinearGradient
import android.graphics.Shader
import android.os.Bundle
import android.widget.CompoundButton
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toDrawable
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.CompositePageTransformer
import androidx.viewpager2.widget.ViewPager2
import cn.taqu.lib.base.bean.UiState.Companion.doException
import cn.taqu.lib.base.bean.UiState.Companion.doSuccess
import cn.taqu.lib.base.helper.VibrationHelper
import cn.taqu.lib.base.router.RouterLaunch
import com.xmhaibao.family.widget.download2Bitmap
import com.xmhaibao.hichat.R
import com.xmhaibao.hichat.bean.HiChatRoomStarStatusBean
import com.xmhaibao.hichat.bean.StarOptionBean
import com.xmhaibao.hichat.bean.StarSkinBean
import com.xmhaibao.hichat.constant.HiChatRoleConstant
import com.xmhaibao.hichat.databinding.HichatStarGiftMainFragmentBinding
import com.xmhaibao.hichat.dialog.HiChatSendUserSelectDialog
import com.xmhaibao.hichat.dialog.growth.ScaleAlphaPageTransformer
import com.xmhaibao.hichat.dialog.star.StarBreathView.HeartBeatListener
import com.xmhaibao.hichat.helper.HiChatRoomCoreHelper
import com.xmhaibao.hichat.track.HiChatRoomTrack
import com.xmhaibao.hichat.viewmodel.HiChatStarGiftViewModel
import dp
import hb.kotlin_extension.clearDrawableStart
import hb.kotlin_extension.countDownCoroutines
import hb.kotlin_extension.drawableEnd
import hb.kotlin_extension.drawableStart
import hb.utils.ColorUtils
import hb.utils.kvcache.KVCacheUtils
import hb.xadapter.XBaseAdapter
import hb.xstatic.core.XBaseFragment
import hb.xstyle.xdialog.XDialog
import hb.xthread.XThreadPool
import hb.xtoast.XToastUtils
import kotlinx.coroutines.Job
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


/**
 * 星星礼物主页fragment
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
class StarMainFragment : XBaseFragment() {
    companion object {
        private const val KEY_TARGET_UUID = "target_uuid"

        /**
         * 特殊星星礼物不再二次确认提醒
         */
        private const val STAR_SEND_NO_REMIND = "star_send_no_remind"
    }

    val mBinding by lazy {
        HichatStarGiftMainFragmentBinding.inflate(layoutInflater)
    }

    /**
     * 星星礼物ViewModel
     */
    val mStarViewModel by activityViewModels<HiChatStarGiftViewModel>()

    /**
     * 连击星星数量
     */
    private var comboStar: Long = 0

    private val mTracker by lazy {
        HiChatRoomTrack()
    }

    /**
     * 星星礼物配置adapter
     */
    private val mStarOptionAdapter by lazy {
        XBaseAdapter(context).apply {
            register(StarOptionBean::class.java, StarOptionViewHolder::class.java)
            setOnItemClickListener { v, position ->
                val item = getItem(position) as? StarOptionBean
                item?.let {
                    if (mStarViewModel.isReceiverNoExist()) {
                        XToastUtils.show("请选择赠送对象")
                        return@let
                    }
                    if (mStarViewModel.currentSelectedSkin()?.isAvailable() == false) {
                        XToastUtils.show("暂未获得该皮肤，无法使用")
                        return@let
                    }
                    if (item.isDiy) {
                        // diy 礼物
                        StarGiftDialog.findContainer(this@StarMainFragment)
                            ?.navigateTo(StarDiyFragment())
                        mTracker.starPageClk(
                            HiChatRoomCoreHelper.currentRoomUuid(),
                            clkName = "diy绘制入口"
                        )
                    } else {
                        // 星星礼物
                        // 是否是不再提醒
                        var isNotRemind = KVCacheUtils.getBoolean(STAR_SEND_NO_REMIND, false)
                        mTracker.starPageClk(
                            HiChatRoomCoreHelper.currentRoomUuid(),
                            clkName = "${it.num}点击"
                        )

                        // 是否余额不足
                        if ((it.num?.toIntOrNull() ?: 0) > mStarViewModel.currentStarBalance()) {
                            XToastUtils.show("星星不足,可在圈内多多活跃领取星星")
                            return@let
                        }
                        val sendFun = {
                            mStarViewModel.sendStar(
                                count = it.num?.toIntOrNull() ?: 0,
                                type = HiChatStarGiftViewModel.STAR_TYPE_ONCE,
                                starId = it.starId
                            )
                        }
                        if (isNotRemind) {
                            // 直接送礼
                            sendFun()
                        } else {
                            XDialog.newBuilder(context)
                                .type(XDialog.TYPE_OPERATE)
                                .title("你将向对方发送${item.num}个星星")
                                .message("确认发送吗?")
                                .checkBox(
                                    "以后不再提示",
                                    false
                                ) { buttonView, isChecked -> isNotRemind = isChecked }
                                .positiveButton(
                                    "确认"
                                ) { dialog, which -> // 送礼
                                    if (isNotRemind) {
                                        KVCacheUtils.putBoolean(
                                            STAR_SEND_NO_REMIND,
                                            true
                                        )
                                    }
                                    sendFun()
                                    dialog?.dismiss()
                                }
                                .negativeButton(
                                    "取消"
                                ) { dialog, which -> dialog?.dismiss() }.show()
                        }
                    }
                }
            }
        }
    }

    /**
     * 星星页面适配器
     */
    private val mStarPagerAdapter by lazy {
        XBaseAdapter(requireContext()).apply {
            register(StarSkinBean::class.java, StarPageViewHolder::class.java)
            setOnViewHolderCreatedListener { holder ->
                // 设置心跳监听器
                if (holder is StarPageViewHolder) {
                    holder.heartBeatListener = heartBeatListener
                }

            }
        }
    }

    /**
     * 当前选中位置
     */
    private var currentStarPosition = 0

    /**
     * 心跳监听器
     */
    private var heartBeatListener: HeartBeatListener? = null

    /**
     * 星星皮肤倒计时任务
     */
    private var starSkinCountdownJob: Job? = null


    override fun onCreateContentView(): Any {
        return mBinding.root
    }


    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        val targetUuid: String? = arguments?.getString(KEY_TARGET_UUID)

        mStarViewModel.fetchStarMain(targetUuid)

        viewLifecycleOwner.lifecycleScope.launch {
            // 星星赠送成功
            mStarViewModel.giftSuccessTypeFlow.collectLatest {
                when (it) {
                    HiChatStarGiftViewModel.STAR_TYPE_ONCE, HiChatStarGiftViewModel.STAR_TYPE_DYI -> {
                        // 星星主页关闭
                        StarGiftDialog.findContainer(this@StarMainFragment)?.dismiss()
                    }

                    else -> {}
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            mStarViewModel.starMainInfoFlow.collectLatest { main ->
                // 繁星殿堂点击事件
                mBinding.bgStarGallery.setOnClickListener {
                    mTracker.starPageClk(
                        HiChatRoomCoreHelper.currentRoomUuid(),
                        clkName = "繁星殿堂"
                    )
                    RouterLaunch.dealJumpData(context, main?.starGalleryUrl)
                }
                // 兑换商店点击事件
                mBinding.groupStarShop.isVisible = main?.exchangeRelation?.isNotEmpty() == true
                mBinding.bgShop.setOnClickListener {
                    mTracker.starPageClk(
                        HiChatRoomCoreHelper.currentRoomUuid(),
                        clkName = "星星商店"
                    )
                    RouterLaunch.dealJumpData(context, main?.exchangeRelation)
                }

                // 星星礼物配置列表
                val starOptions = main?.starOptions ?: mutableListOf()
                if (main?.isDIYOpen() == true) {
                    starOptions.add(StarOptionBean().apply { isDiy = true })
                }
                // 设置布局管理器,小于4个的时候，实行3等分，否则4等分
                val spanCount = if (starOptions.size < 4) {
                    3
                } else {
                    4
                }
                mBinding.rvStarGift.layoutManager = GridLayoutManager(context, spanCount)
                mStarOptionAdapter.items = starOptions

                // 更新星星皮肤数据
                updateStarSkinData(main?.starSkinList)
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            mStarViewModel.starBalanceFlow.collectLatest {
                // 星星余额信息
                mBinding.tvStarBalance.text = it.toString()
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            launch {
                mStarViewModel.starStatueFlow.collectLatest {
                    it.doSuccess { _it ->
                        if (_it == null) {
                            // 星星状态为空
                            StarGiftDialog.findContainer(this@StarMainFragment)?.dismiss()
                            return@doSuccess
                        }
                        when (_it?.starState) {
                            HiChatRoomStarStatusBean.STAR_STATE_FINISH -> {
                                // 已领完
                                mBinding.tvStarStatus.text = "已领完"
                                mBinding.tvStarStatus.isSelected = false
                                mBinding.ivStarStatus.isSelected = false
                                mBinding.tvStarStatus.setTextColor(
                                    ContextCompat.getColor(
                                        requireContext(),
                                        hb.xstyle.R.color.TH_White35
                                    )
                                )
                                mBinding.ivStarStatus.setImageFromResource(R.drawable.hichat_star_unavailable_end)
                                mBinding.bgStarStatus.setOnClickListener { }
                            }

                            HiChatRoomStarStatusBean.STAR_STATE_WAITING -> {
                                // 待领取
                                mBinding.tvStarStatus.text = "可领取"
                                mBinding.tvStarStatus.isSelected = true
                                mBinding.ivStarStatus.isSelected = true
                                mBinding.tvStarStatus.setTextColor(
                                    ColorUtils.getColor("#F7C947")
                                )
                                mBinding.ivStarStatus.setImageFromResource(R.drawable.hichat_star_available_ic)
                                mBinding.bgStarStatus.setOnClickListener {
                                    // 领取星星
                                    mStarViewModel.awardStar()
                                    mTracker.starPageClk(
                                        HiChatRoomCoreHelper.currentRoomUuid(),
                                        clkName = "可领取"
                                    )
                                }
                            }

                            HiChatRoomStarStatusBean.STAR_STATE_COUNT_DOWN -> {
                                mBinding.tvStarStatus.isSelected = false
                                mBinding.ivStarStatus.isSelected = false
                                mBinding.tvStarStatus.text =
                                    mStarViewModel.formatTime(_it.realCountDown)
                                mBinding.tvStarStatus.setTextColor(
                                    ContextCompat.getColor(
                                        requireContext(),
                                        hb.xstyle.R.color.TH_White70
                                    )
                                )
                                mBinding.ivStarStatus.setImageFromResource(R.drawable.hichat_star_unavailable)
                                mBinding.bgStarStatus.setOnClickListener { }
                            }
                        }
                    }
                    it.doException {
                        StarGiftDialog.findContainer(this@StarMainFragment)?.dismiss()
                    }
                }
            }
        }
    }

    override fun initViews() {
        super.initViews()
        mTracker.starPageExp(HiChatRoomCoreHelper.currentRoomUuid())
        initStarOption()
        initComboView()
        initReceiver()
        initStarPager()
        initStarSkin()

        mBinding.viewTopEmpty.setOnClickListener {
            // 空白区域点击关闭弹窗
            StarGiftDialog.findContainer(this@StarMainFragment)
                ?.dismiss()
        }
    }

    /**
     * 初始化星星皮肤数据
     */
    private fun initStarSkin() {
        lifecycleScope.launchWhenResumed {
            mStarViewModel.currentSelectedStarSkinFlow.collectLatest { skin ->
                mBinding.ivContentBg.setImageFromUrl(skin?.skinBgUrl)
                mBinding.tvStarName.text = skin?.skinName ?: ""

                stopStarSkinCountdown()
                if (skin?.isAvailable() == true) {
                    // 弹出星星皮肤替换
                    val noEmotionSkin = withContext(XThreadPool.Image().get().asCoroutineDispatcher()) {
                        skin.skinNoEmotionUrl.download2Bitmap()
                    }
                    mBinding.starJumpView.initDrawable(noEmotionSkin?.toDrawable(resources))
                    mBinding.tvStarName.clearDrawableStart()

                    // 皮肤可用：判断当前自然日是否展示过提示，如果没有则展示5s的tips
                    if (skin.shouldShowLongPressTip) {
                        mBinding.tvLongClkTips.isVisible = true
                        mBinding.tvSkinStatus.isVisible = false
                        delay(5000)
                        mBinding.tvLongClkTips.isVisible = false
                    } else {
                        mBinding.tvLongClkTips.isVisible = false
                    }

                    // 如果是永久 则不开启倒计时
                    if (skin.isPermanent()) {
                        mBinding.tvLongClkTips.isVisible = true
                        mBinding.tvSkinStatus.isVisible = false
                    } else {
                        // 开始倒计时
                        mBinding.tvSkinStatus.isVisible = true
                        startStarSkinCountdown(skin)
                    }
                } else {
                    // 皮肤不可用：直接展示"去获得"
                    mBinding.tvLongClkTips.isVisible = false
                    mBinding.tvSkinStatus.isVisible = true

                    mBinding.tvStarName.drawableStart(R.drawable.hichat_star_lock, drawablePadding = 4.dp)
                    updateStarSkinCountdown(0L)
                }
            }
        }
    }

    /**
     * 启动星星皮肤倒计时
     */
    private fun startStarSkinCountdown(starSkin: StarSkinBean?) {
        // 停止之前的倒计时
        stopStarSkinCountdown()

        starSkinCountdownJob = viewLifecycleOwner.lifecycleScope.launch {
            val currentTime = System.currentTimeMillis() / 1000
            val expireTime = starSkin?.expireTime() ?: 0
            val remainingTime = expireTime - currentTime
            if (remainingTime > 0) {
                repeat(remainingTime.toInt()) {
                    updateStarSkinCountdown(remainingTime - it)
                    delay(1000)
                }
                updateStarSkinCountdown(0L)
            } else {
                updateStarSkinCountdown(0L)
            }

        }

    }

    /**
     * 停止星星皮肤倒计时
     */
    private fun stopStarSkinCountdown() {
        starSkinCountdownJob?.cancel()
        starSkinCountdownJob = null
    }

    /**
     * 更新星星皮肤倒计时显示
     */
    private fun updateStarSkinCountdown(countdown: Long) {
        val countdownText = mStarViewModel.formatCountdownTime(countdown)
        if (countdown > 0) {
            mBinding.tvSkinStatus.text = countdownText
            // 设置倒计时样式
            mBinding.tvSkinStatus.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    hb.xstyle.R.color.TH_White70
                )
            )
            mBinding.tvSkinStatus.drawableEnd(drawableRes = null)
            mBinding.tvStarName.clearDrawableStart()
            mBinding.tvSkinStatus.setOnClickListener {}
        } else {
            mBinding.tvSkinStatus.text = "去获取"
            // 设置去获取的样式
            mBinding.tvSkinStatus.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    hb.xstyle.R.color.TH_White100
                )
            )
            mBinding.tvSkinStatus.drawableEnd(
                drawableRes = R.drawable.hichat_ic_arrow_right_white_12,
                12.dp
            )
            mBinding.tvStarName.drawableStart(R.drawable.hichat_star_lock, drawablePadding = 4.dp)

            mBinding.tvSkinStatus.setOnClickListener {
                mTracker.starPageClk(
                    HiChatRoomCoreHelper.currentRoomUuid(),
                    clkName = "去获取"
                )
                RouterLaunch.dealJumpData(
                    it.context,
                    mStarViewModel.currentSelectedSkin()?.relation
                )
            }
        }
    }

    /**
     * 初始化收礼人信息
     */
    private fun initReceiver() {
        mBinding.bgReceiver.setOnClickListener {
            HiChatSendUserSelectDialog.showDialog(
                context = requireContext(),
                hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid(),
                title = "小星星送给"
            ) {
                val  user = it.getOrNull(0) ?: return@showDialog
                // 切换收礼人后中断本次连击送礼
                mStarViewModel.updateStarCombo(0)
                mBinding.starJumpView.stop()
                mStarViewModel.updateReceiverInfo(user)
            }
        }
        viewLifecycleOwner.lifecycleScope.launch {
            mStarViewModel.starReceiverFlow.collectLatest {
                mBinding.ivReceiverAvatar.isVisible = !it?.avatar.isNullOrEmpty()
                mBinding.tvReceiverName.isVisible = !it?.name.isNullOrEmpty()
                mBinding.tvSelectUserHint.isVisible = it?.name.isNullOrEmpty()
                mBinding.tvMasterTag.isVisible = HiChatRoleConstant.isMaster(it?.role)
                // 星星礼物收礼人信息
                mBinding.ivReceiverAvatar.setImageFromUrl(it?.avatar)
                mBinding.tvReceiverName.text = it?.name ?: ""
            }
        }
    }

    private fun initStarOption() {
        mBinding.rvStarGift.adapter = mStarOptionAdapter
    }

    /**
     * 初始化星星页面
     */
    private fun initStarPager() {
        // 设置适配器
        mBinding.viewPagerStar.adapter = mStarPagerAdapter

        // 设置方向为水平
        mBinding.viewPagerStar.orientation = ViewPager2.ORIENTATION_HORIZONTAL

        // 设置离屏页面限制
        mBinding.viewPagerStar.offscreenPageLimit = 3

        // 设置页面变换器
        val compositeTransformer = CompositePageTransformer()


        // 2. 再添加 ScaleAlphaPageTransformer
        val scaleFactor = 88f / 118f
        compositeTransformer.addTransformer(
            ScaleAlphaPageTransformer(
                minScale = scaleFactor,
                minAlpha = 0.5f
            )
        )

        mBinding.viewPagerStar.setPageTransformer(compositeTransformer)
        mBinding.viewPagerStar.setOnTouchListener { view, event ->
            return@setOnTouchListener mBinding.viewPagerStar.getChildAt(0).onTouchEvent(event)
        }

        // 设置心跳监听器
        heartBeatListener = object : HeartBeatListener {
            var heartX = -1f
            var heartY = -1f

            private fun initHeartPosition() {
                if (heartX == -1f || heartY == -1f) {
                    // 让 jumpView 抛出物体
                    val pagerLocation = IntArray(2)
                    mBinding.viewPagerStar.getLocationOnScreen(pagerLocation)

                    // 计算 jumpView 的位置
                    val jumpViewLocation = IntArray(2)
                    mBinding.starJumpView.getLocationOnScreen(jumpViewLocation)

                    // 计算相对坐标
                    heartX =
                        pagerLocation[0] + mBinding.viewPagerStar.width.toFloat() / 2 - jumpViewLocation[0]
                    heartY =
                        pagerLocation[1] + mBinding.viewPagerStar.height.toFloat() / 2 - jumpViewLocation[1]
                }
            }

            override fun onHeartBeat(x: Float, y: Float, clkType: Int) {
                // 如果收礼人为空，则提示
                if (mStarViewModel.isReceiverNoExist()) {
                    XToastUtils.show("请选择赠送对象")
                    // 停止当前页面的触摸
                    stopCurrentStarTouch()
                    return
                }
                if(mStarViewModel.currentSelectedSkin()?.isAvailable() != true) {
                    if (comboStar > 0) {
                        // 处理连击过程中 皮肤过期异常情况提示
                        XToastUtils.show("星星皮肤已过期，无法赠送")
                    } else {
                        XToastUtils.show("暂未获得该皮肤，无法使用")
                    }
                    stopCurrentStarTouch()
                    mStarViewModel.updateStarCombo(0)
                    return
                }

                val balance = mStarViewModel.currentStarBalance()
                comboStar++
                if (comboStar > balance) {
                    comboStar = balance
                    stopCurrentStarTouch()
                    XToastUtils.show("星星不足,可在圈内多多活跃领取星星")
                    return
                }
                mStarViewModel.updateStarCombo(comboStar)
                // 计算星星位置
                initHeartPosition()
                // 开启震动
                VibrationHelper.startVibration(duration = 50)
                // 在HeartView的位置添加1个爱心
                mBinding.starJumpView.addHearts(
                    heartX,
                    heartY,
                    1,
                    32f
                )
            }

            override fun onHeartBeatEnd(type: Int) {
                val clkName = when (type) {
                    StarBreathView.CLK -> "星星单击赠送"
                    StarBreathView.LONG_CLK -> "星星长按连送"
                    else -> null
                }
                clkName?.let {
                    mTracker.starPageClk(HiChatRoomCoreHelper.currentRoomUuid(), clkName = it)
                }
            }
        }

        // 设置页面切换监听
        mBinding.viewPagerStar.registerOnPageChangeCallback(object :
            ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                // 更新当前位置
                updateCurrentStarPosition(position)
                // 重置连击数量
                mStarViewModel.updateStarCombo(0)
                mBinding.starJumpView.stop()
            }
        })

    }

    /**
     * 停止当前星星的触摸
     */
    private fun stopCurrentStarTouch() {
        // 通过ViewPager2的RecyclerView找到当前页面的ViewHolder
        val recyclerView = mBinding.viewPagerStar.getChildAt(0) as? RecyclerView
        recyclerView?.let { rv ->
            val viewHolder =
                rv.findViewHolderForAdapterPosition(currentStarPosition) as? StarPageViewHolder
            viewHolder?.stopTouch()
        }
    }

    /**
     * 更新当前星星位置
     */
    private fun updateCurrentStarPosition(position: Int) {
        val oldPosition = currentStarPosition
        currentStarPosition = position

        // 更新数据状态
        var currentSelectedSkin: StarSkinBean? = null
        mStarPagerAdapter.items.forEachIndexed { index, item ->
            if (item is StarSkinBean) {
                item.isSelected = index == position
                if (index == position) {
                    currentSelectedSkin = item
                }
            }

        }

        // 更新 ViewModel 中的当前选中星星皮肤
        mStarViewModel.updateCurrentSelectedStarSkin(currentSelectedSkin)

        // 只刷新相关的两个位置
        if (oldPosition != position) {
            mStarPagerAdapter.notifyItemChanged(oldPosition)
            mStarPagerAdapter.notifyItemChanged(position)
        }
    }

    /**
     * 更新星星皮肤数据
     */
    private fun updateStarSkinData(starSkinList: MutableList<StarSkinBean>?) {
        if (starSkinList.isNullOrEmpty()) return
        mStarPagerAdapter.items = starSkinList
        mStarPagerAdapter.notifyDataSetChanged()

        // 找到当前选中的星星皮肤并更新到 ViewModel
        val selectedSkin = starSkinList.find { it.isSelected }
        mStarViewModel.updateCurrentSelectedStarSkin(selectedSkin)
        selectedSkin?.let { skin ->
            // 更新当前位置
            val selectedPosition = starSkinList.indexOf(skin)
            if (selectedPosition >= 0) {
                currentStarPosition = selectedPosition
                // 设置 ViewPager 到选中位置
                mBinding.viewPagerStar.setCurrentItem(selectedPosition, false)
            }
        }
    }

    private fun initComboView() {
        val linearGradient = LinearGradient(
            0f, 0f, 0f, mBinding.tvCombo.textSize,  // 起点和终点坐标
            intArrayOf(
                ColorUtils.getColor("#FFD65C"),
                ColorUtils.getColor("#FFEFBE")
            ),
            null,  // 渐变位置（null 表示均匀分布）
            Shader.TileMode.CLAMP // 填充模式
        )
        mBinding.tvCombo.paint.shader = linearGradient
        viewLifecycleOwner.lifecycleScope.launch {
            mStarViewModel.starComboFlow.collectLatest { it ->
                mBinding.tvCombo.isVisible = it > 0
                mBinding.tvCombo.text = "x$it "
                if (it == 0L) {
                    // 重置
                    comboStar = 0
                }
            }
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (hidden) {
            mBinding.starJumpView.stop()
        }
    }

    override fun onDestroy() {
        // 保存最后选中星星皮肤
        StarSkinBean.saveDefaultSkinId(mStarViewModel.currentSelectedSkin()?.skinId)
        super.onDestroy()
    }

}