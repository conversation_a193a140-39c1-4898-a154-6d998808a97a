package com.xmhaibao.hichat.widget.pk

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import android.os.Bundle
import androidx.recyclerview.widget.DiffUtil
import android.animation.ObjectAnimator
import android.animation.AnimatorSet
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.view.View
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.viewbinding.ViewBinding
import com.airbnb.lottie.LottieAnimationView
import com.xmhaibao.hichat.bean.HiChatPkUserBean
import com.xmhaibao.hichat.bean.pk.PKStatus
import com.xmhaibao.hichat.databinding.HichatItemPkMultiUserCollapsedBinding
import com.xmhaibao.hichat.databinding.HichatItemPkMultiUserExpandedBinding
import com.xmhaibao.hichat.databinding.HichatItemPkMultiUserExpandedStartedBinding
import com.xmhaibao.hichat.databinding.HichatItemPkTeamUserCollapsedBinding
import com.xmhaibao.hichat.databinding.HichatItemPkTeamUserExpandedBinding
import com.xmhaibao.hichat.databinding.HichatItemPkTeamUserExpandedStartedBinding
import com.xmhaibao.hichat.helper.HiChatRoomCoreHelper
import com.xmhaibao.hichat.viewmodel.HiChatRtcViewModel
import com.xmhaibao.hichat.widget.pk.HiChatPKUserDiffCallback.Companion.PAYLOAD_RANK_CHANGE
import hb.kotlin_extension.viewScope
import hb.ximage.fresco.AvatarDraweeView
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class HiChatPKUserAdapter(
    private val isExpanded: Boolean,
    private val isTeamPK: Boolean = true, // 默认为组队PK
    val pkAdapterCallback: PkAdapterCallBack? = null
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val users = mutableListOf<HiChatPkUserBean>()
    var pkStatus = PKStatus.PREPARING


    companion object {
        private const val VIEW_TYPE_USER = 0
        private const val VIEW_TYPE_USER_STARTED = 2

        // PK结束
        private const val VIEW_TYPE_USER_END = 3
    }

    fun submitList(userList: List<HiChatPkUserBean>, isStructuralChange: Boolean = false) {
        // 当PK开始且为收起状态时，如果有锁定用户，则取锁定用户否则只只关心第一个用户。
        // 直接裁剪列表以确保数据一致性。

        val targetList =
            if ((pkStatus == PKStatus.IN_PROGRESS || pkStatus == PKStatus.ENDED) && !isExpanded && userList.isNotEmpty()) {
                val lockUser = userList.find { it.isLock }
                if (lockUser != null) {
                    listOf(lockUser)
                } else {
                    listOf(userList.first())
                }
            } else {
                // 确保每个用户的ID是唯一的，防止复用问题
                userList.distinctBy { it.uuid }
            }

        if (isStructuralChange) {
            this.users.clear()
            this.users.addAll(targetList)
            notifyDataSetChanged()
        } else {
            val diffCallback = HiChatPKUserDiffCallback(this.users, targetList)
            val diffResult = DiffUtil.calculateDiff(diffCallback, true)
            this.users.clear()
            this.users.addAll(targetList)
            diffResult.dispatchUpdatesTo(this)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (pkStatus == PKStatus.PREPARING) VIEW_TYPE_USER else
            if (pkStatus == PKStatus.IN_PROGRESS) VIEW_TYPE_USER_STARTED else VIEW_TYPE_USER_END
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            VIEW_TYPE_USER_STARTED, VIEW_TYPE_USER_END -> {
                val binding = if (isTeamPK) {
                    HichatItemPkTeamUserExpandedStartedBinding.inflate(inflater, parent, false)
                } else {
                    HichatItemPkMultiUserExpandedStartedBinding.inflate(inflater, parent, false)
                }
                UserExpandedStartedViewHolder(binding)
            }

            VIEW_TYPE_USER -> {
                if (isExpanded) {
                    val binding = if (isTeamPK) {
                        HichatItemPkTeamUserExpandedBinding.inflate(inflater, parent, false)
                    } else {
                        HichatItemPkMultiUserExpandedBinding.inflate(inflater, parent, false)
                    }
                    UserExpandedViewHolder(binding)
                } else {
                    val binding = if (isTeamPK) {
                        HichatItemPkTeamUserCollapsedBinding.inflate(inflater, parent, false)
                    } else {
                        HichatItemPkMultiUserCollapsedBinding.inflate(inflater, parent, false)
                    }
                    UserCollapsedViewHolder(binding)
                }
            }

            else -> throw IllegalArgumentException("无效的视图类型")
        }
    }

//    override fun onBindViewHolder(
//        holder: RecyclerView.ViewHolder,
//        position: Int,
//        payloads: MutableList<Any>
//    ) {
//        if (payloads.isEmpty()) {
//            super.onBindViewHolder(holder, position, payloads)
//            return
//        }
//
//        val bundle = payloads[0] as Bundle
//        if (bundle.containsKey(PAYLOAD_RANK_CHANGE)) {
//            // 存在排名变化
//            animateViewChange(holder.itemView)
//        }
//        onBindViewHolder(holder, position)
//    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is UserExpandedStartedViewHolder -> {
                // pk开始展开视图
                val binding = holder.binding
                val tvRank = when (binding) {
                    is HichatItemPkTeamUserExpandedStartedBinding -> binding.tvRank
                    is HichatItemPkMultiUserExpandedStartedBinding -> binding.tvRank
                    else -> null
                }

                val ivAvatar = when (binding) {
                    is HichatItemPkTeamUserExpandedStartedBinding -> binding.ivAvatar
                    is HichatItemPkMultiUserExpandedStartedBinding -> binding.ivAvatar
                    else -> null
                }

                val lottieVoice = when (binding) {
                    is HichatItemPkTeamUserExpandedStartedBinding -> binding.lottieVoice
                    is HichatItemPkMultiUserExpandedStartedBinding -> binding.lottieVoice
                    else -> null
                }

                val tvName = when (binding) {
                    is HichatItemPkTeamUserExpandedStartedBinding -> binding.tvName
                    is HichatItemPkMultiUserExpandedStartedBinding -> binding.tvName
                    else -> null
                }

                val tvScore = when (binding) {
                    is HichatItemPkTeamUserExpandedStartedBinding -> binding.tvScore
                    is HichatItemPkMultiUserExpandedStartedBinding -> binding.tvScore
                    else -> null
                }

                val ivLock = when (binding) {
                    is HichatItemPkTeamUserExpandedStartedBinding -> binding.ivLock
                    is HichatItemPkMultiUserExpandedStartedBinding -> binding.ivLock
                    else -> null
                }
                val user = users[position]
                val rank = user.rank
                holder.itemView.tag = user
                ivAvatar?.let { bindAvatar(it, user) }
//                lottieVoice?.let { bindLottieVoice(it, user) }
                tvName?.text = user.name
                tvScore?.text = user.starCount.toString()
                ivLock?.let {
                    it.isSelected = user.isLock
                    it.setOnClickListener {
                        // 最小化无法点击"锁icon"
                        if(!isExpanded) return@setOnClickListener
                        pkAdapterCallback?.onUserLockClick(user.uuid ?: "", !user.isLock)
                    }
                }
                tvRank?.text =
                    if ((user.starCount?.toIntOrNull() ?: 0) <= 0) "-" else rank.toString()
            }

            is UserExpandedViewHolder -> {
                // PK未开始时
                val binding = holder.binding
                val ivAvatar = when (binding) {
                    is HichatItemPkTeamUserExpandedBinding -> binding.ivAvatar
                    is HichatItemPkMultiUserExpandedBinding -> binding.ivAvatar
                    else -> null
                }

                val tvName = when (binding) {
                    is HichatItemPkTeamUserExpandedBinding -> binding.tvName
                    is HichatItemPkMultiUserExpandedBinding -> binding.tvName
                    else -> null
                }

                val lottieVoice = when (binding) {
                    is HichatItemPkTeamUserExpandedBinding -> binding.lottieVoice
                    is HichatItemPkMultiUserExpandedBinding -> binding.lottieVoice
                    else -> null
                }
                val user = users.getOrNull(position)
                user?.let {
                    holder.itemView.tag = it
                    tvName?.text = it.name
                    ivAvatar?.let { bindAvatar(it, user) }
                }
            }

            is UserCollapsedViewHolder -> {
                val binding = holder.binding
                val (avatarView, lottieVoice) = when (binding) {
                    is HichatItemPkTeamUserCollapsedBinding -> Pair(
                        binding.ivAvatar,
                        binding.lottieVoice
                    )

                    is HichatItemPkMultiUserCollapsedBinding -> Pair(
                        binding.ivAvatar,
                        binding.lottieVoice
                    )

                    else -> Pair(null, null)
                }
                val user = users.getOrNull(position)
                // PK未开始时收起
                user?.let {
                    holder.itemView.tag = it
                    avatarView?.let { bindAvatar(it, user) }
                }
            }
        }
    }

    override fun getItemCount(): Int {
        // 现在添加按钮是固定在布局中的，所以列表大小就是实际用户数
        return users.size
    }

    class UserExpandedViewHolder(val binding: ViewBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            getLottieWaveView()?.setAnimation("lottie/hichat_pk_voice_wave.json")
            getLottieWaveView()?.repeatCount = Int.MAX_VALUE
        }

        fun getLottieWaveView(): LottieAnimationView? {
            val lottieVoice = when (binding) {
                is HichatItemPkTeamUserExpandedBinding -> binding.lottieVoice
                is HichatItemPkMultiUserExpandedBinding -> binding.lottieVoice
                else -> null
            }
            return lottieVoice
        }
    }

    class UserCollapsedViewHolder(val binding: ViewBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            getLottieWaveView()?.setAnimation("lottie/hichat_pk_voice_wave.json")
            getLottieWaveView()?.repeatCount = Int.MAX_VALUE
        }

        fun getLottieWaveView(): LottieAnimationView? {
            val lottieVoice = when (binding) {
                is HichatItemPkTeamUserCollapsedBinding ->
                    binding.lottieVoice

                is HichatItemPkMultiUserCollapsedBinding ->
                    binding.lottieVoice

                else -> null
            }
            return lottieVoice

        }
    }

    class UserExpandedStartedViewHolder(val binding: ViewBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            getLottieWaveView()?.setAnimation("lottie/hichat_pk_voice_wave.json")
            getLottieWaveView()?.repeatCount = Int.MAX_VALUE
        }

        fun getLottieWaveView(): LottieAnimationView? {
            val lottieVoice = when (binding) {
                is HichatItemPkTeamUserExpandedStartedBinding -> binding.lottieVoice
                is HichatItemPkMultiUserExpandedStartedBinding -> binding.lottieVoice
                else -> null
            }
            return lottieVoice
        }
    }


    /**
     * 为文本变化添加淡出淡入动画效果
     *
     * @param view 需要动画效果的View
     */
    private fun animateViewChange(view: View) {
        // 创建淡出动画
        val fadeOut = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f)
        fadeOut.duration = 300 // 淡出动画持续150毫秒
        // 创建淡入动画
        val fadeIn = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
        fadeIn.duration = 300 // 淡入动画持续150毫秒
        // 创建动画集合，按顺序播放
        val animatorSet = AnimatorSet()
        animatorSet.playSequentially(fadeOut, fadeIn)
        // 开始播放动画
        animatorSet.start()
    }

    private fun bindAvatar(avatar: AvatarDraweeView, item: HiChatPkUserBean) {
        avatar.setImageFromUrl(item.avatar)
        avatar.setOnClickListener {
            item.uuid?.let {
                pkAdapterCallback?.onUserClick(it, isOnMic = item.isOnMicSeat())
            }
        }
    }

    override fun onViewAttachedToWindow(holder: RecyclerView.ViewHolder) {
        super.onViewAttachedToWindow(holder)
        val lottieWaveView = when (holder) {
            is UserCollapsedViewHolder -> holder.getLottieWaveView()
            is UserExpandedViewHolder -> holder.getLottieWaveView()
            is UserExpandedStartedViewHolder -> holder.getLottieWaveView()
            else -> null
        }
        when (holder) {
            is UserCollapsedViewHolder, is UserExpandedViewHolder, is UserExpandedStartedViewHolder -> {
                holder.itemView.viewScope.launch {
                    val currentUuid = (holder.itemView.tag as? HiChatPkUserBean)?.uuid
                    HiChatRoomCoreHelper.getViewModel(HiChatRtcViewModel::class.java).micVolumeStateFlow.collectLatest {
                        // 用户音量变化是否包含自己，是的话则播放lottie
                        val vol = it?.firstOrNull { it.userId == currentUuid }
                        val isPlaying = lottieWaveView?.isAnimating ?: false
                        if ((vol?.volume ?: 0) > 0) {
                            if (!isPlaying) {
                                lottieWaveView?.playAnimation()
                                lottieWaveView?.isVisible = true
                            }
                        } else {
                            lottieWaveView?.pauseAnimation()
                            lottieWaveView?.isVisible = false
                        }
                    }
                }

            }
        }
    }

    interface PkAdapterCallBack {
        fun onUserClick(uuid: String, isOnMic: Boolean)


        /**
         * 用户锁定点击
         */
        fun onUserLockClick(uuid: String, isLock: Boolean)
    }
}