package com.xmhaibao.hichat.widget.pk

import android.os.Bundle
import androidx.recyclerview.widget.DiffUtil
import com.xmhaibao.hichat.bean.HiChatPkUserBean
import com.xmhaibao.hichat.bean.pk.HiChatPkResultUserItem

class HiChatPKUserDiffCallback(
    private val oldList: List<HiChatPkUserBean>,
    private val newList: List<HiChatPkUserBean>
) : DiffUtil.Callback() {

    companion object {
        const val PAYLOAD_SCORE_CHANGE = "payload_score_change"
        const val PAYLOAD_RANK_CHANGE = "payload_rank_change"
    }

    override fun getOldListSize(): Int = oldList.size
    override fun getNewListSize(): Int = newList.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        // 用唯一ID判断是否是同一个Item
        return oldList[oldItemPosition].uuid == newList[newItemPosition].uuid
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        // 判断内容是否有变化
        val oldUser = oldList[oldItemPosition]
        val newUser = newList[newItemPosition]

        return (oldUser.uuid == newUser.uuid &&
                oldUser.starCount == newUser.starCount &&
                oldUser.isLock == newUser.isLock) &&
                oldUser.rank == newUser.rank &&
                oldUser.diffContent == newUser.diffContent &&
                // 如果 oldUser 和 newUser 为同一个对象 且 hasChanged 为 true，说明数据发生了变化，需要刷新
                (!newUser.hasChanged || oldUser !== newUser)
    }
} 