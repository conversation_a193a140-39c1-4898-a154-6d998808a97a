package com.xmhaibao.hichat.dialog

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import cn.taqu.lib.base.bean.UiState
import com.xmhaibao.chatroom.api.constants.HiChatRoomInviteType
import com.xmhaibao.hichat.bean.HiChatFunGameItem
import com.xmhaibao.hichat.bean.HiChatRoomUserInfoBean
import com.xmhaibao.hichat.databinding.HichatRoomMoreFunDialogBinding
import com.xmhaibao.hichat.strategy.HiChatFunGameStrategyManager
import com.xmhaibao.hichat.dialog.edit.HiChatRoomInfoEditDialog
import com.xmhaibao.hichat.helper.HiChatPrivateImMsgHelper
import com.xmhaibao.hichat.helper.HiChatRoomCoreHelper
import com.xmhaibao.hichat.strategy.HiChatFunGameStrategy
import com.xmhaibao.hichat.track.HiChatRoomTrack
import com.xmhaibao.hichat.viewholder.HiChatFunGameViewHolder
import com.xmhaibao.hichat.viewmodel.HiChatMicViewModel
import com.xmhaibao.hichat.viewmodel.HiChatRoomOnlineMemberViewModel
import com.xmhaibao.hichat.viewmodel.HiChatRoomSocketViewModel
import com.xmhaibao.hichat.viewmodel.HiChatRoomViewModel
import com.xmhaibao.message.api.router.MessagePinsRouter
import com.xmhaibao.mine.api.constants.MineAttireScene
import com.xmhaibao.mine.api.router.MinePinsRouter
import hb.common.data.AccountHelper
import hb.kotlin_extension.isNull
import hb.kotlin_extension.noneSyncLazy
import hb.utils.ActivityUtils
import hb.xstatic.mvvm.XViewModelProviders
import hb.xstyle.xdialog.XDialog
import hb.xstyle.xdialog.XLifecycleDialog
import hb.xstyle.xdialog.XLoadingDialog
import hb.xtoast.XToastUtils
import hb.xadapter.XBaseAdapter
import com.xmhaibao.hichat.viewmodel.HiChatMoreFunViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch


/**
 * 畅聊圈，底部更多入口弹窗
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
class HiChatRoomMoreFunDialog(
    private var context: Context,
    private var mHiChatUuid: String?,
    private var mHiChatMasterUuidFlow: StateFlow<String>?,
) : XLifecycleDialog(context) {

    companion object {
        fun showDialog(
            context: Context?, hiChatUuid: String?,
            hiChatMasterUuidFlow: StateFlow<String>?,
        ) {
            ActivityUtils.getActivity(context)?.apply {
                if (this.isFinishing) {
                    return
                }
                HiChatRoomMoreFunDialog(this, hiChatUuid, hiChatMasterUuidFlow).show()
            }
        }
    }

    private val mBinding = HichatRoomMoreFunDialogBinding.inflate(layoutInflater)

    private val hiChatRoomViewModel by lazy {
        HiChatRoomCoreHelper.getViewModel(HiChatRoomViewModel::class.java)
    }
    private val hiChatRoomSocketViewModel by lazy {
        HiChatRoomCoreHelper.getViewModel(HiChatRoomSocketViewModel::class.java)
    }
    private val hiChatMicViewModel: HiChatMicViewModel by lazy {
        XViewModelProviders.getViewModel(this, HiChatMicViewModel::class.java)
    }
    private val mMemberViewModel: HiChatRoomOnlineMemberViewModel by lazy {
        HiChatRoomCoreHelper.getViewModel(HiChatRoomOnlineMemberViewModel::class.java)
    }

    /**
     * 畅聊圈更多功能面板 viewModel
     */
    private val mHiChatMoreFunViewModel by lazy {
        XViewModelProviders.getViewModel<HiChatMoreFunViewModel>(
            this, HiChatMoreFunViewModel::class.java
        )
    }

    private val mHiChatFunGameManager by lazy {
        HiChatFunGameStrategyManager(context)
    }

    private val hiChatRoomTrack by noneSyncLazy { HiChatRoomTrack() }
    private var roleType = "非圈主"
    private var userSelectDialog: HiChatSendUserSelectDialog? = null
    private val funGamesAdapter: XBaseAdapter by lazy {
        val adapter = XBaseAdapter(context)
        adapter.apply {
            register(HiChatFunGameItem::class.java, HiChatFunGameViewHolder::class.java)
            setOnItemClickListener { _, position ->
                val item =
                    items.getOrNull(position) as? HiChatFunGameItem ?: return@setOnItemClickListener
                onFunGameClick(item, position)
            }
            setOnViewHolderCreatedListener { holder ->
                (holder as? HiChatFunGameViewHolder)?.apply {
                    attachToWindow = { item, scope ->
                        mHiChatFunGameManager.getStrategy(item?.type ?: "")?.let {
                            it.attach(item, scope)
                            it.setOnEventBlock { event ->
                                // 状态变化回调，刷新UI
                                when (event) {
                                    HiChatFunGameStrategy.GameStrategyEvent.REFRESH -> funGamesAdapter.notifyItemChanged(
                                        position
                                    )

                                    HiChatFunGameStrategy.GameStrategyEvent.DISMISS -> dismiss()
                                }
                            }
                        }
                    }
                }
            }
        }
        adapter
    }

    private var mUnRedMsgNumber = 0
    private var mUnRedFamilyMsgNumber = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
        window?.apply {
            setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            setGravity(Gravity.BOTTOM)
            setWindowAnimations(com.ushengsheng.widget.R.style.PopupAnimation)
        }

        initView()
        initData()
    }

    private fun initData() {
        lifecycleScope.launch {
            mHiChatMasterUuidFlow?.collectLatest {
                if (it == AccountHelper.getAccountUuid()) {
                    hiChatRoomViewModel.getMoreToolsInfo()
                    roleType = "圈主"
                } else {
                    hiChatRoomViewModel.updateMoreToolsInfo()
                    roleType = "非圈主"
                }
            }
        }
        lifecycleScope.launchWhenCreated {
            hiChatRoomViewModel.moreToolsInfoFlow.collectLatest { hiChatRoomToolsInfoBean ->
                if (hiChatRoomToolsInfoBean != null) {
                    mBinding.tvMasterTools.isVisible = true
                    mBinding.svMasterTools.isVisible = true
                    mBinding.tvHiChatHandover.isVisible = true
                    mBinding.tvMuteAllMic.isSelected = hiChatRoomToolsInfoBean.isMuteAll()
                    mBinding.tvMicAuto.isSelected = hiChatRoomToolsInfoBean.isAutoUp()

                    // 更新「其他功能」标题的可见性
                    updateOtherToolsVisibilityForMasterTools()
                }
            }
        }
        lifecycleScope.launchWhenCreated {
            hiChatMicViewModel.muteAllFlow.collectLatest {
                if (it is UiState.Success) {
                    XLoadingDialog.hideLoadingbar()
                    mBinding.tvMuteAllMic.isSelected = !mBinding.tvMuteAllMic.isSelected
                    if (mBinding.tvMuteAllMic.isSelected) {
                        XToastUtils.show("你已开启全员闭麦")
                    } else {
                        XToastUtils.show("你已关闭全员闭麦")
                    }
                } else if (it is UiState.Failed) {
                    XLoadingDialog.hideLoadingbar()
                }
            }
        }
        lifecycleScope.launchWhenCreated {
            hiChatMicViewModel.autoUpFlow.collectLatest {
                if (it is UiState.Success) {
                    XLoadingDialog.hideLoadingbar()
                    mBinding.tvMicAuto.isSelected = !mBinding.tvMicAuto.isSelected
                    if (mBinding.tvMicAuto.isSelected) {
                        XToastUtils.show("已开启自动上麦功能")
                        hiChatRoomSocketViewModel.applyMicUserFlow.emit(null)
                    } else {
                        XToastUtils.show("已关闭自动上麦功能")
                    }
                } else if (it is UiState.Failed) {
                    XLoadingDialog.hideLoadingbar()
                }
            }
        }

        lifecycleScope.launchWhenCreated {
            mMemberViewModel.handOverMasterFlow.collectLatest {
                // 移交圈主成功
                dismiss()
            }
        }
        handlePrivateImMsgMenu()
    }

    private fun initView() {
        initFunGamesObserver()
        mHiChatMoreFunViewModel.fetchMoreGameFun(mHiChatFunGameManager)
        mBinding.apply {
            viewEmpty.setOnClickListener {
                dismiss()
            }
            tvMuteAllMic.setOnClickListener {
                if (tvMuteAllMic.isSelected) {
                    hiChatRoomTrack.hiChatOwnerToolClk(
                        HiChatRoomCoreHelper.currentRoomUuid(), "全员解除闭麦", roleType
                    )
                } else {
                    hiChatRoomTrack.hiChatOwnerToolClk(
                        HiChatRoomCoreHelper.currentRoomUuid(), "全员闭麦", roleType
                    )
                }
                hiChatMicViewModel.muteAllMic(!tvMuteAllMic.isSelected)
            }
            tvMicAuto.setOnClickListener {
                if (tvMicAuto.isSelected) {
                    hiChatRoomTrack.hiChatOwnerToolClk(
                        HiChatRoomCoreHelper.currentRoomUuid(), "关闭自动上麦", roleType
                    )
                } else {
                    hiChatRoomTrack.hiChatOwnerToolClk(
                        HiChatRoomCoreHelper.currentRoomUuid(), "开启自动上麦", roleType
                    )
                }
                XLoadingDialog.showLoadingbar(context)
                hiChatMicViewModel.setAutoUp(!tvMicAuto.isSelected)
            }
            tvMicManager.setOnClickListener {
                hiChatRoomTrack.hiChatOwnerToolClk(
                    HiChatRoomCoreHelper.currentRoomUuid(), "麦位管理", roleType
                )
                dismiss()
                val activity = ActivityUtils.getActivity(context) as? FragmentActivity
                    ?: return@setOnClickListener
                HiChatMicManagerDialog().showDialog(activity.supportFragmentManager.beginTransaction())
            }
            tvMuteManager.setOnClickListener {
                hiChatRoomTrack.hiChatOwnerToolClk(
                    HiChatRoomCoreHelper.currentRoomUuid(), "禁言管理", roleType
                )
                dismiss()
                HiChatMuteManagerDialog.showDialog(context, mHiChatUuid)
            }
            tvDress.setOnClickListener {
                hiChatRoomTrack.hiChatOwnerToolClk(
                    HiChatRoomCoreHelper.currentRoomUuid(), "装扮", roleType
                )
                dismiss()
                MinePinsRouter.launchMineAttireActivity(
                    MineAttireScene.HICHAT, MineAttireScene.TYPE_ID_HICHAT_MEDAL.toString()
                )
            }
            tvHiChatInfo.setOnClickListener {
                hiChatRoomTrack.hiChatOwnerToolClk(
                    HiChatRoomCoreHelper.currentRoomUuid(), "畅聊圈信息", roleType
                )
                dismiss()
                HiChatRoomInfoEditDialog.showDialog(context, mHiChatUuid, mHiChatMasterUuidFlow)
            }
            tvHiChatShare.setOnClickListener {
                hiChatRoomTrack.hiChatOwnerToolClk(
                    HiChatRoomCoreHelper.currentRoomUuid(), "分享畅聊圈", roleType
                )
                dismiss()
                HiChatRoomShareDialog.showDialog(
                    context,
                    mHiChatUuid,
                    hiChatInviteType = HiChatRoomInviteType.HICHAT_INVITE,
                    isMaster = mHiChatMasterUuidFlow?.value == AccountHelper.getAccountUuid()
                )
            }
            tvHiChatMsg.setOnClickListener {
                hiChatRoomTrack.hiChatOwnerToolClk(
                    HiChatRoomCoreHelper.currentRoomUuid(),
                    "消息",
                    roleType
                )
                dismiss()
                MessagePinsRouter.messageService().showMsgHalfOfScreenDialog("", "畅聊圈_半屏")
            }

            tvHiChatHandover.setOnClickListener {
                hiChatRoomTrack.hiChatOwnerToolClk(
                    HiChatRoomCoreHelper.currentRoomUuid(), "移交圈主", roleType
                )
                if (userSelectDialog != null) {
                    userSelectDialog?.show()
                    return@setOnClickListener
                }
                userSelectDialog = HiChatSendUserSelectDialog.showDialog(
                    context = context,
                    hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid(),
                    title = "选择移交对象",
                    autoDismiss = false,
                ) {
                    val user = it.getOrNull(0)
                    if (user.isNull()) {
                        releaseUserSelectDialog()
                        return@showDialog
                    }
                    showChangeMasterDialog(user)
                }
            }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        releaseUserSelectDialog()
    }

    /**
     * 释放用户选择弹窗
     */
    private fun releaseUserSelectDialog() {
        userSelectDialog?.dismiss()
        userSelectDialog = null
    }

    /**
     * 移交圈主 确认弹窗
     * @param user 移交对象
     */
    private fun showChangeMasterDialog(user: HiChatRoomUserInfoBean?) {
        user?.let {
            val builder = XDialog.newBuilder(context)
            builder.message("确认将圈主权力移交给\"${it.name}\"？移交后Ta将维护圈内秩序")
                .positiveButton("确定") { _, _ ->
                    mMemberViewModel.handOverMaster(
                        hiChatUuid = HiChatRoomCoreHelper.currentRoomUuid(), targetUuid = it.uuid
                    )
                }.negativeButton(
                    "取消"
                ) { _, _ -> }.promptType().show()
        }
    }

    /**
     * 初始化趣味玩法观察者
     */
    private fun initFunGamesObserver() {
        lifecycleScope.launch {
            mHiChatMoreFunViewModel.funGamesListFlow.collectLatest { funGamesList ->
                updateFunGamesUI(funGamesList)
                updateOtherToolsVisibility(funGamesList)
            }
        }
    }

    /**
     * 更新趣味玩法UI
     */
    private fun updateFunGamesUI(funGamesList: List<HiChatFunGameItem>) {
        if (funGamesList.isEmpty()) {
            mBinding.tvFunGames.isVisible = false
            mBinding.rvFunGames.isVisible = false
        } else {
            mBinding.tvFunGames.isVisible = true
            mBinding.rvFunGames.isVisible = true
            if (mBinding.rvFunGames.adapter == null) {
                mBinding.rvFunGames.apply {
                    layoutManager =
                        LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
                    adapter = funGamesAdapter
                }
            }
            funGamesAdapter.submitList(funGamesList)
        }
    }

    /**
     * 更新「其他功能」标题的可见性
     * 当「圈主工具」或「趣味玩法」任一有内容时才显示
     */
    private fun updateOtherToolsVisibility(funGamesList: List<HiChatFunGameItem>) {
        val hasFunGames = funGamesList.isNotEmpty()
        val hasMasterTools = mBinding.tvMasterTools.isVisible

        mBinding.tvOtherTools.isVisible = hasFunGames || hasMasterTools
    }

    /**
     * 当圈主工具状态变化时更新「其他功能」标题的可见性
     */
    private fun updateOtherToolsVisibilityForMasterTools() {
        val hasMasterTools = mBinding.tvMasterTools.isVisible
        val hasFunGames = mBinding.tvFunGames.isVisible

        mBinding.tvOtherTools.isVisible = hasFunGames || hasMasterTools
    }

    /**
     * 趣味玩法点击处理
     */
    private fun onFunGameClick(item: HiChatFunGameItem, position: Int) {
        val strategy = mHiChatFunGameManager.getStrategy(item.type ?: "")
        if (strategy != null) {
            // 埋点统计
            hiChatRoomTrack.hiChatOwnerToolClk(
                HiChatRoomCoreHelper.currentRoomUuid(),
                "${if (item.isOpen) "关闭" else ""}${item.name ?: ""}",
                roleType
            )
            // 执行策略
            strategy.handleClick(context, item)
        } else {
            // 不支持类型玩法
        }
    }

    /**
     * 处理半屏私信IM消息入口相关展示
     */
    private fun handlePrivateImMsgMenu() {
        val privateImMsgHelper = HiChatPrivateImMsgHelper()
        privateImMsgHelper.init(this)
        privateImMsgHelper.viewCallback = object : HiChatPrivateImMsgHelper.OnViewCallback {

            override fun updateMsgUnreadCount(count: Int) {
                mUnRedMsgNumber = count
                showUnRedCount(if (mUnRedFamilyMsgNumber > 0) mUnRedMsgNumber + 1 else mUnRedMsgNumber)
            }

            override fun updateFamilyMsgUnreadCount(count: Int) {
                mUnRedFamilyMsgNumber = count
                showUnRedCount(if (count > 0) mUnRedMsgNumber + 1 else mUnRedMsgNumber)
            }
        }
    }

    /**
     * 线上未读数量
     * @param count
     */
    private fun showUnRedCount(count: Int) {
        if (count > 99) {
            mBinding.tvUnReadMsg.text = "99+"
        } else {
            mBinding.tvUnReadMsg.text = count.toString()
        }
        mBinding.tvUnReadMsg.visibility = if (count > 0) View.VISIBLE else View.INVISIBLE
    }
}