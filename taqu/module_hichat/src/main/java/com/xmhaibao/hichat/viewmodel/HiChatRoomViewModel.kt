package com.xmhaibao.hichat.viewmodel

import android.app.Application
import androidx.lifecycle.viewModelScope
import cn.taqu.lib.base.bean.UiState
import cn.taqu.lib.base.bean.UiState.Companion.getSuccessOrNull
import com.xmhaibao.hichat.bean.HiChatMainBean
import com.xmhaibao.hichat.bean.HiChatIntoRoomBean
import com.xmhaibao.hichat.bean.HiChatRoomActivityStatusBean
import com.xmhaibao.hichat.bean.HiChatRoomTagInfoBean
import com.xmhaibao.hichat.bean.HiChatRoomThemeExtendItemBean
import com.xmhaibao.hichat.bean.HiChatRoomToolsInfoBean
import com.xmhaibao.hichat.bean.im.HiChatInfoChangeBean
import com.xmhaibao.hichat.helper.HiChatRoomCoreHelper
import com.xmhaibao.hichat.repository.HiChatRepository
import com.xmhaibao.hichat.repository.HiChatThemeExtentsRepository
import com.xmhaibao.hichat.router.HiChatPinsRouter
import com.xmhaibao.hichat.utils.notJoinHiChat
import com.xmhaibao.xjson.XJson
import hb.common.data.AccountHelper
import hb.common.xstatic.viewmodel.BaseViewModel
import hb.kotlin_extension.noneSyncLazy
import hb.utils.Loger
import hb.xrequest.awaitNullable
import hb.xrequest.launchHttp
import hb.xthread.XThreadPool
import hb.xtoast.XToastUtils
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

/**
 * 跟畅聊房生命周期绑定 viewModel
 * 长生命周期
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
class HiChatRoomViewModel(application: Application) : BaseViewModel(application) {
    companion object {
        private const val TAG = "HiChatRoomViewModel"
    }

    private val mHiChatRepository by lazy {
        HiChatRepository()
    }

    private val mThemeExtentsRepository by noneSyncLazy {
        HiChatThemeExtentsRepository()
    }

    private val mRtcViewModel by lazy {
        HiChatRoomCoreHelper.getViewModel(HiChatRtcViewModel::class.java)
    }

    /**
     * 畅聊房房间ID flow
     * 用 flow承载，后续出现 roomId 变化，重新拉取主接口
     */
    private val _mHiChatRoomIdFlow = MutableStateFlow<String?>(null)

    /**
     * 主接口信息
     */
    private val _mHiChatMainInfoFlow = MutableStateFlow<UiState<HiChatMainBean>>(UiState.Empty)
    val hiChatMainInfoFlow: Flow<UiState<HiChatMainBean>> = _mHiChatMainInfoFlow

    private val _hiChatPageActionFlow =
        MutableSharedFlow<HiChatActionIntent>(0, 10, BufferOverflow.DROP_LATEST)
    val hiChatPageActionFlow: Flow<HiChatActionIntent> = _hiChatPageActionFlow


    private val _themeReplyLeftTimeFlow = MutableSharedFlow<Long>()
    val themeReplyLeftTimeFlow: Flow<Long> = _themeReplyLeftTimeFlow


    /**
     * 主题信息flow
     */
    private val _themeInfoChangeFlow = MutableStateFlow<MutableList<HiChatRoomThemeExtendItemBean>?>(null)
    val themeInfoFlow =
        _themeInfoChangeFlow.combine(_mHiChatMainInfoFlow) { changeThemeInfo, mainInfo ->
            val themeInfo = mainInfo.getSuccessOrNull()?.themeInfo
            if (changeThemeInfo != null) {
                //没有变更取主接口的
                themeInfo?.themeExtends = changeThemeInfo
            }
            themeInfo
        }


    /**
     * 畅聊圈进房消息
     */
    private val _intoRoomInfoFlow = MutableStateFlow<HiChatIntoRoomBean?>(null)
    val intoRoomInfoFlow: Flow<HiChatIntoRoomBean?> = _intoRoomInfoFlow

    /**
     * 畅聊圈活动开关配置
     */
    private val _activityStatusFlow = MutableStateFlow<HiChatRoomActivityStatusBean?>(null)
    val activityStatusFlow: Flow<HiChatRoomActivityStatusBean?> = _activityStatusFlow


    /**
     * 房间名信息flow
     */
    private val _roomNameChangeFlow = MutableStateFlow<HiChatInfoChangeBean?>(null)
    val roomNameInfoFlow =
        _roomNameChangeFlow.combine(_mHiChatMainInfoFlow) { changeThemeInfo, mainThemeInfo ->
            var tempThemeInfo = changeThemeInfo
            if (tempThemeInfo == null) {
                //没有变更取主接口的
                tempThemeInfo = HiChatInfoChangeBean().apply {
                    this.content = (mainThemeInfo as? UiState.Success)?.data?.roomName
                }
            }
            tempThemeInfo
        }

    fun upDataRoomName(roomInfo: HiChatInfoChangeBean) {
        _roomNameChangeFlow.value = roomInfo
    }

    /**
     * 公告信息flow
     */
    private val _announcementChangeFlow = MutableStateFlow<HiChatInfoChangeBean?>(null)
    val announcementInfoFlow =
        _announcementChangeFlow.combine(_mHiChatMainInfoFlow) { changeThemeInfo, mainThemeInfo ->
            var tempThemeInfo = changeThemeInfo
            if (tempThemeInfo == null) {
                //没有变更取主接口的
                tempThemeInfo = HiChatInfoChangeBean().apply {
                    this.content = (mainThemeInfo as? UiState.Success)?.data?.announcement
                }
            }
            tempThemeInfo
        }

    fun upDataRoomAnnouncement(roomInfo: HiChatInfoChangeBean) {
        _announcementChangeFlow.value = roomInfo
    }

    private val _roomBackgroundChangeFlow = MutableStateFlow<HiChatInfoChangeBean?>(null)
    val roomBackgroundChangeFlow =
        _roomBackgroundChangeFlow.combine(_mHiChatMainInfoFlow) { changeThemeInfo, mainThemeInfo ->
            var tempThemeInfo = changeThemeInfo
            if (tempThemeInfo == null) {
                //没有变更取主接口的
                tempThemeInfo = HiChatInfoChangeBean().apply {
                    this.content = (mainThemeInfo as? UiState.Success)?.data?.bgImg
                }
            }
            tempThemeInfo
        }

    /**
     * 更新房间背景图片
     */
    fun updateRoomBackground(roomInfo: HiChatInfoChangeBean) {
        _mHiChatMainInfoFlow.value.getSuccessOrNull()?.apply {
            bgImg = roomInfo.content
        }
        viewModelScope.launch {
            _roomBackgroundChangeFlow.emit(roomInfo)
        }
    }


    /**
     * 房间标签信息
     */
    private val _roomTagChangeFlow = MutableStateFlow<MutableList<HiChatRoomTagInfoBean>?>(null)
    val roomTagChangeFlow =
        _roomTagChangeFlow.combine(_mHiChatMainInfoFlow) { tagList, mainThemeInfo ->
            var tempThemeInfo = tagList
            if (tempThemeInfo == null) {
                //没有变更取主接口的
                tempThemeInfo = (mainThemeInfo as? UiState.Success)?.data?.tagList
            }
            tempThemeInfo
        }

    fun upDataTagList(tagList: MutableList<HiChatRoomTagInfoBean>?) {
        tagList?.apply {
            _roomTagChangeFlow.value = tagList
        }
    }


    /**
     * 更新主信息
     */
    fun updateMain(hiChatMainInfo: HiChatMainBean) {
        _mHiChatMainInfoFlow.value = UiState.Success(hiChatMainInfo)
    }

    /**
     * 更新房间uuid
     */
    fun updateHiChatUuid(uuid: String) {
        _mHiChatRoomIdFlow.value = uuid
    }

    /**
     * 畅聊房Id
     */
    fun hiChatRoomId(): String {
        return _mHiChatRoomIdFlow.value ?: ""
    }


    /**
     * 获取当前房间分类id
     */
    fun getHiChatCateId():String{
        return _mHiChatMainInfoFlow.value.getSuccessOrNull()?.cateID ?: ""
    }

    /**
     * 进入房间
     */
    fun joinRoom() {
        // ack上报
        startOnlineAck()
        // 初始化相关接口请求
        initData()
    }

    private val _moreToolsInfoFlow = MutableStateFlow<HiChatRoomToolsInfoBean?>(null)
    val moreToolsInfoFlow: Flow<HiChatRoomToolsInfoBean?> = _moreToolsInfoFlow

    /**
     * 获取圈主工具信息
     */
    fun getMoreToolsInfo() {
        viewModelScope.launchHttp({
            val hiChatRoomToolsInfoBean =
                mHiChatRepository.getMoreToolsInfo(hiChatUuid = hiChatRoomId()).awaitNullable()
            _moreToolsInfoFlow.emit(hiChatRoomToolsInfoBean)
        })
    }

    /**
     * 更新置空圈主工具信息
     */
    fun updateMoreToolsInfo() {
        _moreToolsInfoFlow.value = null
    }

    /**
     * ack上报
     */
    private suspend fun ack(action: String, hiChatUuid: String = hiChatRoomId()) {
        val scope = if (action == "1") GlobalScope else viewModelScope
        scope.launchHttp({
            val response =
                mHiChatRepository.reportHiChatRoomState(hiChatUuid = hiChatUuid, action = action)
                    .awaitNullable()
            if (action == "2" && response != null) {
                // 是在线上报
                mRtcViewModel.switchMicStatus(response.micStatus)
            }
        }) {
            Loger.e(TAG, "ack上报失败：$it")
            // 圈子解散？
            if (it.notJoinHiChat()) {
                // 退出畅聊圈
                return@launchHttp false
            }
            return@launchHttp true
        }
    }

    private fun startOnlineAck() {
        // 启动上报心跳
        viewModelScope.launch(XThreadPool.HTTP().get().asCoroutineDispatcher()) {
            repeat(Int.MAX_VALUE) {
                delay(5 * 1000)
                ack("2")
            }
        }
    }


    /**
     * 房间信息变更
     */
    fun imHiChatInfoChange(vararg args: Any?) {
        if (args.isEmpty()) {
            return
        }
        if (args.size >= 2) {
            val changeType = args[0].toString()
            val data = XJson.fromJson(args[1].toString(), HiChatInfoChangeBean::class.java)
            when (changeType) {
                //房间名修改变更
                HiChatInfoChangeBean.ROOM_NAME_CHANGE -> {
                    _roomNameChangeFlow.value = data
                }
                //房间公告修改变更
                HiChatInfoChangeBean.ROOM_NOTICE_CHANGE -> {
                    _announcementChangeFlow.value = data
                }
                //房间主题延展内容修改变更
                HiChatInfoChangeBean.ROOM_THEME_EXTENTS_CHANGE -> {
                    getThemeExtendsInfo()
                }
                //房间标签变更
                HiChatInfoChangeBean.ROOM_TAG_CHANGE -> {
                    _roomTagChangeFlow.value = data?.tagList
                }
            }
        }
    }


    /**
     * 获取主题延展信息
     */
    private fun getThemeExtendsInfo(){
        viewModelScope.launchHttp({
            val themeResult = mThemeExtentsRepository.getThemeExtends(hiChatRoomId()).awaitNullable()
            _themeInfoChangeFlow.value = themeResult?.themeExtends ?: mutableListOf()
            _themeReplyLeftTimeFlow.emit(themeResult?.recommendReplyLeft?: 0)
        })
    }

    /**
     * 用户被踢出im
     */
    fun imKickOut(vararg args: Any?) {
        if (args.isEmpty()) {
            return
        }
        if (args.size >= 2) {
            val kickUuid = args[0].toString()
            if (kickUuid == AccountHelper.getAccountUuid()) {
                val toastInfo = args[1].toString()
                if (toastInfo.isNotEmpty()) {
                    XToastUtils.show(toastInfo)
                }
                //走推房逻辑
                HiChatPinsRouter.hiChatService().leaveRoom()
            }
        }
    }

    /**
     * 关闭房间
     * @param needConfirm 是否需要确认
     */
    fun closeRoom(needConfirm: Boolean = true) {
        MainScope().launch {
            _hiChatPageActionFlow.emit(HiChatActionIntent.CloseRoom(needConfirm))
        }
    }


    fun quickRoom(hiChatUuid: String = hiChatRoomId()) {
        GlobalScope.launch {
            ack("1", hiChatUuid = hiChatUuid)
        }
    }

    // region 初始化相关请求

    private fun initData() {
        // 这里后续做时序控制，减少并发
        requestExtraInfo()
    }


    /**
     * 扩展接口请求
     */
    private fun requestExtraInfo() {
        viewModelScope.launchHttp({
            // 延迟1s展示
            delay(1000)
            val hiChatIntoRoomMsg = mHiChatRepository.featHiChatIntoRoom(hiChatUuid = hiChatRoomId())
            hiChatIntoRoomMsg?.let {
                _intoRoomInfoFlow.value = it
            }
        }) {
            return@launchHttp true
        }

        viewModelScope.launchHttp({
            // 获取房间内活动状态
            val activityStatusBean = mHiChatRepository.getHiChatRoomActivityState(hiChatUuid = hiChatRoomId()).awaitNullable()
            activityStatusBean?.let {
                _activityStatusFlow.value = it
            }

        }){
            return@launchHttp true
        }
    }

    // endregion


    override fun onCleared() {
        super.onCleared()
    }
}


/**
 * 畅聊圈 - 页面Action行为
 */
sealed class HiChatActionIntent {
    object Default : HiChatActionIntent()

    /**
     * 关闭房间
     * @param needConfirm 是否需要确认
     *
     */
    class CloseRoom(val needConfirm: Boolean) : HiChatActionIntent()
}