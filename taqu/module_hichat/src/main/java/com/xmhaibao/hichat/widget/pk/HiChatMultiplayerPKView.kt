package com.xmhaibao.hichat.widget.pk

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.core.view.isVisible
import cn.taqu.lib.base.router.RouterLaunch
import com.xmhaibao.hichat.R
import com.xmhaibao.hichat.bean.HiChatPkUserBean
import com.xmhaibao.hichat.bean.pk.HiChatPKResponseBean
import com.xmhaibao.hichat.bean.pk.HiChatPkTeamType
import com.xmhaibao.hichat.bean.pk.PKStatus
import com.xmhaibao.hichat.databinding.HichatIncludePkHeaderBinding
import com.xmhaibao.hichat.databinding.HichatViewMultiplayerPkBinding
import dp


/**
 * 多人PK视图
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
class HiChatMultiplayerPKView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : HiChatBasePKView(context, attrs, defStyleAttr) {
    companion object {

        /**
         * 当前锁定的uuid
         */
        private var mLockUuid: String? = null
        private var mLockRoundId: String? = null
    }

    private val binding = HichatViewMultiplayerPkBinding.inflate(LayoutInflater.from(context), this)
    private val headerBinding = HichatIncludePkHeaderBinding.bind(this)

    private lateinit var expandedAdapter: HiChatPKUserAdapter
    private lateinit var collapsedAdapter: HiChatPKUserAdapter

    private val users = mutableListOf<HiChatPkUserBean>()

    init {
        // 设置渐变背景
        setBackgroundResource(R.drawable.hichat_pk_bg_gradient)
        setupViews()
        setupListeners()
        // 设置初始状态为展开
        setExpanded(true)
        // 设置默认最大人数为20人
        maxTeamSize = 20
    }

    override fun onMaxTeamSizeChanged() {
        updateAddButtonVisibility()
    }


    private fun setupViews() {
        setupListAppearance()

        // 展开适配器（不再需要添加按钮回调，因为按钮现在是固定的）
        expandedAdapter =
            HiChatPKUserAdapter(isExpanded = true, isTeamPK = false, pkAdapterCallback = this)

        // 收起适配器（不再需要添加按钮回调，因为按钮现在是固定的）
        collapsedAdapter =
            HiChatPKUserAdapter(isExpanded = false, isTeamPK = false, pkAdapterCallback = this)

        // 设置展开recycleView属性
        binding.expandedContent.rvUsers.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = expandedAdapter
            // 完全禁用所有动画，包括插入、移除、变化和位置移动动画
//            itemAnimator = null
        }

        // 设置收起 recycleView 属性
        binding.collapsedContent.rvUsers.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = collapsedAdapter
            // 完全禁用所有动画，包括插入、移除、变化和位置移动动画
//            itemAnimator = null
        }

        // 设置标题
        headerBinding.ivTitle.setImageResource(R.drawable.hichat_pk_multi_player_ic)
        binding.collapsedContent.ivAddMember.layoutParams =
            binding.collapsedContent.ivAddMember.layoutParams.apply {
                height = 32.dp
                width = 32.dp
            }

        // 初始化列表提交
        updateLists()
    }

    private fun setupListAppearance() {
        binding.expandedContent.root.setBackgroundResource(R.drawable.hichat_pk_bg_multiplayer_team)
        binding.collapsedContent.root.setBackgroundResource(R.drawable.hichat_pk_bg_multiplayer_team)
    }

    private fun setupListeners() {
        binding.btnToggleExpand.setOnClickListener { toggleExpand() }
    }

    private fun toggleExpand() {
        val isExpanded = binding.expandedContent.root.isVisible
        setExpanded(!isExpanded)
    }

    override fun setExpanded(isExpanded: Boolean) {
        super.setExpanded(isExpanded)
        // 使用基类的公共方法设置展开状态
        setExpandedCommon(
            isExpanded,
            binding.expandedContent.root,
            binding.collapsedContent.root,
            binding.btnToggleExpand
        )

        // 更新引导视图的约束，确保按钮位置正确
        updateContentBottomGuide(isExpanded)

        // 控制添加按钮的显示
        updateAddButtonVisibility()
    }

    /**
     * 更新内容底部引导视图的约束
     * 确保按钮始终在正确的内容容器下方
     */
    private fun updateContentBottomGuide(isExpanded: Boolean) {
        val constraintSet = androidx.constraintlayout.widget.ConstraintSet()
        constraintSet.clone(this)

        if (isExpanded) {
            // 展开状态：引导视图约束到展开内容下方
            constraintSet.connect(
                binding.contentBottomGuide.id,
                androidx.constraintlayout.widget.ConstraintSet.TOP,
                binding.expandedContent.root.id,
                androidx.constraintlayout.widget.ConstraintSet.BOTTOM
            )
        } else {
            // 收起状态：引导视图约束到收起内容下方
            constraintSet.connect(
                binding.contentBottomGuide.id,
                androidx.constraintlayout.widget.ConstraintSet.TOP,
                binding.collapsedContent.root.id,
                androidx.constraintlayout.widget.ConstraintSet.BOTTOM
            )
        }

        constraintSet.applyTo(this)
    }

    private fun updateLists(isStructuralChange: Boolean = false) {
        // 更新锁定状态并标记变化
        users.forEachIndexed { index, user ->
            user.hasChanged = false
            val newLockState = user.uuid == mLockUuid
            if (user.isLock != newLockState) {
                user.isLock = newLockState
                user.hasChanged = true
            }
            user.rank = index + 1
        }

        // 总是传递列表的副本给适配器
        expandedAdapter.submitList(ArrayList(users), isStructuralChange)
        collapsedAdapter.submitList(ArrayList(users), isStructuralChange)

        // 更新添加按钮的显示状态
        updateAddButtonVisibility()
    }

    /**
     * 更新添加按钮的显示状态
     * 展开状态：列表为空时显示中间，有用户时显示顶部，满员时隐藏
     * 收起状态：只在PK未开始且未满时显示
     */
    private fun updateAddButtonVisibility() {
        // 使用基类的方法检查是否应该显示添加按钮
        val showAddButton = shouldShowAddButton(users.size)

        // 展开状态的添加按钮逻辑
        updateExpandedAddButton(
            binding.expandedContent.ivAddMember,
            binding.expandedContent.rvUsers,
            binding.expandedContent.tvEmptyTips,
            showAddButton,
            users.isEmpty()
        )

        // 收起状态的添加按钮逻辑
        updateCollapsedAddButton(
            binding.collapsedContent.ivAddMember,
            tvEmptyTips = binding.collapsedContent.tvEmptyTips,
            showAddButton,
            isEmpty = users.isEmpty()
        )
    }

    /**
     * 绑定多人PK数据
     */
    override fun bindPKData(pkData: HiChatPKResponseBean) {
        super.bindPKData(pkData)

        headerBinding.ivHelp.setOnClickListener {
            RouterLaunch.dealJumpData(context, pkData.rule)
        }

        // 只处理多人PK数据
        if (!pkData.isMultiPlayerPK()) return

        val multiPlayerPK = pkData.multiPlayerPk ?: return

        // 更新成员数据 - 直接使用PKMember，按分数排序
        val members = multiPlayerPK.members ?: emptyList()

        // 更新内部数据
        users.clear()
        users.addAll(members)

        // 更新适配器
        updateLists()

        // 更新添加按钮显示状态
        updateAddButtonVisibility()

        binding.tvBottomTip.isVisible = pkStatus == PKStatus.IN_PROGRESS

        bindActionButtonStatus(
            btnStart = headerBinding.btnStart, btnTimeSettings = headerBinding.btnTimeSettings, isTeamPk = false
        )

        // 展开样式
        bindAddButtonStatus(
            btnAdd = binding.expandedContent.ivAddMember,
            tvEmptyTips = binding.expandedContent.tvEmptyTips,
            currentUsers = users,
            isExpanded = true,
            teamType = HiChatPkTeamType.MULTI_PLAYER,
        )
        // 收起样式
        bindAddButtonStatus(
            btnAdd = binding.collapsedContent.ivAddMember,
            tvEmptyTips = binding.collapsedContent.tvEmptyTips,
            currentUsers = users,
            isExpanded = false,
            teamType = HiChatPkTeamType.MULTI_PLAYER,
        )

        updateRemainTime(binding.tvTimer, pkData.remainTime?.toLongOrNull() ?: -1)
    }

    override fun onRoundIdChanged(mRoundId: String?) {
        // 场次id发生变化，重置锁定的uuid
        if (mLockRoundId != mRoundId) {
            mLockUuid = null
        }
    }

    /**
     * PK状态变化处理
     */
    override fun onPKStatusChanged(pkStatus: String) {
        super.onPKStatusChanged(pkStatus)

        if (pkStatus == PKStatus.IN_PROGRESS) {
            // PK开始
            headerBinding.btnStart.visibility = GONE
            headerBinding.btnTimeSettings.visibility = GONE

            // 设置进度条为PK开始状态
            binding.progressBar.setPkStarted(true)

        } else {
            // PK结束
            headerBinding.btnStart.visibility = VISIBLE
            headerBinding.btnTimeSettings.visibility = VISIBLE
            // 设置进度条为PK结束状态
            binding.progressBar.setPkStarted(false)
        }
        // 更新适配器状态
        expandedAdapter.pkStatus = pkStatus
        collapsedAdapter.pkStatus = pkStatus

        // 更新添加按钮显示状态
        updateAddButtonVisibility()
    }

    override fun onUserLockClick(uuid: String, isLock: Boolean) {
        super.onUserLockClick(uuid, isLock)
        mLockUuid = if (isLock) uuid else null
        updateLists()
    }
}