package com.xmhaibao.hichat.widget

import android.graphics.*
import android.graphics.drawable.Drawable
import kotlin.math.cos
import kotlin.math.sin

/**
 * 渐变边框 Drawable
 * 只绘制边框，内部完全透明，可以让下层内容穿透
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
class GradientBorderDrawable(
    private val borderWidth: Int = 3,
    private val cornerRadius: Int = 36,
    private val gradientAngle: Float = 315f,
    private val startColor: Int = Color.parseColor("#FF5DBE"),
    private val endColor: Int = Color.parseColor("#42C0FF")
) : Drawable() {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val path = Path()
    private val borderPath = Path()
    
    override fun draw(canvas: Canvas) {
        val bounds = bounds
        if (bounds.isEmpty) return

        val width = bounds.width().toFloat()
        val height = bounds.height().toFloat()
        
        // 根据角度计算渐变起始和结束点
        val angleRad = Math.toRadians(gradientAngle.toDouble())
        val centerX = width / 2f
        val centerY = height / 2f
        val radius = maxOf(width, height) / 2f

        val startX = centerX - (radius * cos(angleRad)).toFloat()
        val startY = centerY - (radius * sin(angleRad)).toFloat()
        val endX = centerX + (radius * cos(angleRad)).toFloat()
        val endY = centerY + (radius * sin(angleRad)).toFloat()

        // 创建渐变着色器
        val gradient = LinearGradient(
            startX, startY, endX, endY,
            startColor, endColor,
            Shader.TileMode.CLAMP
        )
        paint.shader = gradient
        paint.style = Paint.Style.FILL

        // 外边框路径
        val outerRect = RectF(0f, 0f, width, height)
        path.reset()
        path.addRoundRect(outerRect, cornerRadius.toFloat(), cornerRadius.toFloat(), Path.Direction.CW)

        // 内边框路径（挖空的部分）
        val innerRect = RectF(
            borderWidth.toFloat(),
            borderWidth.toFloat(),
            width - borderWidth,
            height - borderWidth
        )
        val innerCornerRadius = maxOf(0f, (cornerRadius - borderWidth).toFloat())
        borderPath.reset()
        borderPath.addRoundRect(innerRect, innerCornerRadius, innerCornerRadius, Path.Direction.CW)

        // 使用 Path.Op.DIFFERENCE 来挖空中间部分
        path.op(borderPath, Path.Op.DIFFERENCE)

        // 绘制边框
        canvas.drawPath(path, paint)
    }

    override fun setAlpha(alpha: Int) {
        paint.alpha = alpha
        invalidateSelf()
    }

    override fun setColorFilter(colorFilter: ColorFilter?) {
        paint.colorFilter = colorFilter
        invalidateSelf()
    }

    override fun getOpacity(): Int {
        return PixelFormat.TRANSLUCENT
    }
}
