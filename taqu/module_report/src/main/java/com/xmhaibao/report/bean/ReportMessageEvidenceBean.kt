package com.xmhaibao.report.bean

import android.os.Parcel
import android.os.Parcelable

/**
 * 举报证据-消息内容
 *
 * <AUTHOR>
 * @date 2023/6/5
 */
data class ReportMessageEvidenceBean(
    var msgId: String? = "",
    var createTime: Long = 0
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readLong()
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(msgId)
        parcel.writeLong(createTime)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ReportMessageEvidenceBean> {
        override fun createFromParcel(parcel: Parcel): ReportMessageEvidenceBean {
            return ReportMessageEvidenceBean(parcel)
        }

        override fun newArray(size: Int): Array<ReportMessageEvidenceBean?> {
            return arrayOfNulls(size)
        }
    }
}