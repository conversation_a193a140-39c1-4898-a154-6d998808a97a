package com.xmhaibao.secret.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.LifecycleOwner
import com.xmhaibao.secret.bean.MessageSecretChatMaleDetailBean
import com.xmhaibao.secret.constants.MessageSecretChatConstants.MESSAGE_SECRET_CHAT_MALE_GUIDE_ONCE
import com.xmhaibao.secret.dialog.MessageSecretChatMaleDetailDialog
import com.xmhaibao.secret.vm.MessageSecretChatViewModel
import com.xmhaihao.message.databinding.MessageSecretChatMaleGuideViewBinding
import hb.common.data.AccountHelper
import hb.utils.ActivityUtils
import hb.utils.ColorUtils
import hb.utils.SizeUtils
import hb.utils.kvcache.KVCacheUtils
import hb.ximage.fresco.BaseDraweeView
import hb.xstatic.mvvm.XViewModelProviders
import hb.xthread.XThreadPool

/**
 * 小秘密玩法-男用户引导view
 *
 * <AUTHOR>
 * @date 2023-05-11
 */
class MessageSecretChatMaleGuideView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    companion object{
        /**
         * 是否正在显示引导
         */
        var isShowGuide = false
    }

    /**
     * 用户uuid
     */
    private var mAccountUuid: String? = ""

    /**
     * 布局view
     */
    private val mBinding: MessageSecretChatMaleGuideViewBinding by lazy {
        MessageSecretChatMaleGuideViewBinding.inflate(LayoutInflater.from(context), this)
    }

    /**
     * viewModel
     */
    private val mViewModel: MessageSecretChatViewModel by lazy {
        XViewModelProviders.getViewModel(
            mActivityLifecycleOwner,
            MessageSecretChatViewModel::class.java
        )
    }

    val mContentView: ViewGroup by lazy {
        ActivityUtils.getTopActivity().findViewById(android.R.id.content)
    }


    /**
     * 当前View依附在哪个页面的生命周期
     */
    private val mActivityLifecycleOwner by lazy {
        context as LifecycleOwner
    }

    init {
        setBackgroundColor(ColorUtils.getColor("#********"))
        mViewModel.maleDetailBean.removeObservers(mActivityLifecycleOwner)
        mViewModel.maleDetailBean.observe(mActivityLifecycleOwner) {
            it?.run {
                if (!MessageSecretChatMaleDetailDialog.isShowing) {
                    MessageSecretChatMaleDetailDialog(context, this, false, mAccountUuid).show()
                }
                mViewModel.maleDetailBean.removeObservers(mActivityLifecycleOwner)
            }
        }
    }

    /**
     * 动态设置高亮位置显示
     *
     * @param clSecret 小秘密图标控件
     * @param accountUuid 用户uuid
     */
    fun showGuideView(clSecret: BaseDraweeView, accountUuid: String?) {
        mAccountUuid = accountUuid
        val location = IntArray(2)
        clSecret.getLocationInWindow(location)
        //计算距离左边的距离
        val start: Int = location[0]
        //计算距离顶部的距离
        val top: Int = location[1]
        val params = mBinding.ivSecret.layoutParams as LayoutParams
        params.marginStart = start
        params.topMargin = top
        mBinding.ivSecret.layoutParams = params
        showGuideLottie(start, accountUuid)
        val layoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        mContentView.addView(this, layoutParams)


    }

    /**
     * 显示底部动画引导
     * @param accountUuid 用户uuid
     */
    private fun showGuideLottie(start: Int, accountUuid: String?) {
        mBinding.lottieGuide.post {
            val lottieParams = mBinding.lottieGuide.layoutParams as LayoutParams
            lottieParams.marginStart = start - SizeUtils.dp2px(80f)
            mBinding.lottieGuide.layoutParams = lottieParams
        }
        mBinding.lottieGuide.imageAssetsFolder = "lottie/message_menu_guide/images/"
        mBinding.lottieGuide.setAnimation("lottie/message_menu_guide/data.json")
        mBinding.lottieGuide.playAnimation()
        XThreadPool.Main().delay(2500).execute {
            showBottomDialog()
        }
    }

    /**
     * 展示底部小秘密弹框
     */
    private fun showBottomDialog() {
        //1、lottie动画播放，2、移除当前view，3、自动弹出底部弹框
        mContentView.removeView(this)
        if (!MessageSecretChatMaleDetailDialog.isShowing) {
            MessageSecretChatMaleDetailDialog(context, MessageSecretChatMaleDetailBean(), false, mAccountUuid).show()
        }

    }


    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        //针对所有用户只引导一次
        KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).putBoolean(MESSAGE_SECRET_CHAT_MALE_GUIDE_ONCE, true)
        isShowGuide = true
    }


    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mBinding.lottieGuide.cancelAnimation()
        isShowGuide = false
    }
}