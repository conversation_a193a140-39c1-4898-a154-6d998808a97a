<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="MissingDefaultResource"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivBarTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:placeholderImage="@color/transparent"
        app:viewAspectRatio="10.5" />

    <View
        android:id="@+id/viewPlace"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:background="#FFF2F0"
        app:layout_constraintTop_toBottomOf="@+id/ivBarTop" />

    <TextView
        android:id="@+id/tvEditTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/message_secret_chat_detail_dot_ic"
        android:drawableEnd="@drawable/message_secret_chat_detail_dot_ic"
        android:drawablePadding="6dp"
        android:text="编辑秘密"
        android:textColor="#BD385F"
        android:textSize="@dimen/TH_FONT_B2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/viewPlace" />

</merge>