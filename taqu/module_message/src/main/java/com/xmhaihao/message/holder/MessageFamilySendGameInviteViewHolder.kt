package com.xmhaihao.message.holder

import android.view.ViewGroup
import com.xmhaihao.message.R
import com.xmhaihao.message.bean.family.MessageFamilyGameInviteBean
import com.xmhaihao.message.databinding.FamilySendGameInviteItemBinding
import hb.message.db.entity.HBMessageContentDO

/**
 * description:家族消消乐游戏邀请发送方消息（展示在私信页面）
 *
 * <AUTHOR>
 * @date 2023/12/13
 */
class MessageFamilySendGameInviteViewHolder(parent: ViewGroup) :
    MessageIMBaseViewHolder<MessageFamilyGameInviteBean>(
        parent,
        R.layout.family_send_game_invite_item
    ) {

    private var mBinding: FamilySendGameInviteItemBinding =
        FamilySendGameInviteItemBinding.bind(itemView)

    override fun onBindViewHolder(
        messageContent: HBMessageContentDO?,
        messageContentBean: MessageFamilyGameInviteBean?
    ) {
        super.onBindViewHolder(messageContent, messageContentBean)
        messageContentBean?.let {
            mBinding.gameInviteCLayout.setView(messageContentBean)
        }
    }
}