package com.xmhaihao.message.helper

import android.annotation.SuppressLint
import android.content.Context
import android.os.CountDownTimer
import android.view.View
import android.widget.FrameLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.*
import cn.taqu.lib.base.constants.NavigationTabs
import cn.taqu.lib.base.event.EventNavigationTabChanged
import cn.taqu.lib.base.event.EventXjbUserLoginOut
import com.xmhaibao.message.api.interf.IMessageFemaleTaskDialogController
import com.xmhaihao.message.bean.MessageBannerTypeBean
import com.xmhaihao.message.bean.MessageFemaleTaskListBean
import com.xmhaihao.message.event.MessageBottomBannerEvent
import com.xmhaihao.message.viewModel.MessageFemaleViewModel
import com.xmhaihao.message.widget.MessageFemaleFloatView
import com.xmhaihao.message.widget.MessageFemaleTaskListDialog
import com.xmhaibao.message.api.tracker.MessageWithDrawTracker
import hb.common.data.AccountHelper
import hb.utils.ActivityUtils
import hb.utils.EventBusUtils
import hb.utils.kvcache.KVCacheUtils
import hb.xstatic.tools.XEventBus

/**
 * 女用户成长悬浮窗 控制层-请使用getInstance获取实例对象
 *
 * 对应View层：由外部传入的FrameLayout,控制层会在该View中添加悬浮窗View
 * 对应viewModel层：MessageFemaleViewModel
 *
 * 当初始化的时候，会请求悬浮窗接口，成功回调会添加一个悬浮窗
 *
 * <AUTHOR>
 * @date 2022-06-06
 */
class MessageFemaleTaskDialogController(
    var viewModelStoreOwner: ViewModelStoreOwner?,
    var owner: LifecycleOwner?,
    /**
     *  埋点需求，表示当前在那个tab页面显示的
     */
    var positionName: String
) : IMessageFemaleTaskDialogController, DefaultLifecycleObserver {


    companion object {

        /**
         *  倒计时结束 悬浮窗样式
         */
        const val TIME_END_FLOAT = "00:00:00"

        /**
         * 倒计时结束 任务列表弹窗
         */
        const val TIME_END_TASK_DIALOG = "0小时0分0秒"

        /**
         * 倒计时间隔
         */
        const val TIME_DOWN_DURATION = 1000L


        /**
         * 是否展示悬浮dialog，当倒计时结束的时候，其中一个悬浮关掉了，另一个悬浮判断此开关，同步关掉
         */
        @JvmField
        @Volatile
        var showFloatDialog = false


        @Volatile
        @JvmField
        @SuppressLint("StaticFieldLeak")
        var mController: MessageFemaleTaskDialogController? = null

        /**
         * 提供给多个界面统一的单例
         */
        @JvmStatic
        fun getInstance(
            fragmentActivity: FragmentActivity, positionName: String
        ): MessageFemaleTaskDialogController? {
            if (mController == null) {
                synchronized(MessageFemaleTaskDialogController::class.java) {
                    if (mController == null) {
                        mController = MessageFemaleTaskDialogController(
                            fragmentActivity,
                            fragmentActivity,
                            positionName
                        )
                    }
                }
            }
            return mController
        }

    }

    var mViewModel: MessageFemaleViewModel? = null
    var mDialog: MessageFemaleTaskListDialog? = null

    /**
     * 弹窗bean data
     */
    var mDialogBeanObserver: Observer<MessageFemaleTaskListBean?>
    /**
     * 浮窗bean data
     */
    var mFloatBeanObserver: Observer<MessageFemaleTaskListBean?>

    /**
     * 是否显示浮窗bean data
     */
    var mIsShowFloatObserver: Observer<Boolean>

    init {
        viewModelStoreOwner?.let {
            mViewModel = ViewModelProvider(it)[MessageFemaleViewModel::class.java]
        }

        mDialogBeanObserver = Observer<MessageFemaleTaskListBean?> {
            it?.run {
                //隐藏悬浮窗
                if (isFinishAllTask() || isTimeEnd()) {
                    showFloatDialog = false
                    setShowFloatDialog(showFloatDialog)
                }

                if (!mCurSelectTabNeedShow) {
                    return@run
                }
                if (mDialog?.isShowing == true) {
                    return@run
                }

                //需要重新检查一遍！
                if (mViewModel?.checkShowTaskDialog(this)==false) {
                    return@run
                }
                mViewModel?.saveDialogShowCount(this)

                MessageWithDrawTracker.Instance.trackTqUserGrowthPopExpose(positionName)
                if (mDialog == null) {
                    getContextFromLifecycleOwner(viewModelStoreOwner)?.run {
                        mDialog = MessageFemaleTaskListDialog(this, positionName)
                        mDialog?.setOnDismissListener {
                            mDialog = null
                        }
                    }
                }
                mDialog?.showDialog(
                    this,
                    mViewModel?.defaultBtnText,
                    mViewModel?.defaultSelectPos ?: 0,
                    positionName
                )

            }
        }
        mFloatBeanObserver = Observer<MessageFemaleTaskListBean?> {
            it?.run {

                //倒计时没结束，更新悬浮UI，内部有处理，避免重复刷新UI
                if (!isTimeEnd()) {
                    mFloatViews.forEach { view ->
                        view.updateFloatDialogUI(this)
                    }
                }

                mFloatViews.forEach { view ->
                    view.updateFloatDialogData(this)
                }
                checkStartCountDown(this)
            }
        }

        mIsShowFloatObserver = Observer<Boolean> {
            showFloatDialog = it
            setShowFloatDialog(it)
        }
    }

    private fun getContextFromLifecycleOwner(owner: ViewModelStoreOwner?): Context? {
        return when (owner) {
            is Fragment -> owner.requireContext()
            is FragmentActivity -> owner
            else -> null
        }
    }
    /**
     * 多个悬浮View
     */
    var mFloatViews: MutableList<MessageFemaleFloatView> = mutableListOf()

    var mFloatViewParents: MutableList<FrameLayout> = mutableListOf()

    /**
     * 任务结束时间
     */
    var mEndTime: Long = 0L



    /**
     * 倒计时
     */
    var mCountDownTimer: CountDownTimer? = null


    /**
     * 是否已经开启了倒计时
     */
    @Volatile
    var mIsStartCountDown: Boolean = false

    /**
     * 当前选中的tab是否需要展示任务弹窗
     */
    @Volatile
    var mCurSelectTabNeedShow = true

    /**
     * 切换tab是否需要请求数据
     */
    @Volatile
    var mTogglesTabNeedRequest = true

    /**
     * 上一次记录是否展示女用户悬浮窗，默认false
     */
    var mLastShouldShow = false

    /**
     * 外部获取实例化对象之后，就调用该函数，传入frameLayout
     */
    fun init(frameLayout: FrameLayout) {
        //如果已经添加了，就不重复添加
        if (mFloatViewParents.contains(frameLayout)) {
            return
        }
        //吧父布局添加到队列中
        mFloatViewParents.add(frameLayout)
        //创建一个新的悬浮View
        val floatView = MessageFemaleFloatView(frameLayout.context)
        frameLayout.removeAllViews()
        frameLayout.addView(floatView)
        mFloatViews.add(floatView)
        initViews(floatView)
    }


    /**
     * 外部获取实例化对象之后，就调用该函数，传入frameLayout
     */
    override fun initAndGetFloatView(frameLayout: FrameLayout): View? {
        //如果已经添加了，就不重复添加
        if (mFloatViewParents.contains(frameLayout)) {
            return null
        }
        //吧父布局添加到队列中
        mFloatViewParents.add(frameLayout)
        //创建一个新的悬浮View
        val floatView = MessageFemaleFloatView(frameLayout.context)
        frameLayout.removeAllViews()
        frameLayout.addView(floatView)
        mFloatViews.add(floatView)
        initViews(floatView)

        return floatView
    }


    /**
     * 获取KVCache的key值,
     * 消息中心tab创建的时候，需要根据该值判断是否需要展示悬浮View
     */
    override fun getKvCacheKey(): String {
        return StringBuilder(AccountHelper.getAccountUuid())
            .append("_")
            .append(mViewModel?.mStrategyId)
            .append("_")
            .append("message_female_strategy")
            .toString()
    }


    /**
     * 移除指定悬浮View
     */
    override fun removeFloatView(frameLayout: FrameLayout) {
        if (!mFloatViewParents.contains(frameLayout)) {
            return
        }
        frameLayout.removeAllViews()
        var frameLayoutIndex = -1
        mFloatViewParents.forEachIndexed { index, layout ->
            if (layout == frameLayout) {
                frameLayoutIndex = index
            }
        }
        mFloatViewParents.remove(frameLayout)
        if (frameLayoutIndex != -1) {
            mFloatViews.removeAt(frameLayoutIndex)
        }
    }

    /**
     * 移除监听
     */
    override fun removeObserver() {
        mViewModel?.run {
            mDialogBean.removeObserver(mDialogBeanObserver)
            mFloatDialogBean.removeObserver(mFloatBeanObserver)
            isShowFloatFloat.removeObserver(mIsShowFloatObserver)
        }
    }

    private fun initViews(frameLayout: FrameLayout) {
        frameLayout.setOnClickListener {
            MessageWithDrawTracker.Instance.trackTqUserGrowthIconClick()
            showTaskDialog()
        }
        owner?.lifecycle?.addObserver(this)
        initData(frameLayout.context)
    }

    private fun initData(context: Context) {

        //初始化的时候先清空一下
        mViewModel?.mDialogBean?.value = null
        mViewModel?.mFloatDialogBean?.value = null
        owner?.let { o ->
            mViewModel?.mDialogBean?.observe(o, mDialogBeanObserver)

            mViewModel?.mFloatDialogBean?.observe(o, mFloatBeanObserver)

            mViewModel?.isShowFloatFloat?.observe(o, mIsShowFloatObserver)

            mTogglesTabNeedRequest = true
            updateFloatDialog(mTogglesTabNeedRequest)
        }
    }

    override fun destroy() {
        XEventBus.unregster(this)

        mController = null

        mViewModel = null

        mFloatViews.clear()
        mFloatViewParents.clear()

        mCountDownTimer?.cancel()
        mCountDownTimer = null
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        XEventBus.register(this)
    }

    override fun onResume(owner: LifecycleOwner) {
        startCountDown()
        setShowFloatDialog(showFloatDialog)
    }

    override fun onStop(owner: LifecycleOwner) {
        cancelCountDown()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        destroy()
    }

    /**
     * 监听点击事件
     */
    fun onEventMainThread(event: EventNavigationTabChanged) {
        if (NavigationTabs.TAB_TAG_MESSAGE == event.tabId ||
            NavigationTabs.TAB_TAB_MAKE_FRIEND == event.tabId ||
            NavigationTabs.TAB_TAG_BBS == event.tabId ||
            NavigationTabs.TAB_TAG_MINE == event.tabId
        ) {
            /**
             * 埋点内容，根据选中的不同tab，设置对应的埋点
             */
            positionName = when (event.tabId) {
                NavigationTabs.TAB_TAG_MESSAGE -> "消息中心"
                NavigationTabs.TAB_TAB_MAKE_FRIEND -> "交友列表"
                NavigationTabs.TAB_TAG_BBS -> "广场"
                else -> "我的tab"
            }

            mCurSelectTabNeedShow = true
            setShowFloatDialog(showFloatDialog)
            updateFloatDialog(mTogglesTabNeedRequest)
        } else {
            mCurSelectTabNeedShow = false
        }
    }

    /**
     * 帐号登出事件通知
     *
     * @param event
     */
    fun onEventMainThread(event: EventXjbUserLoginOut?) {
        mTogglesTabNeedRequest = false
        removeObserver()
    }


    /**
     * 请求悬浮窗数据之后，判断是否开启倒计时
     */
    private fun checkStartCountDown(bean: MessageFemaleTaskListBean) {
        //倒计时
        if (mEndTime != bean.remainPopSecond?.toLong() ?: 0L) {
            mEndTime = bean.remainPopSecond?.toLong() ?: 0L
            startCountDown(mEndTime,true)
        }
    }

    /**
     * 刷新悬浮窗，网络请求的入口
     *
     * @param isNeedRequest 是否需要请求
     */
    override fun updateFloatDialog(isNeedRequest: Boolean) {
        if (isNeedRequest) {
            mViewModel?.getFloatDialogInfo()
        }
    }

    /**
     * 展示任务列表
     */
    fun showTaskDialog() {
        mViewModel?.getDialogInfo()
    }

    /**
     * 计算倒计时
     * @param forceStart 强制开始
     */
    private fun startCountDown(endTime: Long,forceStart:Boolean = false) {
        synchronized(this) {
            if (mIsStartCountDown && !forceStart) {
                return
            }
            mIsStartCountDown = true

            //先把之前的置空掉
            mCountDownTimer?.cancel()
            mCountDownTimer = null

            val duration = endTime * 1000 - System.currentTimeMillis()
            if (duration <= 0) {
                mIsStartCountDown = false
                mCountDownTimer?.cancel()
                mCountDownTimer = null
                return
            }

            mCountDownTimer = object : CountDownTimer(duration, TIME_DOWN_DURATION) {
                override fun onTick(millisUntilFinished: Long) {
                    val timeStrFloatView =
                        MessageFemaleViewModel.getCountDownTime(
                            millisUntilFinished / TIME_DOWN_DURATION,
                            true
                        )

                    val timeStrDialog = MessageFemaleViewModel.getCountDownTime(
                        millisUntilFinished / TIME_DOWN_DURATION,
                        false
                    )

                    //更新弹窗时间
                    if (mDialog?.isShowing==true) {
                        mDialog?.updateTime(timeStrDialog)
                    }
                    //更新悬浮窗时间
                    mFloatViews.forEach {
                        it.updateTime(timeStrFloatView)
                    }
                }

                override fun onFinish() {
                    if (mDialog?.isShowing==true) {
                        mDialog?.updateTime(TIME_END_TASK_DIALOG)
                    }
                    mFloatViews.forEach {
                        it.updateTime(TIME_END_FLOAT)
                    }
                    showFloatDialog = false
                    setShowFloatDialog(showFloatDialog)
                    mIsStartCountDown = false
                }

            }
            mCountDownTimer?.start()
        }

    }


    override fun setShowFloatDialog(isShow: Boolean) {
        mFloatViewParents.forEach {
            it.visibility = if (isShow) View.VISIBLE else View.GONE
        }

        mFloatViews.forEach {
            it.visibility = if (isShow) View.VISIBLE else View.GONE
        }

        if (mLastShouldShow != isShow) {
            mLastShouldShow = isShow
            //缓存本地
            KVCacheUtils.putBoolean(getKvCacheKey(), isShow)
            //发送event事件
            EventBusUtils.getEventBus()
                .post(MessageBottomBannerEvent().apply {
                    beanType = MessageBannerTypeBean.TYPE_FEMALE
                    shouldShow = isShow
                })
        }

    }


    override fun startCountDown() {
        if (!mIsStartCountDown && mEndTime > 0L) {
            startCountDown(mEndTime)
        }
    }

    override fun cancelCountDown() {
        if (mIsStartCountDown) {
            mCountDownTimer?.cancel()
            mIsStartCountDown = false
        }
    }


}