package com.xmhaihao.message.activity

import android.content.DialogInterface
import cn.taqu.lib.base.bean.MessageAccountInfo
import cn.taqu.lib.base.event.ForumEventFocusBean
import cn.taqu.lib.base.helper.FollowHelper
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.xmhaibao.message.api.constants.MessageIntentConstants
import com.xmhaibao.message.api.router.MessageRouterPath
import com.xmhaihao.message.event.MessageEventChatMoreClick
import com.xmhaihao.message.viewModel.MessageChatViewModel
import com.xmhaihao.message.widget.MessageChatUserMoreView
import hb.common.data.AccountHelper
import hb.common.xstatic.activity.BaseMVVMActivity
import hb.skin.support.SkinCompatManager
import hb.utils.EventBusUtils
import hb.utils.StringUtils
import hb.xstyle.xdialog.XDialogUtils

@Route(path = MessageRouterPath.MESSAGE_CHAT_SETTING_ACTIVITY)
class MessageChatSettingActivity : BaseMVVMActivity<MessageChatViewModel>() {

    var mAccountInfo: MessageAccountInfo? = null

    @JvmField
    @Autowired(name = MessageIntentConstants.INTENT_EXTRA_CONVERSATION_ID)
    var mConversationId: String? = ""

    @JvmField
    @Autowired(name = MessageIntentConstants.INTENT_EXTRA_FOLLOW_STATUS)
    var mFollowStatus: Int = 0

    lateinit var mMessageChatUserMoreView: MessageChatUserMoreView

    override fun onCreateContentView(): Any {
        mMessageChatUserMoreView = MessageChatUserMoreView(this)
        return mMessageChatUserMoreView
    }

    override fun initViews() {
        super.initViews()
        mAccountInfo = intent.getParcelableExtra<MessageAccountInfo>(MessageIntentConstants.INTENT_MESSAGE_ACCOUNT_INFO)
        ARouter.getInstance().inject(this)
        setupToolbar("聊天设置")
        baseViewModel.mFollowStatusLiveData.observe(this, { followStatus -> //如果之前是好友，操作关注后变成非关注了。那么接触了好友关系
            mFollowStatus = followStatus
            mMessageChatUserMoreView.setFollowStatus(mFollowStatus)
            val event = MessageEventChatMoreClick()
            event.isOnFollow = true
            event.followStatus = followStatus
            EventBusUtils.post(event)
        })
        if (mAccountInfo != null && StringUtils.isNotEmpty(mConversationId)) {
            mMessageChatUserMoreView.setupView(mAccountInfo!!, mConversationId, mFollowStatus,
                    object : MessageChatUserMoreView.MoreCallback {
                        override fun clearHistory() {
                            val event = MessageEventChatMoreClick()
                            event.isClearHistory = true
                            EventBusUtils.post(event)
                        }

                        override fun onFollow() {
                            mAccountInfo?.run {
                                baseViewModel.requestOperateFollow(mFollowStatus, this.accountUuid)
                            }
                        }

                        override fun report() {
                            val event = MessageEventChatMoreClick()
                            event.isReport = true
                            EventBusUtils.post(event)
                        }

                        override fun onBlackListChange( isBlack: Boolean) {
                            val event = MessageEventChatMoreClick()
                            event.isOnBlackListChange = true
                            event.isBlack = isBlack
                            EventBusUtils.post(event)
                        }

                        override fun inviteToCert() {
                            val event = MessageEventChatMoreClick()
                            event.isInviteToCert = true
                            EventBusUtils.post(event)
                        }
                    })
        }
        mAccountInfo?.accountUuid?.let {
            baseViewModel.requestGetRelationLiveInfo(it, readCache = false)
        }
    }

    fun onEventMainThread(event: ForumEventFocusBean) {
        //更新关注状态
        baseViewModel.updateFollowStatus(event.focus)
    }

    override fun isApplyEventBus(): Boolean {
        return true
    }

    override fun applySkin() {
        if (!isApplySkin) {
            return
        }
        super.applySkin()
        if (this::mMessageChatUserMoreView.isInitialized) {
            mMessageChatUserMoreView.applySkin()
        }
    }

    override fun isApplySkin(): Boolean {
        return SkinCompatManager.getInstance().isEnableSkin
    }

}