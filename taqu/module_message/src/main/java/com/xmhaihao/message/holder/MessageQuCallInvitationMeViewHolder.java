package com.xmhaihao.message.holder;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.View;
import android.view.ViewGroup;

import com.xmhaihao.message.R;
import com.xmhaihao.message.bean.MessageChatQuCallInviteBean;

import hb.common.data.AccountHelper;
import hb.message.db.entity.HBMessageContentDO;


/**
 * @author：杨浩 项目：taqu
 * 创建日期：2019/4/19
 * 功能简介：
 */
public class MessageQuCallInvitationMeViewHolder extends MessageQuCallInvitationBaseViewHolder {

    public MessageQuCallInvitationMeViewHolder(@NonNull ViewGroup parent) {
        super(parent, R.layout.message_qu_call_video_invitation_only_item);
    }

    @Override
    public void onBindViewHolder(@Nullable HBMessageContentDO messageContent, @Nullable MessageChatQuCallInviteBean callInvitationInfo) {
        super.onBindViewHolder(messageContent, callInvitationInfo);
        mAvatar1.setImageURI(AccountHelper.getAccountAvatar());
        mAvatar2.setImageURI(AccountHelper.getAccountAvatar());
    }
}
