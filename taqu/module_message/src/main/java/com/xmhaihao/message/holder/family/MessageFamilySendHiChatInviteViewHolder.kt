package com.xmhaihao.message.holder.family

import android.view.ViewGroup
import androidx.core.view.isVisible
import cn.taqu.lib.base.router.ARouterManager
import com.xmhaihao.message.R
import com.xmhaihao.message.bean.MessageFamilyHiChatInviteBean
import com.xmhaihao.message.databinding.MessageFamilyHichatInviteSendItemBinding
import com.xmhaihao.message.holder.MessageIMBaseViewHolder
import hb.message.db.entity.HBMessageContentDO

/**
 * 畅聊圈分享家族消息- 发送方
 *
 * <AUTHOR>
 * @date 2025/02/12
 */
class MessageFamilySendHiChatInviteViewHolder(parent: ViewGroup) :
    MessageIMBaseViewHolder<MessageFamilyHiChatInviteBean>(
        parent,
        R.layout.message_family_hichat_invite_send_item
    ) {

    private val mBinding by lazy {
        MessageFamilyHichatInviteSendItemBinding.bind(itemView)
    }

    var messageContentBean: MessageFamilyHiChatInviteBean? = null

    init {
        mBinding.tvGuideBtn.setOnClickListener {
            messageContentBean?.let {
                ARouterManager.routerStart().dealJumpData_(itemView.context, it.relation)
            }
        }
        mBinding.ivCardBg.setImageFromResource(cn.taqu.lib.base.R.drawable.common_card_background)
    }

    override fun onBindViewHolder(
        messageContent: HBMessageContentDO?,
        messageContentBean: MessageFamilyHiChatInviteBean?
    ) {
        super.onBindViewHolder(messageContent, messageContentBean)
        messageContentBean?.let {
            this.messageContentBean = it
            mBinding.ivCardPic.setImageFromUrl(it.cardPic)
            mBinding.tvCardTitle.text = it.cardTitle
            mBinding.tvCardContent.text = it.cardSubTitle
            mBinding.tvGuideTitle.text = it.cardContent
            mBinding.tvGuideBtn.isVisible = !it.cardBtn.isNullOrEmpty()
            mBinding.tvGuideBtn.text = it.cardBtn


        }
    }

}