package com.xmhaihao.message.holder

import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import cn.taqu.lib.base.utils.SexTypeUtils
import com.airbnb.lottie.LottieAnimationView
import cn.taqu.lib.base.utils.eventlog.AppTrackEventUtils
import com.xmhaibao.family.api.router.FamilyPinsRouter
import com.xmhaihao.message.bean.MessageAuctionRelationBean
import com.xmhaihao.message.helper.MessageLottieAvatarHelper
import com.xmhaihao.message.helper.MessageMyRelationHelper
import hb.common.data.AccountHelper
import hb.drawable.shape.view.HbTextView
import hb.message.db.entity.HBMessageContentDO
import hb.ximage.fresco.BaseDraweeView

/**
 * 家族拍拍关系新结成拍拍关系viewHolder基类
 *
 * <AUTHOR>
 * @date 2022-10-10
 */
abstract class MessageFamilyRelationBaseViewHolder(parent: ViewGroup, resId: Int) :
    MessageIMBaseViewHolder<MessageAuctionRelationBean?>(parent, resId) {


    var mCallBack: OnShowNewPaiRelationCallBack? = null


    override fun onBindViewHolder(
        messageContent: HBMessageContentDO?,
        messageContentBean: MessageAuctionRelationBean?
    ) {
        super.onBindViewHolder(messageContent, messageContentBean)
        messageContentBean?.run {
            getImgLefHead().setImageFromUrl(leftHeadUrl)
            getImgRightHead().setImageFromUrl(rightHeadUrl)

            //标题
            val title = if (TextUtils.isEmpty(content)) "我在拍拍关系中获得了新关系~" else content
            getTvTitle().text = "$title"
            //关系
            getIvRelation().setImageFromUrl(auctionContentUrl)

            getTvLeftName().text = "$leftName"
            getTvRightName().text = "$rightName"

            getImgLeftSex().setImageResource(if (SexTypeUtils.isSexTypeBoy(leftSex)) cn.taqu.lib.base.R.drawable.ic_sex_male else cn.taqu.lib.base.R.drawable.ic_sex_female)
            getImgRightSex().setImageResource(if (SexTypeUtils.isSexTypeBoy(rightSex)) cn.taqu.lib.base.R.drawable.ic_sex_male else cn.taqu.lib.base.R.drawable.ic_sex_female)

            if (TextUtils.equals(messageContentBean.leftUserUuid, AccountHelper.getAccountUuid()) ||
                TextUtils.equals(messageContentBean.rightUserUuid, AccountHelper.getAccountUuid())
            ) {
                getTvPaiPai().visibility = View.GONE
            } else {
                getTvPaiPai().visibility = View.VISIBLE
            }
            getTvPaiPai().setOnClickListener {

                //上报埋点
                val uuids ="${leftUserUuid},${rightUserUuid}"
                AppTrackEventUtils.trackFamilyPaiPaiSucceedClick(uuids,auctionContent)

                //邀请好友拍拍
                val familyUuid = FamilyPinsRouter.messageFamilyService().getFamilyChatActivityFamilyUuid(null, true)
                familyUuid?.apply {
                    MessageMyRelationHelper.selectFamilyMemberInvite(this, "拍拍关系家族消息卡")
                }
            }
            //显示lottie动效
            MessageLottieAvatarHelper.loadLottieAvatar(getLottieLeftBg(), avatarFrameId)
            MessageLottieAvatarHelper.loadLottieAvatar(getLottieRightBg(), avatarFrameId)

            mCallBack?.showNewRelation(
                messageContent?.msgId?:"",
                auctionContent,
                leftUserUuid,
                rightUserUuid
            )
        }
    }


    /**
     * 绑定数据的时候，上报埋点用
     */
    interface OnShowNewPaiRelationCallBack {
        fun showNewRelation(
            msgId: String?,
            relationName: String?,
            uuid: String?,
            uuidOther: String?
        )
    }

    abstract fun getImgLefHead():BaseDraweeView
    abstract fun getImgRightHead():BaseDraweeView
    abstract fun getTvTitle():TextView
    abstract fun getIvRelation():BaseDraweeView
    abstract fun getTvLeftName():TextView
    abstract fun getTvRightName():TextView
    abstract fun getImgLeftSex():ImageView
    abstract fun getImgRightSex():ImageView
    abstract fun getTvPaiPai():HbTextView
    abstract fun getLottieLeftBg(): LottieAnimationView
    abstract fun getLottieRightBg():LottieAnimationView

}