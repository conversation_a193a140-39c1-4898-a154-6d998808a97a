package com.xmhaihao.message.gift.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import hb.common.helper.HostHelper
import hb.utils.StringUtils
import java.io.Serializable

/**
 * 家族套装礼物Banner Bean
 * https://o15vj1m4ie.feishu.cn/wiki/Vblfwb7GdiaPpCkPisvc4hgpnnh
 *
 * <AUTHOR>
 * @since 2023-5-17
 * */
class MessageSuitGiftBannerBean: Serializable, IDoExtra {

    companion object {
        const val BG_TYPE_A = "1"
        const val BG_TYPE_B = "2"
    }

    @SerializedName("icon")
    var giftIcon: String? = ""  // 礼物图标
    @SerializedName("title")
    var title: String? = ""     // 礼物标题
    @SerializedName("content")
    var describe: String? = ""  // 套装礼物概述
    @SerializedName("time")
    var expireTime: String? = "" //结束时间戳

    @SerializedName("bg_color_type")
    var backgroundType: String? = ""  // 背景颜色
    @SerializedName("relation")
    var relation: String? = ""  // 点击跳转

    var expireTimeLong: Long = 0

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        giftIcon = HostHelper.getImageDefaultHost().getWebpUrl_4_1(giftIcon)
        expireTimeLong = StringUtils.stringToLong(expireTime) * 1000
    }
}