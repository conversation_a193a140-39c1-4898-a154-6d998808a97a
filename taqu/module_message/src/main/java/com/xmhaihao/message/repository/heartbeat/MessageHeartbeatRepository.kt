package com.xmhaihao.message.repository.heartbeat

import cn.taqu.lib.base.api.UrlBase
import com.google.gson.JsonObject
import com.xmhaibao.message.api.bean.MessageEventSameSexHeartbeatBean
import com.xmhaihao.message.bean.*
import hb.common.xstatic.HttpParams
import hb.xrequest.XRequest
import hb.xrequest.await

/**
 * <AUTHOR>
 * @desc 心动值相关仓库
 * @date 2023-11-08 10:15
 */
class MessageHeartbeatRepository : UrlBase() {

    companion object {

        @JvmStatic
        fun getIntimateFriends(): XRequest<List<MessageHeartFriendBean>> {
            val httpParams = HttpParams.newBuilder()
                .get(API_HEARTBEAT_V1.plus("/IntimateFriend/getIntimateFriends"))
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 私信升温——第一印象问卷回答
         * */
        suspend fun postImpressionAnswer(
            mineUuid: String,
            userUuid: String,
            answer: String
        ): Any {
            return XRequest.post(API_GW_MESSAGE_V2.plus("/HeartbeatPool/answer"))
                .needTicketId()
                .params("ticket_id", mineUuid)
                .params("other_account_uuid", userUuid)
                .params("answers", answer)
                .asRequest<Any>()
                .await()
        }
    }

    /**
     * 客户端点击心动值桃心弹窗信息
     */
    suspend fun iPopReachFeature(accountUuid: String): MessageChatGetHeartbeatBean {
        return XRequest.get(API_GW_MESSAGE_V2.plus("/HeartbeatScore/iPopReachFeature"))
            .needTicketId(true)
            .params("to_account_uuid", accountUuid)
            .asRequest<MessageChatGetHeartbeatBean>()
            .await()
    }

    /**
     * 客户端上报修改魅力值
     */
    fun updateCharmScoreByUser(ticketId: String?, receiverUUID: String?, sameType: String): XRequest<Any> {
        //extra中存放json实体{"accountUuid":"xxxx"}
        val obj = JsonObject()
        obj.addProperty("receiver_uuid", receiverUUID)
        val httpParams = HttpParams.newBuilder().post(API_PRIVILEGE_V1.plus("/CharmScore/changeCharmScoreByUser"))
            .params("ticket_id", ticketId)
            .params("type", sameType)
            .params("extra", obj.toString())
            .build()
        return XRequest.newRequest(httpParams)
    }

    /**
     * 获取同性心动值
     */
    suspend fun getSameHeartbeatInfo(otherUuid: String): MessageEventSameSexHeartbeatBean? {
        return XRequest.get(API_HEARTBEAT_V1.plus("/Same/getSameInfo"))
            .params("other_account_uuid", otherUuid)
            .needTicketId(true)
            .asRequest<MessageEventSameSexHeartbeatBean>()
            .await()
    }

}