package com.xmhaihao.message.holder.send;

import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.xmhaibao.hbchat.bean.bean.MessageExtraEmojiInfoBean;
import com.xmhaihao.message.R;
import com.xmhaibao.hbchat.bean.bean.MessageChatImageBean;
import com.xmhaihao.message.databinding.MessageImageFromMeItemBinding;
import com.xmhaihao.message.holder.MessageImageBaseViewHolder;
import com.xmhaihao.message.intf.SimpleMessageOperateClickListener;
import com.xmhaihao.message.utils.MessagePriceUtils;
import com.xmhaihao.message.viewModel.MessageChatOperateViewModel;
import com.xmhaihao.message.widget.operate.MessageOperateBasePopWindow;
import com.xmhaihao.message.widget.operate.MessageOperateLauncher;
import com.xmhaihao.message.widget.operate.MessageOperatePopWindow;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import cn.taqu.lib.base.bean.FamilyMessageAccountInfo;
import cn.taqu.lib.base.bean.MessageAccountInfo;
import cn.taqu.lib.base.utils.SexTypeUtils;
import hb.common.helper.HostHelper;
import hb.message.constants.MessageSendStatus;
import hb.message.db.entity.HBMessageContentDO;
import hb.message.intf.HBMessageChatType;
import hb.utils.ScreenUtils;
import hb.utils.SizeUtils;
import hb.utils.StringUtils;
import hb.utils.WebpUtils;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * 私信 自己发的图片   [private_msg / image]
 *
 * <AUTHOR>
 */
public class MessageImageFromMeViewHolder extends MessageImageBaseViewHolder<MessageChatImageBean> {

    MessageImageFromMeItemBinding mBinding;

    /**
     * 该消息的用户昵称
     */
    private String mNickName;


    public MessageImageFromMeViewHolder(@NonNull ViewGroup parent) {
        super(parent, R.layout.message_image_from_me_item);
        mBinding = MessageImageFromMeItemBinding.bind(itemView);
        mBinding.imgRemotePic.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                showMessageOperatePopWindow(v);
                return false;
            }
        });
    }

    private void showMessageOperatePopWindow(View v) {
        if (mMessageChatType == HBMessageChatType.TYPE_PRIVATE) {
            List<String> operateList = new ArrayList<>();
            operateList.add(MessageOperateBasePopWindow.TEXT_REPLY);
            if (MessageChatOperateViewModel.Companion.isCanTaskBack(mMessageContent)) {
                operateList.add(MessageOperateBasePopWindow.TEXT_WITHDRAW);
            }
            operateList.add(MessageOperateBasePopWindow.TEXT_DELETE);
            MessageOperateLauncher.INSTANCE.showPop(v, true, new SimpleMessageOperateClickListener() {
            }, operateList.toArray(new String[0]));
        }else if(mMessageChatType == HBMessageChatType.TYPE_GROUP) {
            List<String> operateList = new ArrayList<>();
            operateList.add(MessageOperateBasePopWindow.TEXT_REPLY);
            if (MessageChatOperateViewModel.Companion.isCanTaskBack(mMessageContent)) {
                operateList.add(MessageOperateBasePopWindow.TEXT_WITHDRAW);
            }
            MessageOperateLauncher.INSTANCE.showPop(v, true,mNickName,HBMessageChatType.TYPE_GROUP, new SimpleMessageOperateClickListener() {
            }, operateList.toArray(new String[0]));
        }
    }

    @Override
    protected void setFamilyUserInfo(HBMessageContentDO item, FamilyMessageAccountInfo accountDetailed) {
        super.setFamilyUserInfo(item, accountDetailed);
        mNickName = accountDetailed.getNickName();
    }

    @Override
    public void onBindViewHolder(@Nullable HBMessageContentDO messageContent, @Nullable MessageChatImageBean messageContentBean) {
        super.onBindViewHolder(messageContent, messageContentBean);
        if (messageContentBean == null || messageContent == null) {
            return;
        }
        mBinding.imgRemotePic.setTag(messageContent);
        chargeImageUrl(messageContentBean);
        if (mAccountInfo != null && !SexTypeUtils.isSameSexType(mAccountInfo.getSexType()) &&mGetChatInfoCallBack != null) {
            MessagePriceUtils.setSendMessagePayStatus(null, mTvMessagePrice, null, messageContentBean
                    , mGetChatInfoCallBack.getMessageChatForumBean());
        }
        if (messageContent.getSendStatus() == MessageSendStatus.SEND_LOADING) {
            mProgressSending.setVisibility(View.VISIBLE);
        } else {
            mProgressSending.setVisibility(View.GONE);
            mTvSendPercent.setVisibility(View.GONE);
        }
        final boolean notHasPriceContent = mTvMessagePrice.getVisibility() == View.GONE;
        if (notHasPriceContent) {
            if (StringUtils.isNotEmpty(messageContentBean.getSubContent())) {
                mTvMessagePrice.setVisibility(View.VISIBLE);
                mTvMessagePrice.setText(messageContentBean.getSubContent());
            } else {
                mTvMessagePrice.setVisibility(View.GONE);
            }
        }

        // 处理表情回复显示
        if (mFamilyReplyEmojiHelper != null) {
            MessageExtraEmojiInfoBean messageExtraEmojiInfoBean = messageContentBean.getEmojiInfo();
            mFamilyReplyEmojiHelper.fillFlexbox(messageContent,
                    ScreenUtils.getScreenWidth() - SizeUtils.dp2px(
                            134f),
                    mBinding.flexboxEmojiReply,
                    messageExtraEmojiInfoBean);
        }
    }

    /**
     * 根据URL类型设置图片
     */
    protected void chargeImageUrl(@Nullable MessageChatImageBean contentBean) {
        if (contentBean == null) {
            return;
        }
        String remoteUrl;
        if (contentBean.getPicUrl() != null && contentBean.getPicUrl().contains("token=")) {
            remoteUrl = WebpUtils.getWebpUrl(contentBean.getPicUrl(), mIsGroup ? HostHelper.getInstance().getHostGroupChat() :
                    HostHelper.getInstance().getHostChat());
        } else {
            remoteUrl = WebpUtils.getWebpUrl_2_1(contentBean.getPicUrl(), mIsGroup ? HostHelper.getInstance().getHostGroupChat() :
                    HostHelper.getInstance().getHostChat());
        }
        String localUrl = contentBean.getLocalImageUrl();
        int width = contentBean.getWidth();
        int height = contentBean.getHeight();
        //没有下载完成，又是发送方，就显示本地。localUrl是发送方才有的
        if (StringUtils.isNotEmpty(localUrl) && new File(localUrl).exists()) {
            setImageLayoutParamsUrl(mIvRemotePic, width, height);
            mIvRemotePic.setImageFromFile(localUrl);
        } else {
            setImage(mIvRemotePic, remoteUrl, width, height);
        }
    }

    /**
     * 覆盖viewHolder 的所有点击事件
     * */
    public void enableSelect(Function0<Unit> l) {
        mBinding.cbReportEvidence.setVisibility(View.VISIBLE);
        mBinding.msgContainer.setOnClickListener((View v) -> l.invoke());
        mBinding.msgContainer.setOnLongClickListener((View v) -> {
            l.invoke();
            return true;
        });
        for(int i = 0; i < mBinding.msgContainer.getChildCount(); i++) {
            mBinding.msgContainer.getChildAt(i).setOnClickListener((View v) -> l.invoke());
            mBinding.msgContainer.getChildAt(i).setOnLongClickListener((View v) -> {
                l.invoke();
                return true;
            });
        }
    }

    /**
     * 设置checkBox的点击状态
     * */
    public void setSelect(boolean select) {
        mBinding.cbReportEvidence.setChecked(select);
    }

}
