package com.xmhaihao.message.page.qubenefit.widget

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.addListener
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import cn.taqu.lib.base.router.ARouterManager
import cn.taqu.lib.base.router.RouterLaunch
import com.xmhaibao.imganim.lottie.XLottiePlayer
import com.xmhaihao.message.databinding.MessageQuBenefitEnterLayoutBinding
import com.xmhaihao.message.page.qubenefit.bean.MessageQuBenefitBean
import com.xmhaihao.message.page.qubenefit.dialog.MessageQuBenefitBubblePopWindow
import com.xmhaihao.message.page.qubenefit.dialog.MessageQuBenefitGuideDialog
import com.xmhaihao.message.page.qubenefit.tracker.MessageQuBenefitTracker
import com.xmhaihao.message.page.qubenefit.viewmodel.MessageQuBenefitViewModel
import dp
import hb.kotlin_extension.gone
import hb.kotlin_extension.visible
import hb.skin.support.SkinCompatManager
import hb.skin.support.widget.SkinCompatSupportable
import hb.utils.StringUtils
import kotlin.math.min

/**
 * 趣福利入口view
 *
 * [飞书需求](https://project.feishu.cn/haibao/story/detail/5673513999)
 * <AUTHOR>
 * @date 2025-04-10
 */
class MessageQuBenefitEnterView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    ConstraintLayout(context, attrs, defStyleAttr), SkinCompatSupportable {

    private val binding by lazy {
        MessageQuBenefitEnterLayoutBinding.inflate(LayoutInflater.from(context), this)
    }

    /**
     * 气泡引导
     */
    private var bubblePop: MessageQuBenefitBubblePopWindow? = null

    /**
     * 入口信息
     */
    private var data: MessageQuBenefitBean? = null
    private var showTagAnim: ValueAnimator? = null
    private var hideTagAnim: ValueAnimator? = null

    init {
        setOnClickListener {
            handlerClick(false)
        }
    }

    private var viewModel: MessageQuBenefitViewModel? = null

    /**
     * 是否首次加载
     */
    private var isFirstLoad = true

    /**
     * 引导弹框
     */
    private var guideDialog: MessageQuBenefitGuideDialog? = null

    fun handlerClick(fromGuide: Boolean){
        RouterLaunch.dealJumpData(context, data?.relation)
        viewModel?.clearRedNum(fromGuide)
        bubblePop?.dismiss()
        MessageQuBenefitTracker.trackEnterClick()
    }

    fun setViewModel(viewModel: MessageQuBenefitViewModel?) {
        this.viewModel = viewModel
    }

    fun showEnter(enterBean: MessageQuBenefitBean?, isGuide: Boolean = false) {
        enterBean?:return
        if (isGuide.not() && isFirstLoad) {
            MessageQuBenefitTracker.trackEnterShow()
            isFirstLoad = false
        }
        data = enterBean
        visible()
        if (binding.lottieBenefitEnter.isAnimating.not()) {
            XLottiePlayer.playById(binding.lottieBenefitEnter, enterBean.lottieId, com.xmhaihao.message.R.drawable.message_qu_benefit_enter_ic)
        }
        val redNum = min(enterBean.redNum, 99)
        binding.tvBenefitRedPointNum.isVisible = redNum > 0
        binding.tvBenefitRedPointNum.updateLayoutParams<ViewGroup.LayoutParams> {
            if (redNum > 10) {
                width = 24.dp
                binding.tvBenefitRedPointNum.shaper().shape(GradientDrawable.RECTANGLE).corner(45f.dp)
            } else {
                width = 16.dp
                binding.tvBenefitRedPointNum.shaper().shape(GradientDrawable.OVAL).corner(0f)
            }
        }
        binding.tvBenefitRedPointNum.text = redNum.toString()

        //正在显示引导，
        if (MessageQuBenefitGuideDialog.isShowingGuide) {
            return
        }

        binding.lottieBenefitEnter.post {
            //找到当前view的位置
            val localWindow = intArrayOf(0, 0)
            binding.lottieBenefitEnter.getLocationOnScreen(localWindow)
            val targetX = localWindow[0].toFloat()
            val targetY = localWindow[1].toFloat()

            //是否已经显示过引导。已经显示过引导，直接显示气泡
            if (MessageQuBenefitViewModel.checkShowGuide().not() && StringUtils.isNotEmpty(enterBean.bubbleText)) {
                showBubblePop(enterBean, targetX)
                showTagImage(enterBean)
                return@post
            }
            //显示引导
            if (StringUtils.isNotEmpty(enterBean.guideText) && MessageQuBenefitViewModel.checkShowGuide()) {
                viewModel?.onResumeShowBubble = false
                if (!ARouterManager.appService().isMessageTabSelected) {
                    viewModel?.onResumeShowGuide = true
                    return@post
                }
                showGuideDialog(enterBean, targetX)
            } else {
                showBubblePop(enterBean, targetX)
                showTagImage(enterBean)
            }
        }
    }

    /**
     * 显示标签图片
     */
    private fun showTagImage(enterBean: MessageQuBenefitBean){
        val imgUrl = if (SkinCompatManager.getInstance().isSkinDark){
            enterBean.tagImgUrlDark
        } else {
            enterBean.tagImgUrl
        }
        if (enterBean.isShowTagImg && imgUrl.isNullOrEmpty().not()) {
            binding.ivBenefitTag.setImageFromUrl(imgUrl)
            enterBean.isShowTagImg = false
            startShowTagAnim()
        } else {
            binding.flBenefitTagRoot.gone()
        }
    }

    fun onResumeShow() {
        viewModel?.let {
            if (isVisible) {
                MessageQuBenefitTracker.trackEnterShow()
            }
            if (!it.onResumeShowGuide && !it.onResumeShowBubble) {
                return
            }
            data?.run {
                binding.lottieBenefitEnter.postDelayed( {
                    val localWindow = intArrayOf(0, 0)
                    binding.lottieBenefitEnter.getLocationOnScreen(localWindow)
                    val targetX = localWindow[0].toFloat()
                    if (it.onResumeShowGuide) {
                        showGuideDialog(this, targetX)
                        it.onResumeShowGuide = false
                        return@postDelayed
                    }

                    if (it.onResumeShowBubble) {
                        showBubblePop(this, targetX)
                    }
                }, 20)
            }
        }
    }


    fun onBubbleShowByTabChanged(showBubble:Boolean){
        bubblePop?.setHidden(!showBubble)
    }

    /**
     * 显示气泡
     */
    private fun showBubblePop(enterBean: MessageQuBenefitBean, targetX: Float) {
        if (!ARouterManager.appService().isMessageTabSelected) {
            viewModel?.onResumeShowBubble = true
            return
        }
        viewModel?.onResumeShowBubble = false
        if (StringUtils.isEmpty(enterBean.bubbleText)) {
            return
        }
        if (bubblePop == null) {
            bubblePop = MessageQuBenefitBubblePopWindow(context, targetX)
        }
        bubblePop?.showPop(enterBean, binding.lottieBenefitEnter)
    }

    /**
     * 页面关闭
     */
    fun onDestroy() {
        bubblePop?.dismiss()
        data = null
        bubblePop = null
        gone()
    }

    private fun startShowTagAnim() {
        if (showTagAnim?.isRunning == true) {
            return
        }
        hideTagAnim?.cancel()

        if (showTagAnim == null) {
            showTagAnim = ValueAnimator.ofInt(0, 74.dp).apply {
                duration = 320
                addUpdateListener {
                    (it.animatedValue as? Int)?.let { value ->
                        binding.flBenefitTagRoot.updateLayoutParams<ViewGroup.LayoutParams> {
                            width = value
                            if (value > 0 && binding.flBenefitTagRoot.isVisible.not()) {
                                binding.flBenefitTagRoot.visible()
                            }
                        }
                    }
                }
            }
        }
        showTagAnim?.start()
        startHideTagAnim()
    }

    private fun startHideTagAnim() {
        if (hideTagAnim?.isRunning == true) {
            return
        }
        if (hideTagAnim == null) {
            hideTagAnim = ValueAnimator.ofInt(74.dp, 0).apply {
                startDelay = 2360
                duration = 320
                addUpdateListener {
                    (it.animatedValue as? Int)?.let { value ->
                        binding.flBenefitTagRoot.updateLayoutParams<ViewGroup.LayoutParams> {
                            width = value
                        }
                    }
                }
                addListener(onEnd = {
                    binding.flBenefitTagRoot.gone()
                })
            }
        }
        hideTagAnim?.start()
    }

    override fun applySkin() {
        if (binding.flBenefitTagRoot.isVisible) {
            val imgUrl = if (SkinCompatManager.getInstance().isSkinDark) {
                data?.tagImgUrlDark
            } else {
                data?.tagImgUrl
            }
            if (imgUrl.isNullOrEmpty().not()) {
                binding.ivBenefitTag.setImageFromUrl(imgUrl)
            }
        }

    }

    override fun isApplySkin(): Boolean {
        return SkinCompatManager.getInstance().isEnableSkin
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        showTagAnim?.cancel()
        showTagAnim?.removeAllListeners()
        showTagAnim?.removeAllUpdateListeners()
        showTagAnim = null

        hideTagAnim?.cancel()
        hideTagAnim?.removeAllListeners()
        hideTagAnim?.removeAllUpdateListeners()
        hideTagAnim = null

        bubblePop?.dismiss()
        bubblePop = null

        hideGuideDialog()
    }

    /**
     * 显示引导弹框
     */
    private fun showGuideDialog(enterBean: MessageQuBenefitBean,
                                targetX: Float){
        guideDialog = MessageQuBenefitGuideDialog(context, enterBean, targetX = targetX) {
            showBubblePop(enterBean, targetX)
            showTagImage(enterBean)
        }
        guideDialog?.setOnDismissListener {
            guideDialog = null
        }
        guideDialog?.show()
    }

    /**
     * 隐藏引导弹框
     */
    fun hideGuideDialog() {
        if (MessageQuBenefitGuideDialog.isShowingGuide) {
            guideDialog?.dismiss()
            guideDialog = null
        }
    }
}