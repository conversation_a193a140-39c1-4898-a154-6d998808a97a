package com.xmhaihao.message.chatplay.chatbenefits.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import com.xmhaihao.message.chatplay.chatbenefits.bean.MessageChatBenefitsBean
import com.xmhaihao.message.databinding.MessageChatBenefitsDetailDialogBinding
import com.xmhaihao.message.imdetail.intf.MessageChatProvider
import com.xmhaihao.message.utils.MessageTimeUtils
import hb.utils.StringUtils
import hb.xstyle.xdialogfragment.XDialogFragment
import kotlin.math.min

/**
 * 【IM边聊边送红包版】私信详情聊天红包任务进度详情弹窗
 * [飞书需求](https://o15vj1m4ie.feishu.cn/wiki/ORq8wRRL5iMBrskijtpc0wJinIb)
 * <AUTHOR>
 * @date 2024/5/24
 */
class MessageChatBenefitsDetailDialogFragment : XDialogFragment() {

    companion object {
        fun newInstance(): MessageChatBenefitsDetailDialogFragment {
            return MessageChatBenefitsDetailDialogFragment().apply {
                setConfig(
                    ConfigStyle.CENTER.config()
                        .setLeftAndRightMargin(0)
                        .setCanceledOnTouchOutside(false)
                        .setCancelable(false)
                )
            }
        }
    }

    private val binding by lazy {
        MessageChatBenefitsDetailDialogBinding.inflate(LayoutInflater.from(context))
    }

    override fun onCreateContentView(): Any {
        return binding.root
    }

    override fun initConfig(argumentBundle: Bundle) {

    }

    override fun initViews(rootView: View?) {
        binding.ivClose.setOnClickListener {
            dismiss()
        }

        binding.ivDialogBg.setImageFromResource(cn.taqu.lib.base.R.drawable.base_brand_red_packet_dialog_bg)
    }

    override fun initData(savedInstanceState: Bundle?) {
        MessageChatProvider.get().chatBenefitsViewModel?.benefitsLiveData?.observe(this) {
            bindData(it)
        }
        MessageChatProvider.get().chatBenefitsViewModel?.finishCountDownLiveData?.observe(this) {
            if (it > 0) {
                binding.tvFinishCountDown.text = "倒计时：${MessageTimeUtils.parseHHMMSSStringToHourEmpty(it)}后结束"
            } else {
                dismiss()
            }
        }
    }

    private fun bindData(bean: MessageChatBenefitsBean?) {
        if (bean == null) {
            return
        }
        binding.tvTitle.text = bean.dialogTitle ?: "聊越多 送越多"

        val curRound = StringUtils.stringToFloat(bean.currentRound)
        val totalRound = StringUtils.stringToFloat(bean.totalRound)

        val progress = if (totalRound > 0) {
            (curRound / totalRound * 100).toInt()
        } else {
            100
        }
        binding.pbTaskProgress.progress = min(progress, 100)

        binding.tvCurrentRound.text = "已发${bean.currentRound}条"
        binding.tvTotalRound.text = "总需${bean.totalRound}条"

        binding.tvRewardTips.text = bean.popUpSubtitle
    }
}