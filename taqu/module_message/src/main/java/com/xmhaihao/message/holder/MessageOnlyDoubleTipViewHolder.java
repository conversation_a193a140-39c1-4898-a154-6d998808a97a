package com.xmhaihao.message.holder;

import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.xmhaihao.message.R;
import com.xmhaihao.message.bean.MessageChatOnlyDoubleTipBean;
import com.xmhaihao.message.databinding.MessageOnlyDoubleTipsItemBinding;

import hb.message.db.entity.HBMessageContentDO;
import hb.message.intf.HBMessageChatType;
import hb.utils.StringUtils;

/**
 * Date: 20200731
 * description:  为解决两条tip固定顺序的问题（原因：服务端时间只存到秒级别不足以区分顺序，在客户端排序时可能顺序会乱）
 *
 * <AUTHOR>
 */
public class MessageOnlyDoubleTipViewHolder extends MessageIMBaseViewHolder<MessageChatOnlyDoubleTipBean> {
    private final MessageOnlyDoubleTipsItemBinding mViewBinding;

    public MessageOnlyDoubleTipViewHolder(@NonNull ViewGroup parent) {
        super(parent, R.layout.message_only_double_tips_item);
        mViewBinding = MessageOnlyDoubleTipsItemBinding.bind(itemView);
        mViewBinding.tvMessageFirstTips.setOnClickListener(this);
    }

    @Override
    public void setMessageChatType(HBMessageChatType mMessageChatType) {
        super.setMessageChatType(mMessageChatType);
    }

    @Override
    public void onBindViewHolder(@Nullable HBMessageContentDO messageContent, @Nullable MessageChatOnlyDoubleTipBean messageContentBean) {
        super.onBindViewHolder(messageContent, messageContentBean);
        if (messageContentBean == null) {
            return;
        }
        if (StringUtils.isNotEmpty(messageContentBean.getContent())) {
            mViewBinding.tvMessageFirstTips.setVisibility(View.VISIBLE);
            mViewBinding.tvMessageFirstTips.setText(messageContentBean.getContent());
        } else {
            mViewBinding.tvMessageFirstTips.setVisibility(View.GONE);
        }
        if (StringUtils.isNotEmpty(messageContentBean.getSecondContent())) {
            mViewBinding.tvMessageSecondTips.setVisibility(View.VISIBLE);
            mViewBinding.tvMessageSecondTips.setText(messageContentBean.getSecondContent());
        } else {
            mViewBinding.tvMessageSecondTips.setVisibility(View.GONE);
        }

    }

}
