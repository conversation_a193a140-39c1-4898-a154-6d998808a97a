package com.xmhaihao.message.valentineDay.holder

import android.view.ViewGroup
import androidx.annotation.NonNull
import com.xmhaihao.message.R
import com.xmhaihao.message.databinding.MessageValentineDayMyCpListLayoutBinding
import com.xmhaihao.message.valentineDay.bean.ValentineDayMyCpBean
import hb.utils.StringUtils
import hb.xadapter.XBaseViewHolder

/**
 * 我的搭档列表
 *
 * <AUTHOR>
 * @date 2022-12-29
 */
class MessageValentineDayMyCpViewHolder(@NonNull parent: ViewGroup) :
    XBaseViewHolder<ValentineDayMyCpBean>(
        parent,
        R.layout.message_valentine_day_my_cp_list_layout
    ) {
    private var mBinding = MessageValentineDayMyCpListLayoutBinding.bind(itemView)
    override fun onBindView(item: ValentineDayMyCpBean?) {
        item?.let {

            mBinding.ivAvatar.setImageFromUrl(it.avatar?:"")
            mBinding.tvNickName.text = it.nickname?:""
            mBinding.forumGenderHeightView.setGenderData(it.sex, StringUtils.stringToInt(it.age?:"0") , "")
        }
        itemView.tag = item


    }

}