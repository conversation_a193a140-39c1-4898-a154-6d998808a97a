package com.xmhaihao.message.deepChat.view

import android.animation.Animator
import android.animation.Animator.AnimatorListener
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.xmhaihao.message.R
import com.xmhaihao.message.databinding.MessageDeepChatCircleViewTipBBinding
import com.xmhaihao.message.deepChat.bean.MessageDeepChatTipBean
import hb.kotlin_extension.gone
import hb.kotlin_extension.visible
import hb.utils.AnimatorUtils
import hb.utils.ColorUtils
import hb.utils.SizeUtils
import hb.utils.SpanUtils
import hb.utils.StringUtils

/**
 * 深度会话私信页tip相关view
 *
 * <AUTHOR>
 * @date 2023-08-01
 */
class MessageDeepChatCircleViewTip(context: Context, attrs: AttributeSet?) : ConstraintLayout(context, attrs) {

    private val mBinding: MessageDeepChatCircleViewTipBBinding by lazy {
        MessageDeepChatCircleViewTipBBinding.inflate(LayoutInflater.from(context), this, true)
    }

    /**
     * tip是否为空
     */
    private var isShowTipEmpty = false

    /**
     * tip显示动画
     */
    private var showTipAnim: Animator? = null

    /**
     * tip隐藏动画
     */
    private var hideTipAnim: Animator? = null

    /**
     * 旧的过期文案
     */
    private var preExpireContent: String? = ""

    /**
     * 设置奖励
     *
     * [showTip] 奖励
     * [hasHitExp] true 命中实验
     */
    fun updateData(showTip: MessageDeepChatTipBean?, hasHitExp: Boolean) {
        isShowTipEmpty = StringUtils.isEmpty(showTip?.tipContent.orEmpty())
        if (hasHitExp) {
            mBinding.tvNextTip.shaper().gradientColor(
                ColorUtils.getColor("#7BA4FE"),
                ColorUtils.getColor("#91A0FF"),
                ColorUtils.getColor("#978DFF")
            )
        }
        if (!isShowTipEmpty) {
            //是否是等待期，目前根据金钱判断
            val isWaitingTime = showTip?.isAwaitingTime == true
            val sp = SpanUtils()
            if (isWaitingTime) {
                sp.append("即可触发下一阶段补贴奖励")
            } else {
                sp.append("下一个任务")
            }
            sp.appendSpace(SizeUtils.dp2px(4f))
                .appendImage(if (hasHitExp) R.drawable.message_deep_chat_circle_view_money_blue_ic else R.drawable.message_deep_chat_circle_view_money_ic)
                .appendSpace(SizeUtils.dp2px(2f))
                .append("+")
                .append(showTip?.tipContent.orEmpty())
                .setBold()
            mBinding.tvNextTip.text = sp.create()
        }

        if (StringUtils.isEmpty(mBinding.tvNextTip.text?.toString())) {
            visibility = View.GONE
        }

        //显示过期气泡
        if (showTip?.expireContent?.isEmpty() == true) {
            mBinding.tvExpireTip.gone()
        } else {
            mBinding.tvExpireTip.visible()
            mBinding.tvExpireTip.text = showTip?.expireContent
            preExpireContent = showTip?.expireContent
        }

        //播放tip动画
        if (!isShowTipEmpty && (showTipAnim == null || visibility != VISIBLE)) {
            visibility = VISIBLE
            playShowTipAnim()
        } else if (StringUtils.isEmpty(showTip?.tipContent) && visibility == VISIBLE) {
            playHideTipAnim()
        }

    }

    /**
     * 显示表情搜索view，不展示tip
     *
     * [showView] true 表情推荐隐藏，可以显示tip
     */
    fun needShowView(showView: Boolean) {
        mBinding.tvNextTip.visibility = if (!isShowTipEmpty && showView) VISIBLE else GONE
    }

    /**
     * 播放tip显示动画
     */
    private fun playShowTipAnim() {
        showTipAnim = AnimatorUtils.translationY(this, 200, SizeUtils.dp2px(26f).toFloat(), 0f)
    }

    /**
     * 播放tip隐藏动画
     */
    private fun playHideTipAnim() {
        mBinding.tvExpireTip.gone()
        hideTipAnim = AnimatorUtils.translationY(this, 200, 0f, SizeUtils.dp2px(26f).toFloat())
        hideTipAnim?.addListener(object : AnimatorListener {
            override fun onAnimationStart(animation: Animator) {
                //do nothing
            }

            override fun onAnimationEnd(animation: Animator) {
                visibility = GONE
            }

            override fun onAnimationCancel(animation: Animator) {
                //do nothing
            }

            override fun onAnimationRepeat(animation: Animator) {
                //do nothing
            }

        })
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        showTipAnim?.cancel()
        hideTipAnim?.cancel()
    }
}