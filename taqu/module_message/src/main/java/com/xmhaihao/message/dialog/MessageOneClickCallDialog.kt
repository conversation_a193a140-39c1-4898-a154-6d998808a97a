package com.xmhaihao.message.dialog

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import cn.taqu.lib.base.constants.RespStatusConstants
import cn.taqu.lib.base.helper.BalanceHelper
import cn.taqu.lib.base.utils.GIORechargePosition
import cn.taqu.lib.base.utils.eventlog.message.MessageTracker
import com.xmhaibao.message.api.utils.MessageChargeSystemHelper
import com.xmhaihao.message.bean.MessageOneClickCallBean
import com.xmhaihao.message.databinding.MessageOneClickCallDialogBinding
import com.xmhaihao.message.dialog.MessageBalanceNotEnoughDialog.Companion.show
import com.xmhaihao.message.heartbeat.bean.MessageCanOpenRechargeBean
import com.xmhaihao.message.imdetail.intf.MessageChatProvider
import com.xmhaihao.message.repository.MessageOneClickCallRepository
import hb.common.data.AccountHelper
import hb.utils.ActivityUtils
import hb.utils.kvcache.KVCacheUtils
import hb.xrequest.RequestCallback
import hb.xstyle.xdialog.XLifecycleDialog
import hb.xtoast.XToastUtils

/**
 * 私信付费-一键召唤弹窗
 * [飞书需求](https://o15vj1m4ie.feishu.cn/wiki/MLoiwDUb7iHGxeke2kRcIWaXnlg)
 * <AUTHOR>
 * @date 2024/3/27
 */
class MessageOneClickCallDialog(
    context: Context,
    private val targetUuid: String?,
    private val data: MessageOneClickCallBean
) : XLifecycleDialog(context) {

    companion object {

        private const val CACHE_NOT_PROMPT = "message_cache_key_one_click_call_prompt"

        /**
         * 不在提示
         */
        fun saveNotPrompt() {
            KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).putBoolean(CACHE_NOT_PROMPT, true)
        }

        private fun isNotPrompt(): Boolean {
            return KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).getBoolean(CACHE_NOT_PROMPT, false)
        }

        fun showDialog(context: Context, targetUuid: String?) {
            val uuid = if (targetUuid.isNullOrEmpty()) {
                MessageChatProvider.get().targetUuid
            } else {
                targetUuid
            }
            if (uuid.isNullOrEmpty()) {
                return
            }
            if (isNotPrompt()) {
                requestSendCall(context, uuid)
                return
            }
            MessageOneClickCallRepository().requestOneClickCallInfo(uuid)
                .execute(object : RequestCallback<MessageOneClickCallBean>() {
                    override fun onSuccess(isCache: Boolean, obj: MessageOneClickCallBean?) {
                        obj?.let {
                            if (ActivityUtils.isActivityAlive(context)) {
                                MessageOneClickCallDialog(context, uuid, it).show()
                            }
                        }
                    }
                })
        }

        private fun requestSendCall(context: Context, targetUuid: String?, successCallback: (() -> Unit)? = null) {
            MessageOneClickCallRepository().requestSendCall(targetUuid).execute(object : RequestCallback<Any>() {
                override fun onSuccess(isCache: Boolean, obj: Any?) {
                    XToastUtils.show("正在召唤中，对方正在马上赶来")
                    successCallback?.invoke()
                    BalanceHelper.requestMyBalance()
                }

                override fun onFailure(code: Int, responseStatus: String?, responseMsg: String?) {
                    super.onFailure(code, responseStatus, responseMsg)
                    if (ActivityUtils.isActivityAlive(context) && responseStatus == RespStatusConstants.BALANCE_NOT_ENOUGH) {
                        MessageChargeSystemHelper.requestIsTargetGroup(context, {
                            val bean= MessageCanOpenRechargeBean().apply {
                                this.source = GIORechargePosition.POSITION_ONE_CLICK_CALL_DIALOG
                            }
                            show(context, bean.source, bean)
                            null
                        }, false, GIORechargePosition.POSITION_ONE_CLICK_CALL_DIALOG)
                    }
                }
            })
        }
    }

    private val binding by lazy {
        MessageOneClickCallDialogBinding.inflate(LayoutInflater.from(context))
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        binding.tvContent.text = data.content
        binding.tvPromptText.text = data.ratioTips
        binding.ivClose.setOnClickListener {
            dismiss()
        }
        binding.viewOneClick.setOnClickListener {
            val isNotPrompt = binding.cbPrompt.isChecked
            if (isNotPrompt) {
                saveNotPrompt()
            }
            MessageTracker.trackOnClickCallPopupClick(isNotPrompt)
            requestSendCall(context, targetUuid) {
                dismiss()
            }
        }
        MessageTracker.trackOnClickCallPopupExpo()
    }

}