package com.xmhaihao.message.widget.bottominput.chat

import android.content.Context
import android.widget.EditText
import com.xmhaibao.hbchat.bottominput.helper.MessageCommonEmojiHelper
import com.xmhaibao.hbchat.bottominput.viewmodel.MessageBottomInputCommonViewModel
import com.xmhaibao.hbchat.bottominput.widget.MessageCommonBottomPanelContainerView
import com.xmhaibao.hbchat.bottominput.widget.emoji.MessageCommonEmojiContainer
import com.xmhaihao.message.widget.bottominput.emoji.chat.MessageChatEmojiContainer

/**
 * @desc:私信
 *
 * <AUTHOR>
 * @date 2023/6/5
 */
class MessagePmEmojiHelper(
    private val context: Context,
    private val viewModel: MessageBottomInputCommonViewModel
) : MessageCommonEmojiHelper(context, viewModel) {


    override fun initBottomEmojiPanel(
        bottomPanelContainer: MessageCommonBottomPanelContainerView,
        editText: EditText
    ): MessageCommonEmojiContainer {
        return MessageChatEmojiContainer(context, bottomPanelContainer, viewModel, editText)
    }
}