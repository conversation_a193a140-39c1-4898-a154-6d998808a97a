package com.xmhaihao.message.repository.recharge

import cn.taqu.lib.base.api.UrlBase
import com.xmhaibao.message.api.bean.MessageChargeSystemEntryBean
import com.xmhaihao.message.bean.MessageBalanceNotEnoughBean
import com.xmhaihao.message.chargesystem.bean.MessageChargeSystemRecommendBean
import hb.common.xstatic.HttpParams
import hb.xrequest.XRequest

/**
 * <AUTHOR>
 * @desc 充值相关仓库
 * @date 2023-11-08 09:23
 */
class MessageRechargeRepository : UrlBase() {

    companion object {
        /**
         * 获取私信充值弹窗信息
         */
        @JvmStatic
        fun getMessageRechargeInfo(): XRequest<MessageBalanceNotEnoughBean> {
            val httpParams = HttpParams.newBuilder().get(API_LIVE_TRADE.plus("/Guide/getRechargeGuidePopup"))
                .needTicketId(true)
                .params("version_number","1")
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 获取充值体系优化入口页面元素
         * */
        @JvmStatic
        fun getChargeSystemEntryElements(isFromRelation: Boolean = false, source: String?): XRequest<MessageChargeSystemEntryBean> {
            val httpParams = HttpParams.newBuilder()
                .get(API_LIVE_TRADE.plus("/ForumInpourGuide/inpourGuide"))
                .needTicketId()
                .params(
                    "from_relation", if (isFromRelation) {
                        "1"
                    } else {
                        "0"
                    }
                )
                .params("source", source)
                .build()
            return XRequest.newRequest(httpParams)
        }


        /**
         * 获取充值体系优化 支付成功后 页面元素
         * */
        fun getChargeSystemPickerElements(): XRequest<MessageChargeSystemRecommendBean> {
            val httpParams = HttpParams.newBuilder()
                .get(API_GW_MESSAGE_V2.plus("/Guide/getInpourRecAccounts"))
                .needTicketId()
                .build()
            return XRequest.newRequest(httpParams)
        }

    }
}