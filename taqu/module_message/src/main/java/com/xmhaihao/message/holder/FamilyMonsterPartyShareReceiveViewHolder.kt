package com.xmhaihao.message.holder

import android.view.ViewGroup
import androidx.core.view.isVisible
import cn.taqu.lib.base.router.RouterLaunch
import cn.taqu.lib.base.xtracker.FamilyTracker
import com.xmhaihao.message.bean.MessageFamilyGameShareBean
import com.xmhaihao.message.databinding.MessageFamilyMonsterPartyShareReceiveItemBinding
import com.xmhaihao.message.holder.family.IFamilyMsgViewHolder
import hb.message.db.entity.HBMessageContentDO
import hb.xtoast.XToastUtils

/**
 * description:神兽-派对游戏-家族战绩分享卡片 接收方
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
class FamilyMonsterPartyShareReceiveViewHolder(parent: ViewGroup) :
    MessageIMBaseViewHolder<MessageFamilyGameShareBean>(
        parent,
        com.xmhaihao.message.R.layout.message_family_monster_party_share_receive_item
    ), IFamilyMsgViewHolder {

    private val mBinding by lazy {
        MessageFamilyMonsterPartyShareReceiveItemBinding.bind(itemView)
    }
    var messageContentBean: MessageFamilyGameShareBean? = null

    var mFamilyUuid:String? = null

    init {
        mBinding.relGameShare.root.setOnClickListener {
            messageContentBean?.let {
                if (!it.isExpire()) {
                    RouterLaunch.dealJumpData(itemView.context, it.relation)
                    FamilyTracker.trackFamilyImShareCardClk(
                        mFamilyUuid,
                        it.sceneType,
                        it.functionType,
                        it.relation
                    )
                } else {
                    XToastUtils.show("分享卡片已过期")
                }
            }
        }
    }

    override fun onBindViewHolder(
        messageContent: HBMessageContentDO?,
        messageContentBean: MessageFamilyGameShareBean?
    ) {
        super.onBindViewHolder(messageContent, messageContentBean)
        this.messageContentBean = messageContentBean
        messageContentBean?.let {
            mBinding.relGameShare.ivBg.setImageFromUrl(it.bgUrl)
            mBinding.relGameShare.tvTeamMemberName.text = it.name
            mBinding.relGameShare.tvDescribe.text = it.subTitle
            mBinding.relGameShare.btnGoChallenge.isVisible = true
            // 设置用户头像列表
            it.avatarList?.let { avatarList->
                mBinding.relGameShare.groupAvatars.setAvatarListUrlListWithString(avatarList)
            }
            FamilyTracker.trackFamilyImShareCardExp(
                mFamilyUuid,
                it.sceneType,
                it.functionType,
                it.relation
            )
        }
    }

    override fun initFamilyViewHolder(familyUuid: String?) {
        mFamilyUuid = familyUuid
    }

}