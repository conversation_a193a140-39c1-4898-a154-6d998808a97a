package com.xmhaihao.message.gift.ui

import android.annotation.SuppressLint
import android.content.Context
import android.os.CountDownTimer
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import cn.taqu.lib.base.router.RouterLaunch
import cn.taqu.lib.base.utils.SpannableStringUtil
import cn.taqu.lib.okhttp.callback.GsonCallBack
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.xmhaihao.message.R
import com.xmhaihao.message.databinding.MessageFamilySuitGiftViewBinding
import com.xmhaihao.message.gift.bean.MessageSuitGiftBannerBean
import com.xmhaihao.message.repository.MessageRepository
import hb.common.helper.ServerTime
import hb.kotlin_extension.noneSyncLazy
import hb.qim.base.utils.QLog
import kotlinx.coroutines.Job
import java.net.URLDecoder
import java.net.URLEncoder

/**
 * 家族套装礼物Banner View
 * https://o15vj1m4ie.feishu.cn/wiki/Vblfwb7GdiaPpCkPisvc4hgpnnh
 *
 * <AUTHOR>
 * @since 2023-5-17
 * */
class MessageSuitGiftBannerView@JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val PERIOD_SECOND = 1000L
    }

    private val mBinding: MessageFamilySuitGiftViewBinding by noneSyncLazy {
        MessageFamilySuitGiftViewBinding.inflate(LayoutInflater.from(context), this)
    }
    // 当前已选中的礼物ID，避免反复点击同一礼物时，会反复请求接口详情
    private var mCurrentShowGiftId = ""



    // 倒计时 Timer
    var mCountDownTimer: CountDownTimer? = null

    /**
     * 更新礼物信息（当前场景：选中不同的礼物）
     * @param giftId 选中的礼物ID
     * */
    @SuppressLint("SetTextI18n")
    fun updateData(giftId: String?, iconUrl: String = "") {
        if(giftId.isNullOrBlank()) {
            return
        }
        mCurrentShowGiftId = giftId
        visibility = GONE
        MessageRepository.getFamilySuitGiftBannerInfo(giftId).execute(object : GsonCallBack<MessageSuitGiftBannerBean>() {
            override fun onSuccess(
                isCache: Boolean,
                data: MessageSuitGiftBannerBean?,
                response: IResponseInfo<out IResponseInfo<*>>
            ) {
                data?:return
                visibility = VISIBLE
                mBinding.apply {
                    if(iconUrl.isNotBlank()) {
                        imgGiftIcon.setImageFromUrl(iconUrl)
                    }
                    tvName.text = data.title
                    tvDescribe.text = data.describe
                    if(data.backgroundType == MessageSuitGiftBannerBean.BG_TYPE_A) {
                        imgBg.setImageResource(R.drawable.message_suit_a_gift_bg)
                    } else if(data.backgroundType == MessageSuitGiftBannerBean.BG_TYPE_B) {
                        imgBg.setImageResource(R.drawable.message_suit_b_gift_bg)
                    }
                    root.setOnClickListener {
                        RouterLaunch.dealJumpData(context, URLDecoder.decode(data.relation, Charsets.UTF_8.name()))
                    }
                    if(data.expireTimeLong > ServerTime.currentTimeMillis()) {
                        // 启动定时器
                        mCountDownTimer?.cancel()
                        mCountDownTimer = object : CountDownTimer(data.expireTimeLong  - ServerTime.currentTimeMillis(), PERIOD_SECOND){
                            override fun onTick(millisUntilFinished: Long) {
                                val remains = (millisUntilFinished / PERIOD_SECOND).toInt()
                                tvDescribe.text = SpannableStringUtil.getChangeText(context, "套装动效触发倒计时：$remains", remains.toString(), hb.xstyle.R.color.TH_Red600)
                            }

                            override fun onFinish() {
                                tvDescribe.text = data.describe
                            }

                        }
                        mCountDownTimer?.start()
                    }
                }
            }

            override fun onFailure(
                isServiceFailure: Boolean,
                response: IResponseInfo<out IResponseInfo<*>>
            ) {
                // ignore
            }

        })
    }

    fun cancelRequestAndHide() {
        mCountDownTimer?.cancel()
        visibility = GONE
        mCurrentShowGiftId = ""
    }
}