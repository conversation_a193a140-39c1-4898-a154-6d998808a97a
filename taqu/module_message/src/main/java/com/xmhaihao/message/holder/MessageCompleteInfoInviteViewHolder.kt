package com.xmhaihao.message.holder

import android.view.View
import android.view.ViewGroup
import com.xmhaihao.message.R
import com.xmhaihao.message.bean.MessageCompleteInfoInviteUserBean
import com.xmhaihao.message.databinding.MessageCompleteInfoInviteItemBinding
import hb.xadapter.XBaseAdapter
import hb.xadapter.XBaseViewHolder

/**
 * 资料完善邀请用户
 *
 * <AUTHOR>
 * @date 2021-10-19
 */
class MessageCompleteInfoInviteViewHolder(parent: ViewGroup?) :
    XBaseViewHolder<MessageCompleteInfoInviteUserBean>(parent, R.layout.message_complete_info_invite_item) {

    private var mBinding = MessageCompleteInfoInviteItemBinding.bind(itemView)
    private var mSelectCallback: SelectCallback? = null
    var mInfo: MessageCompleteInfoInviteUserBean? = null

    override fun onBindView(info: MessageCompleteInfoInviteUserBean) {
        mInfo = info
        mBinding.tvName.text = info.accountName
        mBinding.ivAvatar.setImageFromUrl(info.avatar)
        showUserView(info)
        if (info.isInviteEnable) {
            itemView.alpha = 1.0f
            mBinding.ivSelect.isEnabled = true
            mBinding.tvInfoComplete.visibility = View.GONE
            itemView.isClickable = true
        } else {
            itemView.alpha = 0.5f
            mBinding.tvInfoComplete.visibility = View.VISIBLE
            mBinding.ivSelect.isEnabled = false
            itemView.isClickable = false
        }
        mBinding.ivSelect.isSelected = info.isSelect
        if (mSelectCallback?.getSelectUserList()?.contains(info) == true) {
            info.isSelect = true
            mBinding.ivSelect.isSelected = true
        }
    }

    /**
     * 显示用户信息：性别、认证
     *
     * @param info
     */
    private fun showUserView(info: MessageCompleteInfoInviteUserBean) {
        mBinding.forumGenderView.setGenderData(info.sexType, info.age)
    }

    override fun setOnItemClickListener(itemClickListener: XBaseAdapter.OnItemClickListener?) {
        super.setOnItemClickListener(itemClickListener)
    }

    fun setSelectCallback(selectCallback: SelectCallback) {
        mSelectCallback = selectCallback
    }

    interface SelectCallback {
        fun getSelectUserList(): List<MessageCompleteInfoInviteUserBean>
    }

}