package com.xmhaihao.message.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import com.xmhaihao.message.R
import com.xmhaihao.message.bean.MessageChatRoomJoinInviteBean
import com.xmhaihao.message.databinding.MessageChatRoomInviteNewViewBinding
import hb.common.helper.HostHelper
import hb.drawable.shape.extend.shape
import hb.drawable.shape.view.HbRelativeLayout
import hb.utils.ColorUtils
import hb.utils.SizeUtils
import hb.utils.WebpUtils

/**
 * 私信聊天室邀请进房
 *
 * <AUTHOR>
 * @date 2022-03-02
 */
class MessageChatRoomInviteNewView @JvmOverloads constructor(context: Context, attrs: AttributeSet?
                                                             , defStyleAttr: Int = 0) : HbRelativeLayout(context, attrs, defStyleAttr) {
    private val mBinding = MessageChatRoomInviteNewViewBinding.inflate(LayoutInflater.from(context), this)

    fun onSetViewData(data: MessageChatRoomJoinInviteBean, isRight: Boolean) {
        data.apply {
            mBinding.apply {
                shape(
                    solidColor = ColorUtils.getColor(hb.xstyle.R.color.white),
                    strokeWidth = SizeUtils.dp2px(1f),
                    strokeColor = ColorUtils.getColor("#EFEFEF"),
                    radius = SizeUtils.dp2px(12f).toFloat()
                )
                imgCover.setImageFromUrl(WebpUtils.getWebpUrl_3_1(picUrl, HostHelper.getInstance().hostImg))
                imgRoomType.setImageFromUrl(WebpUtils.getWebpUrl_4_1(icon, HostHelper.getInstance().hostImg))
                tvTitle.text = chatTitle
                tvContent.text = content
            }
        }
    }

}