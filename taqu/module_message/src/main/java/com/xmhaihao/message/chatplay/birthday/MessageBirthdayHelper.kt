package com.xmhaihao.message.chatplay.birthday

import android.content.Context
import android.content.DialogInterface
import cn.taqu.lib.base.bean.MessageAccountInfo
import cn.taqu.lib.base.common.AppAccountBean
import cn.taqu.lib.base.event.EventXjbLogin
import cn.taqu.lib.base.helper.AppConfigHelper
import cn.taqu.lib.base.interf.INavigationAdDialogShowCallback
import cn.taqu.lib.base.router.ARouterManager
import cn.taqu.lib.base.utils.AppTimeUtils
import cn.taqu.lib.base.utils.appdiff.AppDifferentiationUtils
import cn.taqu.lib.base.utils.eventlog.message.MessageTracker
import com.xmhaibao.account.api.interf.infoEdit.OnAccountEditAgeCallback
import com.xmhaibao.account.api.router.AccountPinsRouter
import com.xmhaibao.message.api.constants.ImTypeConstants
import com.xmhaibao.xjson.XJson
import com.xmhaihao.message.chatplay.birthday.bean.MessageBirthdayGreetingCardBean
import com.xmhaihao.message.chatplay.birthday.dialog.MessageBirthdayBlessAnimDialog
import com.xmhaihao.message.chatplay.birthday.dialog.MessageBirthdayRewardDialog
import com.xmhaihao.message.tracker.MessageBirthTracker
import hb.common.data.AccountHelper
import hb.common.data.ApplicationHelper
import hb.common.helper.ServerTime
import hb.message.db.entity.HBMessageContentDO
import hb.utils.EventBusUtils
import hb.utils.TimeUtils
import hb.utils.kvcache.KVCacheUtils
import hb.utils.kvcache.KeyValueStrategy
import hb.xrequest.launchHttp
import hb.xstyle.xdialog.XDialog
import hb.xthread.XThreadPool
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap

/**
 * 【私信-付费】生日祝福帮助类
 * [飞书需求](https://o15vj1m4ie.feishu.cn/wiki/OitMwRkxli35bPky7MZcWKsEngg)
 * <AUTHOR>
 * @date 2024/3/13
 */
object MessageBirthdayHelper {
    /**
     * 记录上次领取过生日奖励的时间
     */
    private const val CACHE_RECEIVED_BIRTH_REWARD = "message_received_birth_reward"

    /**
     * 记录上次校准过生日日期的时间
     */
    private const val CACHE_CHECK_BIRTH_TIME = "message_check_birth_bless_time"

    /**
     * 记录取消校准生日日期的时间
     */
    private const val CACHE_CANCEL_BIRTH_TIME = "message_cancel_birth_bless_time"

    /**
     * 是否需要播放祝福动画弹窗数据缓存Key
     */
    private const val CACHE_BLESS_ANIM_DIALOG = "message_birth_bless_anim_dialog"

    /**
     * 换成祝福送礼/送贺卡时间
     */
    private const val CACHE_BIRTH_BLESS_TIME = "CACHE_BIRTH_BLESS_TIME"

    /**
     * 校准日期时-当前选中的生日日期
     */
    private var curBirth: Long = 0

    /**
     * 广告弹窗回调
     */
    private var adDialogShowCallback: INavigationAdDialogShowCallback? = null

    /**
     * 私信页面-是否需要播放祝福动画弹窗缓存数据
     */
    private var playBlessAnimDialogMap: ConcurrentHashMap<String, MessageBirthdayGreetingCardBean>? = null

    /**
     * 当前生日祝福动画弹窗，需等送礼成功时关闭弹窗
     */
    private var curBlessDialog: DialogInterface? = null

    private var birthKVStrategy: KeyValueStrategy? = null

    /**
     * 记录播放动画的时间，过滤去重短时间多次播放问题
     */
    private var animDialogShowTime = 0L

    /**
     * 当前是否是我自己的生日
     */
    var isMyBirthDay: Boolean = false
        private set


    init {
        EventBusUtils.register(this)
    }


    /**
     * 更新我是否是生日的状态
     */
    fun updateMyBirthDayStatus(isBirth: Boolean) {
        isMyBirthDay = isBirth
    }

    /**
     * 是否可以执行生日祝福功能
     */
    fun isOpenBirthdayBlessing(): Boolean {
        return AppDifferentiationUtils.isTaquApp() && !AppConfigHelper.getInfo().isCloseChatBirthBlessing
    }

    /**
     * 今天是否是生日 & 可以开启生日祝福功能
     *
     * @param birth 需要判断的时间戳 单位：ms
     */
    fun isToDayBirthdayBlessing(birth: Long): Boolean {
        return AppTimeUtils.isBirthday(birth) && isOpenBirthdayBlessing()
    }

    /**
     * 私信详情-需要支持显示头像皇冠的消息类型
     */
    fun isCanBirthdayAvatar(msgType: String?): Boolean {
        return msgType == ImTypeConstants.MESSAGE_CONTENT_TYPE_NORMAL_TEXT
                || msgType == ImTypeConstants.MESSAGE_CONTENT_TYPE_IMAGE
                || msgType == ImTypeConstants.MESSAGE_VOICE_MESSAGE
                || msgType == ImTypeConstants.MESSAGE_CONTENT_TYPE_GIFT
                || msgType == ImTypeConstants.MESSAGE_BIRTHDAY_BLESS_CARD
    }

    /**
     * 校验今天是否需要领取生日奖励
     */
    fun checkNavigationBirthdayDialog(context: Context, adDialogCallback: INavigationAdDialogShowCallback?) {
        adDialogShowCallback = adDialogCallback
        XThreadPool.Main().execute {
            val birth = AppAccountBean.get().identityNoBirth
            if (!isToDayBirthdayBlessing(birth) || isTodayReceivedReward() || isToDayCancelBirthTime()) {
                callbackAdDialogShow(true)
                return@execute
            }
            //未实名认证用户 && 是否是注册时默认的日期，需要触发生日校准 && 今天没有触发过校准过日期
            if (!AppAccountBean.get().isRealName && TimeUtils.millis2String(birth, "yyyyMMdd") == "********" && !isTodayCheckBirthTime()) {
                showCheckDefaultBirthdayDialog(context, birth)
            } else {
                showBirthBlessingGiftPackDialog(context)
            }
        }
    }

    /**
     * 生日是注册是默认的日期的，需要让用户校准确认日期。
     */
    private fun showCheckDefaultBirthdayDialog(context: Context, birth: Long) {
        setTodayCheckBirthTime()
        XDialog.newBuilder(context)
            .title("出生日期是否正确?")
            .message("1995年1月1日是您的生日吗?")
            .negativeButton("不是") { dialog, _ ->
                MessageBirthTracker.Instance.birthAdjustPopClick(false)
                dialog.dismiss()
                setToDayCancelBirthTime()
                showEditAgeDialog(context, birth)
            }
            .positiveButton("是的") { dialog, _ ->
                MessageBirthTracker.Instance.birthAdjustPopClick(true)
                dialog.dismiss()
                showBirthBlessingGiftPackDialog(context)
            }.show()
        MessageBirthTracker.Instance.birthAdjustPopExpo()
    }

    private fun showBirthBlessingGiftPackDialog(context: Context) {
        //正在显示签到弹框，不能显示生日弹框
        if (ARouterManager.mineForBaseService().isShowSignDialog) {
            return
        }
        GlobalScope.launchHttp({
            val rewardInfo = MessageBirthdayRepository.requestBirthdayRewardInfo()
            withContext(XThreadPool.Main().asCoroutineDispatcher()) {
                if (rewardInfo != null) {
                    MessageBirthdayRewardDialog.newInstance(context, rewardInfo).show()
                    setTodayReceivedReward()
                    callbackAdDialogShow(false)
                } else {
                    callbackAdDialogShow(true)
                }

            }
        }, {
            callbackAdDialogShow(true)
            false
        })
    }

    private fun showEditAgeDialog(context: Context, birth: Long) {
        AccountPinsRouter.accountService().editAge(context, birth, object : OnAccountEditAgeCallback {
            override fun onBirthDayTimeSelect(time: Long, age: Int) {
                curBirth = time
            }

            override fun onBirthDayTimeSelectHttp(success: Boolean) {
                //更新时间
                AppAccountBean.get().identityNoBirth = curBirth
                AppAccountBean.get().birth = curBirth
                AppAccountBean.get().save()
                if (AppTimeUtils.isBirthday(curBirth * 1000)) {
                    showBirthBlessingGiftPackDialog(context)
                } else {
                    callbackAdDialogShow(true)
                }
            }

            override fun onDismiss() {
                setToDayCancelBirthTime()
                callbackAdDialogShow(true)
            }
        })
    }

    private fun callbackAdDialogShow(isShow: Boolean) {
        adDialogShowCallback?.onCallback(isShow)
        adDialogShowCallback = null
    }

    /**
     * 今天是否已领取过生日奖励
     */
    private fun isTodayReceivedReward(): Boolean {
        return TimeUtils.isToday(getKVStrategy().getLong(CACHE_RECEIVED_BIRTH_REWARD, 0))
    }

    private fun setTodayReceivedReward() {
        getKVStrategy().putLong(CACHE_RECEIVED_BIRTH_REWARD, getCurrentTimeMillis())
    }

    /**
     * 今天是否执行过校准过生日日期
     */
    private fun isTodayCheckBirthTime(): Boolean {
        return TimeUtils.isToday(getKVStrategy().getLong(CACHE_CHECK_BIRTH_TIME, 0))
    }

    private fun setTodayCheckBirthTime() {
        getKVStrategy().putLong(CACHE_CHECK_BIRTH_TIME, getCurrentTimeMillis())
    }

    /**
     * 今天是否取消了校准日期时间
     */
    private fun isToDayCancelBirthTime(): Boolean {
        return TimeUtils.isToday(getKVStrategy().getLong(CACHE_CANCEL_BIRTH_TIME, 0))
    }

    private fun setToDayCancelBirthTime() {
        getKVStrategy().putLong(CACHE_CANCEL_BIRTH_TIME, getCurrentTimeMillis())
    }

    private fun getKVStrategy(): KeyValueStrategy {
        if (birthKVStrategy == null) {
            birthKVStrategy = createKVStrategy()
        }
        return birthKVStrategy!!
    }

    private fun createKVStrategy() = KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid() + "_message_birthday")

    private fun getCurrentTimeMillis(): Long {
        if (ApplicationHelper.isDevChannel() || ARouterManager.buildConfig().isDebug()) {
            return System.currentTimeMillis()
        } else {
            return ServerTime.currentTimeMillis()
        }
    }


    /**
     * 记录祝福送礼/送贺卡时间
     */
    fun recordBlessTargetUser(targetUuid: String) {
        val yyyyMMdd = TimeUtils.millis2String(getCurrentTimeMillis(), TimeUtils.YYYY_MM_DD)
        getKVStrategy().putBoolean(CACHE_BIRTH_BLESS_TIME.plus(targetUuid).plus(yyyyMMdd), true)
    }

    /**
     * 今天是否送过祝福
     */
    fun isBlessTargetUser(targetUuid: String): Boolean {
        val yyyyMMdd = TimeUtils.millis2String(getCurrentTimeMillis(), TimeUtils.YYYY_MM_DD)
        return getKVStrategy().getBoolean(CACHE_BIRTH_BLESS_TIME.plus(targetUuid).plus(yyyyMMdd), false)
    }

    /**
     * 生日祝福动画提醒弹窗
     */
    fun showPlayBlessAnimDialog(
        context: Context?,
        msgContent: HBMessageContentDO?,
        accountInfo: MessageAccountInfo,
        onSendGiftCallback: ((MessageBirthdayGreetingCardBean) -> Unit)? = null
    ) {
        if (context == null || msgContent == null) {
            return
        }
        //remove缓存的数据
        getPlayBlessAnimDialogData(msgContent.conversationId)
        val blessBean = msgContent.hbMessageContentBeanDO as? MessageBirthdayGreetingCardBean
        if (blessBean != null && checkBlessAnimShow()) {
            MessageBirthdayBlessAnimDialog.show(context, accountInfo, blessBean, onSendGiftCallback) {
                curBlessDialog = it
            }
        }
    }

    /**
     * 生日祝福动画提醒弹窗
     */
    fun showPlayBlessAnimDialog(
        context: Context?,
        conversationId: String?,
        accountInfo: MessageAccountInfo,
        onSendGiftCallback: ((MessageBirthdayGreetingCardBean) -> Unit)? = null
    ) {
        if (context == null || conversationId.isNullOrEmpty()) {
            return
        }
        val blessBean = getPlayBlessAnimDialogData(conversationId)
        if (blessBean != null && checkBlessAnimShow()) {
            MessageBirthdayBlessAnimDialog.show(context, accountInfo, blessBean, onSendGiftCallback) {
                curBlessDialog = it
            }
        }
    }

    /**
     * 校验是否可以播放动画弹窗
     */
    private fun checkBlessAnimShow(): Boolean {
        if (System.currentTimeMillis() - animDialogShowTime < 2000) {
            return false
        }
        animDialogShowTime = System.currentTimeMillis()
        return true
    }


    fun dismissCurBlessDialog() {
        curBlessDialog?.dismiss()
        curBlessDialog = null
    }

    /**
     * 接收到贺卡消息需要添加缓存
     */
    fun putPlayBlessAnimDialog(msgContent: HBMessageContentDO) {
        if (msgContent.contentType != ImTypeConstants.MESSAGE_BIRTHDAY_BLESS_CARD) {
            return
        }
        val blessBean = msgContent.hbMessageContentBeanDO as? MessageBirthdayGreetingCardBean
        if (blessBean != null) {
            getPlayBlessAnimMap()[msgContent.conversationId] = blessBean
        }
        savePlayBlessAnimCache()
    }

    private fun getPlayBlessAnimDialogData(conversationId: String?): MessageBirthdayGreetingCardBean? {
        if (conversationId.isNullOrEmpty()) {
            return null
        }
        val msgContent = getPlayBlessAnimMap().remove(conversationId)
        savePlayBlessAnimCache()
        return msgContent
    }

    private fun getPlayBlessAnimMap(): ConcurrentHashMap<String, MessageBirthdayGreetingCardBean> {
        if (playBlessAnimDialogMap == null) {
            playBlessAnimDialogMap = ConcurrentHashMap<String, MessageBirthdayGreetingCardBean>()
            val json = getKVStrategy().getString(CACHE_BLESS_ANIM_DIALOG, "")
            val map = XJson.fromJson<Map<String, MessageBirthdayGreetingCardBean>>(
                json,
                XJson.getMapType(String::class.java, MessageBirthdayGreetingCardBean::class.java)
            )
            if (!map.isNullOrEmpty()) {
                playBlessAnimDialogMap?.putAll(map)
            }
        }
        return playBlessAnimDialogMap ?: ConcurrentHashMap<String, MessageBirthdayGreetingCardBean>()
    }

    private fun savePlayBlessAnimCache() {
        XThreadPool.IO().execute {
            getKVStrategy().putString(CACHE_BLESS_ANIM_DIALOG, XJson.toJson(playBlessAnimDialogMap))
        }
    }

    fun getBirthdayBlessMessageContent(list: MutableList<HBMessageContentDO>?): HBMessageContentDO? {
        return list?.find {
            it.contentType == ImTypeConstants.MESSAGE_BIRTHDAY_BLESS_CARD
        }
    }

    fun onEventMainThread(event: EventXjbLogin) {
        //切换账号后清空缓存
        if (AccountHelper.isUserLogined()) {
            birthKVStrategy = createKVStrategy()
        } else {
            birthKVStrategy = null

            playBlessAnimDialogMap?.clear()
            playBlessAnimDialogMap = null

            updateMyBirthDayStatus(false)
        }
    }

    fun destroy() {
        curBlessDialog = null
        animDialogShowTime = 0
    }
}