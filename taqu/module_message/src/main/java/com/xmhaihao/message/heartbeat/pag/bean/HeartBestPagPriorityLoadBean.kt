package com.xmhaihao.message.heartbeat.pag.bean

import com.xmhaihao.message.heartbeat.pag.PagDownloadCallback
import java.lang.ref.WeakReference

/**
 * 优先下载的 pag
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
class HeartBestPagPriorityLoadBean(
    /**
     * 优先下载的pag
     */
    val pagId: String,

    /**
     * 结果回调
     */
    var callback: WeakReference<PagDownloadCallback?>? = null,
    /**
     * pagId 对应的md5
     */
    var md5: String? = "",
)