package com.xmhaihao.message.holder

import android.view.ViewGroup
import android.widget.TextView
import cn.taqu.lib.base.bean.MessageAccountInfo
import com.xmhaibao.account.api.router.AccountPinsRouter
import com.xmhaibao.message.api.constants.ImTypeConstants
import com.xmhaihao.message.R
import com.xmhaihao.message.databinding.MessageHomeConversationSuperRItemBinding
import com.xmhaihao.message.helper.systemavatar.MessageSystemAvatarHelper
import com.xmhaihao.message.router.MessageRouter
import com.xmhaihao.message.utils.MessageCommonUtils.Companion.trackNewMessageListClick
import hb.common.data.AccountHelper
import hb.message.db.entity.HBAccountInfoDO
import hb.message.db.entity.HBMessageConversationDO
import hb.message.ui.HBMessageUiConversationViewHolder
import hb.utils.SpanUtils

/**
 * 未回复消息 超R专属客服 消息中心viewHolder
 *
 * <AUTHOR>
 * @date 2023-12-15
 */
class MessageConversationSuperRViewHolder(parent: ViewGroup) :
    HBMessageUiConversationViewHolder<MessageAccountInfo?>(parent, R.layout.message_home_conversation_super_r_item) {



    /**
     * itemView
     */
    private val binding by lazy {
        MessageHomeConversationSuperRItemBinding.bind(itemView)
    }


    override fun onBindItemView(item: HBMessageConversationDO?) {
        if (item == null) {
            return
        }
        MessageSystemAvatarHelper.loadAvatar(item.targetId, binding.ivMessageAvatar)
        binding.tvMessageContent.text = SpanUtils().append(item.msgContent).create()
        setTimeShow(binding.tvMessageTime, item)
        binding.rlRoot.setOnClickListener {
            if (!AccountHelper.isUserLogined()) {
                AccountPinsRouter.launchLogin()
                return@setOnClickListener
            }
            trackNewMessageListClick(item)
            MessageRouter.launchMessageSystemMsgActivity(ImTypeConstants.IM_MESSAGE_TYPE_SUPER_R)
        }
    }

    /**
     * 设置时间
     */
    private fun setTimeShow(tvTime: TextView, item: HBMessageConversationDO) {
        if (item.timeHint == null || item.timeHint?.isEmpty() == true) {
            item.timeHint = MessageConversationViewHolder.calculateTime(item.lastTime)
        }
        tvTime.text = item.timeHint?.ifEmpty {
            ""
        }
    }


    override fun onBindAccountInfoItemView(hbAccountInfoDO: HBAccountInfoDO?, conversationAccountInfo: MessageAccountInfo?) {
        //do nothing
    }
}