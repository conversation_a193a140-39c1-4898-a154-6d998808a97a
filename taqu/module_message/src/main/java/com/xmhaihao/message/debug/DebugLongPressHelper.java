package com.xmhaihao.message.debug;

import android.os.Handler;
import android.os.Looper;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.NonNull;

/**
 * 私信长按联调工具
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
public class DebugLongPressHelper {
    private final String TAG = "DebugLongPressHelper";
    private static final long LONG_PRESS_TIMEOUT = 3000; // 长按时间阈值，单位毫秒
    private final Handler handler = new Handler(Looper.getMainLooper());
    private Runnable longPressRunnable;
    private boolean isLongPressed = false;

    public void setOnLongPressListener(View view, Runnable onLongPress, boolean isChat) {
        if (view == null) {
            return;
        }
        view.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(@NonNull View view) {
                longPressRunnable = new Runnable() {
                    @Override
                    public void run() {
                        isLongPressed = true;
                        onLongPress.run();
                    }
                };

                view.setOnTouchListener((v, event) -> {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            isLongPressed = false;
                            handler.postDelayed(longPressRunnable, LONG_PRESS_TIMEOUT);
                            break;
                        case MotionEvent.ACTION_UP:
                        case MotionEvent.ACTION_CANCEL:
                            if (!isLongPressed) {
                                handler.removeCallbacks(longPressRunnable);
                            }
                            isLongPressed = false;
                            break;
                    }
                    return isChat;
                });
            }

            @Override
            public void onViewDetachedFromWindow(@NonNull View view) {
                view.setOnTouchListener(null);
                handler.removeCallbacksAndMessages(null);
                longPressRunnable = null;
            }
        });
    }
}
