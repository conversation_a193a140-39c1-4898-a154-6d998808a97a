package com.xmhaihao.message.holder

import android.animation.ValueAnimator
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import cn.taqu.lib.base.constants.CommonConstants
import com.xmhaibao.call.api.bean.CallStartParams
import com.xmhaibao.call.api.repository.CallPinsRepository
import com.xmhaibao.call.api.router.CallPinsRouter
import com.xmhaibao.call.api.util.CallPinsTracker
import com.xmhaibao.message.api.constants.ImTypeConstants
import com.xmhaibao.xjson.XJson
import com.xmhaihao.message.R
import com.xmhaihao.message.bean.MessageChatCallVoiceCallCardMsgBean
import com.xmhaihao.message.databinding.MessageChatCallVoiceCallCardLayoutBinding
import com.xmhaihao.message.helper.MessageCallVoiceCardHelper
import dp
import hb.common.helper.ServerTime
import hb.kotlin_extension.gone
import hb.kotlin_extension.invisible
import hb.kotlin_extension.visible
import hb.message.HBChat
import hb.message.db.entity.HBMessageContentDO
import hb.utils.*
import hb.xrequest.awaitNullable
import hb.xrequest.launchHttp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel


/**
 * 渗透_想聊就聊语音 语音通话卡
 *
 * 新增需求: https://project.feishu.cn/haibao/story/detail/5092554616
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
class MessageChatCallVoiceCallCardViewHolder(parent: ViewGroup) :
    MessageChatBaseBindLifecycleOwnerViewHolder<MessageChatCallVoiceCallCardMsgBean>(
        parent,
        R.layout.message_chat_call_voice_call_card_layout
    ) {
    /**
     * 卡片可见时会调用接口进行刷新，这里增加个时间控制下请求的频率
     */
    private var lastRequestTime = 0L

    private var contentBean: MessageChatCallVoiceCallCardMsgBean? = null

    private val binding: MessageChatCallVoiceCallCardLayoutBinding =
        MessageChatCallVoiceCallCardLayoutBinding.bind(itemView)
    private var mainScope: CoroutineScope? = null

    /**
     * 语音签名播放帮助类
     */
    var voiceSignPlayHelper: MessageCallVoiceCardHelper? = null

    /**
     * 正在播放的语音签名 null为停止播放状态
     */
    private val playingItemObserver by lazy {
        Observer<MessageChatCallVoiceCallCardMsgBean?> {
            //如果不为空 则为播放中，播放中且是当前消息的签名才更新为播放中 否则为停止播放的状态
            val isCurItemPlaying =
                it != null && voiceSignPlayHelper?.isPlaying(contentBean) ?: false
            updateVoiceSignPlayStateUi(isCurItemPlaying)
            observePlayingCountDown(isCurItemPlaying)

        }
    }
    private val playCountDownObserver by lazy {
        Observer<Int> { t ->
            if (t >= 0) {
                binding.tvVoiceDuration.text = "${t}s"
            }
        }
    }

    init {
        binding.tvStartCall.setOnClickListener {
            val otherUuid = chatViewModel?.mOtherAccountId
            CallPinsTracker.trackVoiceCardClick(contentBean?.hasVoiceSign() ?: false, "去使用")
            if (otherUuid.isNullOrEmpty()) {
                Loger.w(TAG, "对方uuid为空 不能拨打")
                return@setOnClickListener
            }
            CallPinsRouter.imCallService().requestGetCallInfo(
                it.context,
                CallStartParams.createDefault(
                    otherUuid,
                    CommonConstants.CALL_VOICE_CARD,
                    false,
                    CallPinsTracker.QU_CALL_SYSTEM_GUIDE,
                    true
                )
            )

        }
        binding.ivPlay.setOnClickListener {
            val cardBean = contentBean ?: return@setOnClickListener
            val isPlaying = voiceSignPlayHelper?.isPlaying(cardBean) == true
            Loger.i(TAG, "ivPlay click", contentBean.hashCode(), isPlaying)
            CallPinsTracker.trackVoiceCardClick(cardBean.hasVoiceSign(), "语音签名")
            if (isPlaying) {
                voiceSignPlayHelper?.stopPlay(cardBean)
            } else {
                voiceSignPlayHelper?.playVoiceSign(cardBean)
            }
        }
    }

    override fun onBindViewHolder(
        messageContent: HBMessageContentDO?, contentBean: MessageChatCallVoiceCallCardMsgBean?
    ) {
        super.onBindViewHolder(messageContent, contentBean)
        //viewHolder复用时 可能会重复播放lottie 倒计时 这里重置一下
        if (this.contentBean?.hasVoiceSign() == true) {
            binding.lottieView.cancelAnimation()
            voiceSignPlayHelper?.playingItemLiveData?.removeObserver(playingItemObserver)
            voiceSignPlayHelper?.playingDownCountLiveData?.removeObserver(playCountDownObserver)
        }
        this.contentBean = contentBean
        contentBean?.msgId = messageContent?.msgId
        binding.tvCardTitle.text = "${contentBean?.title}"
        binding.tvCardContent.text = contentBean?.content ?: ""
        binding.tvStartCall.text = contentBean?.btnContent ?: ""
        if (contentBean?.isModelingV2() == true) {
            binding.ivTopRightIcon.gone()
            binding.groupAvatar.visible()
            binding.ivAvatar.setImageFromUrl(contentBean.avatar)
            binding.lottieAvatarElectrocardiogram.playAnimation()
            binding.ivCardAvatar.gone()
            (binding.ivPlay.layoutParams as ConstraintLayout.LayoutParams).apply {
                startToStart =ConstraintLayout.LayoutParams.PARENT_ID
                startToEnd = ConstraintLayout.LayoutParams.UNSET
            }
            binding.lottieView.layoutParams.width =  114.dp
            binding.lottieCardContent.gone()
            binding.tvCardContent.visible()
        }else if (contentBean?.isModelingV3() == true) {
            binding.ivTopRightIcon.visible()
            binding.ivTopRightIcon.setImageResource(R.drawable.message_voice_call_card_top_phone_ic)
            binding.lottieAvatarElectrocardiogram.pauseAnimation()
            binding.groupAvatar.gone()
            binding.ivCardAvatar.visible()
            binding.ivCardAvatar.setImageFromUrl(contentBean.avatar)
            (binding.ivPlay.layoutParams as ConstraintLayout.LayoutParams).apply {
                startToStart =ConstraintLayout.LayoutParams.UNSET
                startToEnd =  binding.ivCardAvatar.id
            }
            binding.lottieView.layoutParams.width =  94.dp
            binding.lottieCardContent.visible()
            if (!contentBean.isCardContentLottiePlay) {
                binding.lottieCardContent.playAnimation()
                contentBean.isCardContentLottiePlay = true
            }
            binding.tvCardContent.invisible()
        } else {
            binding.ivTopRightIcon.visible()
            binding.ivTopRightIcon.setImageResource(R.drawable.message_voice_call_card_top_face_ic)
            binding.lottieAvatarElectrocardiogram.pauseAnimation()
            binding.groupAvatar.gone()
            binding.ivCardAvatar.gone()
            (binding.ivPlay.layoutParams as ConstraintLayout.LayoutParams).apply {
                startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                startToEnd = ConstraintLayout.LayoutParams.UNSET
            }
            binding.lottieView.layoutParams.width =  114.dp
            binding.lottieCardContent.gone()
            binding.tvCardContent.visible()
        }

        if (contentBean?.hasVoiceSign() == true) {
            binding.groupVoiceSign.visible()
            if (voiceSignPlayHelper?.isPlaying(contentBean) == true) {
                binding.tvVoiceDuration.text = "${voiceSignPlayHelper?.playingDownCountLiveData?.value ?: ""}s"
            } else {
                binding.tvVoiceDuration.text = "${contentBean?.voiceSignDuration ?: ""}s"
            }
        } else {
            binding.groupVoiceSign.gone()
        }


        refreshCallBtnVisible()

    }
    /**
     * 重置语音签名播放状态UI
     * @param isPlaying true 播放中
     */
    private fun updateVoiceSignPlayStateUi(isPlaying: Boolean) {
        if (isPlaying) {
            binding.lottieView.repeatCount = ValueAnimator.INFINITE
            binding.lottieView.playAnimation()
            binding.ivPlay.setImageResource(R.drawable.message_call_voice_card_pause_ic)
        } else {
            binding.lottieView.cancelAnimation()
            binding.lottieView.progress = 0f
            binding.ivPlay.setImageResource(R.drawable.message_call_voice_card_play_ic)
            binding.tvVoiceDuration.text = "${contentBean?.voiceSignDuration ?: ""}s"
        }
    }

    /**
     * 更新拨打按钮状态状态
     */
    private fun refreshCallBtnVisible() {
        val isExpired = ((contentBean?.expireTime ?: 0) - ServerTime.currentTimeMillis() / 1000) < 0
        if (isExpired || contentBean?.hasValidCard == 0) {
            binding.tvStartCall.gone()
        } else {
            binding.tvStartCall.visible()
        }
    }


    override fun onViewAttachedToWindow() {
        super.onViewAttachedToWindow()
        mainScope = MainScope()
        voiceSignPlayHelper?.trackCardExpose(contentBean)
        //过期的卡片无需更新
        if (contentBean != null && contentBean?.isExpired() == false) {
            requestHasVoiceCard()
        }
        if (contentBean?.hasVoiceSign() == true) {
            val lifecycleOwner = lifecycleOwner ?: return
            voiceSignPlayHelper?.playingItemLiveData?.observe(lifecycleOwner,playingItemObserver)
        }

    }

    override fun onViewDetachedFromWindow() {
        super.onViewDetachedFromWindow()
        mainScope?.cancel()
        if (contentBean?.hasVoiceSign() == true) {
            voiceSignPlayHelper?.playingItemLiveData?.removeObserver(playingItemObserver)
            voiceSignPlayHelper?.playingDownCountLiveData?.removeObserver(playCountDownObserver)
        }
    }

    override fun onDestroy(owner: LifecycleOwner?) {
        super.onDestroy(owner)
        if (binding.lottieView.isAnimating) {
            binding.lottieView.cancelAnimation()
        }
    }

    /**
     * 设置播放中倒计时订阅
     * @param needObserve true 订阅 false 取消定于
     */
    private fun observePlayingCountDown(needObserve: Boolean) {
        val lifecycleOwner = lifecycleOwner ?: return
        if (needObserve) {
            voiceSignPlayHelper?.playingDownCountLiveData?.observe(
                lifecycleOwner,
                playCountDownObserver
            )
        } else {
            voiceSignPlayHelper?.playingDownCountLiveData?.removeObserver(playCountDownObserver)
        }
    }

    fun updateState(hasVoiceCard: Boolean) {
        updateNewState(mMessageContent, hasVoiceCard)
    }

    /**
     *  请求是否有可用的语音通话卡
     */
    private fun requestHasVoiceCard() {
        //防止短时高频请求
        if (System.currentTimeMillis() - lastRequestTime < 2000) {
            Loger.d(TAG, "requestHasVoiceCard 太频繁了")
            return
        }
        lastRequestTime = System.currentTimeMillis()
        mainScope?.launchHttp({
            val bean = CallPinsRepository.checkCallCardCanUse().awaitNullable()
            updateNewState(mMessageContent, bean?.isEffective() ?: false)
        }, {
            Loger.i(TAG, "检查有无语音通话卡接口请求失败", it.responseStatus)
            true
        })
    }

    private fun updateNewState(messageContent: HBMessageContentDO?, newState: Boolean) {
        messageContent ?: return
        if (messageContent.contentType != ImTypeConstants.MESSAGE_CALL_VOICE_CALL_CARD) {
            return
        }
        var cardBean = messageContent.hbMessageContentBeanDO as? MessageChatCallVoiceCallCardMsgBean

        if (cardBean?.isExpired() == true) {
            return
        }
        if (cardBean?.hasValidCard() == newState) {
            return
        }
        //更新内存的中的状态
        cardBean?.hasValidCard = if (newState) 1 else 0

        val messageBean = messageContent.clone() as HBMessageContentDO
        cardBean = cardBean?.clone() as MessageChatCallVoiceCallCardMsgBean
        messageBean.hbMessageContentBeanDO = cardBean
        messageBean.msgContent = XJson.toJson(cardBean)
        //更新数据库，但不需要直接更新会话 直接更新UI就行
        HBChat.getDBProvider().insertMessageContent(messageBean)
        //HBChat.getPrivateChatProvider().updateMessage(messageBean)
        refreshCallBtnVisible()
    }


}