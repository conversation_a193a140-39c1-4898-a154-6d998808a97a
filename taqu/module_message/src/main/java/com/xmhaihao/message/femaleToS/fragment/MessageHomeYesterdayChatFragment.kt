package com.xmhaihao.message.femaleToS.fragment

import android.os.Bundle
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import cn.taqu.lib.base.model.BaseModel
import com.xmhaibao.message.api.bean.MessageAppConfigBean
import com.xmhaihao.message.R
import com.xmhaihao.message.databinding.MessageEmptyViewBinding
import com.xmhaihao.message.databinding.MessageHomeYesterdayChatFragmentBinding
import com.xmhaihao.message.femaleToS.holder.MessageHomeYesterdayChatViewHolder
import com.xmhaihao.message.femaleToS.viewmodel.MessageFemaleToSViewModel
import com.xmhaihao.message.viewModel.MessageHomeShareViewModel
import hb.common.xstatic.fragment.BaseMVVMFragment
import hb.message.db.entity.HBMessageConversationDO
import hb.utils.CollectionUtils
import hb.utils.StringUtils
import hb.xadapter.XBaseAdapter
import hb.xstatic.core.skin.event.XSkinEventBean
import hb.xstatic.mvvm.XViewModelProviders
import hb.xthread.XThreadPool

/**
 * 新女转S-昨日聊过fragment
 *
 * <AUTHOR>
 * @date 2023-06-25
 */
class MessageHomeYesterdayChatFragment : BaseMVVMFragment<MessageFemaleToSViewModel?>() {

    companion object {
        @JvmStatic
        val instance: MessageHomeYesterdayChatFragment
            get() = MessageHomeYesterdayChatFragment()
    }

    /**
     * 适配器
     */
    private var mAdapter: XBaseAdapter? = null

    /**
     * 会话列表的vm
     */
//    private var mConversationViewModel: MessageConversationViewModel? = null

    /**
     * ShareViewModel
     */
    private var mHomeShareViewModel: MessageHomeShareViewModel? = null

    /**
     * binding
     */
    private lateinit var mBinding: MessageHomeYesterdayChatFragmentBinding

    /**
     * 列表布局管理
     */
    private var mLinearLayoutManager: LinearLayoutManager? = null

    /**
     * 当前聊天列表
     */
    private var mYesterdayChatList = ArrayList<HBMessageConversationDO?>()

    /**
     * 空页面
     */
    private lateinit var mEmptyViewBinding: MessageEmptyViewBinding

    override fun onCreateContentView(): Any {
        mBinding = MessageHomeYesterdayChatFragmentBinding.inflate(this.layoutInflater)
        return mBinding.root
    }

    override fun initViews() {
        super.initViews()
        BaseModel.addRemarkListener(this, true)
    }

    /**
     * 初始化空页面
     */
    private fun initEmptyView() {
        mEmptyViewBinding = MessageEmptyViewBinding.inflate(this.layoutInflater)
        mEmptyViewBinding.tvTitle.text = "昨天还没有和小哥哥聊天哦"
        mEmptyViewBinding.tvTitle.setTextColor(ContextCompat.getColor(mEmptyViewBinding.tvTitle.context, hb.xstyle.R.color.TH_Gray990))
        mEmptyViewBinding.ivEmpty.setImageResource(R.drawable.message_female_to_s_yesterday_empty_ic)
        setEmptyView(mEmptyViewBinding.root, FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT))
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
//        mConversationViewModel = XViewModelProviders.getViewModel(getActivity(), MessageConversationViewModel::class.java)
        mHomeShareViewModel = XViewModelProviders.getViewModel(activity, MessageHomeShareViewModel::class.java)
        mHomeShareViewModel?.yesterdayFragmentPaddingTopLiveData?.observe(this) { paddingTop ->
            mBinding.root.setPadding(mBinding.root.paddingLeft, paddingTop, mBinding.root.paddingRight, mBinding.root.paddingBottom)
        }
    }

    override fun initCompleted() {
        super.initCompleted()
        baseViewModel?.chatData?.observe(this) {
            it?.run {
                initAdapter(this)
            }
        }
        baseViewModel?.getYesterdayChatList()
    }

    override fun onResume() {
        super.onResume()
        if (isVisible && isContentViewCreated) {
            refreshData()
        }
    }

    /**
     * 初始化适配器
     *
     * @param list 数据
     */
    private fun initAdapter(list: ArrayList<HBMessageConversationDO?>) {
        if (CollectionUtils.isEmpty(list)) {
            if (::mEmptyViewBinding.isInitialized) {
                showEmptyView()
            } else {
                initEmptyView()
            }
            return
        }
        mYesterdayChatList = list
        if (mLinearLayoutManager == null) {
            mLinearLayoutManager = LinearLayoutManager(context)
            mBinding.rvChat.layoutManager = mLinearLayoutManager
            mBinding.rvChat.itemAnimator = null
        }
        if (list.isNotEmpty() && mAdapter == null) {
            mAdapter = XBaseAdapter(context)
            mAdapter?.register(
                HBMessageConversationDO::class.java,
                MessageHomeYesterdayChatViewHolder::class.java
            )
        }
        mBinding.rvChat.adapter = mAdapter
        mAdapter?.run {
            this.items = list
        }
    }

    /**
     * 更新当前会话
     */
    fun updateCurrentItem(conversationDO: HBMessageConversationDO?) {
        if (isContentViewCreated){
            XThreadPool.Main().execute {
                var position = -1
                mYesterdayChatList.forEach {
                    if (it is HBMessageConversationDO) {
                        if (StringUtils.equalsIgnoreCase(it.targetId, conversationDO?.targetId)) {
                            mAdapter?.items?.indexOf(it)?.run {
                                position = this
                            }
                            return@forEach
                        }
                    }
                }
                if (position >= 0) {
                    mYesterdayChatList[position] = conversationDO
                    mAdapter?.notifyItemChanged(position)
                }
            }
        }
    }

    override fun isApplyEventBus(): Boolean {
        return true
    }

    override fun isApplyLazyLoad(): Boolean {
        return true
    }

    /**
     * 换肤事件监听
     *
     * @param skinBean [XSkinEventBean]
     */
    fun onEventMainThread(skinBean: XSkinEventBean?) {
        if (::mEmptyViewBinding.isInitialized && isContentViewCreated) {
            mEmptyViewBinding.tvTitle.setTextColor(ContextCompat.getColor(mEmptyViewBinding.tvTitle.context, hb.xstyle.R.color.TH_Gray990))
        }
    }

    /**
     * 刷新数据
     */
    fun refreshData() {
        if (isContentViewCreated) {
            baseViewModel?.getYesterdayChatList()
        }
    }

}