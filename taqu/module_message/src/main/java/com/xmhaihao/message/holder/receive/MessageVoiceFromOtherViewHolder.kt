package com.xmhaihao.message.holder.receive

import android.view.View
import android.view.View.GONE
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import cn.taqu.lib.base.bean.BaseDressInfo
import com.xmhaihao.message.R
import com.xmhaibao.hbchat.bean.bean.MessageVoiceItemBean
import com.xmhaihao.message.databinding.MessageVoiceFromOtherItemBinding
import com.xmhaihao.message.holder.MessageVoiceBaseViewHolder
import com.xmhaihao.message.intf.SimpleMessageOperateClickListener
import com.xmhaihao.message.utils.MessagePriceUtils
import com.xmhaihao.message.widget.operate.MessageOperateBasePopWindow
import com.xmhaihao.message.widget.operate.MessageOperateLauncher
import com.xmhaihao.message.widget.operate.MessageOperatePopWindow
import com.xmhaihao.message.widget.voice.MessageVoiceAnimationView
import hb.message.db.entity.HBMessageContentDO
import hb.message.intf.HBMessageChatType
import hb.skin.support.SkinCompatManager
import hb.utils.ColorUtils
import hb.utils.SizeUtils
import hb.utils.StringUtils
import hb.ximage.fresco.BaseDraweeView

/**
 * 语音发送viewHolder
 *
 * <AUTHOR>
 * @date 2020-11-17
 */
class MessageVoiceFromOtherViewHolder(parent: ViewGroup) : MessageVoiceBaseViewHolder(parent, R.layout.message_voice_from_other_item) {

    var mBinding = MessageVoiceFromOtherItemBinding.bind(itemView)

    /**
     * 当前语音气泡颜色
     */
    var mVoiceBubbleColor: String? = ""

    init {
        mBinding.clVoice.setOnLongClickListener { v ->
            showMessageOperatePopWindow(v)
            false
        }
    }

    private fun showMessageOperatePopWindow(v: View) {
        if (mMessageChatType != HBMessageChatType.TYPE_GROUP) {
            MessageOperateLauncher.showPop(v, object : SimpleMessageOperateClickListener() {},
                MessageOperateBasePopWindow.TEXT_REPLY,
                MessageOperateBasePopWindow.TEXT_DELETE,
                MessageOperateBasePopWindow.TEXT_REPORT
            )
        }
    }

    override fun onBindViewHolder(messageContent: HBMessageContentDO?, messageVoiceItemBean: MessageVoiceItemBean?) {
        super.onBindViewHolder(messageContent, messageVoiceItemBean)
        messageVoiceItemBean?.run {
            /*
             * 动态设置语音消息宽度
             */
            val layoutParams = mBinding.clVoice.layoutParams as ViewGroup.LayoutParams
            layoutParams.width = SizeUtils.dp2px(getVolumeTotalWidth().toFloat()) + SizeUtils.dp2px(
                13f
            )
            //设置语音风险
            mBinding.tvVoiceMessageInvalid.visibility = if (isRiskVoice) View.VISIBLE else GONE
            mBinding.ivOtherVoice.visibility = if (isRiskVoice) View.VISIBLE else GONE
            mBinding.ivUnread.visibility = if (!isVoiceRead && !isRiskVoice) View.VISIBLE else GONE

            if (mGetChatInfoCallBack!=null) {
                //是否需要显示趣豆退回
                MessagePriceUtils.setReceiveVoiceMessagePayStatus(mBinding.tvMessagePrice, this,
                        checkShowAuthentication(mBinding.tvMessagePrice.context),mGetChatInfoCallBack.messageChatForumBean
                )
            }

            //是否显示subContent文案
            showSubContent(mBinding.tvMessagePrice, true, this)

            //显示subContent,不显示违规提醒
            if (mBinding.tvMessagePrice.visibility == View.VISIBLE) {
                mBinding.tvVoiceMessageInvalid.visibility = GONE
            }
        }
        mBinding.clVoice.tag = messageContent
        setAvatarName(messageContent)
        handleBubble(messageContent)
    }

    override fun setBubbleVoiceIv() {
        if (getDressInfo() == null) {
            return
        }
        val voiceBubbleColor = getVoiceBubbleColor()
        if(StringUtils.isEmpty(voiceBubbleColor)){
            return
        }
        val drawable = mBinding.ivOtherVoice.drawable
        drawable?.setTint(ColorUtils.getColor(voiceBubbleColor))
        mBinding.ivOtherVoice.setImageDrawable(drawable)
        mBinding.tvVoiceSeconds.setTextColor(ColorUtils.getColor(voiceBubbleColor))
    }

    override fun getVoiceBubbleColor(dressInfo: BaseDressInfo?): String? {
        return dressInfo?.otherBubbleTextColor
    }

    override fun getVoiceBubbleColor(): String? {
        if (StringUtils.isNotEmpty(mVoiceBubbleColor)) {
            return mVoiceBubbleColor
        }
        try {
            val voiceColorTemp = getVoiceBubbleColor(getDressInfo()!!)
            if (!voiceColorTemp.isNullOrEmpty()) {
                if (!voiceColorTemp.startsWith("#")) {
                    mVoiceBubbleColor = "#$voiceColorTemp"
                }
                mVoiceBubbleColor = voiceColorTemp
                return mVoiceBubbleColor
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    override fun getTextContentBgReSourceWithoutBubble(): Int {
        if (!SkinCompatManager.getInstance().isSkinDark) {
            return R.drawable.message_chat_rel_content_left_bg_ic
        }
        return R.drawable.message_chat_rel_content_left_bg_night_ic
    }

    override fun getBubbleDressPic(dressInfo: BaseDressInfo): String? {
        return dressInfo.otherBubbleDressPic
    }

    override fun getClVoiceView(): ConstraintLayout {
        return mBinding.clVoice
    }

    override fun getTvVoiceSeconds(): TextView {
        return mBinding.tvVoiceSeconds
    }

    override fun getLottieVoicePlay(): MessageVoiceAnimationView {
        return mBinding.lottieVoicePlay
    }

    override fun getIvUnRead(): BaseDraweeView? {
        return mBinding.ivUnread
    }

    /**
     * 覆盖viewHolder 的所有点击事件
     */
    fun enableSelect(l: Function0<Unit>) {
        mBinding.cbReportEvidence.visibility = View.VISIBLE
        mBinding.clVoice.setOnClickListener { l.invoke() }
        mBinding.imgHeadDress.setOnClickListener { l.invoke() }
        mBinding.cbReportEvidence.setOnClickListener { l.invoke() }
    }

    /**
     * 设置checkBox的点击状态
     */
    fun setSelect(select: Boolean) {
        mBinding.cbReportEvidence.isChecked = select
    }

}