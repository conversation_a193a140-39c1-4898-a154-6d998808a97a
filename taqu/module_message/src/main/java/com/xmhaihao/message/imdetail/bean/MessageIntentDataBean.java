package com.xmhaihao.message.imdetail.bean;

import com.xmhaibao.message.api.bean.MessageChatLauncherExtraDataBean;
import com.xmhaibao.message.api.constants.MessageChatOperationValue;

import hb.message.bean.HBMessageScrollBean;

/**
 *  私信详情的外部传参数的Intent数据
 * <AUTHOR>
 * @date 2023-12-25
 */
public class MessageIntentDataBean {
    /**
     * 不为空时，一进入页面需要定位拉取历史记录定位到这条消息所在位置
     */
    private HBMessageScrollBean scrollBean;
    /**
     * 进入私信后的操作：
     * 比如：mOperation = "loveDiary" 打开恋爱日记弹窗
     * "showGift"
     */
    private @MessageChatOperationValue String operation;
    private String preDefineGid;
    private String preDefineSkuid;
    private boolean showInput;
    private String giftName;
    private String giftIcon;
    /**
     * 私信来源
     */
    private int chatSource;

    private String loveDiaryIndex;

    /**
     * 进入私信的参数
     */
    private MessageChatLauncherExtraDataBean extraData;

    public HBMessageScrollBean getScrollBean() {
        return scrollBean;
    }

    public void setScrollBean(HBMessageScrollBean scrollBean) {
        this.scrollBean = scrollBean;
    }

    public @MessageChatOperationValue String getOperation() {
        return operation;
    }

    public int getChatSource() {
        return chatSource;
    }

    public void setChatSource(int chatSource) {
        this.chatSource = chatSource;
    }

    public void setOperation(@MessageChatOperationValue String operation) {
        this.operation = operation;
    }

    public String getPreDefineGid() {
        return preDefineGid;
    }

    public void setPreDefineGid(String preDefineGid) {
        this.preDefineGid = preDefineGid;
    }

    public String getPreDefineSkuid() {
        return preDefineSkuid;
    }

    public void setPreDefineSkuid(String preDefineSkuid) {
        this.preDefineSkuid = preDefineSkuid;
    }

    public boolean isShowInput() {
        return showInput;
    }

    public void setShowInput(boolean showInput) {
        this.showInput = showInput;
    }

    public String getGiftName() {
        return giftName;
    }

    public void setGiftName(String giftName) {
        this.giftName = giftName;
    }

    public String getGiftIcon() {
        return giftIcon;
    }

    public void setGiftIcon(String giftIcon) {
        this.giftIcon = giftIcon;
    }

    public String getLoveDiaryIndex() {
        return loveDiaryIndex;
    }

    public void setLoveDiaryIndex(String loveDiaryIndex) {
        this.loveDiaryIndex = loveDiaryIndex;
    }

    public MessageChatLauncherExtraDataBean getExtraData() {
        return extraData;
    }

    public void setExtraData(MessageChatLauncherExtraDataBean extraData) {
        this.extraData = extraData;
    }
}
