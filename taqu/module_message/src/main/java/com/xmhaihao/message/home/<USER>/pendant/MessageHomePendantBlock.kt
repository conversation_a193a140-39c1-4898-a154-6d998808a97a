package com.xmhaihao.message.home.block.pendant

import android.view.View
import hb.xblockframework.framework.base.UIBlock
import hb.xblockframework.framework.join.IBlockContext

/**
 * 消息tab页挂件block
 * <AUTHOR>
 * @date 2025-06-11
 */
class MessageHomePendantBlock(
    blockContext: IBlockContext,
) : UIBlock(blockContext), IMessageHomePendantLogicService {
    companion object {
        const val TAG = "MessageHomeFloatingBlock"
    }

    override fun onCreateContentView(parent: View?): Any {
        return View(context)
    }

    override fun bindBlockService(): Class<*> {
        return IMessageHomePendantLogicService::class.java
    }

}