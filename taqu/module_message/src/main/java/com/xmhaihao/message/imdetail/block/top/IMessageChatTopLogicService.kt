package com.xmhaihao.message.imdetail.block.top

import android.view.MotionEvent
import android.widget.RelativeLayout
import cn.taqu.lib.base.bean.MessageAccountInfo
import com.xmhaibao.message.api.bean.MessageTopicAssistantCardBean
import com.xmhaihao.message.bean.MessageChatForumBean


/**
 * 私信详情页顶部模块Service
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
interface IMessageChatTopLogicService {
    fun setAudioSensorPlayer()
    fun showReplyTip(showInfoCard: Boolean)
    fun requestShowCardInfo()
    fun setCardHide()
    fun onShowCardInfoView()
    fun getCardInfoHeight(): Int
    fun showDeepMsgProgressViewIfNeed()
    fun showDeepMsgGuideViewIfNeed()
    fun hideDeepMsgProgressView()
    fun updateDeepMsgProgressMargin()
    fun setTopAvatarAndNickName()
    fun handleHeartbeatTouch(ev: MotionEvent)
    fun isShowNotifyGuide(): Boolean
    fun requestHeartbeatInfo(fromHeartbeatMomentIm: Boolean?)
    fun isShowHeartbeat(): Boolean
    fun setMengXinTagVisible(isVisible: Boolean, accountInfo: MessageAccountInfo)
    fun updatePaiPaiView(messageChatForumBean: MessageChatForumBean)
}