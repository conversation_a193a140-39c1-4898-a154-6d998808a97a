package com.xmhaihao.message.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

import hb.convenientbanner.holder.Holder;
import com.xmhaihao.message.R;
import com.xmhaihao.message.bean.AbsMessageHomeAdsBean;
import com.xmhaihao.message.bean.MessageLocalAdsBean;

import hb.common.data.AccountHelper;
import hb.utils.ScreenUtils;
import hb.utils.SizeUtils;
import hb.ximage.fresco.BaseDraweeView;

/**
 * <AUTHOR>
 * @desc 广告轮播
 * @date 2020-07-31 08:31
 **/
@SuppressLint("ViewConstructor")
public class MessageAdsBannerView extends Holder<AbsMessageHomeAdsBean> implements View.OnClickListener {

    /**
     * 轮播图片
     */
    private BaseDraweeView mBgImage;

    private TextView mTvCloseBanner;

    /**
     * 轮播到当前view
     */
    private AbsMessageHomeAdsBean mCurrentAdsBean;

    /**
     * 点击关闭banner监听
     */
    private final OnCloseBannerClickListener mOnCloseBannerClickListener;

    /**
     * 构造方法
     *
     * @param context 上下文
     */
    public MessageAdsBannerView(@NonNull ViewGroup parent, OnCloseBannerClickListener onCloseBannerClickListener) {
        super(parent, R.layout.message_home_banner_layout);
        mOnCloseBannerClickListener = onCloseBannerClickListener;
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.ivBanner) {
            this.mCurrentAdsBean.jumpTo(itemView.getContext());
        } else if (v.getId() == R.id.tvCloseBanner && mOnCloseBannerClickListener != null) {
            mOnCloseBannerClickListener.onCloseBannerClick(v);
        }
    }

    @Override
    public void initView(View itemView) {
        mBgImage = itemView.findViewById(R.id.ivBanner);
        mBgImage.setOnClickListener(this);

        int width = ScreenUtils.getScreenWidth()- SizeUtils.dp2px(32);
        int height = 56 * width / 343;
        ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) mBgImage.getLayoutParams();
        params.height = height;

        mTvCloseBanner = itemView.findViewById(R.id.tvCloseBanner);
        mTvCloseBanner.setOnClickListener(this);
        itemView.setOnClickListener(this);
    }

    @Override
    public void bindData(AbsMessageHomeAdsBean data, int position, int pageSize) {
        this.mCurrentAdsBean = data;
        data.loadImage(mBgImage);
        if (data instanceof MessageLocalAdsBean && AccountHelper.isMale() &&
                ((MessageLocalAdsBean) data).getAdsType() == MessageLocalAdsBean.LocalAdsType.NOTIFY) {
            mTvCloseBanner.setVisibility(View.VISIBLE);
        } else {
            mTvCloseBanner.setVisibility(View.GONE);
        }
    }

    /**
     * banner点击关闭监听
     */
    public interface OnCloseBannerClickListener {
        /**
         * banner点击关闭
         *
         * @param view View
         */
        void onCloseBannerClick(View view);
    }
}
