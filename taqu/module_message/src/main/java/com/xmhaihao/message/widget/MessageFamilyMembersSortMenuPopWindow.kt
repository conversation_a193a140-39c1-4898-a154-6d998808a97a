package com.xmhaihao.message.widget

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.ScaleAnimation
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.PopupWindow
import com.xmhaihao.message.R
import com.xmhaihao.message.databinding.MessageFamilyMembersSortMenuPopWindowBinding
import hb.utils.BarUtils
import hb.utils.SizeUtils


/**
 * 家族成员排序弹框
 *
 * <AUTHOR>
 * @date 2021-04-13
 */
class MessageFamilyMembersSortMenuPopWindow(context: Context) : PopupWindow(context), View.OnClickListener {

    private val mMenuView: View
    private val mClMessageMenu: LinearLayout
    private var mOnMemberSortListener: OnMemberSortMenuListener? = null
    private var mBinding = MessageFamilyMembersSortMenuPopWindowBinding.inflate(LayoutInflater.from(context))

    init {
        mMenuView = mBinding.root
        mMenuView.setOnClickListener { dismiss() }
        mBinding.tvAllContribute.setOnClickListener(this)
        mBinding.tvDefaultSort.setOnClickListener(this)
        mClMessageMenu = mMenuView.findViewById(R.id.clMessageMenu)

        this.contentView = mMenuView
        // 设置SelectPicPopupWindow弹出窗体的宽
        this.width = ViewGroup.LayoutParams.MATCH_PARENT
        // 设置SelectPicPopupWindow弹出窗体的高
        this.height = ViewGroup.LayoutParams.MATCH_PARENT
        this.animationStyle = cn.taqu.lib.base.R.style.AnimationPopup
        // 实例化一个ColorDrawable颜色为半透明
        val dw = ColorDrawable(0x2B000000)
        setBackgroundDrawable(dw)
        isClippingEnabled = false
        isOutsideTouchable = true
        setOnDismissListener {
            mClMessageMenu?.let {
                val animation = mClMessageMenu.animation;
                animation?.cancel()
            }
        }
    }

    override fun showAtLocation(parent: View?, gravity: Int, x: Int, y: Int) {
        parent?.let {
            mClMessageMenu.let {
                val location = IntArray(2)
                parent.getLocationOnScreen(location)
                val lp: FrameLayout.LayoutParams = mClMessageMenu.layoutParams as FrameLayout.LayoutParams;
                lp.topMargin = location[1] + BarUtils.getStatusBarHeight()
                val scaleAnimation = ScaleAnimation(0f, 1f, 0f, 1f,
                        (parent.context.resources.getDimensionPixelSize(R.dimen.message_home_menu_width)
                                - SizeUtils.dp2px(15f)).toFloat(), 0f)
                scaleAnimation.duration = 250
                mClMessageMenu.startAnimation(scaleAnimation)
            }
            super.showAtLocation(parent, gravity, x, y)
        }
    }

    /**
     * 设置点击回调
     * @param onMemberSortMenuListener 监听
     */
    fun setOnMemberSortMenuListener(onMemberSortMenuListener: OnMemberSortMenuListener?) {
        mOnMemberSortListener = onMemberSortMenuListener
    }

    override fun onClick(v: View) {
        dismiss()
        mOnMemberSortListener?.onClick(v)
    }

    interface OnMemberSortMenuListener {
        fun onClick(v: View)
    }

}