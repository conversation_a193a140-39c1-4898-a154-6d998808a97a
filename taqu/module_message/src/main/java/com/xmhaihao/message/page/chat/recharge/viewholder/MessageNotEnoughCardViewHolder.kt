package com.xmhaihao.message.page.chat.recharge.viewholder

import android.view.ViewGroup
import cn.taqu.lib.base.router.ARouterManager
import cn.taqu.lib.base.router.RouterLaunch
import cn.taqu.lib.base.utils.GIORechargePosition
import com.xmhaibao.message.api.utils.MessageChargeSystemHelper.Companion.requestIsTargetGroup
import com.xmhaihao.message.R
import com.xmhaihao.message.databinding.MessageNotEnoughCardViewHolderBinding
import com.xmhaihao.message.dialog.MessageBalanceNotEnoughDialog.Companion.showRechargeDialog
import com.xmhaihao.message.heartbeat.bean.MessageCanOpenRechargeBean
import com.xmhaihao.message.holder.MessageIMBaseViewHolder
import com.xmhaihao.message.page.chat.recharge.bean.MessageNotEnoughCardBean
import com.xmhaihao.message.tracker.MessageBalanceNotEnoughTracker
import hb.message.db.entity.HBMessageContentDO
import hb.utils.ColorUtils
import hb.utils.SpanUtils

/**
 * 私信消息触发余额不足卡片消息
 * [飞书需求](https://o15vj1m4ie.feishu.cn/wiki/PF51weSXxiPe8kkHJ1lc6E59nSd)
 * <AUTHOR>
 * @date 2024-12-17
 */
class MessageNotEnoughCardViewHolder(parent: ViewGroup) :
    MessageIMBaseViewHolder<MessageNotEnoughCardBean>(parent, R.layout.message_not_enough_card_view_holder) {

    val binding by lazy {
        MessageNotEnoughCardViewHolderBinding.bind(itemView).apply {
            ivBg.setImageFromResource(R.drawable.message_delete_all_msg_dialog_bg)
            btnFree.setOnClickListener {
                MessageBalanceNotEnoughTracker.Instance.trackMessageFinishPopClick("", "免费赚币", GIORechargePosition.POSITION_NOT_ENOUGH_CARD_MESSAGE)
                RouterLaunch.dealJumpData(btnFree.context, rechargeBean?.leftRelation)
            }
            btnRecharge.setOnClickListener {
                MessageBalanceNotEnoughTracker.Instance.trackMessageFinishPopClick("", "去充值", GIORechargePosition.POSITION_NOT_ENOUGH_CARD_MESSAGE)
                ARouterManager.tqbeanService().checkReturnUserAndShowDialog(
                    btnRecharge.context,
                    GIORechargePosition.POSITION_NOT_ENOUGH_CARD_MESSAGE
                ) { result ->
                    if (result == false) {
                        requestIsTargetGroup(btnRecharge.context, {
                            showRechargeDialog(btnRecharge.context, GIORechargePosition.POSITION_NOT_ENOUGH_CARD_MESSAGE, MessageCanOpenRechargeBean())
                            null
                        }, false, GIORechargePosition.POSITION_NOT_ENOUGH_CARD_MESSAGE)
                    }
                }
            }
        }

    }

    private var rechargeBean: MessageNotEnoughCardBean? = null

    override fun onBindViewHolder(messageContent: HBMessageContentDO?, messageContentBean: MessageNotEnoughCardBean?) {
        super.onBindViewHolder(messageContent, messageContentBean)
        mMessageContent = messageContent
        rechargeBean = messageContentBean
        messageContentBean?.run {
            binding.tvTitle.text = this.title
            binding.tvContent.text = SpanUtils()
                .append(this.message ?: "")
                .append(this.msgLight ?: "")
                .setForegroundColor(ColorUtils.getColor("#FA3535"))
                .create()
            binding.btnFree.text = this.leftBtnTitle
            binding.tvRecharge.text = this.rightBtnTitle
        }

    }


}