package com.xmhaihao.message.widget;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import hb.message.db.entity.HBMessageContentDO;
import hb.message.interfaces.IMessageChatView;
import hb.message.intf.HBMessageChatViewBinder;
import hb.message.ui.HBMessageUiChatDefaultAdapter;
import hb.message.ui.HBMessageUiChatDefaultItemCallback;

/**
 * 把HBMessageChatHelper的RecyclerView抽离到这里
 * 他趣的私信UI抽离，临时方案，（绑定RV）
 *
 * <AUTHOR>
 * @date 2023-05-10
 */
public class TaquPrivateChatRecyclerView implements IMessageChatView {
    /**
     * 默认的 adapter
     */
    @Nullable
    private ListAdapter mAdapter;

    @Nullable
    private RecyclerView mRecyclerView;

    private Handler mHandler = new Handler(Looper.getMainLooper());

    public TaquPrivateChatRecyclerView(RecyclerView recyclerView, HBMessageChatViewBinder viewBinder) {
        mRecyclerView = recyclerView;
        mAdapter = new HBMessageUiChatDefaultAdapter(new HBMessageUiChatDefaultItemCallback(), viewBinder);
        mRecyclerView.setAdapter(mAdapter);
    }


    @Override
    public void scrollToBottom(boolean smooth) {
        mHandler.removeCallbacksAndMessages(null);
        mHandler.postDelayed(() -> {
            if (mRecyclerView != null) {
                int position = mAdapter.getItemCount() - 1;
                if (position >= 0) {
                    if (smooth) {
                        mRecyclerView.smoothScrollToPosition(position);
                    } else {
                        mRecyclerView.scrollToPosition(position);
                    }
                }
            }
        }, 100);
    }

    @Override
    public void scrollToPosition(int position, boolean smooth) {
        mHandler.removeCallbacksAndMessages(null);
        mHandler.postDelayed(() -> {
            if (mRecyclerView != null) {
                if (position >= 0 && position < mAdapter.getItemCount()) {
                    if (smooth) {
                        mRecyclerView.smoothScrollToPosition(position);
                    } else {
                        mRecyclerView.scrollToPosition(position);
                    }
                }
            }
        }, 100);
    }


    @Override
    public int getLastCompletelyVisibleItemPosition() {
        if (mRecyclerView != null && mRecyclerView.getLayoutManager() != null && mRecyclerView.getLayoutManager() instanceof LinearLayoutManager) {
            LinearLayoutManager layoutManager = (LinearLayoutManager) mRecyclerView.getLayoutManager();
            int lastPos = layoutManager.findLastCompletelyVisibleItemPosition();
            return lastPos;
        }
        return 0;
    }

    @Override
    public void submitList(List<HBMessageContentDO> list) {
        if (mAdapter != null) {
            mAdapter.submitList(list);
        }
    }

    @Override
    public Context getContext() {
        if (mRecyclerView != null) {
            return mRecyclerView.getContext();
        }
        return null;
    }

    public void setEnableLoadMore(boolean loadMoreEnable) {
        if (mAdapter == null) {
            return;
        }
//        if (mAdapter instanceof XBaseAdapter) {
//            ((XBaseAdapter) mAdapter).setLoadMoreable(loadMoreEnable);
//            ((XBaseAdapter) mAdapter).setLoadMoreIdle();
//            mAdapter.notifyDataSetChanged();
//        }
    }

    /**
     * 销毁移除callback
     */
    public void destroy() {
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
        }
        if (mRecyclerView != null) {
            mRecyclerView.setAdapter(null);
            mRecyclerView.setLayoutManager(null);
        }
    }

}
