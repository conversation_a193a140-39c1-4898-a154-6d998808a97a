package com.xmhaihao.message.imdetail.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.Gravity;
import android.view.MotionEvent;
import android.widget.FrameLayout;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.xmhaibao.message.api.bean.MessageChatLauncherExtraDataBean;
import com.xmhaibao.message.api.constants.MessageIntentConstants;
import com.xmhaibao.message.api.event.EventConversationDialogDismiss;
import com.xmhaibao.message.api.router.MessageRouterPath;
import com.xmhaihao.message.R;
import com.xmhaihao.message.imdetail.fragment.MessageChatFragment;
import com.xmhaihao.message.imdetail.intf.MessageChatProvider;
import com.xmhaihao.message.tracker.MessageChatLaunchMonitor;
import com.xmhaihao.message.utils.lottiedownload.MessageBitmapCache;

import cn.taqu.lib.base.interf.AppTrimMemoryCallback;
import cn.taqu.lib.base.startup.ab.StartupAbUtil;
import cn.taqu.lib.base.utils.MemoryPressureManager;
import hb.common.xstatic.activity.BaseActivity;
import hb.message.HBChat;
import hb.message.core.helper.HBMessageChatHelper;
import hb.skin.support.SkinCompatManager;
import hb.utils.BarUtils;
import hb.utils.ScreenUtils;
import hb.xstatic.tools.XEventBus;

/**
 * 新版私信详情页
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Route(path = MessageRouterPath.ChatMessageHalfActivity)
public class MessageChatHalfActivity extends BaseActivity implements AppTrimMemoryCallback {
    @Autowired(name = MessageIntentConstants.INTENT_EXTRA_ACCOUNT_UUID)
    public String mAccountUuid = "";
    @Autowired(name = MessageIntentConstants.INTENT_EXTRA_CONVERSATION_ID)
    public String mConversationId;
    @Autowired(name = MessageIntentConstants.INTENT_CHAT_HALF_DIALOG)
    public boolean mIsHalfDialog = false;

    @Autowired(name = MessageIntentConstants.INTENT_CHAT_EXTRA_DATA_BEAN)
    public MessageChatLauncherExtraDataBean mExtraBean;


    @Override
    protected Object onCreateContentView() {
        MessageChatLaunchMonitor.INSTANCE.startCreateView();
        ARouter.getInstance().inject(this);
        return getRootView();
    }

    @Override
    public void initConfig(Bundle savedInstanceState) {
        super.initConfig(savedInstanceState);
        //显示半透明
        if (mExtraBean != null && mExtraBean.getShowHalfAlpha()) {
            getWindow().setBackgroundDrawable(ContextCompat.getDrawable(this, hb.alive.R.color.black_alpha_60));
        }
        ((HBMessageChatHelper) HBChat.getPrivateChatProvider()).onRelease();
        MessageChatProvider.init(this, this, mAccountUuid, mConversationId,mIsHalfDialog);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }


    @Override
    public void initViews() {
        super.initViews();
        setPageTitle("私信半屏聊天页");
        //设置全屏
        BarUtils.setStatusBarLightMode(this, !SkinCompatManager.getInstance().isSkinDark());
        BarUtils.setFullScreenStatusBarTransparent(this);
        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) getRootView().getLayoutParams();
        params.height = (int) (ScreenUtils.getAppScreenHeight() * 0.66f);
        params.gravity = Gravity.BOTTOM;
        getRootView().setOnClickListener(v -> {
            // 半屏状态下点击关闭页面，如果上一级是私信会话的半屏也一起关闭
            this.finish();
            XEventBus.post(new EventConversationDialogDismiss());
        });
       initFragment();
        // 添加App内存压力回调
        MemoryPressureManager.addAppTrimMemoryCallback(this, this);
    }

    private void initFragment(){
        getRootView().setId(R.id.frameContent);
        MessageChatFragment messageChatFragment = new MessageChatFragment();
        getSupportFragmentManager().beginTransaction()
                .replace(R.id.frameContent, messageChatFragment)
                .commitAllowingStateLoss();
    }
    @Override
    protected boolean isShowToolbar() {
        return false;
    }

    @Override
    protected boolean isApplyEventBus() {
        return true;
    }

    @Override
    public void finish() {
        super.finish();
        if (mExtraBean != null && mExtraBean.getShowHalfAlpha()) {
            overridePendingTransition(cn.taqu.lib.base.R.anim.message_chat_half_dialog_normal, cn.taqu.lib.base.R.anim.message_chat_half_dialog_normal);
        } else {
            overridePendingTransition(cn.taqu.lib.base.R.anim.message_chat_half_dialog_in, cn.taqu.lib.base.R.anim.message_chat_half_dialog_out);
        }
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (MessageChatProvider.get() != null && MessageChatProvider.get().getActivityEventListener() != null) {
            MessageChatProvider.get().getActivityEventListener().dispatchTouchEvent(ev);
        }
        return super.dispatchTouchEvent(ev);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
//        if (MessageChatProvider.getInstance() != null && MessageChatProvider.getInstance().getActivityEventListener() != null) {
//            MessageChatProvider.getInstance().getActivityEventListener().onActivityResult(requestCode,resultCode,data);
//        }
    }

    @Override
    public void onBackPressed() {
        try {
            if (MessageChatProvider.get() != null && MessageChatProvider.get().getActivityEventListener() != null) {
                boolean isIntercept = MessageChatProvider.get().getActivityEventListener().onBackPressed();
                if (isIntercept) {
                    return;
                }
            }
            super.onBackPressed();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void onEventMainThread(EventConversationDialogDismiss event) {
        if (mIsHalfDialog) {
            finish();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        MessageBitmapCache.clean();
        MessageChatProvider.destroy(this);
    }

    @Override
    public void onAppTrimMemory(int level) {
        if (level >= android.content.ComponentCallbacks2.TRIM_MEMORY_BACKGROUND) {
            // 直接清理
            MessageBitmapCache.clean();
        } else if (level >= android.content.ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN || level == android.content.ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL) {
            // 减少一半缓存
            MessageBitmapCache.trimToHalfSize();
        }
    }

    @Override
    public void onAppLowMemory() {
        MessageBitmapCache.clean();
    }

    @Override
    public boolean isApplySkin() {
        return SkinCompatManager.getInstance().isEnableSkin();
    }

    @Override
    public void applySkin() {
        super.applySkin();
        BarUtils.setStatusBarLightMode(this, !SkinCompatManager.getInstance().isSkinDark());
    }
}
