package com.xmhaihao.message.intf

import cn.taqu.lib.okhttp.model.IResponseInfo
import com.xmhaihao.message.bean.MessageFamilyChatOpenRedPacketBean

/**
 * description:开红包回调
 *
 * <AUTHOR>
 * @date 2021/09/16 11:25
 */
interface OnMessageFamilyRedPacketOpenCallback {


    /**
     * 成功回调
     */
    fun onOpenRedPacketSuccess(obj: MessageFamilyChatOpenRedPacketBean?)


    /**
     * 开失败
     */
    fun onOpenRedPacketFail(response: IResponseInfo<*>)
}