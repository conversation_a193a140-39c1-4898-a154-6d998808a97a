package com.xmhaihao.message.bean;

import com.google.gson.annotations.SerializedName;

import java.util.List;

import cn.taqu.lib.okhttp.model.IDoExtra;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import hb.common.data.AccountHelper;
import hb.common.helper.HostHelper;
import hb.utils.CollectionUtils;
import hb.utils.StringUtils;
import hb.utils.WebpUtils;
import hb.xstatic.mvp.interf.IListBean;

/**
 * Created by Yangjm on 2020/7/8 14:00
 * <p>
 * 红包领取详情
 */
public class MessageRedPacketReceiveDetailBean implements IListBean, IDoExtra {


    /**
     * tqbean : 10
     * send_name : 昵称
     * icon : /taqu_ios_avatar_1472009082.jpg
     * is_finish : 1
     * content : 已领取10/20个,共100/200趣豆
     * list : [{"account_uuid":"l0hlznf3phm","tqbean":"20","nickname":"beer2","avatar":"/taqu_ios_avatar_1472009082.jpg","sex":"1","time":"12323"},{"account_uuid":"l0hlznf3phm","tqbean":"10","nickname":"beer2","avatar":"/taqu_ios_avatar_1472009082.jpg","sex":"1","time":"12323"}]
     */

    private String tqbean;
    @SerializedName("send_name")
    private String sendName;
    @SerializedName("send_uuid")
    private String sendUuid;
    private String icon;
    /**
     * 1:结束  0：未结束
     */
    @SerializedName("is_finish")
    private String isFinish;
    private String content;
    /**
     * 祝福语
     */
    private String word;

    @SerializedName("is_mine")
    private String isMine;
    private List<ListBean> list;
    /**
     * 总的趣豆金额（目前用于招募红包）
     */
    @SerializedName("total_tqbean")
    private String totalTqBean;
    /**
     * 家族头像（目前用于家族广场招募红包）
     */
    private String familyIcon;
    @SerializedName("received_num")
    private String receivedNum;
    @SerializedName("total_num")
    private String totalNum;

    /**
     * 红包封面(长条形用于领取详情)
     */
    @SerializedName("red_packet_detail_cover")
    private String redPacketCoverLong;


    /**
     * 用于礼物红包  礼物icon
     */
    @SerializedName("gift_url")
    private String giftUrl;

    /**
     * 用于礼物红包  礼物价值
     */
    @SerializedName("gift_bean")
    private String giftBean;

    /**
     * 用于礼物红包
     */
    @SerializedName("gift_name")
    private String giftName;

    /**
     * 用于礼物红包   查看我的收益的relation
     */
    private String relation;

    public String getRelation() {
        return relation;
    }

    /**
     * 用于礼物红包  礼物icon
     */
    public String getGiftUrl() {
        return giftUrl;
    }

    /**
     * 用于礼物红包  礼物价值
     */
    public String getGiftBean() {
        return giftBean;
    }

    /**
     * 用于礼物红包
     */
    public String getGiftName() {
        return giftName;
    }

    public String getRedPacketCoverLong() {
        return redPacketCoverLong;
    }

    public void setRedPacketCoverLong(String redPacketCover) {
        this.redPacketCoverLong = redPacketCover;
    }

    public boolean isMine() {
        return "1".equals(isMine);
    }

    public void setIsMine(String isMine) {
        this.isMine = isMine;
    }

    public String getSendUuid() {
        return sendUuid;
    }

    public void setSendUuid(String sendUuid) {
        this.sendUuid = sendUuid;
    }

    public String getGreet() {
        return word;
    }

    public void setGreet(String greet) {
        this.word = greet;
    }

    public String getTqbean() {
        return tqbean;
    }

    public void setTqbean(String tqbean) {
        this.tqbean = tqbean;
    }


    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getSendName() {
        return sendName;
    }

    public void setSendName(String sendName) {
        this.sendName = sendName;
    }

    public boolean isFinish() {
        return "1".equals(isFinish);
    }

    public void setIsFinish(String isFinish) {
        this.isFinish = isFinish;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTotalTqBean() {
        return totalTqBean;
    }

    public void setTotalTqBean(String totalTqBean) {
        this.totalTqBean = totalTqBean;
    }

    public String getFamilyIcon() {
        return familyIcon;
    }

    public void setFamilyIcon(String familyIcon) {
        this.familyIcon = familyIcon;
    }

    /**
     * 红包是否全部领完
     *
     * @return
     */
    public boolean isGetAllOver() {
        return receivedNum.equals(totalNum);
    }

    @Override
    public List<ListBean> getList() {
        return list;
    }

    public void setList(List<ListBean> list) {
        this.list = list;
    }

    @Override
    public void doExtra(IResponseInfo response) {
        setFamilyIcon(WebpUtils.getWebpUrl_3_1(getIcon(), HostHelper.getInstance().getHostImg()));
        setIcon(WebpUtils.getWebpUrl_3_1(getIcon(), HostHelper.getInstance().getHostAvatar()));
        setRedPacketCoverLong(WebpUtils.getWebpUrlFull(getRedPacketCoverLong(), HostHelper.getInstance().getHostImg()));
        if (CollectionUtils.isNotEmpty(getList())) {
            for (ListBean bean : getList()) {
                if (bean != null) {
                    bean.doExtra(response);
                }
            }
        }
        if (StringUtils.isNotEmpty(giftUrl)) {
            giftUrl = WebpUtils.getWebpUrl_4_1(giftUrl, HostHelper.getInstance().getHostImg());
        }
    }

    public static class ListBean implements IDoExtra {
        /**
         * account_uuid : l0hlznf3phm
         * tqbean : 20
         * nickname : beer2
         * avatar : /taqu_ios_avatar_1472009082.jpg
         * sex : 1
         * time : 12323
         */

        @SerializedName("account_uuid")
        private String accountUuid;
        private String tqbean = "";
        private String nickname = "";
        private String avatar;
        private String sex = AccountHelper.SexType.FEMALE;
        private String time;
        @SerializedName("is_luck")
        private String isLuck;
        /**
         * 用于礼物红包  礼物icon
         */
        @SerializedName("gift_url")
        private String giftUrl;
        /**
         * 用于礼物红包  礼物name
         */
        @SerializedName("gift_name")
        private String giftName;
        /**
         * 用于礼物红包  礼物价值
         */
        @SerializedName("gift_bean")
        private String giftBean;

        /**
         * 用于礼物红包
         *
         * @return 礼物价值
         */
        public String getGiftBean() {
            return giftBean;
        }

        /**
         * 用于礼物红包
         *
         * @return 礼物礼物icon
         */
        public String getGiftUrl() {
            return giftUrl;
        }

        /**
         * 用于礼物红包
         *
         * @return 礼物name
         */
        public String getGiftName() {
            return giftName;
        }

        public boolean isLuck() {
            return "1".equals(isLuck);
        }

        public void setIsLuck(String isLuck) {
            this.isLuck = isLuck;
        }

        public String getAccountUuid() {
            return accountUuid;
        }

        public void setAccountUuid(String accountUuid) {
            this.accountUuid = accountUuid;
        }

        public String getTqbean() {
            return tqbean;
        }

        public void setTqbean(String tqbean) {
            this.tqbean = tqbean;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getSex() {
            return sex;
        }

        public void setSex(String sex) {
            this.sex = sex;
        }

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }

        @Override
        public void doExtra(IResponseInfo response) {
            setAvatar(WebpUtils.getWebpUrl_4_1(getAvatar(), HostHelper.getInstance().getHostAvatar()));
            if (StringUtils.isNotEmpty(giftUrl)) {
                giftUrl = WebpUtils.getWebpUrl_4_1(giftUrl, HostHelper.getInstance().getHostResource());
            }
        }
    }
}
