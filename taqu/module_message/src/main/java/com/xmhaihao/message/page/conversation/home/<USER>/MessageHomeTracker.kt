package com.xmhaihao.message.page.conversation.home.tracker

import hb.xtracker.JsonObjectFactory
import hb.xtracker.XTrackBase

/**
 * 消息tab页面相关埋点
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
object MessageHomeTracker : XTrackBase() {
    /**
     * 消息列表引导牵线开关引导曝光
     */
    fun trackMessageHomeTriggerGuideShow() {
        trackEvent("message_list_link_switch_cartoon_show")
    }

    /**
     * 消息列表引导牵线开关引导点击【打开】按钮
     *
     * @param status 打开接口结果，成功/失败
     */
    fun trackMessageHomeTriggerGuideClick(status: String?) {
        trackEvent("message_list_link_switch_cartoon_click", JsonObjectFactory().apply {
            put("status", status)
        }.create())
    }

    /**
     * 消息tab-更多面板点击牵线-确认牵线开关弹窗曝光
     */
    fun trackMessageTriggerPopShow() {
        trackEvent("message_list_link_switch_pop_show")
    }

    /**
     * 消息tab-更多面板点击牵线-确认牵线开关弹窗点击
     */
    fun trackMessageTriggerPopClick(position: String?) {
        trackEvent("message_list_link_switch_pop_click", JsonObjectFactory().apply {
            put("position", position)
        }.create())
    }

    /**
     * 消息tab-更多按钮曝光
     */
    fun trackMessageHomeMoreShow() {
        trackEvent("message_list_top_right_cornor_show")
    }

    /**
     * 消息tab-更多按钮点击
     */
    fun trackMessageHomeMoreClick() {
        trackEvent("message_list_top_right_cornor_click")
    }

    /**
     * 消息tab-更多面板点击
     */
    fun trackMessageHomeMorePanelItemClick(position: String?) {
        trackEvent("message_list_top_right_cornor_internal_click", JsonObjectFactory().apply {
            put("position", position)
        }.create())
    }
}