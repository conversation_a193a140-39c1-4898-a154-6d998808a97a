package com.xmhaihao.message.deepChat.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import hb.common.helper.HostHelper
import hb.utils.StringUtils

/**
 * 深度会话消息中心奖励实体类
 *
 * <AUTHOR>
 * @date 2023-03-23
 */
class MessageDeepChatListPopBean : IDoExtra {

    /**
     * 奖励弹框信息
     */
    @SerializedName("pop_info")
    var popBeans = listOf<MessageDeepChatPopBean>()

    /**
     * 是否有下一阶段,左下角是否继续展示
     */
    @SerializedName("has_next_stage")
    var hasNextStage: String? = ""

    /**
     * 是否有下一阶段,左下角是否继续展示
     * 任务状态：1-进行中，2-完成
     */
    @SerializedName("task_status")
    var taskStatus: String? = ""

    /**
     * 是否命中s保级城市实验
     */
    @SerializedName("is_hit_exp")
    var isHitExp: String? = ""

    /**
     * 过期气泡文案
     */
    @SerializedName("expire_content")
    var expireContent: String? = ""

    /**
     * 是否存在下一阶段
     *
     * @return true：有下一阶段 ，需要继续倒计时， false:没有下一阶段，直接隐藏view
     */
    fun hasNextStage(): Boolean {
        return StringUtils.equalsIgnoreCase("1", hasNextStage)
    }

    /**
     * 当前任务是否已全部完成（否，表示有下个阶段的任务）
     */
    fun isTaskFinish() = "2" == taskStatus

    /**
     * 是否命中s保级城市实验
     *
     * @return true：命中实验
     */
    fun hasHitExp(): Boolean {
        return StringUtils.equalsIgnoreCase("1", isHitExp)
    }

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        popBeans.forEach {
            it.redFinishLottieImg =
                HostHelper.getImageDefaultHost().getWebpUrl_2_1(it.redFinishLottieImg)
            it.blueFinishLottieImg =
                HostHelper.getImageDefaultHost().getWebpUrl_2_1(it.blueFinishLottieImg)
        }
    }
}