package com.xmhaihao.message.setting.bean

import com.google.gson.annotations.SerializedName

/**
 * 获取用户设置的铃声和震动开关状态
 * [飞书需求](https://o15vj1m4ie.feishu.cn/wiki/YiqAwzkGuiZo1ZkwZZZcSi5tnvd)
 * <AUTHOR>
 * @date 2024/12/17
 */
data class MessageRingingVibrationBean(
    /**
     * 0:未设置 1:已开启 2:已关闭
     */
    @SerializedName("ringing_status")
    var ringingStatus: String? = null,
    @SerializedName("vibration_status")
    var vibrationStatus: String? = null
) {
    /**
     * 是否开启铃声
     */
    fun isOpenRinging() = ringingStatus == "1"

    /**
     * 是否开启震动
     */
    fun isOpenVibration() = vibrationStatus == "1"

    /**
     * 是否未设置过铃声
     */
    fun isNotRinging() = ringingStatus == "0"

    /**
     * 是否未设置过震动
     */
    fun isNotVibration() = ringingStatus == "0"
}