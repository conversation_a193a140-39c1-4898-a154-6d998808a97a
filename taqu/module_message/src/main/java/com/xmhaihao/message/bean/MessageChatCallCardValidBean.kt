package com.xmhaihao.message.bean

import com.google.gson.annotations.SerializedName
import com.xmhaibao.call.api.bean.CallHostStatusInfo

/**
 * 通话卡（新） 有效性验证
 * https://api-gw.admin.internal.taqu.cn/docs/gw-api/gw-api-1e52mpb80rl1o
 * <AUTHOR>
 * @date 2022-09-14
 */
class MessageChatCallCardValidBean : CallHostStatusInfo() {
    companion object {
        /**
         * 卡片有效
         */
        const val STATUS_VALID = "1"
    }

    /**
     * 0失效，1有效 2已经使用
     */
    @SerializedName("card_status")
    var cardStatus: String? = ""

    /**
     * 判断通话卡是否有效
     * @return true 有效
     */
    fun isValid(): Boolean {
        return STATUS_VALID == cardStatus
    }

    /**
     * toast 提示文案
     * 服务端有返回值就提示，目前约定的是cardStatus不为0时有返回
     */
    @SerializedName("alert_msg")
    var alertMsg:String?=""
}