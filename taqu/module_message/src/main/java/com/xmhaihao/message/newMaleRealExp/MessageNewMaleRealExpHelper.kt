package com.xmhaihao.message.newMaleRealExp

import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStore
import androidx.lifecycle.ViewModelStoreOwner
import cn.taqu.lib.base.im.BaseEmitterListener
import com.xmhaibao.message.api.constants.ImTypeConstants
import cn.taqu.lib.base.interf.IMessageNewMaleRealExp
import com.xmhaihao.message.newMaleRealExp.friendtab.MessageNewMaleRealExpFriendTabHelper
import com.xmhaihao.message.newMaleRealExp.tracker.MessageNewMaleRealExpTracker
import com.xmhaihao.message.newMaleRealExp.viewmodel.MessageNewMaleRealExpViewModel
import hb.common.data.AccountHelper
import hb.message.HBChat
import hb.message.constants.MessageSendStatus
import hb.message.core.helper.HBMessageDBHelper
import hb.message.db.entity.HBMessageContentDO
import hb.message.db.intf.HBMessageDataListener
import hb.qim.base.QimClient
import hb.utils.AppUtils
import hb.utils.StringUtils
import hb.utils.kvcache.KVCacheUtils

/**
 * 新男帮助类
 
 * <AUTHOR>
 * @date 2024-07-31
 */
object MessageNewMaleRealExpHelper : IMessageNewMaleRealExp, ViewModelStoreOwner {

    /**
     * 一键搭讪失败
     */
    private const val IM_SEND_MSG_FAILED = "msg_send_fail"


    private val viewModelStore: ViewModelStore = ViewModelStore()

    /**
     * 是否已经插入卡片key
     */
    private const val NEW_MALE_CARD_INSERT = "new_male_card_insert"

    /**
     * 是否已经点击快捷打招呼key
     */
    private const val NEW_MALE_QUICK_REPLY_CLICK = "new_male_quick_reply_click"


    /**
     * NewMaleRealExpViewModel
     */
    fun getNewMaleRealExpViewModel(): MessageNewMaleRealExpViewModel {
        return ViewModelProvider(
            this,
            ViewModelProvider.AndroidViewModelFactory.getInstance(AppUtils.getApp())
        )[MessageNewMaleRealExpViewModel::class.java]
    }

    /**
     * 交友 Tab 帮助类
     */
    private val friendTabHelper: MessageNewMaleRealExpFriendTabHelper by lazy {
        MessageNewMaleRealExpFriendTabHelper()
    }

    /**
     * 埋点类
     */
    val newMaleRealExpTracker by lazy {
        MessageNewMaleRealExpTracker()
    }

    fun initAction(){
        requestGetConfig()
        registerStrikeUpFailedIm()
    }
    private fun registerStrikeUpFailedIm(){
        QimClient.register(IM_SEND_MSG_FAILED,object : BaseEmitterListener() {
            override fun callback(vararg args: Any?) {
                val msgId = args.firstOrNull() as? String ?:return
                if (StringUtils.isEmpty(msgId)){
                    return
                }
                HBMessageDBHelper.get().getMessageContentByMsgId(msgId,object:HBMessageDataListener<HBMessageContentDO>(){
                    override fun onSuccess(t: HBMessageContentDO?) {
                        t?:return
                        t.contentType = ImTypeConstants.PRIVATE_MSG_TEXT
                        t.sendStatus = MessageSendStatus.SEND_FAIL
                        HBChat.getInstance().addMessageContentAndConversation("MessageNewMaleRealExpHelper#registerStrikeUpFailedIm",t)
                    }

                    override fun onFailure(errorMessage: String?) {
                    }
                })
            }
        })
    }

    /**
     * 是否支持，第一判断条件
     */
    fun isSupport(): Boolean {
        if (AccountHelper.isFemale()) {
            return false
        }
        return true
    }

    /**
     * 获取配置
     */
    fun requestGetConfig() {
        getNewMaleRealExpViewModel().requestGetConfig()
    }

    /**
     * 记录插入卡片的用户
     *
     * @param accountUuid 对方用户uuid
     */
    fun saveNewMaleCardInsert(accountUuid: String) {
        KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
            .putBoolean("${NEW_MALE_CARD_INSERT}$accountUuid", true)
    }

    /**
     * 是否当前会话插入过卡片
     *
     * @param accountUuid 对方账号uuid
     */
    fun hasNewMaleCardInsert(accountUuid: String): Boolean {
        return KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
            .getBoolean("${NEW_MALE_CARD_INSERT}$accountUuid", false)
    }

    /**
     * 记录当前用户已经点击过快捷打招呼
     *
     * @param accountUuid 对方用户uuid
     */
    fun saveNewMaleQuickReplyClick(accountUuid: String) {
        KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
            .putBoolean("${NEW_MALE_QUICK_REPLY_CLICK}$accountUuid", true)
    }
    /**
     * 记录当前用户已经点击过的快捷打招呼-通用
     *
     * @param accountUuid 对方用户uuid
     */
    fun saveNewMaleQuickReplyClickByCommon(accountUuid: String) {
        KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
            .putBoolean("common${NEW_MALE_QUICK_REPLY_CLICK}$accountUuid", true)
    }

    /**
     * 是否当前用户已经点击过快捷打招呼
     *
     * @param accountUuid 对方账号uuid
     */
    fun hasNewMaleQuickReplyClick(accountUuid: String): Boolean {
        return KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
            .getBoolean("${NEW_MALE_QUICK_REPLY_CLICK}$accountUuid", false)
    }
    /**
     * 是否当前用户已经点击过快捷打招呼-通用
     *
     * @param accountUuid 对方账号uuid
     */
    fun hasNewMaleQuickReplyClickByCommon(accountUuid: String): Boolean {
        return KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
            .getBoolean("common${NEW_MALE_QUICK_REPLY_CLICK}$accountUuid", false)
    }

    override fun onFriendTabSelect() {
        friendTabHelper.onTabSelected()
    }

    override fun onFriendTabUnSelect() {
        friendTabHelper.onTabUnSelected()
    }

    override fun onAccountLogin() {
        requestGetConfig()
    }

    override fun onAccountLogout() {
        getNewMaleRealExpViewModel().onAccountLogout()
        friendTabHelper.onAccountLogout()
    }

    override fun putNotSendUuid(uuid: String?, isFemale: Boolean, lastSendMsgTime: Long) {
        getNewMaleRealExpViewModel()?.putNotSendUuid(uuid, isFemale, lastSendMsgTime)
    }

    override fun clearSendFemaleUuidMap() {
        getNewMaleRealExpViewModel()?.clearSendFemaleUuidMap()
    }


    override fun release() {
        viewModelStore.clear()
        QimClient.unregister(IM_SEND_MSG_FAILED)
    }

    /**
     * 退出私信页，清空数据
     */
    override fun releaseIm(){
        getNewMaleRealExpViewModel()?.releaseIm()
    }

    override fun getViewModelStore(): ViewModelStore {
        return viewModelStore
    }
}

