package com.xmhaihao.message.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import com.xmhaibao.hbchat.bean.bean.MessageChatPayBean
import hb.common.helper.HostHelper
import hb.common.helper.ServerTime
import hb.utils.StringUtils
import hb.utils.WebpUtils

/**
 * 类描述：喜帖卡片
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
class MessageChatRoomWeddingCardBean(
    @SerializedName("weeding_uuid")
    var weddingUuid: String?,
    @SerializedName("title")
    var title: String?,
    @SerializedName("role")
    var role: String?, //  嘉宾角色 1-伴郎 2-伴娘 3-观众
    @SerializedName("groom_avatar")
    var groomAvatar: String?,
    @SerializedName("bride_avatar")
    var brideAvatar: String?,
    @SerializedName("wedding_time")
    var weddingTime: String?,
    @SerializedName("wedding_status")
    var weddingStatus: String?,// 0-未处理 1-接收 2-拒绝
    @SerializedName("wedding_pattern")
    var weddingPattern: String?,// 模式 1-竞价 2-买断
    @SerializedName("relation")
    var relation: String?, // 跳转H5页面地址
    @SerializedName("wedding_bg")
    var weddingBg: String?,
    @SerializedName("wedding_expire_time")
    var weddingExpireTime: String?,
    @SerializedName("wedding_version")
    var weddingVersion: String?,
    @SerializedName("wedding_bg_v2")
    var weddingBgV2: String?,
    @SerializedName("wedding_love_bg")
    var weddingLoveBg: String?
    ) : MessageChatPayBean(), IDoExtra {
    companion object {
        const val STATUS_UNHANDLE = "0"
        const val STATUS_ACCEPT = "1"
        const val STATUS_REFUSE = "2"
    }

    /**
     * true-喜帖未处理
     */
    fun isUnHandle() = "0" == weddingStatus

    /**
     * true - 已买断
     */
    fun isBoughtOut() = "2" == weddingPattern

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        groomAvatar?.let {
            groomAvatar = WebpUtils.getWebpUrl_3_1(it, HostHelper.getInstance().hostAvatar)
        }
        brideAvatar?.let {
            brideAvatar = WebpUtils.getWebpUrl_3_1(it, HostHelper.getInstance().hostAvatar)
        }
        weddingBg?.let {
            weddingBg = WebpUtils.getWebpUrl_2_1(it, HostHelper.getInstance().hostImg)
        }
        weddingBgV2 = HostHelper.getImageDefaultHost().getWebpUrl_2_1(weddingBgV2)
        weddingLoveBg = HostHelper.getImageDefaultHost().getWebpUrl_3_1(weddingLoveBg)
    }

    /**
     * true-婚礼已过期
     */
    fun isExpired() =
        StringUtils.stringToLong(weddingExpireTime) < ServerTime.currentTimeMillis() / 1000

    fun getStatusTxt(): String {
        val currentTime = ServerTime.currentTimeMillis() / 1000
        val startTime = StringUtils.stringToLong(weddingTime)
        val endTime = StringUtils.stringToLong(weddingExpireTime)
        return when {
            currentTime in startTime..endTime -> "进行中"
            currentTime > endTime -> "已结束"
            else -> "未开始"
        }
    }

    /**
     * 1-伴郎 2-伴娘 3-观众
     */
    fun getRoleTxt(): String? =
        when (role) {
            "1" -> "伴郎"
            "2" -> "伴娘"
            "3" -> "观众"
            else -> null
        }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        if (!super.equals(other)) return false

        other as MessageChatRoomWeddingCardBean

        if (weddingUuid != other.weddingUuid) return false
        if (weddingStatus != other.weddingStatus) return false

        return true
    }

    override fun hashCode(): Int {
        var result = weddingUuid?.hashCode() ?: 0
        result = 31 * result + (weddingStatus?.hashCode() ?: 0)
        return result
    }
}