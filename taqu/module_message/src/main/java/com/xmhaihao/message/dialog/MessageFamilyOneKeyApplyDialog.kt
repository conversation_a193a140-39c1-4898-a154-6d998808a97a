package com.xmhaihao.message.dialog

import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.View.VISIBLE
import cn.taqu.lib.base.utils.GrowingIOUtils
import cn.taqu.lib.base.utils.eventlog.AppTrackEventUtils
import com.xmhaibao.family.api.router.FamilyPinsRouter
import com.xmhaihao.message.bean.MessageFamilyCheckCanJoinBean
import com.xmhaibao.family.bean.MessageFamilyHomeRecommendStatusBean
import com.xmhaihao.message.databinding.MessageFamilyOneKeyApplyDialogBinding
import com.xmhaihao.message.repository.FamilyRepository
import com.xmhaihao.message.router.MessageRouter
import hb.utils.*
import hb.xrequest.RequestCallback
import hb.xstyle.xdialog.XLifecycleDialog
import hb.xtoast.XToastUtils

/**
 * description:家族一键申请弹窗
 *
 * 场景eg:
 * 1.财富等级达到8级才可以创建家族哦，去下面的家族玩玩吧～
 * 2.该家族设置了魅力等级15级以上才可加入，看看下面的家族吧～
 *
 *
 *
 * <AUTHOR>
 * @date 2022/04/25 14:23
 */
class MessageFamilyOneKeyApplyDialog(
    context: Context,
    private var messageFamilyCheckCanJoinBean: MessageFamilyCheckCanJoinBean
) :
    XLifecycleDialog(context) ,View.OnClickListener{

    private lateinit var mBinding: MessageFamilyOneKeyApplyDialogBinding


    companion object {

        fun show(
            messageFamilyCheckCanJoinBean: MessageFamilyCheckCanJoinBean
        ) {
            val topActivity = ActivityUtils.getTopActivity()
            topActivity?.let {
                MessageFamilyOneKeyApplyDialog(topActivity, messageFamilyCheckCanJoinBean).show()
                AppTrackEventUtils.trackApplyOtherFamilyExpose()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = MessageFamilyOneKeyApplyDialogBinding.inflate(layoutInflater)
        setContentView(mBinding.root)
        val allWidth = ScreenUtils.getScreenWidth() - 2 * SizeUtils.dp2px(38f)
        mBinding.root.layoutParams.width = allWidth
        //25item间距  20：是margin left right
        val itemWidth = (allWidth - 2 * SizeUtils.dp2px(25f) - 2 * SizeUtils.dp2px(20f)) / 3
        mBinding.ivLeft.layoutParams.width = itemWidth
        mBinding.ivLeft.layoutParams.height = itemWidth
        mBinding.ivCenter.layoutParams.width = itemWidth
        mBinding.ivCenter.layoutParams.height = itemWidth
        mBinding.ivRight.layoutParams.width = itemWidth
        mBinding.ivRight.layoutParams.height = itemWidth
        mBinding.tvSubTitle.text = messageFamilyCheckCanJoinBean.reason
        messageFamilyCheckCanJoinBean.recommendFamilyList?.let {
            if (it.isNotEmpty() && it[0] != null) {
                mBinding.ivLeft.setImageFromUrl(it[0]!!.image)
                mBinding.tvLeft.text = it[0]!!.familyName
                mBinding.tvLeft.visibility = VISIBLE
                mBinding.ivLeft.visibility = VISIBLE
                mBinding.ivLeft.setOnClickListener(this)
            }
            if (it.size > 1 && it[1] != null) {
                mBinding.ivCenter.setImageFromUrl(it[1]!!.image)
                mBinding.tvCenter.text = it[1]!!.familyName
                mBinding.ivCenter.visibility = VISIBLE
                mBinding.tvCenter.visibility = VISIBLE
                mBinding.ivCenter.setOnClickListener(this)
            }
            if (it.size > 2 && it[2] != null) {
                mBinding.ivRight.setImageFromUrl(it[2]!!.image)
                mBinding.tvRight.text = it[2]!!.familyName
                mBinding.ivRight.visibility = VISIBLE
                mBinding.tvRight.visibility = VISIBLE
                mBinding.ivRight.setOnClickListener(this)
            }
        }
        mBinding.tvApply.setOnClickListener(this)
    }

    override fun onClick(v: View?) {
        val list = messageFamilyCheckCanJoinBean.recommendFamilyList
        when (v?.id) {
            mBinding.ivLeft.id -> {
                if (list != null && list.isNotEmpty() && list[0] != null && StringUtils.isNotEmpty(list[0]!!.familyUuid)) {
                    FamilyPinsRouter.launchMessageFamilyDetailActivity(list[0]!!.familyUuid)
                }
            }
            mBinding.ivCenter.id -> {
                if (list != null && list.size > 1 && list[1] != null && StringUtils.isNotEmpty(list[1]!!.familyUuid)) {
                    FamilyPinsRouter.launchMessageFamilyDetailActivity(list[1]!!.familyUuid)
                }
            }
            mBinding.ivRight.id -> {
                if (list != null && list.size > 2 && list[2] != null && StringUtils.isNotEmpty(list[2]!!.familyUuid)) {
                    FamilyPinsRouter.launchMessageFamilyDetailActivity(list[2]!!.familyUuid)
                }
            }
            mBinding.tvApply.id -> {
                AppTrackEventUtils.trackApplyOtherFamilyClick()
                FamilyRepository.checkJoinFromCheck().execute(object : RequestCallback<MessageFamilyHomeRecommendStatusBean>() {
                    override fun onSuccess(isCache: Boolean, obj: MessageFamilyHomeRecommendStatusBean?) {
                        obj?.run {
                            if (StringUtils.equalsIgnoreCase(applyStatus, "1") ||
                                StringUtils.equalsIgnoreCase(applyStatus, "3")
                            ) {
                                XToastUtils.show("您已成功加入家族")
                                //直接进入家族
                                conversationId?.run {
                                    FamilyPinsRouter.familyService().launchMessageFamilyChatActivity(this)
                                    GrowingIOUtils.trackMessageFamilyShow("其他",obj.familyUuid)
                                }
                            } else {
                                XToastUtils.show("加入失败")
                            }
                        }
                        dismiss()
                    }
                })
            }
        }
    }
}