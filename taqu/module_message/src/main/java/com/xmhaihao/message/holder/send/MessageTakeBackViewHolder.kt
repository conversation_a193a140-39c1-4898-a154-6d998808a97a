package com.xmhaihao.message.holder.send

import android.text.method.LinkMovementMethod
import android.view.View
import android.view.ViewGroup
import com.xmhaihao.message.R
import com.xmhaibao.message.api.bean.MessageTakeBackBean
import com.xmhaihao.message.databinding.MessageTakeBackItemBinding
import com.xmhaihao.message.holder.MessageIMBaseViewHolder
import com.xmhaihao.message.utils.AbstractMessageTipClickableSpan
import com.xmhaihao.message.utils.MessageCommonUtils
import hb.message.db.entity.HBMessageContentDO
import hb.message.intf.HBMessageChatType
import hb.utils.ColorUtils
import hb.utils.SpanUtils

class MessageTakeBackViewHolder(parent: ViewGroup) :
    MessageIMBaseViewHolder<MessageTakeBackBean>(parent, R.layout.message_take_back_item) {

    var mBinding = MessageTakeBackItemBinding.bind(itemView)
    var mOnReeditClickListener: OnReeditClickListener? = null

    override fun onBindViewHolder(messageContent: HBMessageContentDO?, messageContentBean: MessageTakeBackBean?) {
        super.onBindViewHolder(messageContent, messageContentBean)
        if (mIsSender) {
            val spanUtils = SpanUtils().append("你撤回了一条消息")
            if (messageContentBean?.content?.isNotEmpty() == true) {
                var foregroundColor = ColorUtils.getColor(hb.xstyle.R.color.C5D)
                spanUtils.append(" ").append("重新编辑").setForegroundColor(foregroundColor)
                    .setClickSpan(object : AbstractMessageTipClickableSpan(foregroundColor) {
                        override fun onClick(widget: View) {
                            mOnReeditClickListener?.onReeditClick(messageContentBean.content)
                            MessageCommonUtils.trackMsgSourceClick(mMessageContent?.msgId, messageContentBean.msgSource,messageContent)
                        }
                    })
            }
            mTvMessageTips?.movementMethod = LinkMovementMethod()
            mTvMessageTips?.text = spanUtils.create()
        } else {
            if (true == messageContentBean?.describe?.isNotEmpty()) {
                mTvMessageTips?.text = "${messageContentBean.describe}"
            } else {
                mTvMessageTips?.text = "对方撤回了一条消息"
            }
        }

    }

    override fun setMessageChatType(mMessageChatType: HBMessageChatType?) {
        super.setMessageChatType(mMessageChatType)
        mTvMessageTips?.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0)
    }

    fun setOnReeditClickListener(onReeditClickListener: OnReeditClickListener) {
        mOnReeditClickListener = onReeditClickListener
    }

    interface OnReeditClickListener {
        fun onReeditClick(content: String, isMouthAlert:Boolean = false)
    }
}