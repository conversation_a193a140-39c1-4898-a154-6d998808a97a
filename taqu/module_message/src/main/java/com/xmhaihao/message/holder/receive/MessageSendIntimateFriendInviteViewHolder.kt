package com.xmhaihao.message.holder.receive

import android.util.Log
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintSet
import com.xmhaihao.message.R
import com.xmhaihao.message.bean.MessageFamilyIntimateFriendInviteBean
import com.xmhaihao.message.databinding.MessageFamilyIntimateFriendInviteSenderBinding
import com.xmhaihao.message.holder.MessageIMBaseViewHolder
import hb.message.HBMessageHelper
import hb.message.db.entity.HBMessageContentDO
import hb.utils.SizeUtils

/**
 * 家族密友 - 密友邀请 消息ViewHolder
 *
 * <AUTHOR>
 * @since 2023-8-14
 * */
class MessageSendIntimateFriendInviteViewHolder(parent: ViewGroup): MessageIMBaseViewHolder<MessageFamilyIntimateFriendInviteBean>(parent, R.layout.message_family_intimate_friend_invite_sender) {

    companion object {
        private const val TEXT_NONE = "等待对方回应"
        private const val TEXT_ACCEPTED = "对方已同意"
        private const val TEXT_REFUSED = "对方已拒绝"
        private const val TEXT_INVALID = "已失效"
    }

    private val binding: MessageFamilyIntimateFriendInviteSenderBinding =
        MessageFamilyIntimateFriendInviteSenderBinding.bind(itemView)

    override fun onBindViewHolder(
        messageContent: HBMessageContentDO?,
        messageContentBean: MessageFamilyIntimateFriendInviteBean?
    ) {
        super.onBindViewHolder(messageContent, messageContentBean)
        messageContentBean?:return
        binding.ivInviteBg.setImageFromResource(getFamilyTopBannerRes(messageContentBean))
        setStroke(messageContentBean)
        when(messageContentBean.inviteStatus.toString()) {
            MessageFamilyIntimateFriendInviteBean.INVITE_STATUS_ACCEPTED -> {
                binding.ivInviteStatus.setImageFromResource(R.drawable.message_family_intimate_friend_invite_approved)
                binding.tvSubTitle.text = TEXT_ACCEPTED
            }
            MessageFamilyIntimateFriendInviteBean.INVITE_STATUS_REFUSED -> {
                binding.ivInviteStatus.setImageFromResource(R.drawable.message_family_intimate_friend_invite_refused)
                binding.tvSubTitle.text = TEXT_REFUSED
            }
            MessageFamilyIntimateFriendInviteBean.INVITE_STATUS_INVALID -> {
                binding.ivInviteStatus.setImageFromResource(R.drawable.message_family_intimate_friend_invite_invalid)
                binding.tvSubTitle.text = TEXT_INVALID
            }
            else -> {
                binding.ivInviteStatus.setImageFromResource(R.drawable.message_family_intimate_friend_invite_waiting)
                binding.tvSubTitle.text = TEXT_NONE
            }
        }
    }

    private fun getFamilyTopBannerRes(messageContentBean: MessageFamilyIntimateFriendInviteBean) :Int {
        if(messageContentBean.source == MessageFamilyIntimateFriendInviteBean.INVITE_FROM_FAMILY) {
            return when(messageContentBean.inviteStatus.toString()) {
                MessageFamilyIntimateFriendInviteBean.INVITE_STATUS_ACCEPTED -> {
                    R.drawable.message_family_intimate_friend_invite_top_approved
                }
                MessageFamilyIntimateFriendInviteBean.INVITE_STATUS_INVALID, MessageFamilyIntimateFriendInviteBean.INVITE_STATUS_REFUSED -> {
                    R.drawable.message_family_intimate_friend_invite_top_invalid
                }
                else -> { R.drawable.message_family_intimate_friend_invite_top_waiting }
            }
        }

        return R.drawable.message_family_intimate_friend_private_invite_top
    }

    private fun setStroke(messageContentBean: MessageFamilyIntimateFriendInviteBean) {
        val topMargin = if(messageContentBean.source == MessageFamilyIntimateFriendInviteBean.INVITE_FROM_FAMILY) {
            SizeUtils.dp2px(30F)
        } else {
            0
        }
        val set = ConstraintSet()
        set.clone(binding.clInviteMain)
        set.connect(binding.inviteStroke.id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, topMargin)
        set.connect(binding.inviteStroke.id, ConstraintSet.BOTTOM, binding.bottomSpace.id, ConstraintSet.BOTTOM)
        set.applyTo(binding.clInviteMain)
    }
}