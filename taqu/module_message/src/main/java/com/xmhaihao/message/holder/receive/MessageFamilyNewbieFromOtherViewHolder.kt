package com.xmhaihao.message.holder.receive

import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.HorizontalScrollView
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.core.content.ContextCompat
import cn.taqu.lib.base.at.AtUserUtils
import cn.taqu.lib.base.utils.AccountDefaultAvatarUtils
import cn.taqu.lib.base.widget.RCFrameLayout
import com.xmhaibao.family.api.router.FamilyPinsRouter
import com.xmhaibao.forum.api.router.ForumPinsRouter
import com.xmhaibao.mine.api.widget.MinePlayAudioView
import com.xmhaihao.message.R
import com.xmhaihao.message.bean.MessageChatTextExtraBean
import com.xmhaihao.message.bean.MessageFamilyNewbieBean
import com.xmhaihao.message.databinding.MessageFamilyNewbieFromOtherItemBinding
import com.xmhaihao.message.holder.MessageFamilyNewbieBaseViewHolder
import com.xmhaihao.message.viewModel.MessageFamilyChatViewModel
import hb.common.data.AccountHelper
import hb.drawable.shape.view.HbLinearLayout
import hb.location.XLocation
import hb.location.region.XRegionService
import hb.message.db.entity.HBMessageContentDO
import hb.utils.AppUtils
import hb.utils.ColorUtils
import hb.utils.StringUtils
import hb.utils.kvcache.KVCacheUtils
import kotlin.random.Random

/**
 * 家族新人发送方viewHolder
 *
 * <AUTHOR>
 * @date 2021-08-03
 */
class MessageFamilyNewbieFromOtherViewHolder(parent: ViewGroup) : MessageFamilyNewbieBaseViewHolder(parent, R.layout.message_family_newbie_from_other_item) {

    companion object {
        const val KEY_HAS_GREET_NEW_MEMBER = "key_has_greet_new_member_"
    }

    val mBinding = MessageFamilyNewbieFromOtherItemBinding.bind(itemView)
    
    override fun onBindViewHolder(messageContent: HBMessageContentDO?, newbieBean: MessageFamilyNewbieBean?) {
        super.onBindViewHolder(messageContent, newbieBean)
        setAvatarName(messageContent)
        newbieBean?.run {
            updateNewbieInfo(newbieBean, null)

            /*
            * 点击欢迎TA
            */
            mBinding.btnWelcome.setOnClickListener {
                if(FamilyPinsRouter.messageFamilyService().isFamilyChatActivity(mBinding.btnWelcome.context)) {
                    val atMeJson = AtUserUtils.getAtMeJson(newbieBean.accountUuid, newbieBean.accountName)
                    val messageChatTextExtraBean = MessageChatTextExtraBean()
                    messageChatTextExtraBean.welcomeTip = "1"

                    //获取欢迎术语
                    welcomeTips?.let { list->
                        if(list.size==0){
                            FamilyPinsRouter.messageFamilyService()
                                .sendTextMessage(mBinding.btnWelcome.context,
                                    "@" + newbieBean.accountName + " 欢迎加入大家庭！",
                                    atMeJson,
                                    messageChatTextExtraBean
                                )
                            return@let
                        }
                        val content: String = list[Random.nextInt(0,list.size)]
                        FamilyPinsRouter.messageFamilyService().sendTextMessage(mBinding.btnWelcome.context,
                            "@" + newbieBean.accountName + " "+content, atMeJson, messageChatTextExtraBean)
                    }
                    messageContent?.let { msg ->
                        KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).putBoolean(KEY_HAS_GREET_NEW_MEMBER + msg.msgId, true)
                        it.isEnabled = false
                        (it as? TextView)?.text = "已欢迎"
                        mBinding.btnWelcome.setTextColor(ContextCompat.getColor(mContext, hb.xstyle.R.color.white))
                    }
                }
            }
            messageContent?.let {
                if(KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).getBoolean(KEY_HAS_GREET_NEW_MEMBER + it.msgId, false)) {
                    mBinding.btnWelcome.isEnabled = false
                    mBinding.btnWelcome.text = "已欢迎"
                    mBinding.btnWelcome.setTextColor(ContextCompat.getColor(mContext, hb.xstyle.R.color.white))
                } else {
                    mBinding.btnWelcome.isEnabled = true
                    mBinding.btnWelcome.text = "欢迎TA"
                    mBinding.btnWelcome.setTextColor(ContextCompat.getColor(mContext, hb.xstyle.R.color.black))
                }
            }
        }

        mBinding.relContent.setOnClickListener {
            ForumPinsRouter.launchPersonalHomePageActivity(mContext, messageContent?.senderUuid)
        }

    }

    /**
     * 更新ui显示信息
     *
     * @param newbieBean 新人信息
     */
    override fun updateNewbieInfo(newbieBean: MessageFamilyNewbieBean, viewModel: MessageFamilyChatViewModel?) {
        super.updateNewbieInfo(newbieBean,null)
        val url = AccountDefaultAvatarUtils.getABTestAvatar(newbieBean.avatar)
        mBinding.ivHeadBg.setImageFromUrl(url)
        if (StringUtils.isEmpty(newbieBean.city)) {
            mBinding.tvDistance.visibility = View.GONE
        } else {
            mBinding.tvDistance.visibility = View.VISIBLE
            mBinding.tvDistance.text = "${newbieBean.cityName}"
        }

        //完善资料
        val material = newbieBean.getCompleteMaterial()
        if (StringUtils.isEmpty(material)) {
            mBinding.tvAge.visibility = View.GONE

        } else {
            mBinding.tvAge.visibility = View.VISIBLE
            mBinding.tvAge.text = material
        }

        //个人简介
        if (StringUtils.isEmpty(newbieBean.personalProfile)) {
            mBinding.tvIntroduce.visibility = View.GONE
        } else {
            mBinding.tvIntroduce.visibility = View.VISIBLE
            mBinding.tvIntroduce.text = newbieBean.personalProfile
        }

        //真人认证
        if (StringUtils.equalsIgnoreCase("1", newbieBean.faceReal)) {
            mBinding.ivAuthIcon.setImageResource(cn.taqu.lib.base.R.drawable.message_realauth_ic)
            mBinding.tvAuthStatus.text = "已认证"
        } else {
            mBinding.ivAuthIcon.setImageResource(R.drawable.message_not_realauth_ic)
            mBinding.tvAuthStatus.text = "未认证"
        }

        try {
            //是否已经开启定位
            if (XLocation.hasLocationPermission(AppUtils.getApp())) {
                mBinding.tvDistance.visibility = View.VISIBLE
                val locationIdBean = XLocation.getLocationIdBean()
                val startLat = locationIdBean?.latitude
                val startLon = locationIdBean?.longitude
                val endLat = newbieBean.lat
                val endLon = newbieBean.lon

                if (StringUtils.isNotEmpty(startLat) && StringUtils.isNotEmpty(startLon)
                        && StringUtils.isNotEmpty(endLon) && StringUtils.isNotEmpty(endLat)) {
                    val distance = getDistance(startLon?.toDouble()!!, startLat?.toDouble()!!, endLon?.toDouble()!!,endLat?.toDouble()!!)
                    if (distance < 1) {
                        mBinding.tvDistance.text = "${newbieBean.cityName} <1km"
                    } else {
                        mBinding.tvDistance.text = "${newbieBean.cityName} ${distance}km"
                    }

                } else {
                    mBinding.tvDistance.visibility = View.GONE
                }

            } else {
                mBinding.tvDistance.visibility = View.GONE
            }
        }catch (e:Exception){

        }

        //个人资料
        mBinding.tvAge.text = newbieBean.getCompleteMaterial()

        //个人简介
        mBinding.tvIntroduce.text = newbieBean.personalProfile

        //语音签名
        if (StringUtils.equalsIgnoreCase("1", newbieBean.voiceSign)) {
            mBinding.playAudioView.visibility = View.VISIBLE
        } else {
            mBinding.playAudioView.visibility = View.GONE
        }

        //底部按钮左侧的文案
        mBinding.tvWelComeLabel.text = "${newbieBean.welcomeText}"


        //关系
        if(TextUtils.isEmpty(newbieBean.inviteRelation)) {
            mBinding.llInviteRelation.visibility = View.GONE
        }else {
            mBinding.tvInviteRelation.text = "${newbieBean.inviteRelation}"
            mBinding.llInviteRelation.visibility = View.VISIBLE

        }


    }

    override fun isMyViewHolder():Boolean = false

    override fun getLlAudioAuth(): HbLinearLayout? {
        return null
    }

    override fun getPlayAudioView(): MinePlayAudioView {
        return mBinding.playAudioView
    }

    override fun getIvAudioIcon(): ImageView? {
        return null
    }

    override fun getLlLabelContainer(): LinearLayoutCompat {
        return mBinding.llLabelContainer
    }

    override fun getHorScrollView(): HorizontalScrollView {
        return mBinding.horScrollView
    }

    override fun getFlHeadBg(): RCFrameLayout {
        return mBinding.flHeadBg
    }

    override fun getTvAudioStatus(): TextView? {
        return null
    }

}