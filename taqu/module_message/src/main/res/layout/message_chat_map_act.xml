<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <com.baidu.mapapi.map.MapView
        android:id="@+id/mapView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


    <hb.drawable.shape.view.HbLinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="44dp"
        android:orientation="vertical"
        android:paddingLeft="16dp"
        android:paddingTop="12dp"
        android:paddingRight="16dp"
        android:paddingBottom="12dp"
        app:corner="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:solid="@color/TH_Navy001">


        <TextView
            android:id="@+id/tvTitle"
            style="@style/Text.T5"
            android:textColor="@color/TH_Gray990"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            tools:text="的时空观度搜开关的时空观度搜开关的时空观度搜开关的时空观度搜开关的时空观度搜开关" />

        <TextView
            android:id="@+id/tvSubtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/TH_Gray400"
            android:textSize="@dimen/TH_FONT_N1"
            tools:text="的时空观度搜开关的时空观度搜开关的时空观度搜开关的时空观度搜开关的时空观度搜开关" />

    </hb.drawable.shape.view.HbLinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>