<?xml version="1.0" encoding="utf-8"?>
<hb.drawable.shape.view.HbConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    app:corner="@dimen/dp_16"
    app:gradient_color_end="#DFEAFF"
    app:gradient_color_start="#FFE4F5"
    app:gradient_linear_orientation="left_right"
    android:layout_marginStart="16dp"
    android:layout_marginEnd="16dp"
    android:id="@+id/clRoot"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvType"
        style="@style/Text.T6.G1"
        android:textStyle="bold"
        tools:text="竞拍"
        android:layout_marginTop="8dp"
        android:layout_marginStart="23dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />


    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvRelationRemove"
        android:gravity="center"
        style="@style/Text.T8.G1"
        android:text="删除关系"
        app:corner="50dp"
        app:solid="#59FFFFFF"
        android:drawablePadding="3dp"
        android:paddingEnd="7dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_marginTop="7dp"
        android:layout_marginEnd="10dp"
        android:drawableStart="@drawable/message_relation_remove_ic"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />


    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvRelationShow"
        android:gravity="center"
        style="@style/Text.T8.G1"
        android:text="展示关系"
        app:corner="50dp"
        app:solid="#59FFFFFF"
        android:drawablePadding="3dp"
        android:paddingEnd="7dp"
        android:layout_marginTop="7dp"
        android:layout_marginEnd="10dp"
        android:drawableStart="@drawable/message_relation_show"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvStick"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvStick"
        android:gravity="center"
        style="@style/Text.T8.G1"
        android:text="置顶展示"
        app:corner="50dp"
        app:solid="#59FFFFFF"
        android:drawablePadding="3dp"
        android:paddingEnd="7dp"
        android:layout_marginTop="7dp"
        android:layout_marginEnd="13dp"
        android:drawableStart="@drawable/message_relation_stick_top"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <hb.drawable.shape.view.HbView
        android:id="@+id/viewCenter"
        app:corner="@dimen/dp_16"
        app:solid="@color/w5"
        android:layout_marginTop="5dp"
        android:layout_marginStart="13dp"
        android:layout_marginEnd="13dp"
        app:layout_goneMarginBottom="16dp"
        app:layout_constraintTop_toBottomOf="@id/tvType"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tvRenewal"
        android:layout_width="@dimen/dp_0"
        android:layout_height="105dp" />

    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/avatarMyself"
        app:roundingBorderWidth="1dp"
        app:roundingBorderColor="@color/white"
        android:layout_marginTop="18dp"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintTop_toTopOf="@id/viewCenter"
        app:layout_constraintStart_toStartOf="@id/viewCenter"
        app:layout_constraintEnd_toStartOf="@id/imgRelation"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_50" />

    <hb.ximage.fresco.BaseDraweeView
        app:actualImageScaleType="fitXY"
        android:id="@+id/imgRelation"
        app:placeholderImage="@color/transparent"
        android:layout_marginTop="9dp"
        android:layout_marginStart="5dp"
        android:layout_marginEnd="5dp"
        app:layout_constraintTop_toTopOf="@id/viewCenter"
        app:layout_constraintStart_toEndOf="@id/avatarMyself"
        app:layout_constraintEnd_toStartOf="@id/avatarOther"
        android:layout_width="100dp"
        android:layout_height="64dp" />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/imgRelationExpire"
        app:placeholderImage="@color/transparent"
        app:layout_constraintTop_toTopOf="@id/avatarMyself"
        app:layout_constraintBottom_toBottomOf="@id/avatarMyself"
        app:layout_constraintStart_toEndOf="@id/avatarMyself"
        app:layout_constraintEnd_toStartOf="@id/avatarOther"
        android:layout_width="51dp"
        android:layout_height="21dp" />

    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/avatarOther"
        app:roundingBorderWidth="1dp"
        app:roundingBorderColor="@color/white"
        android:layout_marginTop="18dp"
        app:layout_constraintTop_toTopOf="@id/viewCenter"
        app:layout_constraintStart_toEndOf="@id/imgRelation"
        app:layout_constraintEnd_toEndOf="@id/viewCenter"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_50" />

    <TextView
        android:id="@+id/tvTime"
        style="@style/Text.T7.G2"
        tools:text="剩余：7天4小时"
        android:layout_marginTop="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/imgRelation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvRelieveRelation"
        style="@style/Text.T6_2.G1"
        android:gravity="center"
        android:textStyle="bold"
        android:paddingStart="17dp"
        android:paddingEnd="17dp"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        app:corner="50dp"
        app:solid="@color/white"
        android:text="解除关系"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="10dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintTop_toBottomOf="@id/viewCenter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvRenewal"
        android:minWidth="82dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvRenewal"
        style="@style/Text.T6_2.White"
        android:gravity="center"
        android:textStyle="bold"
        android:paddingStart="17dp"
        android:paddingEnd="17dp"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        app:corner="50dp"
        app:solid="#FF429A"
        android:text="续期"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="10dp"
        app:layout_constraintTop_toBottomOf="@id/viewCenter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvRelieveRelation"
        app:layout_constraintEnd_toEndOf="parent"
        android:minWidth="82dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</hb.drawable.shape.view.HbConstraintLayout>