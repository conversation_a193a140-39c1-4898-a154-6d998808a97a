<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llPaipai"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="horizontal"
    android:visibility="gone"
    tools:visibility="visible">

    <TextView
        android:id="@+id/tvPaipaiNickName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="2dp"
        android:ellipsize="end"
        android:paddingTop="1.5dp"
        android:paddingBottom="1.5dp"
        android:singleLine="true"
        android:textColor="#FFCA44"
        android:textSize="8sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/imgPaipaiIcon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="昵称昵称昵称" />

    <TextView
        android:id="@+id/tvPaipaiRelationName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="0.5dp"
        android:ellipsize="end"
        android:paddingStart="14.5dp"
        android:paddingTop="0.5dp"
        android:paddingEnd="6dp"
        android:paddingBottom="0.5dp"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="9sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvPaipaiNickName"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="CP" />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/imgPaipaiIcon"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="3dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:placeholderImage="@color/transparent" />
</androidx.constraintlayout.widget.ConstraintLayout>


