<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clInviteCardRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="102dp">

    <hb.drawable.shape.view.HbView
        android:id="@+id/viewBaseBg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="11dp"
        app:corner_bottom_left="9dp"
        app:corner_bottom_right="9dp"
        app:corner_top_left="3dp"
        app:corner_top_right="9dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:stroke_color="@color/TH_Gray200"
        app:stroke_width="1dp" />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivBg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="1dp"
        android:layout_marginTop="1dp"
        android:layout_marginEnd="1dp"
        app:actualImageScaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/viewBaseBg"
        app:placeholderImage="@color/transparent"
        app:viewAspectRatio="2.5744"
        tools:backgroundImage="@drawable/message_secret_date_detail_bg" />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivTitle"
        android:layout_width="188dp"
        android:layout_height="56dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:placeholderImage="@color/transparent"
        tools:backgroundImage="@drawable/message_secret_date_meeting" />

    <hb.xemoji.view.XEmojiTextView
        android:id="@+id/tvContent"
        style="@style/Text.B4.G600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:layout_marginTop="47dp"
        android:background="@drawable/message_secret_date_invite_text"
        android:lineSpacingExtra="2dp"
        android:paddingLeft="4dp"
        android:paddingRight="4dp"
        android:paddingTop="6dp"
        android:paddingBottom="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="和你聊天的感觉真好，今天20:00~21:00让我们多了解彼此吧，不见不散！" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvLeftBtn"
        style="@style/Text.B4.G990"
        android:layout_width="0dp"
        android:layout_height="30dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="6dp"
        android:gravity="center"
        android:visibility="gone"
        app:corner="16dp"
        app:layout_constraintEnd_toStartOf="@+id/tvRightBtn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvContent"
        app:solid="@color/TH_Navy100"
        tools:text="查看邀请"
        tools:visibility="visible" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvRightBtn"
        style="@style/Text.B4.B100"
        android:layout_width="0dp"
        android:layout_height="30dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="12dp"
        android:gravity="center"
        android:textStyle="bold"
        android:visibility="gone"
        app:corner="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvLeftBtn"
        app:layout_constraintTop_toBottomOf="@+id/tvContent"
        app:layout_goneMarginStart="12dp"
        app:solid="@color/TH_Yellow600"
        tools:text="立刻赴约"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivStatusIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="44dp"
        android:layout_marginEnd="8dp"
        android:paddingBottom="8dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:id="@+id/spaceBottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="12dp"
        app:layout_constraintTop_toBottomOf="@+id/tvRightBtn"
        app:layout_goneMarginTop="16dp" />
</androidx.constraintlayout.widget.ConstraintLayout>