<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <include
        layout="@layout/message_time_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <RelativeLayout
        android:id="@+id/rlHead"
        android:layout_width="64dp"
        android:layout_height="64dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTimeLabel">

        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/imgHead"
            style="@style/chat_message_right_avatar"
            android:visibility="gone"
            app:placeholderImage="@color/transparent"
            app:roundedCornerRadius="@dimen/message_avatar_radius"
            tools:background="@color/c1"
            tools:visibility="visible" />

        <com.xmhaihao.message.widget.MessageAvatarFrameLayout
            android:id="@+id/imgHeadFrame"
            style="@style/chat_message_right_avatar_frame"
            android:visibility="gone"
            app:placeholderImage="@color/transparent"
            tools:background="@color/black_alpha_10"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/imgHeadDress"
            android:layout_width="wrap_content"
            android:layout_height="13dp"
            android:layout_alignStart="@id/imgHead"
            android:layout_alignEnd="@id/imgHead"
            android:layout_alignParentBottom="true"
            android:visibility="gone"
            tools:visibility="visible" />
    </RelativeLayout>

    <com.xmhaihao.message.widget.MessageChatRightUserView
        android:id="@+id/messageGroupChatUserView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginEnd="2dp"
        app:layout_constraintRight_toLeftOf="@+id/rlHead"
        app:layout_constraintTop_toTopOf="@+id/rlHead"
        tools:layout_height="20dp"
        tools:layout_width="50dp" />

    <hb.drawable.shape.view.HbConstraintLayout
        android:id="@+id/relInviteCard"
        android:layout_width="230dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="66dp"
        android:paddingBottom="8dp"
        app:corner="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/messageGroupChatUserView"
        app:layout_constraintWidth_percent="0.693"
        app:layout_goneMarginTop="35dp"
        app:solid="@color/TH_Navy001"
        app:stroke_color="@color/TH_Gray150"
        app:stroke_width="1dp"
        tools:parentTag="hb.drawable.shape.view.HbConstraintLayout">

        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/ivCardBg"
            android:layout_width="match_parent"
            android:layout_height="70dp"
            app:layout_constraintRight_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:placeholderImage="@color/transparent"
            app:roundedCornerRadius="12dp" />

        <hb.ximage.fresco.BaseDraweeView
            app:placeholderImage="@color/transparent"
            android:id="@+id/ivCardPic"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:roundedCornerRadius="8dp" />

        <TextView
            android:id="@+id/tvCardTitle"
            style="@style/Text.B2.G990"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8.5dp"
            android:ellipsize="end"
            android:lineSpacingExtra="0dp"
            android:textStyle="bold"
            android:maxLines="2"
            app:layout_constraintBottom_toTopOf="@id/tvCardContent"
            android:layout_marginEnd="16dp"
            app:layout_constraintLeft_toRightOf="@+id/ivCardPic"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="小鬼当家小鬼小鬼当家小鬼小鬼当家小鬼小鬼当家小鬼" />

        <TextView
            android:id="@+id/tvCardContent"
            style="@style/Text.N1.G400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:lineSpacingExtra="0dp"
            android:maxLines="2"
            app:layout_constraintBottom_toTopOf="@id/cardBarrier"
            app:layout_constraintEnd_toEndOf="@id/tvCardTitle"
            tools:text="那么这里的描述就可以那么这里的描述就可以那么这里的描述就可以"
            app:layout_constraintStart_toStartOf="@+id/tvCardTitle"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvCardTitle" />


        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/cardBarrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="ivCardPic,tvCardContent" />

        <TextView
            android:id="@+id/tvGuideTitle"
            android:textSize="@dimen/TH_FONT_B3"
            android:textColor="@color/TH_Gray990"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            app:layout_goneMarginEnd="8dp"
            android:ellipsize="end"
            android:singleLine="true"
            tools:text="来家族，认识更多朋友"
            app:layout_constraintEnd_toStartOf="@+id/tvGuideBtn"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cardBarrier" />

        <hb.drawable.shape.view.HbTextView
            android:id="@+id/tvGuideBtn"
            style="@style/Text.N1"
            android:layout_marginEnd="8dp"
            android:layout_width="wrap_content"
            android:layout_height="24dp"
            android:layout_marginTop="8dp"
            android:layout_marginStart="8dp"
            android:gravity="center"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            android:textColor="@color/black"
            app:corner="16dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cardBarrier"
            app:solid="@color/TH_Yellow600"
            tools:text="去看看" />

    </hb.drawable.shape.view.HbConstraintLayout>

    <include
        android:id="@+id/includeStatus"
        layout="@layout/message_chat_item_reset_read_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/relInviteCard"
        app:layout_constraintEnd_toStartOf="@+id/relInviteCard"
        app:layout_constraintTop_toTopOf="@+id/relInviteCard" />

</androidx.constraintlayout.widget.ConstraintLayout>