<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/relContent"
    android:gravity="end"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clAnswer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="3dp"

        android:background="@drawable/message_chat_new_right_bg">

        <ImageView
            android:id="@+id/ivPic"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginTop="0.5dp"
            android:layout_marginStart="0.5dp"
            android:src="@drawable/message_heart_words_from_me_ic"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvContent"
            style="@style/Text.T5_2.G1"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:ellipsize="end"
            android:lineSpacingExtra="2dp"
            android:paddingStart="14dp"
            android:paddingEnd="14dp"
            tools:text="你有没有对比你大很心吗？大很心哈\n答:会"
            android:textColor="@color/black_alpha_90"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</RelativeLayout>

