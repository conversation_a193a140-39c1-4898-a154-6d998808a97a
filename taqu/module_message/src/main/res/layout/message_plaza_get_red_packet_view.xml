<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="32dp"
    android:background="@color/g6">

    <ImageView
        android:id="@+id/mIvPlazaRedPacketIcon"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginStart="16dp"
        android:src="@drawable/message_plaza_get_red_packet_ic"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/mIvPlazaRedPacketHint"
        style="@style/Text.B4.G990"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/mIvPlazaRedPacketIcon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="12名成员正在直播、多人语言中" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:src="@drawable/message_chat_member_select_arrow_ic"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</merge>