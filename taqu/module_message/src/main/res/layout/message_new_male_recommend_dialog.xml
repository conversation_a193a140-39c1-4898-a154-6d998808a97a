<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="30dp"
        android:src="@drawable/base_close_white_32_ic"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <hb.xbanner.view.XBannerView
        android:id="@+id/bannerCard"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="12dp"
        app:banner_indicatorAlign="CENTER_HORIZONTAL"
        app:banner_indicatorNormalColor="@color/white_alpha_50"
        app:banner_indicatorRadius="3.5dp"
        app:banner_indicatorSelectColor="@color/white"
        app:layout_constraintBottom_toTopOf="@id/tvAccostAll"
        app:layout_constraintTop_toBottomOf="@id/ivClose" />


    <TextView
        android:id="@+id/tvAccostAll"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="59dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="59dp"
        android:background="@drawable/message_accost_all_shape_bg"
        android:gravity="center"
        android:paddingTop="9dp"
        android:paddingBottom="9dp"
        android:text="一键搭讪所有"
        android:textColor="#830038"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bannerCard"
        tools:layout_marginBottom="40dp" />

</androidx.constraintlayout.widget.ConstraintLayout>