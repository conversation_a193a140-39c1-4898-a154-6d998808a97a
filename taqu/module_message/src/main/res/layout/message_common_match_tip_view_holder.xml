<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="10dp"
    android:layout_gravity="center_horizontal">

    <include
        android:id="@+id/tvTimeLabel"
        layout="@layout/message_time_layout"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewBg"
        android:layout_width="0dp"
        android:layout_height="22dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTimeLabel"
        app:layout_goneMarginTop="14dp" />

    <View
        android:id="@+id/viewAvatarBg"
        android:layout_width="32dp"
        android:layout_height="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewBg"
        app:layout_constraintEnd_toStartOf="@+id/tvContent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@+id/viewBg"
        app:layout_constraintTop_toTopOf="@+id/viewBg" />

    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/ivSender"
        android:layout_width="18dp"
        android:layout_height="18dp"
        app:layout_constraintStart_toStartOf="@+id/viewAvatarBg"
        app:layout_constraintTop_toTopOf="@+id/viewAvatarBg"
        app:roundingBorderColor="@color/TH_Gray001"
        app:roundingBorderWidth="1dp" />

    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/ivSelf"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginStart="14dp"
        app:layout_constraintStart_toStartOf="@+id/viewAvatarBg"
        app:layout_constraintTop_toTopOf="@+id/viewAvatarBg"
        app:roundingBorderColor="@color/TH_Gray001"
        app:roundingBorderWidth="1dp" />

    <hb.xemoji.view.XEmojiTextView
        android:id="@+id/tvContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:gravity="center_vertical"
        android:textColor="@color/TH_Gray400"
        android:textSize="@dimen/TH_FONT_B4"
        app:layout_constraintBottom_toBottomOf="@+id/viewAvatarBg"
        app:layout_constraintEnd_toEndOf="@+id/viewBg"
        app:layout_constraintStart_toEndOf="@+id/viewAvatarBg"
        app:layout_constraintTop_toTopOf="@+id/viewAvatarBg"
        tools:text="你们匹配成功" />


</androidx.constraintlayout.widget.ConstraintLayout>

