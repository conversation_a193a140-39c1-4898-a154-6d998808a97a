<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="62dp">

    <hb.xemoji.view.XEmojiTextView
        android:id="@+id/tvPostTitle"
        style="@style/Text.T6_1.G1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginEnd="8dp"
        android:layout_toLeftOf="@+id/ivPostImg"
        android:ellipsize="end"
        android:lineSpacingExtra="1dp"
        android:maxLines="2"
        tools:text="你好啊电sdf水电费水电费水电费水电费收到是收到收到史蒂夫是地方" />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivPostImg"
        android:layout_width="46dp"
        android:layout_height="46dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        app:roundedCornerRadius="4dp"
        app:roundingBorderColor="@color/black_alpha_05"
        app:roundingBorderWidth="0.5dp" />

    <View
        android:id="@+id/viewTopDot"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_alignParentTop="true"
        android:background="@drawable/message_family_news_dotted_line" />
</RelativeLayout>