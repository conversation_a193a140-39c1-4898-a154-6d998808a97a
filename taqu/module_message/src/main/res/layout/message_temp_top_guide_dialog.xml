<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <View
        android:id="@+id/viewTopMask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/black_alpha_60"
        app:layout_constraintTop_toTopOf="parent" />


    <View
        android:id="@+id/viewBottomMask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/black_alpha_60"
        app:layout_constraintBottom_toBottomOf="parent" />


    <ImageView
        android:id="@+id/ivItem"
        app:layout_constraintTop_toBottomOf="@id/viewTopMask"
        app:layout_constraintBottom_toTopOf="@id/viewBottomMask"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="78dp"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="100dp"
        android:maxWidth="206dp"
        android:id="@+id/tvGuideIcon"
        style="@style/Text.B2"
        android:textColor="@color/black"
        tools:text="她在期待你的回复！快看看她在期待你的回复！快看看！她在期待你的回复！快看看！她在期待你的回复！快看看！！"
        android:background="@drawable/message_temp_top_guide_text_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>