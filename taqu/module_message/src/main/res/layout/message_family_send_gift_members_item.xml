<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="74dp"
    tools:background="@color/white">

    <ImageView
        android:id="@+id/mIvSelect"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="20dp"
        android:src="@drawable/message_family_send_gift_members_ic_checked"
        app:layout_constraintBottom_toBottomOf="@+id/mIvFamilyMemberAvatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/mIvFamilyMemberAvatar" />

    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/mIvFamilyMemberAvatar"
        android:layout_width="54dp"
        android:layout_height="54dp"
        android:layout_marginStart="60dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:placeholderImage="@color/transparent" />

    <ImageView
        android:id="@+id/mIvFamilyMemberAvatarDress"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/mIvFamilyMemberAvatar"
        app:layout_constraintEnd_toEndOf="@+id/mIvFamilyMemberAvatar"
        app:layout_constraintStart_toStartOf="@+id/mIvFamilyMemberAvatar" />

    <TextView
        android:id="@+id/mTvFamilyMemberName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="16dp"
        android:includeFontPadding="false"
        android:textColor="@color/g1"
        android:textSize="17sp"
        app:layout_constraintStart_toEndOf="@+id/mIvFamilyMemberAvatar"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="雷珊玉" />

    <TextView
        android:id="@+id/mTvFamilyName"
        style="@style/Text.T7.G2"
        android:layout_width="wrap_content"
        android:layout_height="16dp"
        android:layout_marginLeft="3dp"
        android:background="@drawable/message_plaza_user_dialog_family_info_bg"
        android:drawableTint="#3F8AFB"
        android:paddingLeft="3dp"
        android:paddingRight="3dp"
        android:textColor="#3F8AFB"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/mTvFamilyMemberName"
        app:layout_constraintStart_toEndOf="@+id/mTvFamilyMemberName"
        app:layout_constraintTop_toTopOf="@+id/mTvFamilyMemberName"
        tools:text="丿王氏灬贵族"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/mTvFamilyMemberAtAllHint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:text="所有人"
        android:textColor="@color/g1"
        android:textSize="17sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/mIvFamilyMemberAvatar"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <cn.taqu.lib.base.widget.ForumGenderView
        android:id="@+id/forumGenderView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="8dp"
        app:layout_constraintStart_toEndOf="@id/mIvFamilyMemberAvatar"
        app:layout_constraintTop_toBottomOf="@+id/mTvFamilyMemberName"
        tools:layout_height="16dp"
        tools:layout_width="32dp" />

    <TextView
        android:id="@+id/mTvFamilyMemberContribute"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/message_family_members_contribute_bg"
        android:includeFontPadding="false"
        android:paddingLeft="5dp"
        android:paddingTop="2dp"
        android:paddingRight="5dp"
        android:paddingBottom="2dp"
        android:textColor="@color/c2"
        android:textSize="@dimen/t7"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/forumGenderView"
        app:layout_constraintStart_toEndOf="@+id/forumGenderView"
        app:layout_constraintTop_toBottomOf="@+id/mTvFamilyMemberName"
        app:layout_goneMarginStart="10dp"
        tools:text="99贡献"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/mIvFamilyMemberOperation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:src="@drawable/message_family_members_more_ic"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivLabel"
        android:layout_width="45dp"
        android:layout_height="20dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/mTvFamilyMemberName"
        app:layout_constraintStart_toEndOf="@+id/mTvFamilyMemberName"
        app:layout_constraintTop_toTopOf="@+id/mTvFamilyMemberName"
        app:placeholderImage="@color/transparent" />

</androidx.constraintlayout.widget.ConstraintLayout>