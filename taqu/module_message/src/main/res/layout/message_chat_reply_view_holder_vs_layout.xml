<?xml version="1.0" encoding="utf-8"?>

<hb.drawable.shape.view.HbRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rlReply"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:visibility="gone"
    tools:visibility="visible"
    android:paddingEnd="10dp"
    app:corner="8dp"
    app:solid="@color/TH_Gray150">

    <hb.xemoji.view.XEmojiTextView
        android:id="@+id/tvReplyContent"
        style="@style/Text.B4.G600"
        android:paddingStart="10dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:maxLines="2"
        android:lineSpacingExtra="@dimen/dp_0"
        android:ellipsize="end"
        android:text="再不理我" />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivReplyPic"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_toEndOf="@+id/tvReplyContent"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        app:roundedCornerRadius="6dp"
        android:layout_marginStart="10dp"
        android:visibility="gone"
        tools:visibility="visible"
        />

</hb.drawable.shape.view.HbRelativeLayout>


