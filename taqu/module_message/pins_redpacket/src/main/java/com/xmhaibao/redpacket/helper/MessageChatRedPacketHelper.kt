package com.xmhaibao.redpacket.helper

import cn.taqu.lib.okhttp.callback.GsonCallBack
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.xmhaibao.message.api.callback.MessageSendCommonGsonCallback
import com.xmhaibao.redpacket.repository.PinsRedPacketRepository
import hb.message.db.entity.HBMessageContentDO
import hb.message.intf.HBMessageChatType
import hb.utils.StringUtils
import hb.utils.ViewUtils
import hb.xstyle.xdialog.XLoadingDialog

class MessageChatRedPacketHelper {
    companion object {
        /**
         * 家族发红包
         *
         * @param familyUuid  家族id
         * @param singleValue 单个红包的面额
         * @param totalNum    红包数量
         * @param word        祝福语
         * @param isPlaza     是否是聊天广场
         */
        fun sendFamilyRedPacket(
            familyUuid: String?,
            isPlaza: Boolean,
            singleValue: Int,
            totalNum: Int,
            word: String?,
            callBack: GsonCallBack<HBMessageContentDO?>?
        ) {
            if (StringUtils.isEmpty(familyUuid) || singleValue <= 0 || totalNum <= 0) {
                XLoadingDialog.hideLoadingbar()
                return
            }
            if (ViewUtils.isQuickClick(1000)) {
                XLoadingDialog.hideLoadingbar()
                return
            }
            PinsRedPacketRepository.sendRedPacket(
                familyUuid,
                singleValue,
                totalNum,
                word
            )
                .execute<HBMessageContentDO>(
                    MessageSendCommonGsonCallback(
                        if (isPlaza) HBMessageChatType.TYPE_PLAZA else HBMessageChatType.TYPE_GROUP,
                        object : GsonCallBack<HBMessageContentDO?>() {
                            override fun onSuccess(
                                isCache: Boolean,
                                obj: HBMessageContentDO?,
                                response: IResponseInfo<*>
                            ) {
                                callBack?.onSuccess(isCache, obj, response)
                            }

                            override fun onFailure(
                                isServiceFailure: Boolean,
                                response: IResponseInfo<*>
                            ) {
                                callBack?.onFailure(isServiceFailure, response)
                            }
                        })
                )
        }

        /**
         * 礼物红包
         *
         * @param familyUuid  家族id
         * @param totalBean 红包总的面额
         * @param totalNum    红包数量
         * @param word        祝福语
         * @param isPlaza     是否是聊天广场
         */
        fun sendRedPacket(
            familyUuid: String?,
            isPlaza: Boolean,
            totalBean: Int,
            totalNum: Int,
            word: String?,
            callBack: GsonCallBack<HBMessageContentDO?>?,
        ) {
            if (StringUtils.isEmpty(familyUuid) || totalBean <= 0 || totalNum <= 0) {
                XLoadingDialog.hideLoadingbar()
                return
            }
            if (ViewUtils.isQuickClick(1000)) {
                XLoadingDialog.hideLoadingbar()
                return
            }
            PinsRedPacketRepository.sendGiftRedPacket(familyUuid, totalBean, totalNum, word)
                .execute(MessageSendCommonGsonCallback(
                    if (isPlaza) HBMessageChatType.TYPE_PLAZA else HBMessageChatType.TYPE_GROUP,
                    object : GsonCallBack<HBMessageContentDO?>() {
                        override fun onSuccess(isCache: Boolean, obj: HBMessageContentDO?, response: IResponseInfo<*>) {
                            callBack?.onSuccess(isCache, obj, response)
                        }

                        override fun onFailure(isServiceFailure: Boolean, response: IResponseInfo<*>) {
                            callBack?.onFailure(isServiceFailure, response)
                        }
                    }))
        }
    }
}