package com.xmhaibao.cp.holder

import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import com.xmhaibao.cp.bean.FamilyIntimateFriendTabBean
import com.xmhaibao.cp.constants.FamilyIntimateFriendViewType
import com.xmhaibao.cp.tracker.FamilyIntimateFriendTracker
import com.xmhaibao.cp.viewModel.FamilyCpStructureViewModel
import com.xmhaibao.cp.viewModel.FamilyIntimateFriendViewModel
import com.xmhaibao.family.widget.OnlineDotViewHelper
import com.xmhaihao.message.R
import com.xmhaihao.message.databinding.FamilyIntimateFriendGirlTabItemBinding
import hb.kotlin_extension.gone
import hb.kotlin_extension.visible
import hb.utils.ActivityUtils
import hb.xadapter.XBaseViewHolder

/**
 * 家族密友 - 我的密友Viewholder
 *
 * <AUTHOR>
 * @since 2023-8-18
 * */
class FamilyIntimateFriendGirlTabViewHolder(parent: ViewGroup): XBaseViewHolder<FamilyIntimateFriendTabBean>(parent, R.layout.family_intimate_friend_girl_tab_item) {

    private val binding by lazy {
        FamilyIntimateFriendGirlTabItemBinding.bind(itemView)
    }

    private val viewModel: FamilyIntimateFriendViewModel by lazy {
        ViewModelProvider(ActivityUtils.getAppCompActivity(itemView.context)).get(
            FamilyIntimateFriendViewModel::class.java)
    }

    private val cpStructViewModel: FamilyCpStructureViewModel by lazy {
        ViewModelProvider(ActivityUtils.getAppCompActivity(itemView.context)).get(
            FamilyCpStructureViewModel::class.java)
    }

    private val familyOnlineDotView by lazy {
        OnlineDotViewHelper(binding.vsOnlineDot)
    }

    override fun onBindView(item: FamilyIntimateFriendTabBean?) {
        item?:return
        if (item.uuid.isNullOrEmpty()) {
            showEmpty()
            return
        }
        onSelect(item)
        binding.goldFrame.setImageFromUrl(item.goldFrame)
        binding.goldFrame.isVisible = item.isGoldFriend()
        binding.avatar.setImageFromUrl(item.avatar)
        binding.tvName.text = item.nickName
        familyOnlineDotView.setIsOnline(item.isOnline())
        if(item.isRewardAvailable()) {
            binding.tvRewardAvailable.visible()
        } else {
            binding.tvRewardAvailable.gone()
        }
        itemView.setOnClickListener {
            FamilyIntimateFriendTracker.trackFamilyCloseFriendPageClk(cpStructViewModel.getSceneUuid(), cpStructViewModel.intimateFriendBizScene, clkName = "密友头像")
            if (!item.selected) {
                viewModel.tabUpdateSelected(item.uuid)
            }
        }
    }

    /**
     * 选中后的样式变化
     * */
    private fun onSelect(item: FamilyIntimateFriendTabBean) {
        if(item.selected) {
            binding.normalFrame.isVisible = !item.isGoldFriend()
            binding.tabArrow.visible()
            return
        }
        binding.normalFrame.gone()
        binding.tabArrow.gone()
    }


    private fun showEmpty(){
        // 展示 + 邀请按钮
        binding.avatar.setImageFromResource(R.drawable.family_more_close_friend)
        itemView.setOnClickListener {
            viewModel.navigateTo(FamilyIntimateFriendViewType.VIEW_TYPE_INTIMATE_FRIEND_INVITES_SECONDARY)
        }
        familyOnlineDotView.setIsOnline(false)
        binding.goldFrame.gone()
        binding.tabArrow.gone()
        binding.tvRewardAvailable.gone()
        binding.normalFrame.gone()
        binding.tvName.text = "添加"
    }
}