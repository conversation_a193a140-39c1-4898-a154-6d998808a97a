package com.xmhaibao.cp.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import androidx.core.view.isVisible
import cn.taqu.lib.base.router.RouterLaunch
import com.xmhaibao.family.event.EventFamilyIntimateFriendChatRoomAgree
import com.xmhaihao.message.databinding.FamilyIntimateFriendChatroomBuildSuccessDialogLayBinding
import dp
import hb.xstyle.xdialog.XLifecycleDialog
import com.xmhaihao.message.R
import hb.common.data.AccountHelper

/**
 * 聊天室房间内达成密友弹框
 *
 * <AUTHOR>
 * @date 2025/4/8
 */
class FamilyIntimateFriendChatroomBuildSuccessDialog(
    ctx: Context,
    val event: EventFamilyIntimateFriendChatRoomAgree?
) :
    XLifecycleDialog(ctx) {
    private lateinit var binding: FamilyIntimateFriendChatroomBuildSuccessDialogLayBinding


    @SuppressLint("SetTextI18n")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = FamilyIntimateFriendChatroomBuildSuccessDialogLayBinding.inflate(layoutInflater)
        setContentView(binding.root)
        // 禁止点击外部关闭弹窗
        setCanceledOnTouchOutside(false)
        binding.ivImage.setImageFromResource(R.drawable.family_intimate_friend_build_success_bg)
        event?.let {
            var otherName: String? = null
            if (it.newerUuid == AccountHelper.getAccountUuid()) {
                binding.userAvatar.setImageFromUrl(it.newerAvatar)
                binding.oppAvatar.setImageFromUrl(it.olderAvatar)
                otherName = it.olderName
            } else if (it.olderUuid == AccountHelper.getAccountUuid()) {
                binding.userAvatar.setImageFromUrl(it.olderAvatar)
                binding.oppAvatar.setImageFromUrl(it.newerAvatar)
                otherName = it.newerName
            }
            binding.tvMessage.text = "你和 $otherName 成为密友啦!"
            binding.ivViewTask.isVisible = it.viewTaskRelation?.isNotEmpty() ?: false
            binding.ivGoSee.isVisible = it.goSeeRelation?.isNotEmpty() ?: false
        }
        binding.ivViewTask.setOnClickListener {
            RouterLaunch.dealJumpData(it.context, event?.viewTaskRelation)
            dismiss()
        }
        binding.ivClose.setOnClickListener {
            dismiss()
        }
        binding.ivGoSee.setOnClickListener {
            RouterLaunch.dealJumpData(it.context, event?.goSeeRelation)
            dismiss()
        }
    }

    override fun onStart() {
        super.onStart()
        window?.attributes = window?.attributes?.apply {
            width = 248.dp
            height = 262.dp
            gravity = Gravity.CENTER
        }
    }
}