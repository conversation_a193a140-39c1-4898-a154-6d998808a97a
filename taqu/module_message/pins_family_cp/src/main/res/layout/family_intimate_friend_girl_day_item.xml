<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="56dp"
    android:layout_height="85dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tvDays"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="第7天"
        style="@style/Text.N1.G600"
        android:textSize="11sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivIncome"
        android:layout_marginTop="4dp"/>

    <hb.drawable.shape.view.HbView
        android:id="@+id/progressLeft"
        android:layout_width="0dp"
        android:layout_height="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/progressRight"
        android:layout_marginBottom="9dp"
        app:solid="#BA64FF"/>

    <hb.drawable.shape.view.HbView
        android:id="@+id/progressRight"
        android:layout_width="0dp"
        android:layout_height="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/progressLeft"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="9dp"
        app:solid="#BA64FF"/>

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivIncome"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="13dp"
        app:placeholderImage="@color/transparent"/>

    <ImageView
        android:id="@+id/ivRewardReminder"
        android:layout_width="43dp"
        android:layout_height="25dp"
        android:scaleType="fitXY"
        android:src="@drawable/family_intimate_friend_reward_available_ic"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"/>

</androidx.constraintlayout.widget.ConstraintLayout>