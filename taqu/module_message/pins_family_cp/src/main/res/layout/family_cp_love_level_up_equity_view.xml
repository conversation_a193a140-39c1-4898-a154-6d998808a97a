<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:parentTag="hb.drawable.shape.view.HbConstraintLayout"

    android:layout_width="64dp"
    android:layout_height="match_parent">


    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivIcon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="11dp"
        android:layout_width="40dp"
        android:layout_height="40dp"/>
    <TextView
        tools:text="CP等级标签"
        android:layout_marginTop="4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivIcon"
        android:maxLines="1"
        android:id="@+id/tvName"
        style="@style/Text.N1"
        android:textColor="#FF4185"

        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
</merge>