package com.xmhaibao.tacitbox.predict.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Point
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import com.xmhaibao.tacitbox.predict.bean.FamilyTacitBoxFlightEffectBean
import com.xmhaibao.imganim.lottie.XLottiePlayer
import com.xmhaihao.message.R
import com.xmhaihao.message.databinding.FamilyTacitBoxFlightViewBinding
import hb.kotlin_extension.gone
import hb.kotlin_extension.visible
import hb.utils.ActivityUtils
import hb.utils.ScreenUtils
import hb.utils.SizeUtils

/**
 * 【家族 异性互动玩法】送礼 飘屏 飞机特效
 * 【需求文档】https://o15vj1m4ie.feishu.cn/wiki/P7dFwPHcQifGO2kU3RgcIPQvnih
 *
 * @since 2023-9-27
 * <AUTHOR>
 * */
class FamilyTacitBoxFlightEffectView : ConstraintLayout {

    companion object {
        /**
         * Lottie 动画过程中的旋转角度
         * */
        private const val ROTATION = 30F
        /**
         * y轴偏移量(适配屏幕位置)
         * */
        private const val TOP_OFFSET = 0F
    }

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    private val binding by lazy {
        FamilyTacitBoxFlightViewBinding.inflate(LayoutInflater.from(context), this, true)
    }

    private val screenWidth by lazy { ScreenUtils.getAppScreenWidth() }
    private val rotateAnim by lazy {
        ObjectAnimator.ofFloat(binding.lottieFlight, View.ROTATION, ROTATION, 0F).apply {
            duration = 5000
            repeatCount = 0
            repeatMode = ValueAnimator.RESTART
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    super.onAnimationStart(animation)
                    showOnTop()
                }

                override fun onAnimationEnd(animation: Animator) {
                    super.onAnimationEnd(animation)
                    dismiss()
                }
            })
            addUpdateListener { anim ->
                (anim.animatedValue as? Float)?.let {process ->
                    // 在父类View中移动
                    (binding.root.parent as? View)?.let {
                        val point = getCurrentPoint(process / ROTATION)
                        it.x = point.x.toFloat()
                        it.y = point.y.toFloat()
                    }
                }

            }
        }
    }

    /**
     * 设置播放动效的参数
     * */
    fun setFlightData(bean : FamilyTacitBoxFlightEffectBean) {
        binding.avatar.setImageFromUrl(bean.avatar)
        binding.tvDescribe.text = bean.text
        // lottie有效时，准备播放lottie
        if(binding.lottieFlight.isAnimating) {
            binding.lottieFlight.cancelAnimation()
        }
        if(!bean.lottieId.isNullOrBlank()) {
            XLottiePlayer.playById(binding.lottieFlight, bean.lottieId, R.drawable.family_gift_predict_lottie_default_ic)
        } else if(!bean.lottieAliasName.isNullOrBlank()) {
            XLottiePlayer.playByAlias(binding.lottieFlight, bean.lottieAliasName, R.drawable.family_gift_predict_lottie_default_ic)
        } else {
            // 播放内置特效文件
            binding.lottieFlight.setAnimation(FamilyTacitBoxFlightEffectBean.FLIGHT_LOTTIE_PATH)
            binding.lottieFlight.imageAssetsFolder = FamilyTacitBoxFlightEffectBean.FLIGHT_IMAGE_PATH
            binding.lottieFlight.playAnimation()
        }
        animateGo()
    }

    private fun animateGo() {
        if (rotateAnim.isRunning) {
            rotateAnim.cancel()
        }
        rotateAnim.start()
    }

    /**
     * 贝塞尔曲线 —— 计算出当前Point
     * 二阶贝塞尔曲线，计算公式：https://zhuanlan.zhihu.com/p/136647181
     *
     * @param process 当前进度 （0, 1.0f）
     * */
    private fun getCurrentPoint(process: Float) : Point{
        val remain = 1.0f - process
        val pointStart by lazy {
            Point(-measuredWidth, SizeUtils.dp2px(TOP_OFFSET))
        }
        val pointEnd by lazy {
            Point(screenWidth, SizeUtils.dp2px(TOP_OFFSET))
        }
        val pointB by lazy {
            Point(measuredWidth, SizeUtils.dp2px(60F))
        }
        return Point(
            (remain * remain * pointEnd.x + 2 * process * remain * pointB.x + process * process * pointStart.x).toInt(),
            (remain * remain * pointEnd.y + 2 * process * remain * pointB.y + process * process * pointStart.y).toInt()
        )
    }


    /**
     * 从parentView中移出
     */
    fun dismiss() {
        gone()
        (parent as? ViewGroup)?.removeView(this@FamilyTacitBoxFlightEffectView)
    }

    /**
     * 展示在最顶层
     * */
    fun showOnTop() {
        dismiss()
        visible()
        ActivityUtils.getAppCompActivity(context)?.window?.findViewById<FrameLayout>(android.R.id.content)?.apply {
            addView(this@FamilyTacitBoxFlightEffectView)
        }
    }
}