
println ":taqu:resGuard.gradle installed."

apply plugin: "AndResGuard"

andResGuard {
    use7zip = true
    useSign = true
    // It will keep the origin path of your resources when it's true
    keepRoot = false
    // If set, name column in arsc those need to proguard will be kept to this value
    fixedResName = "arg"
    // It will merge the duplicated resources, but don't rely on this feature too much.
    // it's always better to remove duplicated resource from repo
    mergeDuplicatedRes = true
    whiteList = [
            // for icon
            "R.drawable.*launcher*",
            "R.drawable.yw_*",
            "R.drawable.push",
            "R.drawable.push_small",
            "R.drawable.mz_push_notification_small_icon",
            "R.drawable.xemojis*",
            "R.drawable.emoji*",
            "R.drawable.smiley*",
            // for 通知铃声
            "R.raw.notification",
            // for layout
            "R.layout.getui_notification",
            "R.layout.push_expandable_big_image_notification",
            "R.layout.push_expandable_big_text_notification",
            "R.layout.push_pure_pic_notification",
            // for string
            "R.string.PORT_NUMBER",
            "R.string.xcommon_hb_env",
            "R.string.tencent_auth_id",
            "R.string.xxxx_is_single_mode",
            // for dimen
            "R.dimen.navigation_bar_height",
            "R.dimen.status_bar_height",
            // for id
            // 由于会影响 androidx.constraintlayout.widget.Group/Barrier 等使用，所以所有 id 都不混淆
            "R.id.*",
            "R.id.network_available_sign_in",
            "R.id.vGift*",
            "R.id.ivGift*",
            "R.id.rlGift*",
            "R.id.ivLebel*",
            // for 闪验
            "R.anim.umcsdk*",
            "R.drawable.umcsdk*",
            "R.layout.layout_shanyan*",
            "R.id.shanyan_view*",
            // for alibaba
            "R.string.utanalytics*",
            // for 微博
            "R.drawable.retry_btn_selector",
            "R.drawable.weibosdk*",
            // for umeng
            "R.anim.umeng*",
            "R.string.umeng*",
            "R.string.UM*",
            "R.string.tb_*",
            "R.layout.umeng*",
            "R.layout.socialize_*",
            "R.layout.*messager*",
            "R.layout.tb_*",
            "R.color.umeng*",
            "R.color.tb_*",
            "R.style.*UM*",
            "R.style.umeng*",
            "R.drawable.umeng*",
            "R.drawable.tb_*",
            "R.drawable.sina*",
            "R.drawable.qq_*",
            "R.drawable.tb_*",
            "R.id.umeng*",
            "R.id.*messager*",
            "R.id.progress_bar_parent",
            "R.id.socialize_*",
            "R.id.webView",
            // for huawei push
            "R.string.hms_*",
            "R.string.connect_server_fail_prompt_toast",
            "R.string.getting_message_fail_prompt_toast",
            "R.string.no_available_network_prompt_toast",
            "R.string.third_app_*",
            "R.string.upsdk_*",
            "R.style.upsdkDlDialog",
            "R.style.AppTheme",
            "R.style.AppBaseTheme",
            "R.dimen.upsdk_dialog_*",
            "R.color.upsdk_*",
            "R.layout.upsdk_*",
            "R.drawable.upsdk_*",
            "R.drawable.hms_*",
            "R.layout.hms_*",
            "R.id.hms_*",
            // for vivo
            "R.id.notify*",
            "R.drawable.vivo*",
            // 推送相关
            "R.id.*push*",
            "R.drawable.*push*",
            "R.layout.*push*",
            // for 智齿客服
            "R.string.*sobo*",
            "R.string.*sobot*",
            "R.style.*sobot*",
            "R.dimen.*sobot*",
            "R.color.*sobot*",
            "R.layout.*sobot*",
            "R.drawable.selector_ic_back",
            "R.drawable.*sobot*",
            "R.drawable.expression*",
            "R.integer.*sobot*",
            "R.anim.anim_alpha",
            "R.anim.push_left_in",
            "R.anim.push_left_out",
            "R.anim.push_right_in",
            "R.anim.push_right_out",
            "R.anim.sobot*",
            // 华为厂商推送 HMS Core SDK
            "R.string.hms*",
            "R.string.connect_server_fail_prompt_toast",
            "R.string.getting_message_fail_prompt_toast",
            "R.string.no_available_network_prompt_toast",
            "R.string.third_app_*",
            "R.string.upsdk_*",
            "R.layout.hms*",
            "R.layout.upsdk_*",
            "R.drawable.upsdk*",
            "R.color.upsdk*",
            "R.dimen.upsdk*",
            "R.style.upsdk*",
            "R.string.agc*",
            //滴滴
            "R.string.dk*",
    ]
    compressFilePattern = [
            "*.png",
            "*.jpg",
            "*.jpeg",
            "*.gif",
    ]
    sevenzip {
        // 非 jenkins，则使用 SevenZip，否则使用命令行
        if (BUILD_URL == null || BUILD_URL == "") {
            artifact = 'com.tencent.mm:SevenZip:1.2.18'
        }else {
            // jenkins 上使用
            path = "/usr/bin/7za"
        }
    }
    if ("他趣".equals(project.ext.appName)) {
        // res资源混淆使用的首字符(字符不能重复!!!)
        arsFirstLetterStr = "vkrsqmzxfcwoygnduejibaplth"
        //  res资源混淆使用的其他字符(字符不能重复!!!)
        arsOtherLetterStr = "815u9bzdla37o62k0y4rjsgqev_tnichxmfwp"
    } else if ("恰聊".equals(project.ext.appName)) {
        // res资源混淆使用的首字符(字符不能重复!!!)
        arsFirstLetterStr = "tfbzlkywrespjanoivxqdchmug"
        //  res资源混淆使用的其他字符(字符不能重复!!!)
        arsOtherLetterStr = "fz9ier20bkwm1o7hl4svgq8_6d3jxantu5pcy"
    }else if ("脱单".equals(project.ext.appName)) {
        // res资源混淆使用的首字符(字符不能重复!!!)
        arsFirstLetterStr = "puyzodkngievbmfhwatsxclrjq"
        //  res资源混淆使用的其他字符(字符不能重复!!!)
        arsOtherLetterStr = "26tbsa_xon95jkzidle4gph8yc73vmu0rqf1w"
    }else if ("风月".equals(project.ext.appName)) {
        // res资源混淆使用的首字符(字符不能重复!!!)
        arsFirstLetterStr = "ynisakuzbwjfrvedcqpomxlthg"
        //  res资源混淆使用的其他字符(字符不能重复!!!)
        arsOtherLetterStr = "o6imqegk4rf9z3xha5l8cn01vs27juwpdt_by"
    }else if ("觅友".equals(project.ext.appName)) {
        // res资源混淆使用的首字符(字符不能重复!!!)
        arsFirstLetterStr = "vsmdwxqntbglfcorjakhyzeipu"
        //  res资源混淆使用的其他字符(字符不能重复!!!)
        arsOtherLetterStr = "8ob7vdtgke960m_xq21njar5uc3lpzs4hfiwy"
    }


    /**
     * Optional: if finalApkBackupPath is null, AndResGuard will overwrite final apk
     * to the path which assemble[Task] write to
     **/
    // finalApkBackupPath = "${project.rootDir}/final.apk"

    /**
     * Optional: Specifies the name of the message digest algorithm to user when digesting the entries of JAR file
     * Only works in V1signing, default value is "SHA-1"
     **/
    // digestalg = "SHA-256"
}

if (TINKER_BUILD_TYPE != "HOTFIX") {
    return
}

project.afterEvaluate {
    // 基准包相关文件存储路径
    def originBasePath = project.rootProject.projectDir.canonicalPath + "/build/hotfix/" + HOTFIX_BUILD_ID
    android.applicationVariants.all { variant ->
        // 校验base文件是否已下载
        if(variant.buildType.name == "release") {
            def hotfixResourceMappingFile = file("$originBasePath/mapping/resource_mapping_${project.name}-${variant.flavorName}-${variant.buildType.name}.txt")
            if(!hotfixResourceMappingFile.exists()) {
                throw RuntimeException("${hotfixResourceMappingFile.canonicalPath}文件未找到")
            }

            def resguardTask = tasks.getByName("resguard${variant.name.capitalize()}")
            resguardTask.doFirst {
                project.andResGuard.mappingFile = hotfixResourceMappingFile
            }
        }
    }
}
/**
 * 生成andResGuard随机唯一字符脚本
 * @param isFirstLetters  是否是首字母的
 * @return
 */
String generateUniqueLetters(boolean isFirstLetters) {
    int length
    String allowedChars
    if (isFirstLetters) {
        // 首字符，只能支持字母
        length = 26
        allowedChars = "abcdefghijklmnopqrstuvwxyz"
    } else {
        // 其他字符，可以字母+其他数字
        length = 37
        allowedChars = "0123456789_abcdefghijklmnopqrstuvwxyz"
    }
    List<Character> chars = allowedChars.chars().mapToObj { it as char }.collect()
    // 列表字符列表
    Collections.shuffle(chars)
    return chars.subList(0, length).join("")
}