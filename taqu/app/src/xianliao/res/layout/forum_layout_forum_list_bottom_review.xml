<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        xmlns:app="http://schemas.android.com/apk/res-auto">

        <TextView
            android:id="@+id/tvForm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="12dp"
            android:includeFontPadding="false"
            android:paddingTop="6dp"
            android:textColor="@color/g3"
            android:textSize="@dimen/t7"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rlVideo"
            tools:text="圈子名称"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/ivWetBg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/forum_post_list_dianzan_btn"
            app:layout_constraintEnd_toStartOf="@+id/tvWet"
            app:layout_constraintTop_toTopOf="@+id/tvWet"
            app:layout_constraintBottom_toBottomOf="@+id/tvWet"
            android:layout_marginLeft="11dp"
            android:visibility="gone"
            tools:visibility="visible"/>

        <TextView
            android:id="@+id/tvWet"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center|left"
            android:textColor="@color/g2"
            android:textSize="@dimen/t7"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@+id/ivComment"
            app:layout_constraintTop_toTopOf="@+id/ivComment"
            app:layout_constraintBottom_toBottomOf="@+id/ivComment"
            android:paddingLeft="6dp"
            android:paddingRight="6dp"
            tools:text="顶下 Ta"
            android:text=""
            tools:visibility="visible"/>

        <ViewStub
            android:id="@+id/viewStubLikes"
            android:layout="@layout/forum_post_list_likes_view"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginLeft="5dp"
            app:layout_constraintTop_toTopOf="@+id/tvWet"
            app:layout_constraintBottom_toBottomOf="@+id/tvWet"/>

        <ImageView
            android:id="@+id/ivComment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/ivShare"
            app:layout_constraintEnd_toStartOf="@+id/tvComment"
            app:layout_constraintTop_toTopOf="@+id/ivShare"
            android:paddingLeft="16dp"
            android:paddingRight="4dp"
            android:src="@drawable/forum_post_list_comment_ic" />

        <TextView
            android:id="@+id/tvComment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/g2"
            android:textSize="@dimen/t7"
            app:layout_constraintBottom_toBottomOf="@+id/ivShare"
            app:layout_constraintEnd_toStartOf="@+id/ivShare"
            app:layout_constraintTop_toTopOf="@+id/ivShare"
            tools:text="124"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/ivShare"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_marginTop="6dp"
            android:paddingLeft="6dp"
            android:paddingRight="16dp"
            android:src="@drawable/forum_post_list_share_ic"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvForm" />

        <View
            android:id="@+id/line"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="6dp"
            android:background="@color/g6"
            app:layout_constraintTop_toBottomOf="@+id/ivWetBg" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>