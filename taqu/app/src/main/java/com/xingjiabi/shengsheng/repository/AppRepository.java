package com.xingjiabi.shengsheng.repository;

import com.huawei.hms.support.log.common.Base64;
import com.xingjiabi.shengsheng.app.model.DeepLinkModel;
import com.xingjiabi.shengsheng.bean.AppAccountInitBean;

import cn.taqu.lib.base.api.UrlBase;
import cn.taqu.lib.base.utils.AccountUtils;
import hb.common.xstatic.HttpParams;
import hb.xrequest.XRequest;
import kotlin.text.Charsets;

/**
 * [App] api仓库
 *
 * <AUTHOR>
 * @date 2021-04-07
 */
public final class AppRepository extends UrlBase {

    /**
     * 马甲包-获取首页tab控制
     */
    public static final String GET_HOME_TAB_BY_CLONE = API_GW_FORUM_NEW_V5 + "/Account/isShowTab";

    /**
     * 客户端启动调用，初始化操作
     */
    public static final String INITIALIZE_SOMETHING = API_DISPATCH_V1 + "/Report/initializeSomething";

    /**
     * 登录/启动调用的初始化接口，做一些业务处理（如:支付宝绑定失效通知）
     */
    public static final String INITIALIZE_ACCOUNT = API_GW_I_V1 + "/Info/initializeAccount";

    public static XRequest<Object> initializeSomething() {
        HttpParams httpParams = HttpParams.newBuilder().post(INITIALIZE_SOMETHING)
                .needTicketId(true)
                .build();
        return XRequest.newRequest(httpParams);
    }

    /**
     * 登录/启动app调用接口 做一些业务处理
     * @param scene
     * @return
     */
    public static XRequest<AppAccountInitBean> initializeAccount() {
        // 场景，1-启动app，2-登录
        String fromLoginSuccess = "2";
        String scene = "1";
        if (AccountUtils.fromLoginSuccess) {
            scene = fromLoginSuccess;
        }
        HttpParams httpParams = HttpParams.newBuilder().post(INITIALIZE_ACCOUNT)
                .needTicketId()
                .params("scene", scene)
                .build();
        return XRequest.newRequest(httpParams);
    }

    /**
     * 新人奖励进阶任务
     */
    public static final String NEWBIE_FEMALE_REWARD_MORE = API_GW_FORUM_NEW_V5 + "/Guide/newbieFemaleRewardStage2";

    public static XRequest<Object> getNewbieFemaleRewardMore() {
        HttpParams httpParams = HttpParams.newBuilder().get(NEWBIE_FEMALE_REWARD_MORE)
                .needTicketId(true)
                .build();
        return XRequest.newRequest(httpParams);
    }


    /**
     * 获取承接页的跳转URL
     *
     * @return
     */
    public static final String GET_DEEP_LINK_URL = API_GW + "/admp/v1/Report/getDeviceDeepLink";

    public static XRequest<DeepLinkModel> getDeepLink() {
        HttpParams httpParams = HttpParams.newBuilder().post(GET_DEEP_LINK_URL)
                .build();
        return XRequest.newRequest(httpParams);
    }

    /**
     * 上报客户端切换tab操作
     */
    public static final String TAB_CHECK = API_GW + "/taqu-dispatch/v1/Risk/tabCheck";

    public static XRequest<Object> tabCheck() {
        HttpParams httpParams = HttpParams.newBuilder().get(TAB_CHECK)
                .needTicketId(true)
                .build();
        return XRequest.newRequest(httpParams);
    }

    /**
     * 上报华为Track信息
     *
     * @param enterAppGalleryTime 在广告位点击安装按钮的时间
     * @param installedFinishTime 应用安装完成的时间
     * @param startDownloadTime   应用开始下载的时间
     * @param trackId             付费推广的归因信息(json)
     * @param referrerEx          下载链接地址中设置的referrer参数
     */
    public static XRequest<Object> postTrack(String enterAppGalleryTime,
                                             String installedFinishTime,
                                             String startDownloadTime,
                                             String trackId,
                                             String referrerEx) {
        HttpParams httpParams = HttpParams.newBuilder().post(API_GW + "/mp-admp-data/v1/attribution/hwFb")
                .params("ad_click_time", enterAppGalleryTime)
                .params("install_finish_time", installedFinishTime)
                .params("start_download_time", startDownloadTime)
                .params("track_id", Base64.encode(trackId.getBytes(Charsets.UTF_8)))
                .params("referrer", referrerEx)
                .build();
        return XRequest.newRequest(httpParams);
    }
}
