package com.xingjiabi.shengsheng.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.android.arouter.exception.HandlerException;
import com.alibaba.android.arouter.facade.Postcard;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.facade.callback.NavCallback;
import com.alibaba.android.arouter.launcher.ARouter;
import com.google.common.collect.Sets;
import com.xmhaibao.account.api.router.AccountPinsRouter;
import com.xmhaibao.account.api.router.AccountRouterPath;
import com.xmhaibao.call.api.router.CallPinsRouter;
import com.xmhaibao.chatroom.api.router.ChatRoomPinsRouter;
import com.xmhaibao.common_level.arouter.CommonLevelRouterPath;
import com.xmhaibao.family.api.router.FamilyPinsRouter;
import com.xmhaibao.forum.api.router.ForumPinsRouter;
import com.xmhaibao.live.api.router.LivePinsRouter;
import com.xmhaibao.message.api.router.MessagePinsRouter;
import com.xmhaibao.mine.api.router.FriendPinsRouter;
import com.xmhaibao.mine.api.router.MinePinsRouter;
import com.xmhaibao.multichat.api.router.MultiChatPinsRouter;
import com.xmhaibao.noble.api.contants.NoblePinsConstants;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import cn.taqu.lib.base.constants.PushConstants;
import cn.taqu.lib.base.helper.AppConfigHelper;
import cn.taqu.lib.base.router.ARouterManager;
import cn.taqu.lib.base.router.RouterLaunch;
import cn.taqu.lib.base.router.service.RelationService;
import cn.taqu.lib.base.router.service.RouterServicePath;
import cn.taqu.lib.base.router.service.RouterStartService;
import cn.taqu.lib.base.share.DefaultShareInfoActionExtraListener;
import cn.taqu.lib.base.utils.TaquDialogUtils;
import cn.taqu.lib.base.xtracker.AppCommonTracker;
import hb.common.data.AccountHelper;
import hb.common.widget.webview.WebViewActivity;
import hb.logupload.XLogUpload;
import hb.thirdtools.sharelogin.share.ShareDialog;
import hb.thirdtools.sharelogin.share.ShareInfo;
import hb.utils.ActivityUtils;
import hb.utils.Loger;
import hb.utils.StringUtils;


/**
 * 功能:公共跳转工具类
 * Created by hqy on 2018/5/29.
 * <p>
 * 2019-05-08 已废弃<br>
 * <p>
 * 由于路由跳转牵扯太多类要修改，所以暂不修改，待组件化大部分完成后再整理<br>
 * <p>
 * 组件module的跳转请用 {@link ARouterManager#routerStart()}<br>
 * <p>
 * 注意：新增的公共跳转请用路由的形式<br>
 *
 * @see RouterStartService
 * @see RouterLaunch
 * @see ARouterManager
 * @see RouterLaunch#dealJumpData(Context, String)
 */
@Deprecated
@Route(path = RouterServicePath.COMMON_LAUNCH_SERVICE)
public class CommonStartUtil implements RouterStartService {
    private static final String TAG = "CommonStartUtil";

    private static long lastClickTime;

    @Override
    public void dealJumpData_(Context context, String data, boolean isAnim) {
        dealJumpData(context, data, isAnim);
    }

    @Override
    public void dealJumpData_(Context context, String data) {
        dealJumpData(context, data);
    }

    @Override
    public void dealJumpData_(Context context, HashMap<String, String> paramMap) {
        dealJumpData(context, paramMap);
    }

    @Override
    public void resetClick() {
        //重置点击时间，处理场景：公共跳转-请求接口-再次跳转时会导致最后一次跳转不执行。
        lastClickTime = 0;
    }

    public static void dealJumpData(Context activity, String data, boolean isAnim) {
        dealJumpRelationWithNewRouter(activity, data, isAnim);
    }

    public static void dealJumpData(Context activity, String data) {
        dealJumpRelationWithNewRouter(activity, data, true);
    }


    public static void dealJumpData(Context activity, HashMap<String, String> paramMap) {
        if (paramMap == null || paramMap.isEmpty()) {
            return;
        }
        if (AppConfigHelper.getInfo().isCloseNewArouterJump() || !isOpenRouterJump(paramMap)) {
            dealJumpData(activity, paramMap, true);
        } else {
            String data = ARouterManager.map2UrlByEncode(paramMap);
            dealJumpRelationByRouter(activity, data, true);
        }
    }

    /**
     * 模块是否已经完成Router跳转改造，完成改造后，可以增加判断，开启开关。跳转执行/xxx/xxx新路由跳转方式
     *
     * @param paramMap 跳转参数，map类型
     * @return 是否开启 true：开启的模块。
     */
    private static boolean isOpenRouterJump(HashMap<String, String> paramMap) {
        String module = paramMap.get(PushConstants.PUSH_VALUE_MODULE);
        String action = paramMap.get(PushConstants.PUSH_VALUE_ACTION);
        return PushConstants.PUSH_MODULE_QUCALL.equalsIgnoreCase(module) ||
                (PushConstants.PUSH_MODULE_ACCOUNT.equalsIgnoreCase(module) && AccountRouterPath.PersonalMyLife.equalsIgnoreCase(action))
                || PushConstants.PUSH_MODULE_HICHAT.equalsIgnoreCase(module)
                || PushConstants.PUSH_MODULE_FORUM.equalsIgnoreCase(module)
                || PushConstants.PUSH_MODULE_ME.equalsIgnoreCase(module)
                || PushConstants.PUSH_MODULE_MINE.equalsIgnoreCase(module)
                || PushConstants.PUSH_MODULE_MESSAGE.equalsIgnoreCase(module)
                || PushConstants.PUSH_MODULE_IM.equalsIgnoreCase(module)
                || PushConstants.PUSH_MODULE_FAMILY.equalsIgnoreCase(module)
                || PushConstants.PUSH_MODULE_MULTI_CHAT.equals(module)
                || PushConstants.PUSH_MODULE_SHELL.equalsIgnoreCase(module)
                || PushConstants.PUSH_MODULE_STAR_SHINE.equalsIgnoreCase(module)
                || PushConstants.PUSH_TYPE_CHAT_ROOM.equalsIgnoreCase(module);
    }

    public static void dealJumpRelationWithNewRouter(Context activity, String data, boolean isAnim) {
        HashMap<String, String> paramMap = StringUtils.url2Map(data);
        if (paramMap == null || paramMap.isEmpty()) {
            return;
        }
        if (AppConfigHelper.getInfo().isCloseNewArouterJump() || !isOpenRouterJump(paramMap)) {
            dealJumpData(activity, paramMap, isAnim);
        } else {
            dealJumpRelationByRouter(activity, data, isAnim);
        }
    }

    /**
     * 默认走arouter，找不到再执行公用跳转
     * （String类型跳转，转换成Uri）
     *
     * @param context Context
     * @param data    String类型跳转地址
     * @param isAnim  是否需要动画
     */
    private static void dealJumpRelationByRouter(Context context, String data, boolean isAnim) {
        if (TextUtils.isEmpty(data)) {
            return;
        }
        dealJumpUri(context, Uri.parse(data), isAnim);
    }

    /**
     * 默认走arouter，找不到再执行公用跳转
     *
     * @param context Context
     * @param uri     跳转uri
     * @param isAnim  是否需要动画
     */
    private static void dealJumpUri(Context context, Uri uri, boolean isAnim) {
        if (null == uri || TextUtils.isEmpty(uri.toString())) {
            return;
        }
        try {
            ARouter.getInstance().build(uri).navigation(context, new NavCallback() {
                @Override
                public void onFound(Postcard postcard) {
                    super.onFound(postcard);
                    Loger.i(TAG, "dealJumpUri onFound postcard=", postcard.toString());
                }

                @Override
                public void onInterrupt(Postcard postcard) {
                    super.onInterrupt(postcard);
                    //拦截器等超时异常会上报
                    if (postcard.getTag() != null && postcard.getTag() instanceof HandlerException) {
                        HandlerException handlerException = (HandlerException) postcard.getTag();
                        //过滤"No message.",通常是主动在拦截器里onInterrupt(null)。
                        if (!"No message.".equalsIgnoreCase(handlerException.getMessage())) {
                            XLogUpload.newBuilder("ArouterError").text1("onInterrupt异常")
                                    .params("Exception", Log.getStackTraceString(handlerException))
                                    .params("uri",uri.toString())
                                    .post();
                        }
                    }
                }

                @Override
                public void onLost(Postcard postcard) {
                    Loger.i(TAG, "dealJumpUri onInterrupt postcard=", postcard.toString());
                    dealJumpData(context, StringUtils.url2Map(uri.toString()), isAnim);
                }

                @Override
                public void onArrival(Postcard postcard) {
                    //do nothing
                }
            });
        } catch (Exception e) {
            dealJumpData(context, StringUtils.url2Map(uri.toString()), isAnim);
            XLogUpload.newBuilder("ArouterError").text1("dealJumpUri")
                    .params("Exception", Log.getStackTraceString(e))
                    .params("uri",uri.toString())
                    .post();
            e.printStackTrace();
        }
    }

    /**
     * 解析点击事件，并跳转到相应页面。
     *
     * @param activity 页面
     * @param paramMap 数据 paramMap by lxj 修改为直接支持Map。2014-05-15
     * @return 返回首页的底部选中项
     */
    private static void dealJumpData(final Context activity, HashMap<String, String> paramMap, boolean isAnim) {
        long time = System.currentTimeMillis();
        if (Math.abs(time - lastClickTime) < 50) {
            return;
        }
        lastClickTime = time;
        try {
            Loger.i(TAG,paramMap.toString());
            String module = paramMap.get(PushConstants.PUSH_VALUE_MODULE);
            String action = paramMap.get(PushConstants.PUSH_VALUE_ACTION);
            String title = paramMap.get(PushConstants.PUSH_VALUE_TITLE);
            String id = paramMap.get(PushConstants.PUSH_VALUE_ID);
            String url = paramMap.get(PushConstants.PUSH_VALUE_URL);
            String cid = paramMap.get(PushConstants.PUSH_VALUE_CID);
            String param = paramMap.get(PushConstants.PUSH_VALUE_PARAM);
            String name = paramMap.get(PushConstants.PUSH_VALUE_NAME);
            String source = paramMap.get(PushConstants.PUSH_TYPE_SOURCE);
            // deeplink 跳转链接来源
            String linkFrom = paramMap.get(PushConstants.PUSH_TYPE_LINK_FROM);
            if (!TextUtils.isEmpty(linkFrom)) {
                // 上报埋点
                AppCommonTracker.INSTANCE.trackDeeplinkFrom(linkFrom, map2Url(paramMap, PushConstants.PUSH_TYPE_LINK_FROM));
            }
            Intent intent = null;
            switch (module) {
                // 交流区
                case PushConstants.PUSH_MODULE_FORUM:
                    ForumPinsRouter.forumRelationService().dealRelation(activity, action, paramMap);
                    break;
                //我的页面
                case PushConstants.PUSH_MODULE_MINE:
                    MinePinsRouter.mineRelationService().dealRelation(activity, action, paramMap);
                    break;
                case PushConstants.PUSH_MODULE_DRESS:
                    MinePinsRouter.mineRelationService().dealDressData(activity, action, paramMap);
                    break;
                case PushConstants.PUSH_MODULE_QUCALL:
                    CallPinsRouter.callRelationService().dealRelation(activity, action, paramMap);
                    break;
                case PushConstants.PUSH_TYPE_CHAT_ROOM:
                    ChatRoomPinsRouter.chatRoomRelationService().dealRelation(activity, action, paramMap);
                    break;
                case PushConstants.PUSH_MODULE_BEAN:
                    if (PushConstants.PUSH_TYPE_ACTION_RECHANGECALL.equalsIgnoreCase(action)) {
                        TaquDialogUtils.showBeanRechangeDlg(activity);
                    }
                    break;
                // WAP 和 WEB 同一个处理
                case PushConstants.PUSH_MODULE_WAP:
                case PushConstants.PUSH_MODULE_WEB:
                    // 推送的web页面
                    ARouterManager.baseRelationService().dealRelation(activity, action, paramMap);
                    break;
                case PushConstants.PUSH_MODULE_SHARE:
                    String shareContent = paramMap.get(PushConstants.PUSH_VALUE_CONTENT); // 内容(目前用于分享)
                    String shareImgUrl = paramMap.get(PushConstants.PUSH_VALUE_IMG_URL); // 图片URL地址(目前用于分享)
                    String shareUrl = paramMap.get(PushConstants.PUSH_VALUE_SHARE_URL);// 分享URL地址(目前用于分享)
                    String shareChannel = paramMap.get(PushConstants.PUSH_VALUE_SHARE_CHANNEL);// 分享渠道
                    String copyText = paramMap.get(PushConstants.PUSH_VALUE_SHARE_COPY_TEXT);
                    // 分享模块(目前是给外部第三方使用的)
                    if (PushConstants.PUSH_TYPE_ACTION_SHARE_WXCIRCLE.equalsIgnoreCase(action)) {
                        // 微信朋友圈分享
                        ShareInfo info = new ShareInfo();
                        info.setTitle(title);
                        info.setContent(shareContent);
                        info.setImageUrl(shareImgUrl);
                        info.setUrl(shareUrl);
                        info.setId(id);
                        info.setCopyText(copyText);
                        ShareDialog dialog = new ShareDialog(activity, info);
                        dialog.setShareActionDoExtraListener(new DefaultShareInfoActionExtraListener());
                        dialog.show();
                    } else if (PushConstants.PUSH_TYPE_ACTION_SHARE_All.equalsIgnoreCase(action)) {
                        ShareInfo info = new ShareInfo();
                        info.setTitle(title);
                        info.setContent(shareContent);
                        info.setImageUrl(shareImgUrl);
                        info.setUrl(shareUrl);
                        info.setId(id);
                        info.setCopyText(copyText);
                        ShareDialog dialog = new ShareDialog(activity, info);
                        dialog.setShareActionDoExtraListener(new DefaultShareInfoActionExtraListener());
                        dialog.show();
                    }
                    break;
                case PushConstants.PUSH_MODULE_ME:
                    MinePinsRouter.mineRelationService().dealMeData(activity, action, paramMap);
                    break;
                case PushConstants.PUSH_MODULE_PERSONAL:
                    if (PushConstants.PUSH_TYPE_ACTION_HOMEPAGE.equalsIgnoreCase(action)) {
                        String isMySelf = paramMap.get("isMyself");
                        if ("1".equals(isMySelf)) {
                            ForumPinsRouter.launchPersonalHomePageActivity(activity, AccountHelper.getAccountUuid());
                        } else {
                            String accountId = paramMap.get(PushConstants.PUSH_VALUE_ACCOUNT_ID);
                            String sendGift = paramMap.get(PushConstants.PUSH_VALUE_SEND_GIFT);
                            ForumPinsRouter.launchPersonalHomePageActivity(activity, accountId, source, TextUtils.equals("1", sendGift));
                        }
                    }
                    break;
                case PushConstants.PUSH_MODULE_FIND:
                    if (PushConstants.PUSH_TYPE_ACTION_LIST.equalsIgnoreCase(action)) {
                        //2019-02-12 下线功能
                    } else if (PushConstants.PUSH_TYPE_ACTION_FIND_COVER.equalsIgnoreCase(action)) {
                    }
                    break;
                case PushConstants.PUSH_MODULE_IM:
                    MessagePinsRouter.messageRelationService().dealImData(activity, action, paramMap);
                    break;
                case PushConstants.PUSH_MODULE_MESSAGE:
                    MessagePinsRouter.messageRelationService().dealRelation(activity, action, paramMap);
                    break;
                case PushConstants.PUSH_MODULE_FAMILY:
                    FamilyPinsRouter.familyRelationService().dealRelation(activity, action, paramMap);
                    break;
                case PushConstants.PUSH_MODULE_ACCOUNT:
                    if (PushConstants.PUSH_TYPE_ACTION_LOGIN.equals(action)) {
                        AccountPinsRouter.accountService().launchLogin();
                    } else if (TextUtils.equals("account_retrieve", action)) {
                        //账号找回
                        Activity topActivity = ActivityUtils.getTopActivity();
                        if (topActivity instanceof WebViewActivity) {
                            ActivityUtils.finishActivity(topActivity);
                        }
                        AccountPinsRouter.launchAccountRetrieveActivity(source);
                    }
                    break;
                //交友
                case PushConstants.PUSH_MODULE_FRIEND:
                    if (PushConstants.PUSH_FRIEND_ACTION_INTERESTED.equalsIgnoreCase(action)) {
                        FriendPinsRouter.friendService().showFriendInterestedListDialog(source);
                    }
                    break;
                //直播
                case PushConstants.PUSH_MODULE_LIVE:
                    if (NoblePinsConstants.PUSH_TYPE_ACTION_NOBLE.equalsIgnoreCase(action)) {
                        ARouterManager.nobleService().launchNobleListActivity();
                    } else {
                        LivePinsRouter.liveRelationService().dealRelation(activity, action, paramMap);
                    }
                    break;
                //璀璨等级特权
                case PushConstants.PUSH_MODULE_RADIANT_LEVEL:
                    RelationService serviceImpl = ARouterManager.brightLevelService().getRelationServiceImpl();
                    if (serviceImpl != null) {
                        serviceImpl.dealRelation(activity, action, paramMap);
                    }
                    break;
                //用户等级特权
                case PushConstants.PUSH_MODULE_COMMON_LEVEL:
                    if (CommonLevelRouterPath.COMMON_LEVEL_ACTIVITY_ACTION.equalsIgnoreCase(action)) {
                        ARouterManager.commonLevelService().launchCommonLevelActivity(source);
                    }
                    break;
                case PushConstants.PUSH_MODULE_APP:
                    if (PushConstants.PUSH_TYPE_ACTION_BACK_VIEW.equalsIgnoreCase(action)) {
                        Activity topActivity = ActivityUtils.getActivity(activity);
                        if (topActivity == null) {
                            topActivity = ActivityUtils.getTopActivity();
                        }
                        if (topActivity != null) {
                            topActivity.finish();
                        }
                    }
                    break;
                case PushConstants.PUSH_MODULE_MULTI_CHAT:
                    MultiChatPinsRouter.multiChatRelationService().dealRelation(activity,action,paramMap);
                    break;

                // 通用业务层——实名认证
                case PushConstants.PUSH_VALUE_FULL_BUSINESS:
                    ARouterManager.commonActivityService().dealRelation(activity, action, paramMap);
                    break;
                // 贝壳
                case PushConstants.PUSH_MODULE_SHELL:
                    RelationService shellRelationServiceImpl = ARouterManager.shellService().getShellRelationServiceImpl();
                    if (shellRelationServiceImpl != null) {
                        shellRelationServiceImpl.dealRelation(activity, action, paramMap);
                    }
                    break;
                // 成长体系
                case PushConstants.PUSH_MODULE_STAR_SHINE:
                    RelationService starShineService = ARouterManager.starShineService().getStarShineRelationServiceImpl();
                    if (starShineService != null) {
                        starShineService.dealRelation(activity, action, paramMap);
                    }
                    break;
                default:
                    break;
            }
            if (!isAnim) {
                // 如果不需要跳转动画
                if (activity instanceof Activity) {
                    ((Activity) activity).overridePendingTransition(0, 0);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private static void startActivity(Context activity, Intent intent) {
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        activity.startActivity(intent);
    }

    @Override
    public void init(Context context) {

    }

    /**
     * map转换url xx=1&bb=2
     * 后续转移至 hb.utils#StringUtils中
     * @return url
     */
    private static String map2Url(Map<String, String> map,String... ignoreKeys){
        StringBuffer sb = new StringBuffer();
        Set<String> ignoreKeysSet = Sets.newHashSet(ignoreKeys);
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if(ignoreKeysSet.contains(entry.getKey())){
                continue;
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        if (sb.length() != 0) {
            return sb.substring(0, sb.length() - 1);
        }
        return sb.toString();
    }




}
