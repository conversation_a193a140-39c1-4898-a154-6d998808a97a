<?xml version="1.0" encoding="utf-8"?>
<hb.drawable.shape.view.HbFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fl_live_msg_item"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginRight="73dp"
    android:layout_marginTop="1dp"
    android:layout_marginBottom="1dp"
    android:padding="8dp"
    android:clipChildren="false"
    app:corner="8dp"
    app:solid="@color/black_alpha_30"
    android:clipToPadding="false"
    android:lineSpacingExtra="3dp"
    android:orientation="horizontal">


    <hb.xemoji.view.XEmojiTextView
        tools:text="系统消息"
        android:id="@+id/tvCallMsg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lineHeight="17dp"
        android:textColor="@color/white_alpha_75"
        android:textSize="12sp" />

</hb.drawable.shape.view.HbFrameLayout>