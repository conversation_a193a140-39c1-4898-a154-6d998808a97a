<?xml version="1.0" encoding="utf-8"?>
<hb.drawable.shape.view.HbConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    app:solid="@color/TH_Navy002"
    android:paddingBottom="16dp"
    app:corner="16dp"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/ivBgHeader"
        android:layout_width="270dp"
        android:layout_height="100dp"
        android:background="@drawable/live_fans_group_chat_create_guide_bg"
        app:layout_constraintEnd_toEndOf="@id/vBackground"
        app:layout_constraintStart_toStartOf="@id/vBackground"
        app:layout_constraintTop_toTopOf="parent" />


    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivIcon"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        app:layout_constraintEnd_toEndOf="@id/ivBgHeader"
        app:layout_constraintStart_toStartOf="@id/ivBgHeader"
        app:layout_constraintTop_toTopOf="@id/ivBgHeader"
        app:placeholderImage="@color/transparent"
        tools:placeholderImage="@color/c1" />

    <TextView
        android:id="@+id/tvRepairCardCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="3dp"
        android:layout_marginBottom="3dp"
        android:textColor="@color/white"

        android:textSize="@dimen/TH_FONT_H3"
        app:layout_constraintBottom_toBottomOf="@id/ivIcon"
        app:layout_constraintEnd_toEndOf="@id/ivIcon" />

    <TextView
        android:id="@+id/tvRepairTitle"
        style="@style/Text.H2.G990"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@id/ivBgHeader"
        app:layout_constraintStart_toStartOf="@id/ivBgHeader"
        app:layout_constraintTop_toBottomOf="@id/ivIcon"
        tools:text="补签说明" />

    <TextView
        android:id="@+id/tvRepairDesc"
        style="@style/Text.B2.G600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginTop="6dp"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="@id/ivBgHeader"
        app:layout_constraintStart_toStartOf="@id/ivBgHeader"
        app:layout_constraintTop_toBottomOf="@id/tvRepairTitle"
        tools:text="补签提示文案" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvCancel"
        style="@style/Text.H3.B100"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:text="取消"
        app:corner="30dp"
        app:layout_constraintEnd_toStartOf="@id/tvConfirm"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@id/ivBgHeader"
        app:layout_constraintTop_toBottomOf="@id/tvRepairDesc"
        app:layout_constraintVertical_weight="1"
        app:solid="@color/TH_Navy100" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvConfirm"
        style="@style/Text.H3.B100"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:text="立即补签"
        app:corner="30dp"
        app:layout_constraintTop_toBottomOf="@id/tvRepairDesc"
        app:layout_constraintEnd_toEndOf="@id/ivBgHeader"
        app:layout_constraintStart_toEndOf="@id/tvCancel"
        app:layout_constraintVertical_weight="1"
        app:solid="@color/TH_Yellow600" />


</hb.drawable.shape.view.HbConstraintLayout>