<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <hb.drawable.shape.view.HbView
        android:id="@+id/viewBg"
        android:layout_width="0dp"
        android:layout_height="215dp"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="15dp"
        android:layout_marginBottom="27dp"
        app:corner="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:solid="@color/white" />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivIcon"
        android:layout_width="161dp"
        android:layout_height="83dp"
        android:layout_marginTop="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/viewBg"
        app:placeholderImage="@drawable/call_game_guess_invite_ic" />

    <TextView
        android:id="@+id/tvInviteContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="@color/TH_Gray990"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivIcon" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/btnCancel"
        android:layout_width="137dp"
        android:layout_height="46dp"
        android:layout_marginBottom="27dp"
        android:gravity="center"
        android:text="取消邀请"
        android:textColor="#FF2442"
        android:textStyle="bold"
        app:corner="23dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewBg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:stroke_color="#FF2442"
        app:stroke_width="1dp" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/btnRefuse"
        android:layout_width="137dp"
        android:layout_height="46dp"
        android:layout_marginEnd="13dp"
        android:layout_marginBottom="27dp"
        android:gravity="center"
        android:text="残忍拒绝"
        android:textColor="#FF2442"
        android:textStyle="bold"
        android:visibility="gone"
        app:corner="23dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewBg"
        app:layout_constraintEnd_toStartOf="@+id/btnAccept"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:stroke_color="#FF2442"
        app:stroke_width="1dp" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/btnAccept"
        android:layout_width="137dp"
        android:layout_height="46dp"
        android:layout_marginBottom="27dp"
        android:gravity="center"
        android:text="接受邀请"
        android:textColor="@color/white"
        android:textStyle="bold"
        android:visibility="gone"
        app:corner="23dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewBg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btnRefuse"
        app:solid="#FF2442" />

    <cn.taqu.lib.base.widget.rangeseekbar.RangeSeekBar
        android:id="@+id/progressBarResourceLoading"
        android:layout_width="242dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="40dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewBg"
        app:layout_constraintEnd_toEndOf="@+id/viewBg"
        app:layout_constraintStart_toStartOf="@+id/viewBg"
        app:rsb_gravity="bottom"
        app:rsb_indicator_arrow_size="3dp"
        app:rsb_indicator_background_color="#FF2442"
        app:rsb_indicator_height="22dp"
        app:rsb_indicator_radius="5dp"
        app:rsb_indicator_show_mode="alwaysShow"
        app:rsb_indicator_text_size="12sp"
        app:rsb_indicator_width="36dp"
        app:rsb_mode="single"
        app:rsb_progress_color="#FF2442"
        app:rsb_progress_default_color="#FFDADA"
        app:rsb_progress_height="10dp"
        app:rsb_progress_radius="10dp"
        app:rsb_indicator_margin="5dp"
        app:rsb_thumb_height="10dp"
        app:rsb_thumb_drawable="@color/transparent"
        />

</merge>