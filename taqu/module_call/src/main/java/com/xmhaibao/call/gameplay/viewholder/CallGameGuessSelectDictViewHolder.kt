package com.xmhaibao.call.gameplay.viewholder

import android.view.ViewGroup
import com.xmhaibao.call.R
import com.xmhaibao.call.databinding.CallGameGuessSelectDictItemBinding
import com.xmhaibao.call.gameplay.bean.CallGameGuessPlayConfigBean
import hb.utils.ColorUtils
import hb.xadapter.XBaseViewHolder

/**
 * description: 选择题型 ViewHolder
 *
 * <AUTHOR>
 * @date 2024/4/17
 */
class CallGameGuessSelectDictViewHolder(parent: ViewGroup) :
    XBaseViewHolder<CallGameGuessPlayConfigBean.DictBean>(
        parent,
        R.layout.call_game_guess_select_dict_item
    ) {

    val binding = CallGameGuessSelectDictItemBinding.bind(itemView)
    var item: CallGameGuessPlayConfigBean.DictBean?=null

    override fun onBindView(item: CallGameGuessPlayConfigBean.DictBean?) {
        this.item = item
        item?.run {
            binding.ivDict.setImageFromUrl(img)
            binding.tvDict.text = title
            if (isSelected) {
                binding.ivDict.apply {
                    scaleY = 1.1f
                    scaleX = 1.1f
                    setBackgroundResource(R.drawable.call_game_guess_select_dict_icon_s_bg)
                }
                binding.tvDict.setBackgroundResource(R.drawable.call_game_guess_select_dict_text_s_bg)
                binding.tvDict.setTextColor(ColorUtils.getColorByRGBA("#985626"))
            } else {
                binding.ivDict.apply {
                    scaleY = 1.0f
                    scaleX = 1.0f
                    setBackgroundResource(R.drawable.call_game_guess_select_dict_icon_n_bg)
                }
                binding.tvDict.setBackgroundResource(R.drawable.call_game_guess_select_dict_text_n_bg)
                binding.tvDict.setTextColor(ColorUtils.getColorByRGBA("#594133"))
            }
        }
    }

}