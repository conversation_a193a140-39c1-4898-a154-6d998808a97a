package com.xmhaibao.call.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.LinearLayout
import com.xmhaibao.call.R
import com.xmhaibao.call.databinding.CallBottomButtonViewBinding
import com.xmhaibao.call.databinding.CallCallEarningsMarkViewBinding
import dp
import hb.utils.ColorUtils
import hb.utils.Loger


/**
 *  通话页面底部按钮控件
 * 上图标 下文字
 * <AUTHOR>
 * @date 2023-11-22
 */
class CallBottomButtonView @kotlin.jvm.JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : LinearLayout(context, attrs) {

    private val redDotRadius = 4.dp.toFloat()

    private var dotX = 0F
    private var dotY = 0F
    private var isNeedShowRedDot = false

    private val paint by lazy {
        Paint().apply {
            setColor(ColorUtils.getColor("#FF5A5A"))
            style = Paint.Style.FILL
        }
    }

    private val binding: CallBottomButtonViewBinding by lazy {
        CallBottomButtonViewBinding.bind(this)
    }

    init {

        LayoutInflater.from(context).inflate(R.layout.call_bottom_button_view, this)
        val array = context.obtainStyledAttributes(attrs, R.styleable.CallBottomButtonView)
        val btnText = array.getString(R.styleable.CallBottomButtonView_btn_text)
        val btnIcon = array.getDrawable(R.styleable.CallBottomButtonView_btn_icon)
        array.recycle()
        binding.ivIcon.setImageDrawable(btnIcon)
        binding.tvName.text = btnText ?: ""
        orientation = VERTICAL
    }

    fun setBtnText(text: CharSequence?) {
        binding.tvName.text = text ?: ""
    }

    fun setIconResId(resId: Int) {
        binding.ivIcon.setImageResource(resId)
    }

    /**
     * 设置是否显示小红点
     * @param visible
     */
    fun setRedDotVisible(visible: Boolean) {
        isNeedShowRedDot = visible
        postInvalidate()
    }


    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        super.onLayout(changed, l, t, r, b)
        dotX = (r - l) - 10.dp.toFloat() - redDotRadius
        dotY = redDotRadius
    }

    override fun dispatchDraw(canvas: Canvas) {
        super.dispatchDraw(canvas)
        if (isNeedShowRedDot) {
            canvas.drawCircle(dotX, dotY, redDotRadius, paint)
        }
    }
}