package com.xmhaibao.call.risk.event

import hb.common.data.AccountHelper
import hb.qim.base.core.IResponseParse
import hb.utils.EventBusUtils

/**
 * 触发风控提示
 *  需求地址：https://o15vj1m4ie.feishu.cn/docx/NWPLdXafvoTsmCxM5Fqcx6YHn4D
 *  技术文档：https://o15vj1m4ie.feishu.cn/wiki/DoaAwWt2Fi5cdnkc8cpc9S90nhb
 * <AUTHOR>
 * @date 2023-09-07
 */
class RiskTipsEvent : IResponseParse<RiskTipsEvent> {
    companion object {
        /**
         *  触发风控的类型 命中第一帧弹窗
         */
        const val RISK_TYPE_SHOW_DIALOG = "1"

        /**
         *   N帧涉黄 toast提示 挂断通话
         */
        const val RISK_TYPE_END_CALL = "2"
    }

    /**
     * 通话uuid
     */
    var callUuid: String? = ""

    /**
     *  触发风控的类型 1｜2
     *  1：命中第一帧弹窗 1
     *  2： N帧涉黄 toast提示 延迟禁播
     */
    var riskType: String? = ""

    /**
     * 触发方Uuid
     */
    var triggerUuid: String? = ""

    /**
     * 提示文案
     */
    var contentTxt: String? = ""

    override fun parseResponse(event: String?, vararg args: Any?): RiskTipsEvent {

        if (args.isNotEmpty()) {
            callUuid = args[0]?.toString() ?: ""
            riskType = args[1]?.toString() ?: ""
            triggerUuid = args[2]?.toString() ?: ""
            contentTxt = args[3]?.toString() ?: ""
            EventBusUtils.post(this)
        }
        return this
    }

    /**
     * 是否为己方触发的
     */
    fun isSelfTrigger(): Boolean = triggerUuid == AccountHelper.getAccountUuid()

    /**
     * 命中1帧涉黄 弹窗提示
     */
    fun isShowRiskDialog(): Boolean = riskType == RISK_TYPE_SHOW_DIALOG

    /**
     * 连续涉黄命中N帧 结束通话
     */
    fun isEndCallRisk(): Boolean = riskType == RISK_TYPE_END_CALL

    override fun toString(): String {
        return "RiskTipsEvent(callUuid=$callUuid, riskType=$riskType, triggerUuid=$triggerUuid, contentTxt=$contentTxt)"
    }

}