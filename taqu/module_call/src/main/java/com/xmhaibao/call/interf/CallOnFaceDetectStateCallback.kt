package com.xmhaibao.call.interf

/**
 * 人脸检测状态回调
 *
 * <AUTHOR>
 * @date 2022-06-11
 */
interface CallOnFaceDetectStateCallback {
    /**
     * 人脸检测结果变化
     *
     * @param hasFace true 有人脸 false 无人脸
     */
    fun onFaceDetectChanged(hasFace: Boolean)

    /**
     * 超过A 秒 匹配冻结
     *
     */
    fun onMatchBlocking()

    /**
     * 超过B秒 强制退出匹配
     */
    fun onForceExitMatching()

    /**
     * 恢复人脸检测
     */
    fun onRestoreDetect()
}