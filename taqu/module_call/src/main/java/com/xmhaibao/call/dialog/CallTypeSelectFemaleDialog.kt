package com.xmhaibao.call.dialog

import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.ushengsheng.widget.BaseBottomDialog
import com.xmhaibao.call.R
import com.xmhaibao.call.api.bean.FemaleCallTypeParams
import com.xmhaibao.call.api.feature.topicguide.widget.CallTopicGuideInfoView
import com.xmhaibao.call.api.router.CallPinsRouter
import com.xmhaibao.call.api.util.CallPinsTracker
import com.xmhaibao.call.api.util.CallReplaceWordsUtils
import com.xmhaibao.call.bean.CallSelectDialogBannerBean
import com.xmhaibao.call.databinding.CallTypeSelectFemaleDialogBinding
import com.xmhaibao.call.helper.StartCallHelper
import com.xmhaibao.call.repository.CallCommonRepository
import dp
import hb.kotlin_extension.gone
import hb.kotlin_extension.visible
import hb.utils.ActivityUtils
import hb.xrequest.awaitNullable
import hb.xrequest.launchHttp
import hb.xtoast.XToastUtils

/**
 * 女用户主动通话类型弹窗选择
 *
 * 2023-12-4 新增圣诞活动，顶部新增活动图及UI修改：飞书需求（https://o15vj1m4ie.feishu.cn/wiki/Nn0dwUnP2iZke5kxrvtczGnNnHf）
 *
 * <AUTHOR>
 * @date 2023-05-30
 */
class CallTypeSelectFemaleDialog(
    context: Context,
    var params: FemaleCallTypeParams
) : BaseBottomDialog(context), View.OnClickListener {

    companion object {
        /**
         * 显示 女用户主动通话类型弹窗
         * @param context 上下文
         * @param params 参数
         */
        fun showDialog(context: Context, params: FemaleCallTypeParams) {
            val activity = ActivityUtils.getActivity(context)
            activity?.apply {
                if (this.isFinishing) {
                    return
                }
                val dialog =
                    CallTypeSelectFemaleDialog(this, params)
                dialog.show()
            }
        }
    }

    override fun getLayoutResId() = R.layout.call_type_select_female_dialog
    private val mBinding: CallTypeSelectFemaleDialogBinding by lazy {
        val root = findViewById<View>(R.id.clDialogRoot)
        CallTypeSelectFemaleDialogBinding.bind(root)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window?.setDimAmount(0.6f)
        initData()
        requestBannerList()
        initListener()

    }

    private fun initListener() {
        mBinding.tvCallSetting.setOnClickListener(this)
        mBinding.tvVideoCall.setOnClickListener(this)
        mBinding.tvAudioCall.setOnClickListener(this)
        mBinding.tvCancel.setOnClickListener(this)
    }

    private fun initData() {
        loadTipsContent()
        loadCallSelectText()
        setTopicGuideInfoView()
    }

    private fun requestBannerList() {
        this.lifecycleScope.launchHttp({
            val data =
                CallCommonRepository.getCallSelectDialogBannerList(params.otherUuid)
                    .awaitNullable()
            setBannerView(data)
        }, {
            setBannerView(null)
            true
        })

    }




    /**
     * 处理顶部tips消息
     */
    private fun loadTipsContent() {
//        if (params.heartScoreLimit > 0) {
//            mBinding.tvTips.text = "心动值达到${params.heartScoreLimit}解锁语音视频功能"
//        } else {
//            mBinding.tvTips.text = "剩余${params.activeCallNum}次主动聊天机会"
//        }
        val tipsContent = CallReplaceWordsUtils.replaceContent(params.callStatusInfo?.titleStyleTextBean)
        if (tipsContent.isNullOrEmpty().not()) {
            mBinding.tvTips.visible()
            mBinding.tvTips.text = tipsContent
        } else {
            mBinding.tvTips.gone()
        }
    }

    /**
     * 处理拨打选项文案逻辑
     */
    private fun loadCallSelectText() {
        val statusInfo = params.callStatusInfo
        when (params.callStatus) {
            1 -> {
                mBinding.tvAudioCall.isVisible = true
                mBinding.tvVideoCall.isVisible = false
                mBinding.viewAudioLine.isVisible = true
                mBinding.viewVideoLine.isVisible = false
            }
            2 -> {
                mBinding.tvAudioCall.isVisible = false
                mBinding.tvVideoCall.isVisible = true
                mBinding.viewAudioLine.isVisible = false
                mBinding.viewVideoLine.isVisible = true
            }
            3 -> {
                mBinding.tvAudioCall.isVisible = true
                mBinding.tvVideoCall.isVisible = true
                mBinding.viewAudioLine.isVisible = true
                mBinding.viewVideoLine.isVisible = true
            }
        }
        statusInfo?.let {
            if (it.callPrice.isNullOrEmpty().not()) {
                mBinding.tvAudioCall.text = "语音通话（${it.callPrice}${it.unit}/分钟）"
            }
            if (it.videoPrice.isNullOrEmpty().not()) {
                mBinding.tvVideoCall.text = "视频通话（${it.videoPrice}${it.unit}/分钟）"
            }
        }
        val rightId = if (params.activeCallNum <= 0) R.drawable.call_lock_new_ic else 0
        mBinding.tvAudioCall.setCompoundDrawablesWithIntrinsicBounds(0, 0, rightId, 0)
        mBinding.tvVideoCall.setCompoundDrawablesWithIntrinsicBounds(0, 0, rightId, 0)
    }

    private fun setTopicGuideInfoView() {
        if (CallTopicGuideInfoView.checkCanShowView(params.callStatusInfo?.topicGuideInfo)) {
            mBinding.callTopicGuide.setData(
                params.callStatusInfo?.topicGuideInfo,
                params.callStatusInfo?.topicGuideInfo?.otherUuid,
                false,
                false
            )
        }
    }

    override fun onClick(v: View?) {
        when (v) {
            mBinding.tvVideoCall -> {
                handlerCallClick(true)
            }
            mBinding.tvAudioCall -> {
                handlerCallClick(false)
            }
            mBinding.tvCallSetting -> {
                CallPinsRouter.launchCallHostSettingActivity()
            }
            mBinding.tvCancel -> {
                dismiss()
            }
        }
    }

    /**
     * 设置顶部图片数据
     */
    private fun setBannerView(bean: CallSelectDialogBannerBean?) {
        mBinding.bannerView.setBanner(bean)
        if (bean?.bannerList.isNullOrEmpty()) {
            mBinding.bannerView.gone()
        } else {
            mBinding.bannerView.visible()
        }
        val layoutParams = (mBinding.bannerView.layoutParams as MarginLayoutParams)
        if (mBinding.tvTips.isVisible) {
            layoutParams.setMargins(0, 0, 0, 22.dp)
        } else {
            layoutParams.setMargins(0, 0, 0, 0.dp)
        }
    }
    /**
     * 处理视频和语音点击事件
     * @param isVideo 是否是视频 true 是
     */
    private fun handlerCallClick(isVideo: Boolean) {
        if (params.heartScoreLimit > 0) {
            XToastUtils.show("双方心动值达到${params.heartScoreLimit}可解锁音视频功能")
            return
        }
        if (params.activeCallNum <= 0) {
            XToastUtils.show("你今天已多次邀请对方，明天再来吧")
            return
        }
        CallPinsTracker.trackQuCallButtonMessage(
            params.quCallPosition,
            if (isVideo) "视频" else "语音",
            params.source,
            params.otherUuid
        )
        StartCallHelper.checkAndStartCallFromFemale(
            mContext, params.otherUuid, params.source, isVideo, params.quCallPosition, false,""
        )
        dismiss()
    }

}
