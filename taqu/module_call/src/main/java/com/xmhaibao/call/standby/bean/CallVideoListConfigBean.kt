package com.xmhaibao.call.standby.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import java.io.Serializable

/**
 * 视频列表配置
 * 2024年5月1期 [需求](https://project.feishu.cn/haibao/story/detail/3768830153) 新增
 * <AUTHOR>
 * @date 2024-05-09
 */
class CallVideoListConfigBean : Serializable, IDoExtra {

    /**
     * 是否显示搭讪按钮  1显示 0不显示
     */
    @SerializedName("show_strike_up")
    var showStrikeUp: String? = ""


    /**
     * 视频曝光时长 单位秒 -1时不上报
     * [新增需求](https://project.feishu.cn/haibao/story/detail/4044873494)
     */
    @SerializedName("video_expose_threshold_time")
    var videoThresholdTime: String? = ""

    /**
     * 每个视频停留时长达到下发的时长 播放立即连线按钮上呼吸lottie
     * 单位秒 客户端默认5s
     */
    @SerializedName("stay_second")
    var staySecond: String? = "5"

    /**
     *  是否显示引导划卡
     */
    @SerializedName("show_slider_tip")
    var showSliderTip:String = "0"

    /**
     * 看视频赚免费次数的时间
     */
    @SerializedName("earn_free")
    var earnFreeList:List<EarnFreeBean> = mutableListOf()

    /**
     *每次任务可以获取的免费次数
     */
    @SerializedName("free_count")
    var freeCount: String = "1"

    /**
     * 充值福袋引导，1-展示，0-不展示
     */
    @SerializedName("lucky_bag_recharge_guide")
    var luckyBagRechargeGuide:String = ""

    /**
     * UI改版样式
     * 1显示实验组样式 0 显示旧样式
     */
    @SerializedName("is_new_style")
    var newStyle: String? = ""

    /**
     *  是否显示引导划卡
     *  true:是。
     */
    fun isShowSliderTip(): Boolean {
        return "1" == showSliderTip
    }

    /**
     * 充值福袋引导，1-展示，0-不展示
     *  true:是。
     */
    fun isShowLuckyBagRechargeGuide(): Boolean {
        return "1" == luckyBagRechargeGuide
    }

    /**
     * 是否显示搭讪按钮
     */
    fun isShowStrikeUp() = showStrikeUp == "1"

    /**
     * 是否显示新样式
     */
    fun isNewStyle(): Boolean {
        return newStyle == "1"
    }


    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        //do nothing
    }

    override fun toString(): String {
        return "showStrikeUp=$showStrikeUp"
    }

    class EarnFreeBean : Serializable {
        @SerializedName("task_time")
        var taskTime: Int = 0

        @SerializedName("task_id")
        var taskId: String = ""

        @SerializedName("task_free")
        var freeCount: String = "1"
    }

}