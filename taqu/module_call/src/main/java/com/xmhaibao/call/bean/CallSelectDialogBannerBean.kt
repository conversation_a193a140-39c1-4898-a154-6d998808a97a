package com.xmhaibao.call.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName

/**
 * TODO 类描述
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
class CallSelectDialogBannerBean : IDoExtra {
    @SerializedName("banner_list")
    var bannerList: List<CallSelectDialogBannerItemBean>? = null
    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        bannerList?.forEach {
            it.doExtra(response)
        }
    }
}