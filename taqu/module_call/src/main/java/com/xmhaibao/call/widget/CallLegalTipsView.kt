package com.xmhaibao.call.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.xmhaibao.call.R
import com.xmhaibao.call.databinding.CallLegalTipsViewBinding
import hb.drawable.shape.view.HbLinearLayout


/**
 * 趣聊风控提示控件
 *
 * <AUTHOR>
 * @date 2023-11-26
 */
class CallLegalTipsView @kotlin.jvm.JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : HbLinearLayout(context, attrs) {
    private val binding = CallLegalTipsViewBinding.inflate(LayoutInflater.from(context), this)

    fun setText(txt: CharSequence?) {
        binding.tvLegal.text = txt ?: ""
    }
    /**
     * 设置图标
     *
     * @param resId 图标资源ID
     */
    fun setIconResId(resId: Int) {
        binding.ivLegalIcon.setImageResource(resId)
    }
    /**
     * 设置文本的颜色
     *
     * @param color 要设置的颜色值
     */
    fun setLegalTextColor(color: Int) {
        binding.tvLegal.setTextColor(color)
    }


}