package com.xmhaibao.call.callwidget.banner

import com.xmhaibao.call.interf.CallCommonOperateListener

/**
 * activity 提供给banner的能力
 *
 * <AUTHOR>
 * @date 2023-11-21
 */
interface ICallActivityBanner : CallCommonOperateListener {
    /**
     * 显示首充弹窗
     * @param source 弹窗来源
     * @param countDownTime 倒计时时间
     */
    fun onCallFirstRechargeGiftDialogShow(source: String?, countDownTime: Int)



    /**
     * 显示心愿单弹窗
     *
     */
    fun onShowWishListDialog()
}