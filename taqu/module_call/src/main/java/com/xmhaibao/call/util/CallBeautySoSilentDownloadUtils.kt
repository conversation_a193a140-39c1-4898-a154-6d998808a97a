package com.xmhaibao.call.util

import android.os.Looper
import com.xmhaibao.bytedance.ByteConfig
import com.xmhaibao.call.mt.BeautyManager
import hb.dynamic.DynamicStore
import hb.dynamic.download.DynamicDownloadConsumer
import hb.kotlin_extension.delayLaunch
import hb.utils.Loger
import hb.utils.NetworkUtils
import hb.xthread.XThreadPool
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume

/**
 * 美颜组件静默预下载
 *
 * **静默下载时机**
 * * 女用户启动应用时静默下载
 * * 男用户进入standby匹配页面静默下载
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
object CallBeautySoSilentDownloadUtils {
    private const val TAG = "CallBeautySoDownloadUtils"

    /**
     * 是否已经下载成功
     */
    private var isDownloadSuccess = false

    /**
     * 是否已经在下载了
     */
    private var isDownloading = false

    /**
     * 下载失败重试次数
     */
    private var maxRetryCount = 10

    /**
     * 当前重试次数
     */
    private var retryCount = 1

    /**
     * 回调监听
     */
    private val callBacks = mutableListOf<DownloadCallBack>()

    /**
     * 标记是否检查过美颜组件下载
     */
    private var isChecked = false

    /**
     * 开始检查
     * 下载成功和下载中直接无需重复处理
     */
    fun startCheck() {
        if (isDownloadSuccess || isDownloading) {
            Loger.d(
                TAG, "美颜组件静默下载检查",
                "isDownloadSuccess=$isDownloadSuccess  isDownloading=$isDownloading"
            )
            return
        }
        retryCount = 1
        performDownload()
    }

    fun addListener(callBack: DownloadCallBack) {
        if (!callBacks.contains(callBack)) {
            callBacks.add(callBack)
        }
    }

    fun removeListener(callBack: DownloadCallBack) {
        if (callBacks.contains(callBack)) {
            callBacks.remove(callBack)
        }
    }

    /**
     * 通知下载结果
     */
    private fun notifyDownloadResult(success: Boolean) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            callBacks.forEach {
                it.call(success)
            }
            return
        }
        GlobalScope.launch(XThreadPool.Main().asCoroutineDispatcher()){
            callBacks.forEach {
                it.call(success)
            }
        }
    }

    /**
     * 执行下载
     */
    private fun performDownload() {
        if (isDownloadSuccess) {
            notifyDownloadResult(true)
            return
        }

        GlobalScope.launch(XThreadPool.Main().asCoroutineDispatcher()) {
            val io = XThreadPool.IO().get().asCoroutineDispatcher()
            val isDownload = withContext(io) {
                //耗时操作
                isRealDownSuccess(BeautyManager.MT_CHECK_DOWN_CALL_SILENT_DOWNLOAD)
            }
            //标记已经检查过是否下载成功
            isChecked = true
            if (isDownload) {
                Loger.d(TAG, "美颜资源已下载")
                isDownloadSuccess = true
                notifyDownloadResult(true)
                return@launch
            }
            //开始下载美颜组件
            isDownloading = true
            val downloadState = downloadBeautify()
            if (downloadState) {
                //下载完成再检查一下是否成功
                isDownloadSuccess = withContext(io) {
                    isRealDownSuccess(BeautyManager.MT_CHECK_DOWN_CALL_SILENT_DOWNLOAD)
                }
            }
            //下载成功 回调回去
            if (isDownloadSuccess) {
                isDownloading = false
                notifyDownloadResult(true)
                Loger.d(TAG, "美颜组件下载成功")
                return@launch
            }
            //下载失败，重试机制
            retryDownload()

        }
    }

    /**
     * 重试
     */
    private fun retryDownload() {
        // 不需要失败重试 或者重试失败次数到最大次还是失败
        if (maxRetryCount == 0 || retryCount >= maxRetryCount) {
            Loger.d(TAG, "美颜组件重试下载失败 ${retryCount}/${maxRetryCount}")
            isDownloading = false
            notifyDownloadResult(false)
            return
        }

        if (!NetworkUtils.isConnected()) {
            Loger.d(TAG, "美颜组件重试下载 网络不可用，等待网络可用继续重试")
            NetworkUtils.registerNetworkStatusChangedListener(networkStatusChangedListener)
            return
        }
        Loger.d(TAG, "美颜组件重试下载 （${retryCount}/${maxRetryCount}）")
        //网络正常连接 稍微延迟下再重试
        retryCount++
        GlobalScope.delayLaunch(retryCount * 1000L) {
            performDownload()
        }
    }

    /**
     * 网络状态监听
     */
    private val networkStatusChangedListener =
        object : NetworkUtils.OnNetworkStatusChangedListener {
            override fun onDisconnected() {
                //do nothing
            }

            override fun onConnected(networkType: NetworkUtils.NetworkType?) {
                NetworkUtils.unregisterNetworkStatusChangedListener(this)
                retryDownload()
            }

        }

    /**
     * 是否下载成功
     */
    private fun isRealDownSuccess(from : String): Boolean {
        return BeautyManager.getInstance().isMTSoAllDown(from)
    }

    /**
     * 下载美颜so 和 资源
     */
    private suspend fun downloadBeautify() = suspendCancellableCoroutine<Boolean> { coroutine ->
        Loger.d(TAG, "美颜组件开始下载....")

        DynamicStore.getInstance().checkDynamic(arrayOf(ByteConfig.RESOURCE, ByteConfig.RESOURCE_LICENSE),
            arrayOf(ByteConfig.EFFECT_SO),
            "",
            true,
            null,
            object : DynamicDownloadConsumer {
                override fun onSuccess() {
                    Loger.d(TAG, "美颜组件下载成功")
                    coroutine.resume(true)
                }

                override fun onProgress(progress: Int) {
                    //ignore
                }

                override fun onFailure(failureCode: String) {
                    Loger.d(TAG, "美颜组件下载失败 错误码：$failureCode")
                    CallUpLoadLog.onBeautyDownloadFailure(failureCode, retryCount)
                    coroutine.resume(false)
                }
            })
    }

    /**
     * 是否下载成功
     */
    fun isDownload(from: String): Boolean {
        return if (isChecked) {
            isDownloadSuccess
        } else {
            isRealDownSuccess(from)
        }
    }

    interface DownloadCallBack {
        fun call(isSuccess: Boolean)
    }
}