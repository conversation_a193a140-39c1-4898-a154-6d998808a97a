package com.xmhaibao.call.feature.atmospherebg.dialog

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import cn.taqu.lib.base.router.RouterLaunch
import com.xmhaibao.call.core.CallViewModelProviders
import com.xmhaibao.call.databinding.CallBackgroundDressPreviewDialogBinding
import com.xmhaibao.call.feature.atmospherebg.bean.CallBackgroundDressItemBean
import com.xmhaibao.call.feature.atmospherebg.viewmodel.CallBackgroundDressViewModel
import com.xmhaibao.call.util.CallTracker
import dp
import hb.utils.ColorUtils
import hb.xstyle.R
import hb.xstyle.xdialog.XLifecycleDialog
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * 装扮背景预览弹窗
 *
 * <AUTHOR>
 * @date 2024-03-27
 */
class CallBackgroundDressPreviewDialog(
    context: Context,
    private val dressBean: CallBackgroundDressItemBean,
    private val tabName: String? = ""
) : XLifecycleDialog(context) {
    companion object {
        fun showDialog(
            context: Context,
            dressBean: CallBackgroundDressItemBean,
            tabName: String?
        ) {
            CallBackgroundDressPreviewDialog(context, dressBean, tabName).show()
        }
    }

    private val scope = MainScope()
    private val binding by lazy {
        CallBackgroundDressPreviewDialogBinding.inflate(LayoutInflater.from(context))
    }

    private val viewModel by lazy {
        CallViewModelProviders.getViewModel(CallBackgroundDressViewModel::class.java)
    }

    /**
     * 右边按钮点击事件处理
     */
    private var positiveBtnClickAction: (() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        setCanceledOnTouchOutside(false)
        initViews()
        initListener()

        CallTracker.trackBackgroundDressPreviewDialogExpose(
            viewModel.callChannelInfo?.relationId,
            dressBean.id,
            tabName
        )
    }

    private fun initListener() {
        binding.btnCancel.setOnClickListener { dismiss() }
        binding.btnOk.setOnClickListener {
            positiveBtnClickAction?.invoke()
        }
    }

    private fun initViews() {
        binding.ivPreview.setImageFromUrl(dressBean.previewIcon)
        binding.tvName.text = dressBean.name.orEmpty()
        binding.tvDesc.text = dressBean.desc.orEmpty()
        setButtonView()
    }

    /**
     * 按钮
     */
    private fun setButtonView() {
        val colorStr = "#FF264D"
        if (dressBean.isAlreadyOwned()) {
            //已拥有
            if (dressBean.isDressed()) {
                binding.btnOk.text = "卸下装扮"
                binding.btnOk.shaper().solid(ColorUtils.getColor(R.color.white))
                    .stroke(1.dp, ColorUtils.getColor(colorStr))
                    .create()
                binding.btnOk.setTextColor(ColorUtils.getColor(colorStr))
                positiveBtnClickAction = {
                    changeDressState(false)
                }
            } else {
                binding.btnOk.text = "立即装扮"
                binding.btnOk.shaper().solid(ColorUtils.getColor(colorStr))
                    .stroke(1.dp, ColorUtils.getColor(colorStr))
                    .create()
                binding.btnOk.setTextColor(ColorUtils.getColor(R.color.white))
                positiveBtnClickAction = {
                    changeDressState(true)
                }
            }
        } else {
            //未拥有
            binding.btnOk.shaper().solid(ColorUtils.getColor(colorStr))
                .stroke(1.dp, ColorUtils.getColor(colorStr))
                .create()
            binding.btnOk.setTextColor(ColorUtils.getColor(R.color.white))
            if (dressBean.relation.isNullOrEmpty()) {
                binding.btnOk.text = "我知道了"
                positiveBtnClickAction = {
                    dismiss()
                }
            } else {
                binding.btnOk.text = "立即获取"
                positiveBtnClickAction = {
                    RouterLaunch.dealJumpData(context, dressBean.relation)
                    dismiss()
                }
            }

        }
    }


    /**
     * 切换装扮状态
     * @param isUse true 使用装扮  false 卸下装扮
     */
    private fun changeDressState(isUse: Boolean) {
        if (isUse) {
            CallTracker.trackBackgroundDressUse(
                viewModel.callChannelInfo?.relationId,
                dressBean.id,
                "通话背景介绍弹窗"
            )
        } else {
            CallTracker.trackBackgroundDressUnUse(
                viewModel.callChannelInfo?.relationId,
                dressBean.id,
                "通话背景介绍弹窗"
            )
        }
        scope.launch {
            val result = viewModel.changeDressState(dressBean, isUse)
            if (result.isSuccess || result.isDressExpire()) {
                //成功之后关闭弹窗
                dismiss()
            }
        }
    }
}