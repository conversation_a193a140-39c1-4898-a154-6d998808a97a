package com.xmhaibao.call.bean

import com.google.gson.annotations.SerializedName
import hb.utils.StringUtils

/**
 * 趣聊-通话结束 - 女用户奖励
 *
 * <AUTHOR>
 * @date 2024/10/30
 */
class CallFinishRewardBean {

    /**
     * "reward": {
     *                 "obtain_unit": "蓝票",//收益单位
     *                 "today_obtain_score": "50",//今日获得奖励
     *                 "week_obtain_score": "50"//本周累计奖励
     *             }
     */

    @SerializedName("obtain_unit")
    var obtainUnit: String? = ""

    @SerializedName("today_obtain_score")
    var todayObtainScore: String? = ""

    @SerializedName("week_obtain_score")
    var weekObtainScore: String? = ""

    @SerializedName("today_obtain_score_before_call")
    var todayObtainScoreBeforeCall = ""

    /**
     * 判断 todayObtainScore 是否大于 todayObtainScoreBeforeCall
     */
    fun isTodayScoreIncreased(): Boolean {
        val todayScore = StringUtils.stringToFloat(todayObtainScore)
        val beforeCallScore = StringUtils.stringToFloat(todayObtainScoreBeforeCall)
        return todayScore > beforeCallScore
    }

}