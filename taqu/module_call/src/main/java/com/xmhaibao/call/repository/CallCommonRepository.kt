package com.xmhaibao.call.repository

import cn.taqu.lib.base.api.UrlBase
import cn.taqu.lib.base.live.model.LiveHostAgreementInfo
import cn.taqu.lib.okhttp.enums.RequestMode
import com.xmhaibao.call.bean.CallBeautyLabelBean
import com.xmhaibao.call.bean.CallCenterUserBean
import com.xmhaibao.call.bean.CallChatAssistantTopicBean
import com.xmhaibao.call.bean.CallFinishReviewTagsBean
import com.xmhaibao.call.bean.CallNewFinishBean
import com.xmhaibao.call.bean.CallSelectDialogBannerBean
import com.xmhaibao.call.bean.CallSelectDialogTopIconBean
import com.xmhaibao.call.bean.CallTopicLikeSuccessBean
import com.xmhaibao.call.bean.CallTopicRecommendBean
import com.xmhaibao.call.feature.atmospherebg.bean.CallBackgroundDressChangeBean
import com.xmhaibao.call.feature.atmospherebg.bean.CallBackgroundDressListBean
import com.xmhaibao.call.feature.doubleIncome.bean.CallDoubleIncomeConfigBean
import com.xmhaibao.call.feature.interactiveEffect.bean.CallSpecialEffectEntranceTipsBean
import com.xmhaibao.call.feature.interactiveEffect.bean.CallUseSpecialEffectResultBean
import com.xmhaibao.call.recharge.bean.CallRechargeWrapperBean
import com.xmhaibao.call.task.bean.CallGrowTaskPageDataBean
import com.xmhaibao.call.task.bean.CallTaskFriendDataBean
import com.xmhaibao.xjson.XJson
import hb.common.xstatic.HttpParams
import hb.xrequest.XRequest
import hb.xrequest.asAwait
import hb.xrequest.async
import hb.xrequest.awaitNullable
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred

/**
 * 通用Repository
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
class CallCommonRepository : UrlBase() {
    companion object {


        /**
         * 互动玩法特效 请求入口气泡提示
         * @param callUuid 通话uuid
         * @param otherUuid 对方uuid
         */
        fun requestEntrancePropTips(
            callUuid: String?,
            otherUuid: String?,
        ): XRequest<List<CallSpecialEffectEntranceTipsBean>?> {
            return XRequest.get(API_GW_CALL_PANEL_TOOLS_V1.plus("/SpecialEffectGuide/tips"))
                .needTicketId()
                .params("call_uuid", callUuid)
                .params("other_uuid", otherUuid)
                .asRequest()
        }

        /**
         * 互动玩法特效 使用特效
         * @param callUuid 通话uuid
         * @param otherUuid 对方uuid
         * @param effectId 资源id 当res_type为beauty_so 时  为0
         *
         */
        fun useSpecialEffect(
            callUuid: String?,
            otherUuid: String?,
            effectId: String?,
        ): XRequest<CallUseSpecialEffectResultBean> {
            return XRequest.post(API_GW_FORUM_CALL_V1.plus("/SpecialEffect/useEffect"))
                .needTicketId()
                .params("call_uuid", callUuid)
                .params("other_uuid", otherUuid)
                .params("effect_id", effectId)
                .asRequest()
        }

        /**
         * 互动玩法特效 上报美颜未下载完成
         * @param callUuid 通话uuid
         * @param otherUuid 对方uuid
         * @param effectId 资源id
         */
        fun reportBeautySoHasNotDownloaded(
            callUuid: String?,
            otherUuid: String?,
            effectId: String? = "",
        ): XRequest<Any> {
            return reportHasNotDownloadedResource(callUuid, otherUuid, "beauty_so", effectId)
        }

        /**
         * 互动玩法特效 上报资源未下载完成
         * @param callUuid 通话uuid
         * @param otherUuid 对方uuid
         * @param effectId  faceU Id
         */
        fun reportFaceUResHasNotDownloaded(
            callUuid: String?,
            otherUuid: String?,
            effectId: String?,
        ): XRequest<Any> {
            return reportHasNotDownloadedResource(callUuid, otherUuid, "faceu_res", effectId)
        }

        /**
         * 互动玩法特效 上报资源未下载完成
         * @param callUuid 通话uuid
         * @param otherUuid 对方uuid
         * @param resType 资源类型 ，美颜sdk beauty_so  faceU资源faceu_res
         * @param effectId 资源id
         *
         */
        @JvmStatic
        fun reportHasNotDownloadedResource(
            callUuid: String?,
            otherUuid: String?,
            resType: String,
            effectId: String? = "0",
        ): XRequest<Any> {
            return XRequest.post(API_GW_FORUM_CALL_V1.plus("/SpecialEffect/reportHasNotDownloadedResource"))
                .needTicketId()
                .params("call_uuid", callUuid)
                .params("other_uuid", otherUuid)
                .params("res_type", resType)
                .params("effect_id", effectId)
                .asRequest()
        }


        /**
         * 互动玩法特效 付费解锁
         * @param effectId
         * @param callSource  通话source
         *
         */
        fun requestEffectPayUnlock(
            effectId: String?,
            callSource: String?,
        ): XRequest<Any?> {
            return XRequest.post(API_GW_FORUM_CALL_V1.plus("/SpecialEffect/buyEffect"))
                .needTicketId()
                .params("source", callSource)
                .params("effect_id", effectId)
                .asRequest()
        }

        /**
         * 获取装扮列表
         * [接口地址](https://api-gw.admin.internal.taqu.cn/docs/gw-api/gw-api-1ffvnmiso9stb)
         * @param callUuid 通话uuid
         * @param otherUuid 对方uuid
         *
         */
        @JvmStatic
        fun getDressList(
            callUuid: String?,
            otherUuid: String?,
        ): XRequest<CallBackgroundDressListBean> {
            return XRequest.get(API_GW_FORUM_CALL_V1.plus("/BackgroundDress/list"))
                .needTicketId()
                .params("call_uuid", callUuid)
                .params("other_uuid", otherUuid)
                .asRequest()
        }

        /**
         * 使用/卸载装扮
         * [接口地址](https://api-gw.admin.internal.taqu.cn/docs/gw-api/gw-api-1ffvnvovmc6lr)
         * @param callUuid 通话uuid
         * @param otherUuid 对方uuid
         * @param dressId 装扮id
         * @param isUse 是否使用 true 使用装扮 false 卸载装扮
         *
         */
        @JvmStatic
        fun changeBackgroundDressState(
            callUuid: String?,
            otherUuid: String?,
            dressId: String,
            isUse: Boolean,
        ): XRequest<CallBackgroundDressChangeBean> {
            return XRequest.post(API_GW_FORUM_CALL_V1.plus("/BackgroundDress/switchStatus"))
                .needTicketId()
                .params("call_uuid", callUuid)
                .params("other_uuid", otherUuid)
                .params("dress_id", dressId)
                .params("dress_action", if (isUse) "1" else "0")
                .asRequest()
        }

        /**
         * 获取趣聊充值礼包信息，根据不同的recharge_type展示不同的弹窗
         *
         * @param otherUuid 对方Uuid
         * @param rechargeSource 余额不足source
         */
        suspend fun getRechargeInfo(
            otherUuid: String?,
            rechargeSource: String?,
            callSource: String?,
        ): CallRechargeWrapperBean? {
            return XRequest.get(API_CALL_TRADE_V1.plus("/GuideRecharge/getRechargePacketInfo"))
                .needTicketId()
                .params("receive_uuid", otherUuid)
                .params("recharge_source", rechargeSource)
                .params("call_source", callSource)
                .asRequest<CallRechargeWrapperBean?>().awaitNullable()
        }

        /**
         * (不)使用卡通脸接口
         * [接口文档](#https://gw.test2.hbmonitor.com/call/v1/CartoonFace/use)
         * @param callUuid 通话uuid
         * @param otherUuid 对方uuid
         * @param isUseAction 是否使用卡通脸
         * @param isUseAction 是否有开关样式
         *  - Standby匹配为video_cartooncontrol实验的实验组对象是传true
         *  - 私信通话为private_cartoonface实验的实验组对象且美颜生效
         */
        suspend fun useCartoonFace(
            callUuid: String?,
            otherUuid: String?,
            isUseAction: Boolean,
            userSwitchEnable: Boolean,
        ): Any? {
            return XRequest.post(API_GW_CALL_HELPER_V1.plus("/EffectCartoonFace/use"))
                .needTicketId()
                .params("call_uuid", callUuid)
                .params("other_uuid", otherUuid)
                .params("action", if (isUseAction) "1" else "0")
                .params("cartoon_face_user_switch", if (userSwitchEnable) "1" else "0")
                .asRequest<Any?>().awaitNullable()
        }

//        /**
//         * 给对方推送toast
//         * @param callUuid 通话uuid
//         * @param otherUuid 对方uuid
//         * @param businessType 业务类型
//         * @param toastText toast文案
//         * @param extra 携带的数据
//         */
//        @JvmStatic
//        fun putToOtherToast(
//            callUuid: String?,
//            otherUuid: String?,
//            businessType: String,
//            toastText: String? = "",
//            extra: Any? = null
//        ): XRequest<Any>? {
//            return putToast(callUuid, otherUuid, businessType, toastText, null, extra)
//        }
        /**
         * （公共）推送toast
         * [接口文档](#https://api-gw.admin.internal.taqu.cn/docs/gw-api/gw-api-1fpv2ovobvo6b)
         * @param callUuid 通话uuid
         * @param otherUuid 对方uuid
         * @param businessType 业务类型
         * @param toOtherMsg 给对方推的toast
         * @param toSelfMsg 给自己推的toast
         * @param extra 携带的数据
         */
        @JvmStatic
         fun putToast(
            callUuid: String?,
            otherUuid: String?,
            businessType: String,
            toOtherMsg: String? = "",
            toSelfMsg: String? = "",
            extra: Any? = null,
        ): XRequest<Any> {
            val requestBuilder = XRequest.post(API_GW_FORUM_CALL_V1.plus("/IMPush/IMNotify"))
                .needTicketId()
                .params("call_uuid", callUuid)
                .params("other_uuid", otherUuid)
                .params("business_type", businessType)
                .params(
                    "msg",
                    XJson.toJson(mapOf("to_self" to toSelfMsg, "to_other" to toOtherMsg))
                )
            if (extra != null) {
                requestBuilder.params("extra", XJson.toJson(extra))
            }

            return requestBuilder.asRequest()
        }

        /**
         * 趣聊-推荐的话题内容
         */
        suspend fun getTopicRecommend(page: String?, otherUuid: String?, callUuid: String?): CallTopicRecommendBean? {
            val httpParams = HttpParams.newBuilder()["$API_GW_CALL_PANEL_TOOLS_V1/MsgHelper/getRecommend"]
                .needTicketId(true)
                .params("page", page)
                .params("other_uuid", otherUuid)
                .params("call_uuid", callUuid)
                .build()
            return XRequest.newRequest<CallTopicRecommendBean?>(httpParams).awaitNullable()
        }

        /**
         * 趣聊-获取话题分类
         */
        private val CALL_CHAT_ASSISTANT: String = "$API_GW_CALL_PANEL_TOOLS_V1/MsgHelper/getCategoryList"

        fun callChatAssistant(
            ticketId: String?,
            otherUuid: String?,
            callUuid: String?,
        ): XRequest<List<CallChatAssistantTopicBean>> {
            val httpParams = HttpParams.newBuilder()[CALL_CHAT_ASSISTANT]
                .params("ticket_id", ticketId)
                .params("other_uuid", otherUuid)
                .params("call_uuid", callUuid)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 趣聊-话题喜欢、不喜欢
         * @param topicId 话题id
         * @param like 1：喜欢；2不喜欢
         */
        fun operationLikeChatAssistant(
            topicId: String?,
            like: Boolean,
        ): XRequest<CallTopicLikeSuccessBean?> {
            val httpParams = HttpParams.newBuilder().post("$API_GW_CALL_PANEL_TOOLS_V1/MsgHelper/operationLike")
                .needTicketId()
                .params("helper_id", topicId)
                .params("action", if (like) "1" else "2")
                .build()
            return XRequest.newRequest(httpParams)
        }


        /**
         * 通知服务端是否给女用户视频打码
         * @param enableMosaic true 打码 false 取消打码
         * @param callUuid 通话uuid
         * @param otherUuid 对方uuid
         */
        suspend fun requestNotifyOtherMosaic(enableMosaic:Boolean, callUuid: String?, otherUuid: String?): Any? {
            val httpParams = HttpParams.newBuilder().post("$API_GW_FORUM_CALL_V1/CallMatchV3/mosaic")
                .needTicketId(true)
                .params("other_uuid", otherUuid)
                .params("call_uuid", callUuid)
                .params("mosaic", if (enableMosaic)"1" else "0")
                .build()
            return XRequest.newRequest<Any?>(httpParams).awaitNullable()
        }
        /**
         * 获取对方用信息
         * @param otherUUID 对方的uuid
         */
        suspend fun getCallOtherUserInfo(otherUUID: String?): CallCenterUserBean? {
            val httpParams = HttpParams.newBuilder()["$API_GW_CALL_USERS_V1/Call/accountBaseInfo"]
                .needTicketId()
                .params("other_uuid", otherUUID)
                .build()
            return XRequest.newRequest<CallCenterUserBean?>(httpParams).awaitNullable()
        }

        /**
         * 获取通话结束信息
         */
        suspend fun getCallEndInfo(scope: CoroutineScope, callUuid: String?): Deferred<CallNewFinishBean?> {
            val params = HttpParams.newBuilder()["$API_GW_FORUM_CALL_V1/Call/getEndCallInfo"]
                .params("call_uuid", callUuid)
                .params("return_other_account_info","1")
                .needTicketId(true)
                .build()
            return XRequest.newRequest<CallNewFinishBean>(params).asAwait<CallNewFinishBean>().async(scope)
        }

        /**
         * 获取评价配置
         */
        suspend fun getReviewConfig(scope: CoroutineScope,fromSource: String?,otherUuid:String?): Deferred<CallFinishReviewTagsBean?> {
            val params = HttpParams.newBuilder()["$API_GW_CALL_USER_LINKS_V1/CallExperienceReview/getConfigV2"]
                .needTicketId()
                .params("call_source",fromSource)
                .params("other_uuid",otherUuid)
                .build()
            return XRequest.newRequest<CallFinishReviewTagsBean>(params).asAwait().async(scope)
        }

        /**
         * 提交评价
         */
        suspend fun submitReview(
            callUuid: String?,
            score: String,
            tagIds: String,
            content: String?,
            greetText: String?,
            autoSendGreet: String?,
        ): CallNewFinishBean? {
            val params = HttpParams.newBuilder().post("$API_GW_CALL_USER_LINKS_V1/CallExperienceReview/submit")
                .params("call_uuid", callUuid)
                .params("score", score)
                .params("tag_ids", tagIds)
                .params("content", content)

                .needTicketId(true)
            if (greetText?.isNotEmpty() == true) {
                params.params("greet_text", greetText)
            }
            if (autoSendGreet?.isNotEmpty() == true) {
                params.params("auto_send_greet", autoSendGreet)
            }
            return XRequest.newRequest<CallNewFinishBean>(params.build()).awaitNullable()
        }

        /**
         * 获取拨打弹窗顶部的图片
         * @param otherUuid 对方uuid
         */
        fun getCallSelectDialogTopIcon(
            otherUuid: String?,
        ): XRequest<CallSelectDialogTopIconBean?> {

            return HttpParams.newBuilder()
                .get("$API_GW_CALL_GUIDE_V1/PrivateMsg/getCallAlertTopIcon")
                .params("other_uuid", otherUuid)
                .params("version_number", "1")
                .needTicketId()
                .build().asRequest()
        }
        /**
         * 获取拨打弹窗顶部的图片
         * @param otherUuid 对方uuid
         */
        fun getCallSelectDialogBannerList(
            otherUuid: String?,
        ): XRequest<CallSelectDialogBannerBean?> {

            return HttpParams.newBuilder()
                .get("$API_GW_CALL_GUIDE_V1/PrivateMsg/getCallAlertBannerList")
                .params("other_uuid", otherUuid)
                .params("version_number", "2")
                .needTicketId()
                .build().asRequest()
        }


        /**
         * 获取女用户优质女标签信息
         */
        fun getBeautyLabel(): XRequest<CallBeautyLabelBean?> {
            return HttpParams.newBuilder()
                .get("$API_GW_CALL_GUIDE_V1/MatchGuide/getBeautyLabel")
                .needTicketId()
                .build().asRequest()
        }

        /**
         * 获取协议信息
         */
        private val GET_HOST_AGREEMENT: String = "$API_LIVE/Agreement/getAgreement"

        suspend fun getHostAgreement(module: String?): LiveHostAgreementInfo? {
            val httpParams = HttpParams.newBuilder()[GET_HOST_AGREEMENT]
                .requestMode(RequestMode.REQUEST_NETWORK_ONLY)
                .needTicketId(true)
                // module 直播:1 社区:2 趣聊(聊主):3 趣聊(用户):4 （默认值1),5 聊天室 ，他趣隐私协议17，配配隐私协议18
                .params("module", module)
                .build()
            return XRequest.newRequest<LiveHostAgreementInfo?>(httpParams).awaitNullable()
        }

        /**
         * 请求签署协议
         */
        suspend fun requestAgreeAgreement(agreementId:String,module:String):Any?{
            return XRequest.get(API_LIVE.plus("/Agreement/agreeAgreement"))
                .params("agreement_id",agreementId)
                .params("module",module)
                .needTicketId()
                .asRequest<Any>()
                .awaitNullable()
        }

        /**
         * 使用翻倍收益卡
         *
         * @param otherUuid 对方uuid
         * @param callUuid 通话uuid
         * @return CallDoubleIncomeConfigBean
         */
        suspend fun requestDoubleIncomeUse(
            otherUuid: String?, callUuid: String?,
            cardId: String?,
        ): CallDoubleIncomeConfigBean? {
            return XRequest.post(API_CALL_TRADE_V1.plus("/DoubleEarn/use"))
                .needTicketId()
                .params("other_uuid", otherUuid)
                .params("call_uuid", callUuid)
                .params("card_id", cardId)
                .asRequest<CallDoubleIncomeConfigBean?>()
                .awaitNullable()
        }

        /**
         * 获取通话任务页数据
         */
        suspend fun getGrowTaskPageData(): CallGrowTaskPageDataBean? {
            return XRequest.get(API_GW_CALL_ACTIVITY.plus("/GrowTask/getTaskPageData"))
                .needTicketId()
                .asRequest<CallGrowTaskPageDataBean>()
                .awaitNullable()
        }

        /**
         * 获取好友列表
         */
        suspend fun requestFriendList(): CallTaskFriendDataBean? {
            return XRequest.get(API_GW_CALL_ACTIVITY.plus("/GrowTaskFriend/getFriends"))
                .needTicketId()
                .asRequest<CallTaskFriendDataBean>()
                .awaitNullable()
        }


    }

}