package com.xmhaibao.call.feature.common.bean

import hb.qim.base.core.IResponseParse
import hb.utils.EventBusUtils

/**
 * 刷新背包事件
 * 周活动迭代添加 业务内通用 [技术文档](https://o15vj1m4ie.feishu.cn/wiki/WY7xwXz4UiKH5Mk650ncvMTQn2c)
 * <AUTHOR>
 * @date 2024-03-05
 */
class CallRefreshBackpackEvent : IResponseParse<CallRefreshBackpackEvent> {
    var callUuid: String? = ""

    /**
     * 标记事件是否已经处理了
     */
    private var isProcessed = false

    /**
     * 标记是否已经处理过了
     */
    fun isProcessed(): Boolean {
        return isProcessed;
    }

    /**
     * 标记为已处理过了
     */
    fun setProcessed() {
        isProcessed = true
    }

    override fun parseResponse(event: String?, vararg args: Any?): CallRefreshBackpackEvent {
        if (args.isNotEmpty()) {
            callUuid = args[0]?.toString() ?: ""
            EventBusUtils.post(this)
        }
        return this
    }

}