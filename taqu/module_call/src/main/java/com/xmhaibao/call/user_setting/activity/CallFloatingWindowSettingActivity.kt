package com.xmhaibao.call.user_setting.activity

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.text.method.LinkMovementMethod
import android.view.View
import cn.taqu.lib.base.utils.FloatPermissionManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.facade.enums.RouteFlag
import com.xmhaibao.call.api.router.CallRouterPath
import com.xmhaibao.call.databinding.CallFloatingWindowSettingLayBinding
import com.xmhaibao.call.user_setting.viewmodel.CallUserSettingViewModel
import com.xmhaibao.call.util.CallTracker
import hb.common.xstatic.activity.BaseMVVMActivity
import hb.kotlin_extension.isNull
import hb.utils.AppUtils
import hb.utils.SpanUtils


/**
 * 音视频通话设置
 * <AUTHOR>
 * @date 2023-07-26
 */
@Route(path = CallRouterPath.CALL_FLOATING_WINDOW_SETTING_ACTIVITY, name = "音视频通话设置",extras = RouteFlag.RELATION_DOC_ENABLED)
class CallFloatingWindowSettingActivity : BaseMVVMActivity<CallUserSettingViewModel>() {

    private lateinit var mBinding: CallFloatingWindowSettingLayBinding

    override fun onCreateContentView(): Any {
        mBinding = CallFloatingWindowSettingLayBinding.inflate(layoutInflater)
        return mBinding.root
    }

    override fun initViews() {
        super.initViews()
        setupToolbar("音视频通话设置").setTextColor(hb.xstyle.R.color.TH_Gray990)
        val desc = SpanUtils()
            .append("开启悬浮窗后，在锁屏状态或")
            .append(AppUtils.getAppName())
            .append("在后台运行状态下，也能收到语音或视频通话提醒")
            .create()
        mBinding.tvDesc.text = desc
        val descOther = SpanUtils()
            .append("建议您在开启以下权限，以确保能即时通知道您语音或视频通话。\r\n")
            .append("1、后台高耗电\r\n")
            .append("2、锁屏显示\r\n")
            .append("3、后台弹出界面")
            .create()
        mBinding.tvDescOther.text = descOther
        mBinding.tvDescOther.movementMethod = LinkMovementMethod.getInstance()
        mBinding.viewPlace1.setOnClickListener {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            val uri: Uri = Uri.fromParts("package", context.packageName, null)
            intent.data = uri
            context.startActivity(intent)
        }
        mBinding.ckBox.isChecked = FloatPermissionManager.commonROMPermissionCheck(this)
        mBinding.ckBox.setOnClickListener {
            if (mBinding.ckBox.isChecked) {
                // 表示原来未开启状态 点击后开启
                mBinding.ckBox.isChecked = false
                FloatPermissionManager.commonROMPermissionApply(this)
            }
        }
    }

    private var introRrl: String? = null
    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        baseViewModel.getSuspendedWindow()
        // 获取页面数据结果
        baseViewModel.mGetSuspendedWindowResult.observe(this) {
            hideLoadingBar()
            introRrl = it.introRrl
            if (it.info.isNull()) {
                mBinding.tvTips.visibility = View.GONE
                mBinding.ivBlue.visibility = View.GONE
            } else {
                mBinding.tvTips.visibility = View.VISIBLE
                mBinding.tvTips.text = it.info?.tips
                mBinding.ivBlue.visibility = View.VISIBLE
                mBinding.ivBlue.setImageFromUrl(it.info?.img)
            }
            resetCheckBoc(FloatPermissionManager.commonROMPermissionCheck(this))
        }

        // 设置开启关闭结果
        baseViewModel.mSetSuspendedWindowResult.observe(this) {
            if (!it) {
                hideLoadingBar()
                return@observe
            }
            mBinding.ckBox.postDelayed({
                baseViewModel.getSuspendedWindow()
            }, 50)
        }
    }

    override fun onResume() {
        super.onResume()
        if (isPause) {
            showLoadingBar()
            baseViewModel.getSuspendedWindow()
            val permission = FloatPermissionManager.commonROMPermissionCheck(this)
            if (mBinding.ckBox.isChecked != permission) {
                mBinding.ckBox.isChecked = permission
                baseViewModel.setSuspendedWindow(if (mBinding.ckBox.isChecked) "1" else "0")
                CallTracker.trackSuspendedWindowClick(if (mBinding.ckBox.isChecked) "打开" else "关闭")
                resetCheckBoc(permission)
            }
        }
    }

    private fun resetCheckBoc(isHavePermission: Boolean) {
        mBinding.ckBox.visibility = if (isHavePermission) View.GONE else View.VISIBLE
        mBinding.placeHolderView2.visibility = if (isHavePermission) View.VISIBLE else View.GONE
    }

    private var isPause = false
    override fun onPause() {
        super.onPause()
        isPause = true
    }
}