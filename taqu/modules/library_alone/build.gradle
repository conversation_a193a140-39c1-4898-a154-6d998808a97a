plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'com.google.devtools.ksp'
}

ksp {
    arg("AROUTER_MODULE_NAME", project.getName())
}

android {
    compileSdkVersion project.compileSdkVersion

    defaultConfig {
        minSdkVersion project.minSdkVersion
        targetSdkVersion project.targetSdkVersion
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    namespace 'com.xmhaibao.library.alone'
}

dependencies {
    implementation project(":library_base")
    api "hb:xalone-annotations:$HB_ALONE"
    api "hb:xalone-api:$HB_ALONE"
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.2.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.1'
    ksp "$AROUTER_COMPILER"
}

// 组件标识符。
// 如果有":"的则前面为groupId，后面为artifactId。
// 如果没有":"的则为groupId，artifactId会赋值project.getName()
group = "hb:xalone-library"
// 版本号，在开发阶段请使用 SNAPSHOT 后缀打包
version = "0.2.0"