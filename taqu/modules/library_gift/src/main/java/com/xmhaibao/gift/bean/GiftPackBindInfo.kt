package com.xmhaibao.gift.bean

import android.text.TextUtils
import com.google.gson.annotations.SerializedName

/**
 * 背包礼物 绑定用户信息
 *
 * <AUTHOR>
 * @date 2024-01-05
 */
class GiftPackBindInfo {
    var uuid: String? = ""
    var nickname: String? = ""
    var avatar: String? = ""

    @SerializedName("family_uuid")
    var familyUuid: String? = null

    fun isSameFamily(familyUuid: String?): Bo<PERSON>an {
        return TextUtils.equals(this.familyUuid, familyUuid)
    }
}