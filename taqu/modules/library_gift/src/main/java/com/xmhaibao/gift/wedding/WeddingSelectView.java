package com.xmhaibao.gift.wedding;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;


import com.xmhaibao.gift.R;

import hb.ximage.fresco.AvatarDraweeView;
import hb.utils.ColorUtils;

/**
 * @auther Su<PERSON>han<PERSON>eng
 * @date 2020/4/26
 * @desc 选择用户控件
 */
public class WeddingSelectView extends FrameLayout {
    /**
     * 0 证婚人
     */
    public static final int TYPE_WITNESS = 0;
    /**
     * 1 伴郎
     */
    public static final int TYPE_GROOMSMAN = 1;
    /**
     * 2 伴娘
     */
    public static final int TYPE_BRIDESMAID = 2;

    private int type;

    private ImageView mIvBg;
    private AvatarDraweeView mAvatar;
    private TextView mTvName;

    public WeddingSelectView(Context context) {
        this(context, null);
    }

    public WeddingSelectView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public WeddingSelectView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs);
    }

    private void init(AttributeSet attrs) {
        TypedArray a = getContext().obtainStyledAttributes(attrs, R.styleable.LiveWeddingSelectUserType);
        if (a != null) {
            type = a.getInteger(R.styleable.LiveWeddingSelectUserType_wedding_userType, 0);
            a.recycle();
        }
        LayoutInflater.from(getContext()).inflate(R.layout.gift_wedding_select_view, this, true);
        mIvBg = findViewById(R.id.iv_bg);
        mAvatar = findViewById(R.id.ivAvatarHost);
        mTvName = findViewById(R.id.tvName);
        initView(false);
    }

    private void initView(boolean isSelect) {
        switch (type) {
            case TYPE_WITNESS:
                mIvBg.setImageResource(isSelect ? R.drawable.gift_wedding_select_witness_selected_ic : R.drawable.gift_wedding_select_witness_un_selected_ic);
                break;
            case TYPE_GROOMSMAN:
                mIvBg.setImageResource(isSelect ? R.drawable.gift_wedding_select_groomsman_selected_ic : R.drawable.gift_wedding_select_groomsman_un_selected_ic);
                break;
            case TYPE_BRIDESMAID:
                mIvBg.setImageResource(isSelect ? R.drawable.gift_wedding_select_bridesmaid_selected_ic : R.drawable.gift_wedding_select_bridesmaid_un_selected_ic);
                break;
            default:
                break;
        }
    }

    public void setData(String name,String avatar){
        initView(true);
        mAvatar.setImageURI(avatar);
        mAvatar.setVisibility(VISIBLE);
        mTvName.setText(name);
        mTvName.setTextColor(ColorUtils.getColor("#617ED0"));
    }

    public void resetData(){
        initView(false);
        mAvatar.setVisibility(GONE);
        mTvName.setText("·点击选人·");
        mTvName.setTextColor(ColorUtils.getColor(hb.xstyle.R.color.g2));
    }
}
