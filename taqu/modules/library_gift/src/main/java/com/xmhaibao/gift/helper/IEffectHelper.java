package com.xmhaibao.gift.helper;



import com.ffmpeg.effect.IHbEffectView;
import com.xmhaibao.urd.bean.ResourceItemBean;

import java.util.Map;

/**
 * @auther <PERSON><PERSON>han<PERSON>eng
 * @date 2020/3/30
 * @desc
 */
public interface IEffectHelper {
    void setScene(int scene);

    /**
     * 设置房间标识
     * @param roomId 房间标识
     */
    void setRoomId(String roomId);

    /**
     * 设置日志副标题信息
     * @param subLogContent
     */
    void setSubLogContent(String subLogContent);

    void addHbEffectCallback(EffectCallback callback);

    void startAnimationByGid(int priority, String effectId);

    void startAnimation(int priority, ResourceItemBean bean, boolean isShowJump);

    void startAnimation(int priority, ResourceItemBean bean, long delayMillis);

    void startAnimation(int priority, ResourceItemBean bean, long delayMillis, Map<String, String> map);

    void startAnimationByGid(int priority, String effectId, long delayMillis);

    void startAnimationByGid(int priority, String effectId, long delayMillis, Map<String, String> map);

    void startAnimationByGid(int priority, String effectId, long delayMillis, Map<String, String> map, boolean isShowJump);

    void release();

    /**
     * 设置特效View接口
     * @param effectView 具体播放特效view接口
     */
    void setIEffectView(IEffectView effectView);

    IHbEffectView getCurrentView();

    void interruptAnim(int priority);

    /**
     * 设置是否允许动效内置音频播放
     * @param enable enable
     */
    void setAudioPlayEnable(boolean enable);


    /**
     * 设置播放器来源，用于日志上报
     * @param source 来源
     */
    void setPlayerSource(String source);
}
