<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="80dp"
    android:layout_height="63dp"
    android:clipChildren="false"
    android:clipToPadding="false">

    <FrameLayout
        android:layout_width="60dp"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginEnd="4dp"
        android:clipChildren="false"
        android:clipToPadding="false">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/ivAnimViewBtn"
            android:layout_width="60dp"
            android:layout_height="60dp"
            app:lottie_autoPlay="false"
            app:lottie_loop="true" />

        <cn.taqu.library.widget.StrokeTextView
            android:id="@+id/tv_live_gift_super_get_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="47.5dp"
            android:gravity="center_horizontal"
            android:singleLine="true"
            android:text="抢到50趣豆"
            android:textColor="@color/white"
            android:textSize="11sp"
            android:visibility="invisible"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/tv_live_gift_super_flower"
            android:layout_width="44dp"
            android:layout_height="65dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="47.5dp"
            android:scaleType="fitXY"
            android:src="@drawable/gift_ic_super_gift_flower"
            android:visibility="invisible"
            tools:visibility="visible" />

    </FrameLayout>


    <com.xmhaibao.gift.widget.CountdownViewForLive
        android:id="@+id/countdownview"
        android:layout_width="50dp"
        android:layout_height="14dp"
        android:layout_gravity="end"
        android:layout_marginTop="23dp"
        android:layout_marginEnd="9dp"
        android:background="@drawable/gift_wedding_rob_countdown_bg"
        android:visibility="invisible"
        app:isShowTimeBgDivisionLine="true"
        app:isTimeTextBold="true"
        app:suffixTextColor="@color/white"
        app:suffixTextSize="11sp"
        app:timeBgDivisionLineSize="3dp"
        app:timeTextColor="@color/white"
        app:timeTextSize="11sp" />

    <ImageView
        android:layout_width="80dp"
        android:layout_height="18dp"
        android:layout_gravity="bottom|end"
        android:src="@drawable/gift_wedding_countdown_send_ic" />
</FrameLayout>