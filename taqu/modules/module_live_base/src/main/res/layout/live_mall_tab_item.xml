<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
             android:layout_width="wrap_content"
             android:layout_height="match_parent"
             android:background="@color/transparent"
             android:minWidth="50dp"
    >

    <ImageView
        android:id="@+id/live_mall_tab_divider_iv"
        android:layout_width="0.5dp"
        android:layout_height="8dp"
        android:background="#A6A6AB"
        android:visibility="invisible"
        android:layout_gravity="center_vertical"/>

    <TextView
        android:id="@+id/live_mall_tab_title_tv"
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:textColor="@color/live_mall_tab_text_color"
        android:textSize="@dimen/t7"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        />

</FrameLayout>
