package hb.message.bean;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;

/**
 * @author：杨浩 项目：taqu
 * 创建日期：2020-02-28
 * 功能简介：未读消息信息
 */
public class HBMessageUnreadInfo {

    @SerializedName("conversation_id")
    private String conversationId;
    @SerializedName("group_uuid")
    private String groupUuid;
    @SerializedName("conversation_type")
    private String conversationType;
    @SerializedName("last_msg_id")
    private String lastMsgId;
    @SerializedName("unread_num")
    private int unreadNum;

    public String getConversationId() {
        if(TextUtils.isEmpty(conversationId)){
            return groupUuid;
        }
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getGroupUuid() {
        return groupUuid;
    }

    public void setGroupUuid(String groupUuid) {
        this.groupUuid = groupUuid;
    }

    public String getConversationType() {
        return conversationType;
    }

    public void setConversationType(String conversationType) {
        this.conversationType = conversationType;
    }

    public String getLastMsgId() {
        return lastMsgId;
    }

    public void setLastMsgId(String lastMsgId) {
        this.lastMsgId = lastMsgId;
    }

    public int getUnreadNum() {
        return unreadNum;
    }

    public void setUnreadNum(int unreadNum) {
        this.unreadNum = unreadNum;
    }
}
