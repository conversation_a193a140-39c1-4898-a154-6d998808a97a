package hb.message.bean;

import com.google.gson.annotations.SerializedName;

import hb.message.constants.HBMessageConstants;


/**
 * @author：杨浩 项目：taqu
 * 创建日期：2020-03-09
 * 功能简介：好友 account uuid 的信息
 */
public class HBMessageAccountFriendUuidInfo {

    /**
     * 好友的 uuid
     */
    @SerializedName("friend_id")
    private String mAccountUuid;

    /**
     * 改变的状态成为好友、解除好友
     */
    @SerializedName("status")
    private int mChangeState;

    /**
     * 状态发生改变的时间
     */
    @SerializedName("update_time")
    private String mChangeTime;

    /**
     * 备注名
     */
    @SerializedName("remark_name")
    private String mRemarkName;

    public String getRemarkName() {
        return mRemarkName;
    }

    public void setRemarkName(String remarkName) {
        mRemarkName = remarkName;
    }

    public String getAccountUuid() {
        return mAccountUuid;
    }

    public void setAccountUuid(String accountUuid) {
        mAccountUuid = accountUuid;
    }

    public int getChangeState() {
        return mChangeState;
    }

    public void setChangeState(int changeState) {
        mChangeState = changeState;
    }

    public String getChangeTime() {
        return mChangeTime;
    }

    public void setChangeTime(String changeTime) {
        mChangeTime = changeTime;
    }

    /**
     * 是否添加好友
     *
     * @return
     */
    public boolean isAddFriend() {
        return HBMessageConstants.FRIEND_CHANGE_STATE_ADD == mChangeState;
    }

    /**
     * 拉黑好友
     *
     * @return
     */
    public boolean isBlackFriend() {
        return HBMessageConstants.FRIEND_CHANGE_STATE_BLACK == mChangeState;
    }

    /**
     * 是否删除好友
     *
     * @return
     */
    public boolean isDeleteFriend() {
        return HBMessageConstants.FRIEND_CHANGE_STATE_DELETE == mChangeState;
    }
}
