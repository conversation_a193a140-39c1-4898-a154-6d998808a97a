package hb.message.bean;


import android.net.Uri;

import hb.qim.base.core.IResponse;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import hb.message.db.entity.HBAccountInfoDO;
import hb.message.db.entity.HBMessageContentDO;
import hb.message.db.entity.HBMessageConversationDO;

/**
 * @author：杨浩 项目：taqu
 * 创建日期：2020-02-27
 * 功能简介：消息拦截器数据保存类
 */
public class HBMessageInterceptorDataProvider {

    /**
     * 待写入的会话表
     */
    @Nullable
    private HBMessageConversationDO mConversation;

    /**
     * 待写入的消息表
     */
    @Nullable
    private HBMessageContentDO mMessageContent;

    /**
     * 等待写入的用户信息
     */
    @Nullable
    private HBAccountInfoDO mAccountInfo;

    /**
     * im 收到的原始消息
     */
    @Nullable
    private IResponse mIResponse;

    /**
     * 通知栏配置提供者
     */
    @Nullable
    private NotifyConfigProvider mNotifyDataProvider;

    /**
     * 是否启用数据库，一票否决
     * 为 false 的时候不写入数据
     */
    private boolean enableDb = true;

    /**
     * 收到消息的用户 id
     */
    private String mAccountUuid;

    @NonNull
    private List<String> mAnalysisLog = new ArrayList<>();

    /**
     * 是否是从 im 接收到到
     */
    private boolean isImReceive;

    public boolean isImReceive() {
        return isImReceive;
    }

    public void setImReceive(boolean isImReceive) {
        this.isImReceive = isImReceive;
    }

    @NonNull
    public List<String> getAnalysisLog() {
        return Collections.unmodifiableList(mAnalysisLog);
    }

    /**
     * 保存用于分析耗时的日志
     *
     * @param analysisLog
     */
    public void saveAnalysisLog(String analysisLog) {
        mAnalysisLog.add(analysisLog);
    }

    public String getAccountUuid() {
        return mAccountUuid;
    }

    public HBMessageInterceptorDataProvider(String accountUuid) {
        mAccountUuid = accountUuid;
    }

    public boolean isEnableDb() {
        return enableDb;
    }

    @Nullable
    public HBAccountInfoDO getAccountInfo() {
        return mAccountInfo;
    }

    public void setAccountInfo(@Nullable HBAccountInfoDO accountInfo) {
        mAccountInfo = accountInfo;
    }

    public void setEnableDb(boolean enableDb) {
        // 只要之前有设置为 false 就不写入数据库
        this.enableDb = enableDb && this.enableDb;
    }

    @Nullable
    public HBMessageConversationDO getConversation() {
        return mConversation;
    }

    public void setConversation(@Nullable HBMessageConversationDO conversation) {
        mConversation = conversation;
    }

    @Nullable
    public HBMessageContentDO getMessageContent() {
        return mMessageContent;
    }

    public void setMessageContent(@Nullable HBMessageContentDO messageContent) {
        mMessageContent = messageContent;
    }

    @Nullable
    public IResponse getIResponse() {
        return mIResponse;
    }

    public void setIResponse(@Nullable IResponse IResponse) {
        mIResponse = IResponse;
    }

    @Nullable
    public NotifyConfigProvider getNotifyDataProvider() {
        return mNotifyDataProvider;
    }

    public void setNotifyDataProvider(@Nullable NotifyConfigProvider notifyDataProvider) {
        mNotifyDataProvider = notifyDataProvider;
    }

    /**
     * 未读相关的数据
     */
    public static class UnreadDataProvider {

        /**
         * 未读消息流程获取到的，未读消息集合
         */
        @Nullable
        private List<HBMessageContentDO> mUnreadMessageContent;

    }

    /**
     * 因为涉及到通知栏的参数较多，提取出来
     */
    public static class NotifyConfigProvider {

        /**
         * 是否显示通知，一票否决
         */
        private boolean isNotifyShow;

        /**
         * 默认的消息分组 id
         */
        private static final String CHANNEL_ID_PRIVATE_MSG = "channel_id_private_msg";

        /**
         * 默认的消息等级
         */
        public static final int IMPORTANCE_HIGH = 4;

        /**
         * 私信通知 id
         */
        public static final int PRIVATE_NOTIFY_ID = 101;

        /**
         * 通知的分组 id
         */
        private String mNotifyChannelId;

        /**
         * 通知的名称
         */
        private String mNotifyChannelName;

        /**
         * 消息的等级
         */
        private int mNotifyImportance;

        /**
         * 消息是否需要震动
         */
        private boolean isNotifyVibrate;

        /**
         * 消息是否需要铃声
         */
        private boolean isNotifyRingtone;

        /**
         * 通知标题
         */
        private String mNotifyTitle;

        /**
         * 通知内容
         */
        private String mNotifyContent;

        /**
         * 通知点击跳转
         */
        private String mNotifyRelaction = "";

        /**
         * 通知 id
         */
        private int mNotifyId;

        /**
         * 通知栏小icon
         */
        @IdRes
        private int mNotificationSmallIcon;

        /**
         * 通知栏大icon
         */
        @IdRes
        private int mNotificationLargeIcon;
        /**
         * 通知栏大icon是服务端地址
         */
        private String mNotificationLargeIconUrl;
        /**
         * 通知栏音效
         */
        private Uri mRingtoneUri;

        /**
         * 是否需要刷新桌面角标
         */
        private boolean isRefreshBadge;


        private NotifyConfigProvider() {

        }

        private NotifyConfigProvider(boolean isNotifyShow, String notifyChannelId, String notifyChannelName,
                                     int notifyImportance, boolean isNotifyVibrate, boolean isNotifyRingtone,
                                     String notifyTitle, String notifyContent, String notifyRelaction, int notifyId,
                                     int notificationSmallIcon, int notificationLargeIcon, String notificationLargeIconUrl,
                                     Uri ringtoneUri,
                                     boolean isRefreshBadge) {
            this.isNotifyShow = isNotifyShow;
            mNotifyChannelId = notifyChannelId;
            mNotifyChannelName = notifyChannelName;
            mNotifyImportance = notifyImportance;
            this.isNotifyVibrate = isNotifyVibrate;
            this.isNotifyRingtone = isNotifyRingtone;
            mNotifyTitle = notifyTitle;
            mNotifyContent = notifyContent;
            mNotifyRelaction = notifyRelaction;
            mNotifyId = notifyId;
            mNotificationSmallIcon = notificationSmallIcon;
            mNotificationLargeIcon = notificationLargeIcon;
            mNotificationLargeIconUrl = notificationLargeIconUrl;
            mRingtoneUri = ringtoneUri;
            this.isRefreshBadge = isRefreshBadge;
        }

        public boolean isRefreshBadge() {
            return isRefreshBadge;
        }

        public Uri getRingtoneUri() {
            return mRingtoneUri;
        }

        public int getNotificationSmallIcon() {
            return mNotificationSmallIcon;
        }

        public int getNotificationLargeIcon() {
            return mNotificationLargeIcon;
        }

        public String getNotificationLargeIconUrl() {
            return mNotificationLargeIconUrl;
        }

        public boolean isNotifyShow() {
            return isNotifyShow;
        }

        public String getNotifyChannelId() {
            return mNotifyChannelId;
        }

        public String getNotifyChannelName() {
            return mNotifyChannelName;
        }

        public int getNotifyImportance() {
            return mNotifyImportance;
        }

        public boolean isNotifyVibrate() {
            return isNotifyVibrate;
        }

        public boolean isNotifyRingtone() {
            return isNotifyRingtone;
        }

        public String getNotifyTitle() {
            return mNotifyTitle;
        }

        public String getNotifyContent() {
            return mNotifyContent;
        }

        public String getNotifyRelaction() {
            return mNotifyRelaction;
        }

        public int getNotifyId() {
            return mNotifyId;
        }

        public static class Builder {

            /**
             * 是否显示通知，一票否决
             */
            private boolean isNotifyShow = true;

            /**
             * 通知的分组 id
             */
            private String mNotifyChannelId = CHANNEL_ID_PRIVATE_MSG;

            /**
             * 通知的名称
             */
            private String mNotifyChannelName = "消息";

            /**
             * 消息的等级
             */
            private int mNotifyImportance = IMPORTANCE_HIGH;

            /**
             * 消息是否需要震动
             */
            private boolean isNotifyVibrate = true;

            /**
             * 消息是否需要铃声
             */
            private boolean isNotifyRingtone = true;

            /**
             * 通知标题
             */
            private String mNotifyTitle = "新消息";

            /**
             * 通知内容
             */
            private String mNotifyContent = "你有新的消息";

            /**
             * 通知点击跳转
             */
            private String mNotifyRelaction = "";

            /**
             * 通知 id
             */
            private int mNotifyId = PRIVATE_NOTIFY_ID;

            /**
             * 通知栏小icon
             */
            @IdRes
            private int mNotificationSmallIcon;

            /**
             * 通知栏大icon
             */
            @IdRes
            private int mNotificationLargeIcon;

            /**
             * 通知栏大icon是服务端地址
             */
            private String mNotificationLargeIconUrl;

            /**
             * 通知栏音效
             */
            private Uri mRingtoneUri;

            /**
             * 是否需要刷新桌面角标
             */
            private boolean isRefreshBadge = false;

            public Builder setRefreshBadge(boolean refreshBadge) {
                isRefreshBadge = refreshBadge;
                return this;
            }

            public Builder setRingtoneUri(Uri ringtoneUri) {
                mRingtoneUri = ringtoneUri;
                return this;
            }

            public Builder setNotificationLargeIcon(int notificationLargeIcon) {
                mNotificationLargeIcon = notificationLargeIcon;
                return this;
            }

            public void setNotificationLargeIconUrl(String notificationLargeIconUrl) {
                this.mNotificationLargeIconUrl = notificationLargeIconUrl;
            }

            public Builder setNotificationSmallIcon(int notificationSmallIcon) {
                mNotificationSmallIcon = notificationSmallIcon;
                return this;
            }

            public Builder setNotifyShow(boolean notifyShow) {
                // 只要之前有设置为 false 就不显示通知
                isNotifyShow = isNotifyShow && notifyShow;
                return this;
            }

            public Builder setNotifyChannelId(String notifyChannelId) {
                mNotifyChannelId = notifyChannelId;
                return this;
            }

            public Builder setNotifyChannelName(String notifyChannelName) {
                mNotifyChannelName = notifyChannelName;
                return this;
            }

            public Builder setNotifyImportance(int notifyImportance) {
                mNotifyImportance = notifyImportance;
                return this;
            }

            public Builder setNotifyVibrate(boolean notifyVibrate) {
                isNotifyVibrate = notifyVibrate;
                return this;
            }

            public Builder setNotifyRingtone(boolean notifyRingtone) {
                isNotifyRingtone = notifyRingtone;
                return this;
            }

            public Builder setNotifyTitle(String notifyTitle) {
                mNotifyTitle = notifyTitle;
                return this;
            }

            public Builder setNotifyContent(String notifyContent) {
                mNotifyContent = notifyContent;
                return this;
            }

            public Builder setNotifyRelaction(String notifyRelaction) {
                mNotifyRelaction = notifyRelaction;
                return this;
            }

            public Builder setNotifyId(int notifyId) {
                mNotifyId = notifyId;
                return this;
            }

            public NotifyConfigProvider build() {
                return new NotifyConfigProvider(isNotifyShow, mNotifyChannelId, mNotifyChannelName,
                        mNotifyImportance, isNotifyVibrate, isNotifyRingtone, mNotifyTitle,
                        mNotifyContent, mNotifyRelaction, mNotifyId, mNotificationSmallIcon,
                        mNotificationLargeIcon, mNotificationLargeIconUrl, mRingtoneUri, isRefreshBadge);
            }
        }
    }
}
