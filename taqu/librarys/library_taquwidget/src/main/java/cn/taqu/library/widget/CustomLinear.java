package cn.taqu.library.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.LinearLayout;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/1/29.
 */
public class CustomLinear extends LinearLayout {


    public CustomLinear(Context context) {
        super(context);
    }

    public CustomLinear(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomLinear(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        return true;
    }
}
