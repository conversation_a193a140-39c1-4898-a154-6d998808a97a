package cn.taqu.library.widget.list.viewholder

import android.view.LayoutInflater
import android.view.ViewGroup
import cn.taqu.library.widget.list.bean.TqListItemCheckBean
import com.ushengsheng.widget.R
import com.ushengsheng.widget.databinding.TaqulistMultipleViewBinding
import com.ushengsheng.widget.databinding.TqlistItemBinding

/**
 * 类描述：通用列表组件——Multiple样式
 *
 * <AUTHOR>
 * @date 2024-02-02
 */
class TqListMultipleViewHolder(viewBinding: TqlistItemBinding) :
    TqListBaseViewHolder<TqListItemCheckBean>(viewBinding) {
    private lateinit var multipleViewBinding: TaqulistMultipleViewBinding

    constructor(parent: ViewGroup) : this(
        TqlistItemBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
    )

    override fun inflateViewByHoldType() {
        rootViewBinding.stubContent.layoutResource = R.layout.taqulist_multiple_view
        multipleViewBinding =
            TaqulistMultipleViewBinding.bind(rootViewBinding.stubContent.inflate())
    }

    override fun onBindViewByHoldType(item: TqListItemCheckBean) {
        multipleViewBinding.ivMultipleCheck.setImageResource(if (item.isItemCheck) R.drawable.tqlist_multiple_checked else R.drawable.tqlist_multiple_unchecked)
    }


}