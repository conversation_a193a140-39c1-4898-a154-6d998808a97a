<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/image"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/llMoreGridPhoto"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right|bottom"
        android:visibility="gone"
        android:minWidth="37dp"
        android:layout_margin="4dp"
        android:paddingBottom="2dp"
        android:paddingTop="2dp"
        android:paddingLeft="4dp"
        android:paddingRight="4dp"
        android:background="@drawable/dy_more_grid_photo_bg"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/ivMorePhotoLeft"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="3dp"
            android:src="@drawable/icon_more_photo_left" />

        <TextView
            android:id="@+id/tvPicNum"
            android:layout_width="wrap_content"
            android:layout_height="14dp"
            android:textColor="@color/alipay_TextColorWhite"
            android:textSize="10sp"
            tools:text="99张"
            tools:visibility="visible" />

    </LinearLayout>
</merge>