apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdkVersion project.compileSdkVersion

    lintOptions {
        abortOnError false
    }
    defaultConfig {
        minSdkVersion project.minSdkVersion
        targetSdkVersion project.targetSdkVersion
        versionCode project.versionCode
        versionName project.versionName
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    viewBinding {
        enabled = true
    }
    namespace 'com.ushengsheng.widget'
}

dependencies project.library_taquwidget_dependencies
