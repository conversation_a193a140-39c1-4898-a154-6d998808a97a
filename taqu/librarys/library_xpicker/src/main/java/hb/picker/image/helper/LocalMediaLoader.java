package hb.picker.image.helper;

import android.Manifest;
import android.annotation.SuppressLint;
import android.database.Cursor;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

import androidx.annotation.RequiresPermission;
import androidx.fragment.app.FragmentActivity;
import androidx.loader.app.LoaderManager;
import androidx.loader.content.CursorLoader;
import androidx.loader.content.Loader;
import hb.picker.image.model.LocalMedia;
import hb.picker.image.model.LocalMediaFolder;
import hb.picker.image.utils.SdkVersionUtils;
import hb.xpermission.XPermissions;
import hb.xthread.XThreadPool;


public class LocalMediaLoader {

    public static final int TYPE_IMAGE = 1;
    public static final int TYPE_VIDEO = 2;

    private final static String[] IMAGE_PROJECTION = {
            MediaStore.Images.Media.DATA,
            MediaStore.Images.Media.DISPLAY_NAME,
            MediaStore.Images.Media.DATE_ADDED,
            MediaStore.Images.Media._ID,
    MediaStore.Images.Media.WIDTH,
    MediaStore.Images.Media.HEIGHT};

    private final static String[] VIDEO_PROJECTION = {
            MediaStore.Video.Media.DATA,
            MediaStore.Video.Media.DISPLAY_NAME,
            MediaStore.Video.Media.DATE_ADDED,
            MediaStore.Video.Media._ID,
            MediaStore.Video.Media.DURATION};

    private FragmentActivity activity;

    private static final Executor THREAD_SINGLE_STAT_EXECUTOR = Executors.newSingleThreadExecutor();

    public LocalMediaLoader(FragmentActivity activity) {
        this.activity = activity;
    }

    /**
     * @param imageLoadListener
     * @param includeGif        是否加载包括gif
     */
    public void loadAllImage(boolean includeGif, final LocalMediaLoadListener imageLoadListener) {
        loadAllMedia(TYPE_IMAGE, includeGif, imageLoadListener);
    }


    /**
     * @param imageLoadListener
     * @param includeGif        是否加载包括gif
     */
    public void loadFirstImage(boolean includeGif, final LocalMediaLoadListener imageLoadListener) {
        loadFirstMedia(TYPE_IMAGE, includeGif, imageLoadListener);
    }

    public void loadAllVideo(final LocalMediaLoadListener imageLoadListener) {
        loadAllMedia(TYPE_VIDEO, false, imageLoadListener);
    }

    public void loadFirstVideo(final LocalMediaLoadListener imageLoadListener) {
        loadFirstMedia(TYPE_VIDEO, false, imageLoadListener);
    }

    /**
     * 获取全部视频或者图片
     *
     * @param type       加载类型 TYPE_IMAGE = 1 TYPE_VIDEO = 2
     * @param includeGif
     */
    public void loadAllMedia(final int type, final boolean includeGif, final LocalMediaLoadListener imageLoadListener) {
        new XPermissions.Builder(activity).rationale("为了上传手机图片、及保存文件至手机，请先同意存储权限")
                .settingsDialogContent("为了上传手机图片、及保存文件至手机，请先同意存储权限！打开应用设置页面以修改应用权限。")
                .permissions(TYPE_IMAGE == type ? XPermissions.getReadMediaImagesPermissions() : XPermissions.getReadMediaVideoPermissions())
                .listener(new XPermissions.OnPermissionListener() {
                    @SuppressLint("MissingPermission")
                    @Override
                    public void onGranted(List<String> perms) {
                        if (perms.size() == 1) {
                            String selection = null;
                            String[] selectionArgs = null;
                            if (type == TYPE_IMAGE) {
                                selection = includeGif ? null : MediaStore.Images.Media.MIME_TYPE + "=? or " + MediaStore.Images.Media.MIME_TYPE + "=? or " + MediaStore.Images.Media.MIME_TYPE + "=? ";
                                selectionArgs = includeGif ? null : new String[] { "image/jpeg", "image/png", "image/jpg" };
                            }
                            load(type, selection, selectionArgs, false, imageLoadListener);
                        }
                    }

                    @Override
                    public void onDenied(List<String> perms) {
                    }
                })
                .build()
                .request();
    }


    /**
     * 获取第一张视频或者图片
     *
     * @param type       加载类型 TYPE_IMAGE = 1 TYPE_VIDEO = 2
     * @param includeGif
     */
    public void loadFirstMedia(final int type, final boolean includeGif, final LocalMediaLoadListener imageLoadListener) {
        new XPermissions.Builder(activity).rationale("为了上传手机图片、及保存文件至手机，请先同意存储权限")
                .settingsDialogContent("为了上传手机图片、及保存文件至手机，请先同意存储权限！打开应用设置页面以修改应用权限。")
                .permissions(TYPE_IMAGE == type ? XPermissions.getReadMediaImagesPermissions() : XPermissions.getReadMediaVideoPermissions())
                .listener(new XPermissions.OnPermissionListener() {
                    @SuppressLint("MissingPermission")
                    @Override
                    public void onGranted(List<String> perms) {
                        if (perms.size() == 1) {
                            String selection = null;
                            String[] selectionArgs = null;
                            if (type == TYPE_IMAGE) {
                                selection = includeGif ? null : MediaStore.Images.Media.MIME_TYPE + "=? or " + MediaStore.Images.Media.MIME_TYPE + "=? or " + MediaStore.Images.Media.MIME_TYPE + "=? ";
                                selectionArgs = includeGif ? null : new String[] { "image/jpeg", "image/png", "image/jpg" };
                            }
                            load(type, selection, selectionArgs, true, imageLoadListener);
                        }
                    }

                    @Override
                    public void onDenied(List<String> perms) {
                    }
                })
                .build()
                .request();
    }


    /**
     * @param type
     * @param selection
     * @param selectionArgs
     * @param queryFirst    是否只查找第一张
     * @return
     */
    private CursorLoader createCursorLoader(int type, String selection, String[] selectionArgs, boolean queryFirst) {
        CursorLoader cursorLoader = null;

        if (type == TYPE_IMAGE) {
            String sortOrder = MediaStore.Images.Media.DATE_ADDED + " DESC";
            sortOrder = queryFirst ? (SdkVersionUtils.checkedAndroid_R() ? sortOrder : sortOrder + " limit 0,1") : sortOrder;
            cursorLoader = new CursorLoader(
                    activity, MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    IMAGE_PROJECTION, selection,
                    selectionArgs, sortOrder);
        } else if (type == TYPE_VIDEO) {
            String sortOrder = VIDEO_PROJECTION[2] + " DESC";
            sortOrder = queryFirst ? (SdkVersionUtils.checkedAndroid_R() ? sortOrder : sortOrder + " limit 0,1") : sortOrder;
            cursorLoader = new CursorLoader(
                    activity, MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
                    VIDEO_PROJECTION, null, null, sortOrder);
        }
        return cursorLoader;
    }

    /**
     * @param type          加载类型 TYPE_IMAGE = 1 TYPE_VIDEO = 2
     * @param selection
     * @param selectionArgs
     * @param
     */
    private void load(final int type, final String selection, final String[] selectionArgs, final boolean queryFirst, final LocalMediaLoadListener imageLoadListener) {
        activity.getSupportLoaderManager().initLoader(type, null, new LoaderManager.LoaderCallbacks<Cursor>() {
            @Override
            public Loader<Cursor> onCreateLoader(int id, Bundle args) {
                return createCursorLoader(id, selection, selectionArgs, queryFirst);
            }

            @Override
            public void onLoadFinished(Loader<Cursor> loader, final Cursor data) {
                THREAD_SINGLE_STAT_EXECUTOR.execute(new Runnable() {
                    @Override
                    public void run() {
                        // 在界面被 destory 之后 data 会被关闭
                        // 这里有可能会报错，虽然已经加了 isClosed 判断但是还是有可能出现，目前只有加 try 这样比较合适
                        final ArrayList<LocalMediaFolder> imageFolders = new ArrayList<>();
                        LocalMediaFolder allImageFolder = new LocalMediaFolder();
                        List<LocalMedia> allImages = new ArrayList<>();
                        try {
                            if (data != null && !data.isClosed()) {
                                int count = data.getCount();
                                if (count > 0) {
                                    if (data == null || data.isClosed()) {
                                        return;
                                    }
                                    data.moveToFirst();
                                    do {
                                        if (data == null || data.isClosed()) {
                                            return;
                                        }
                                        String path = data.getString(data.getColumnIndexOrThrow(IMAGE_PROJECTION[0]));
                                        // 如原图路径不存在或者路径存在但文件不存在,就结束当前循环
                                        if (TextUtils.isEmpty(path) || !new File(path).exists()) {
                                            continue;
                                        }
                                        String name = data.getString(data.getColumnIndexOrThrow(IMAGE_PROJECTION[1]));
                                        long dateTime = data.getLong(data.getColumnIndexOrThrow(IMAGE_PROJECTION[2]));
                                        int duration = (type == TYPE_VIDEO ? data.getInt(data.getColumnIndexOrThrow(VIDEO_PROJECTION[4])) : 0);
                                        int width = data.getInt(data.getColumnIndexOrThrow(IMAGE_PROJECTION[4]));
                                        int height = data.getInt(data.getColumnIndexOrThrow(IMAGE_PROJECTION[5]));
                                        LocalMedia image = new LocalMedia(path, dateTime, duration,width,height);

                                        LocalMediaFolder folder = getImageFolder(path, imageFolders);

                                        folder.getImages().add(image);
                                        folder.setImageNum(folder.getImageNum() + 1);


                                        allImages.add(image);
                                        allImageFolder.setImageNum(allImageFolder.getImageNum() + 1);
                                    } while (data.moveToNext());

                                    if (allImages.isEmpty()) {
                                        allImageFolder.setFirstImagePath("");
                                    } else {
                                        allImageFolder.setFirstImagePath(allImages.get(0).getPath());
                                    }
                                    allImageFolder.setName("所有图片");
                                    allImageFolder.setImages(allImages);

                                    imageFolders.add(allImageFolder);
                                    sortFolder(imageFolders);
//                        data.close();
                                }
                                if (imageLoadListener != null && data != null && !data.isClosed()) {
                                    XThreadPool.Main().execute(new Runnable() {
                                        @Override
                                        public void run() {
                                            if (imageLoadListener != null && data != null && !data.isClosed()) {
                                                imageLoadListener.loadComplete(imageFolders);
                                            }
                                        }
                                    });
                                }
                            }
                        } catch (Exception exception) {
                            exception.printStackTrace();
                        }
                    }
                });
            }

            @Override
            public void onLoaderReset(Loader<Cursor> loader) {
            }
        });
    }

    private void sortFolder(List<LocalMediaFolder> imageFolders) {
        // 文件夹按图片数量排序
        Collections.sort(imageFolders, new Comparator<LocalMediaFolder>() {
            @Override
            public int compare(LocalMediaFolder lhs, LocalMediaFolder rhs) {
                if (lhs.getImages() == null || rhs.getImages() == null) {
                    return 0;
                }
                int lsize = lhs.getImageNum();
                int rsize = rhs.getImageNum();
                return lsize == rsize ? 0 : (lsize < rsize ? 1 : -1);
            }
        });
    }

    private LocalMediaFolder getImageFolder(String path, List<LocalMediaFolder> imageFolders) {
        File imageFile = new File(path);
        File folderFile = imageFile.getParentFile();

        for (LocalMediaFolder folder : imageFolders) {
            if (folder.getName().equals(folderFile.getName())) {
                return folder;
            }
        }
        LocalMediaFolder newFolder = new LocalMediaFolder();
        newFolder.setName(folderFile.getName());
        newFolder.setPath(folderFile.getAbsolutePath());
        newFolder.setFirstImagePath(path);
        imageFolders.add(newFolder);
        return newFolder;
    }

    public interface LocalMediaLoadListener {
        void loadComplete(List<LocalMediaFolder> folders);
    }

}
