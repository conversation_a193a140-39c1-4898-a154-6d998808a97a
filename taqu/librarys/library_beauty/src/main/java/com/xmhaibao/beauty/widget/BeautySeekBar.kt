package com.xmhaibao.beauty.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.os.Build
import android.util.AttributeSet
import com.xmhaibao.beauty.R

/**
 *  美颜 seekBar 支持进度条中心为起点的显示样式
 * * 默认绘制 最左侧为起点 最右侧为终点:  0_________100
 * * 中间为起点绘制 中心点为起点 两侧为结束点: -50____0____50
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
class BeautySeekBar : androidx.appcompat.widget.AppCompatSeekBar {
    private val TAG = "BeautySeekBar"

    /**
     * 绘制画笔
     */
    private val paint = Paint()

    /**
     * 进度条的边界
     * 设置进度条可绘制的区域,主要限制左侧两侧间距
     */
    private var progressBounds = RectF()

    /**
     * 进度条宽度
     */
    private var progressSize = 16f

    /**
     * 进度条起止圆角大小
     */
    private var progressRadius = 16f

    /**
     * 进度条最小值
     */
    private var progressMin = 0

    /**
     * 进度条颜色
     */
    private var progressColor = Color.WHITE

    /**
     * 进度条背景色
     */
    private var progressBgColor = Color.parseColor("#65FFFFFF")



    private val defaultDrawer by lazy {
        DefaultDrawerImpl()
    }
    private val middleStartDrawer by lazy {
        MiddleStartDrawerImpl()
    }

    private var drawer: AbsProgressDrawer = defaultDrawer

    /**
     * 进度滑块
     */
    private var tickMark = TickMark()

    constructor(context: Context) : super(context) {
        init(null, 0)
    }

    constructor(context: Context, attrs: AttributeSet? = null) : super(context, attrs) {
        init(attrs, 0)
    }

    constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init(attrs, defStyleAttr)
    }

    private fun init(attrs: AttributeSet?, defStyleAttr: Int) {

        val attr = context.obtainStyledAttributes(
            attrs, R.styleable.BeautySeekBar, defStyleAttr, 0
        )
        tickMark.width = attr.getDimension(R.styleable.BeautySeekBar_tickMarkWidth, tickMark.width)
        tickMark.height =
            attr.getDimension(R.styleable.BeautySeekBar_tickMarkHeight, tickMark.height)
        tickMark.radius =
            attr.getDimension(R.styleable.BeautySeekBar_tickMarkRadius, tickMark.radius)
        tickMark.textSize =
            attr.getDimension(R.styleable.BeautySeekBar_tickMarkTextSize, tickMark.textSize)
        tickMark.textColor =
            attr.getColor(R.styleable.BeautySeekBar_tickMarkTextColor, tickMark.textColor)
        progressSize = attr.getDimension(R.styleable.BeautySeekBar_progressBarRadius, progressSize)
        progressRadius =
            attr.getDimension(R.styleable.BeautySeekBar_progressBarRadius, progressRadius)
        progressColor =
            attr.getColor(R.styleable.BeautySeekBar_progressBarColor, progressColor)
        progressBgColor =
            attr.getColor(R.styleable.BeautySeekBar_progressBarBgColor, progressBgColor)

        attr.recycle()

    }


    /**
     * 修改进度展示样式
     * @param isMiddleStart true 中心为起点的显示样式
     * 默认绘制 最左侧为起点 最右侧为终点:  0_________100
     * 中间为起点绘制 中心点为起点 两侧为结束点: -50____0____50
     */
    fun changeStyle(isMiddleStart: Boolean) {
        drawer = if (isMiddleStart) {
            middleStartDrawer
        } else {
            defaultDrawer
        }
        resetDrawerConfig()
    }

    /**
     * 获取当前进度条最小值
     */
    fun getMinValue(): Int {
        return progressMin
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        //设置进度的区域
        progressBounds.set(
            paddingLeft.toFloat(), 0F,
            (measuredWidth - paddingRight).toFloat(),
            measuredHeight.toFloat()
        )
        resetDrawerConfig()
    }

    override fun setMin(min: Int) {
        super.setMin(min)
        val tmpMin = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            getMin()
        } else {
            if (min > max) {
                max
            } else {
                min
            }
        }
        progressMin = tmpMin
    }


    /**
     * 进度条的样式
     * @param size 进度条的高度
     * @param size 进度条的左右的圆角大小
     */
    fun setProgressBarStyle(size: Float, radius: Float) {
        this.progressSize = size
        this.progressRadius = radius
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas ?: return
        drawer.progress = progress
        drawBackground(canvas)
        drawer.onDraw(canvas)
    }

    /**
     * 重置配置
     */
    private fun resetDrawerConfig() {
        drawer.max = max
        drawer.min = progressMin
        drawer.progress = progress
        drawer.progressColor = progressColor
        //进度条的值
        drawer.progressRadius = progressRadius
        drawer.progressSize = progressSize
        drawer.progressBounds = progressBounds
        drawer.tickMark = tickMark

        invalidate()
    }


    //绘制进度条背景
    private fun drawBackground(canvas: Canvas) {
        paint.color = progressBgColor
        canvas.drawRoundRect(
            progressBounds.left,
            progressBounds.height() / 2 - progressSize / 2,
            progressBounds.right,
            progressBounds.height() / 2 + progressSize / 2,
            progressRadius,
            progressRadius,
            paint
        )

    }


    class TickMark {
        /**
         * 滑块宽度
         */
        var width: Float = 60F

        /**
         * 滑块高度
         */
        var height: Float = 40F

        /**
         * 滑块圆角大小
         */
        var radius: Float = 16F

        /**
         * 滑块颜色
         */
        var color = Color.WHITE

        /**
         * 字体大小
         */
        var textSize: Float = 16F

        /**
         * 字体颜色
         */
        var textColor = Color.BLACK

    }

    /**
     * 进度条绘制抽象类
     *
     */
    abstract class AbsProgressDrawer {
        /**
         * 最大值进度
         */
        var max = 100

        /**
         * 最小值进度
         */
        var min = 0

        /**
         * 当前进度
         */
        var progress: Int = 0

        /**
         * 进度条宽度
         */
        var progressSize = 16f

        /**
         * 进度条圆角大小
         */
        var progressRadius = 16f

        /**
         * 进度条颜色
         */
        var progressColor = Color.WHITE

        /**
         * 任务进度的区域
         */
        var progressBounds = RectF()

        /**
         * 进度滑块
         */
        var tickMark = TickMark()

        /**
         * 绘制
         * @param canvas
         */
        abstract fun onDraw(canvas: Canvas)

    }

    /**
     * 默认绘制器
     */
    class DefaultDrawerImpl : AbsProgressDrawer() {
        private val paint = Paint()

        init {
            paint.isAntiAlias = true
            paint.textSize = 20f
            paint.color = Color.BLUE
        }


        override fun onDraw(canvas: Canvas) {
            if (progressBounds.width() <= 0) {
                return
            }

            //当前进度占比
            val tmpProgress = progress * 1.0F / (max - min)
            //当前进度值对应的宽度
            val curProWidth = progressBounds.width() * tmpProgress
            val right = progressBounds.left + curProWidth
            val top = progressBounds.height() / 2 - progressSize / 2
            val bottom = progressBounds.height() / 2 + progressSize / 2
            paint.color = progressColor
            canvas.drawRoundRect(
                progressBounds.left, top, right, bottom,
                progressRadius, progressRadius,
                paint
            )

            //绘制滑块
            val tickMarkLeft = right - tickMark.width / 2f
            val tickMarkRight = right + tickMark.width / 2f
            val tickMarkTop = progressBounds.height() / 2 - tickMark.height / 2f
            val tickMarkBottom = progressBounds.height() / 2 + tickMark.height / 2f
            paint.color = tickMark.color
            canvas.drawRoundRect(
                tickMarkLeft, tickMarkTop, tickMarkRight, tickMarkBottom,
                tickMark.radius, tickMark.radius,
                paint
            )
            //绘制文字进度  相对进度条的终点为文案的中点
            val textBounds = Rect()
            paint.color = tickMark.textColor
            paint.textSize = tickMark.textSize
            paint.getTextBounds("$progress", 0, "$progress".length, textBounds)

            val textLeft = right - textBounds.width() / 2f
            val textTop = progressBounds.height() / 2f + textBounds.height() / 2

            canvas.drawText("$progress", textLeft, textTop, paint)

        }

    }

    /**
     * 中心作为起点的绘制器
     */
    class MiddleStartDrawerImpl : AbsProgressDrawer() {
        private val paint = Paint()

        /**
         * 实时进度条范围
         */
        private var realProgressRect = RectF()


        init {
            paint.isAntiAlias = true
            paint.textSize = 20f
            paint.color = Color.BLUE
        }


        override fun onDraw(canvas: Canvas) {
            if (progressBounds.width() <= 0) {
                return
            }
            val middle = progressBounds.left + progressBounds.width() / 2
            val tmpProgress = progress / (max - min).toFloat()
            //绘制进度条
            drawProgressBar(canvas, middle, tmpProgress)
            //绘制进度滑块
            drawTickMark(canvas, tmpProgress)

        }


        /**
         * 绘制进度条
         * @param canvas
         * @param middle 中间（起点）
         * @param progressValue 进度比例
         */
        private fun drawProgressBar(
            canvas: Canvas, middle: Float, progressValue: Float
        ) {
            if (progressValue < 0.5) {
                //进度在左侧
                //矩形的左侧为进度的位置 右侧为中心点
                val left = progressBounds.left + progressBounds.width() * progressValue
                val right = middle
                val top = progressBounds.height() / 2 - progressSize / 2
                val bottom = progressBounds.height() / 2 + progressSize / 2
                realProgressRect.set(left, top, right, bottom)
            } else {
                //进度在右侧
                val left = middle
                val right = progressBounds.left + progressBounds.width() * progressValue
                val top = progressBounds.height() / 2 - progressSize / 2
                val bottom = progressBounds.height() / 2 + progressSize / 2
                realProgressRect.set(left, top, right, bottom)
            }
            paint.color = progressColor
            canvas.drawRoundRect(
                realProgressRect,
                progressRadius,
                progressRadius,
                paint
            )
        }

        /**
         * 绘制滑块
         * @param canvas
         * @param progressValue 进度值
         */
        private fun drawTickMark(canvas: Canvas, progressValue: Float) {
            var tickMarkLeft: Float
            if (progressValue < 0.5) {
                //滑块在进度条的左侧
                tickMarkLeft = realProgressRect.left - tickMark.width / 2f
            } else {
                //滑块在进度条的右侧
                tickMarkLeft = realProgressRect.right - tickMark.width / 2f
            }
            val tickMarkRight = tickMarkLeft + tickMark.width
            val tickMarkTop = progressBounds.height() / 2 - tickMark.height / 2f
            val tickMarkBottom = progressBounds.height() / 2 + tickMark.height / 2f
            paint.color = tickMark.color
            canvas.drawRoundRect(
                tickMarkLeft, tickMarkTop, tickMarkRight, tickMarkBottom,
                tickMark.radius,
                tickMark.radius,
                paint
            )
            //绘制文字进度  相对进度条的终点为文案的中点
            val textBounds = Rect()
            paint.color = tickMark.textColor
            paint.textSize = tickMark.textSize
            val tmpProgressText = calcProgressUiValue(progress)
            paint.getTextBounds("$tmpProgressText", 0, "$tmpProgressText".length, textBounds)
            var textLeft: Float
            if (progressValue < 0.5) {
                //滑块在进度条的左侧
                textLeft = realProgressRect.left - textBounds.width() / 2f
            } else {
                //滑块在进度条的右侧
                textLeft = realProgressRect.right - textBounds.width() / 2f
            }
            val textTop = progressBounds.height() / 2f + textBounds.height() / 2
            canvas.drawText("$tmpProgressText", textLeft, textTop, paint)
        }


        /**
         * 计算Ui显示的进度值
         */
        private fun calcProgressUiValue(progress: Int): Int {
            return progress - (max - min) / 2
        }


    }
}