<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/rlIndicator"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="40dp"
        android:layout_above="@+id/seekBarContainer"
        android:background="@drawable/beautify_bg_dialog_custom_prompt"
        android:visibility="gone"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="10dp"
            android:textColor="@color/white"
            android:textSize="@dimen/t5_2" />

        <TextView
            android:id="@+id/tvValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="10dp"
            android:textColor="@color/white"
            android:textSize="@dimen/t5_2" />
    </RelativeLayout>

    <FrameLayout
        android:id="@+id/live_beautify_dismiss_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/seekBarContainer"
        android:layout_marginBottom="30dp" />

    <hb.drawable.shape.view.HbConstraintLayout
        android:id="@+id/seekBarContainer"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_above="@+id/container_bottom"
        android:paddingBottom="20dp"
        android:translationY="20dp"
        app:corner_bottom_left="0dp"
        app:corner_bottom_right="0dp"
        app:corner_top_left="8dp"
        app:corner_top_right="8dp"
        app:solid="@color/black_alpha_30">

        <TextView
            android:id="@+id/tvSeekbarLeft"
            android:layout_width="43dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="-50"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@id/seek_bar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/seek_bar" />

        <com.xmhaibao.beauty.widget.BeautySeekBar
            android:id="@+id/seek_bar"
            android:layout_width="match_parent"
            android:layout_marginTop="3dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="32dp"
            android:layout_marginRight="32dp"
            android:focusable="true"
            android:max="100"
            android:maxHeight="2dp"
            android:minHeight="2dp"
            android:padding="10dp"
            android:progressDrawable="@null"
            android:thumb="@null"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:progressBarBgColor="#6CFFFFFF"
            app:progressBarColor="#FFFFFF"
            app:progressBarRadius="3dp"
            app:progressBarSize="3dp"
            app:tickMarkHeight="14dp"
            app:tickMarkRadius="6dp"
            app:tickMarkTextColor="#282121"
            app:tickMarkTextSize="10sp"
            app:tickMarkWidth="24dp"

            />

        <TextView
            android:id="@+id/tvSeekbarRight"
            android:layout_width="43dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="50"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@id/seek_bar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/seek_bar" />
    </hb.drawable.shape.view.HbConstraintLayout>

    <LinearLayout
        android:id="@+id/container_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="bottom"
        android:layout_above="@id/bottomContainer"
        android:background="@drawable/beautify_bg_white_radius_16_top">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="39dp">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabBeauty"
                android:layout_width="wrap_content"
                android:layout_height="39dp"
                android:layout_alignParentLeft="true"
                android:layout_toLeftOf="@id/tvReset"
                app:tabBackground="@color/transparent"
                app:tabContentStart="10dp"
                app:tabGravity="start"
                app:tabIndicator="@drawable/beautify_tab_indicator"
                app:tabIndicatorColor="@color/TH_Yellow600"
                app:tabIndicatorFullWidth="false"
                app:tabMinWidth="44dp"
                app:tabMode="scrollable"
                app:tabPaddingEnd="0dp"
                app:tabPaddingStart="0dp"
                app:tabRippleColor="@android:color/transparent"
                app:tabSelectedTextColor="@color/TH_Gray990"
                app:tabTextColor="@color/TH_Gray600" />

            <TextView
                android:id="@+id/tvReset"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:layout_marginRight="16dp"
                android:drawableLeft="@drawable/beautify_setting_reset_ic"
                android:drawablePadding="2dp"
                android:gravity="center_vertical"
                android:text="@string/beautify_option_reset"
                android:textColor="@color/g1"
                android:textSize="@dimen/t6" />
        </RelativeLayout>

        <View
            android:id="@+id/line"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginRight="9.6dp"
            android:background="#f1f1f1" />

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vpBeauty"
            android:layout_width="match_parent"
            android:layout_height="95dp" />

        <LinearLayout
            android:id="@+id/llBottomRoot"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="26dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvCancel"
                style="@style/Btn3.Large"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:visibility="gone"
                tools:text="@string/beautify_option_cancel" />

            <View
                android:id="@+id/viewBottomBtnSpace"
                android:layout_width="10dp"
                android:layout_height="1dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvConfirm"
                style="@style/Btn1.Large"
                android:layout_width="0dp"
                android:layout_weight="1"
                tools:text="@string/beautify_option_save" />
        </LinearLayout>
    </LinearLayout>
    <FrameLayout
        android:id="@+id/bottomContainer"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
    <ImageView
        android:id="@+id/ivBeautifyBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="20dp"
        android:src="@drawable/ic_beauty_back"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/ivBeautifyClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="12dp"
        android:padding="8dp"
        android:src="@drawable/ic_beauty_close_white"
        android:visibility="gone" />
</RelativeLayout>