package com.xmhaibao.bytedance.glthread.exception;

import androidx.annotation.Nullable;

/**
 * GLThread 异常积累
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
public class GLThreadBaseException extends Throwable {
    /**
     * 未知错误
     */
    public static final int ERR_UNKNOWN = -100;
    /**
     * glContext 初始化失败
     */
    public static final int ERR_GL_INITIALIZE_FAILURE = 100;
    /**
     * glContext 丢失
     */
    public static final int ERR_GL_CONTEXT_LOST = 200;
    /**
     * glContext 恢复失败
     */
    public static final int ERR_GL_CONTEXT_RECOVERY_FAILURE = 300;
    private int errorCode;

    public GLThreadBaseException(int errorCode) {
        this.errorCode = errorCode;
    }

    public GLThreadBaseException(int errorCode, @Nullable String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public GLThreadBaseException(int errorCode, @Nullable String message, @Nullable Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public GLThreadBaseException(int errorCode, @Nullable Throwable cause) {
        super(cause);
        this.errorCode = errorCode;
    }

    public int getErrorCode() {
        return errorCode;
    }
}
