package hb.dynamic.check;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import androidx.lifecycle.Lifecycle;

import java.util.List;

import hb.dynamic.code.DynamicFileSolder;
import hb.dynamic.code.NativeLibraryProgressTask;
import hb.dynamic.code.NativeLibraryTask;
import hb.dynamic.code.downloadStrategy.DynamicDownloadStrategy;
import hb.dynamic.code.task.DynamicTaskFactory;

/**
 * 下载任务流程
 *
 * <AUTHOR>
 * @date 2020-12-24
 */
public interface DynamicDownloadProcessor extends DynamicScanProcessor {

    /**
     * 获取需要执行的 task
     *
     * @param taskInfos          基准文件信息
     * @param downloadStrategy   下载速度策略
     * @param dynamicTaskFactory 任务工厂，所有任务都在这当中定义了模版，从这里获取
     * @param lifecycle          处理生命周期
     * @param consumer
     * @return
     */
    @NonNull
    public List<NativeLibraryTask> getDownloadWorkProcesses(@NonNull List<DynamicFileSolder> taskInfos,
                                                            @NonNull DynamicDownloadStrategy downloadStrategy,
                                                            @NonNull DynamicTaskFactory dynamicTaskFactory,
                                                            @Nullable Lifecycle lifecycle,
                                                            @Nullable NativeLibraryProgressTask.TaskProgressListener consumer);

    /**
     * 下载完成之后处理的一些业务流程
     *
     * @param fileSolderInfo 本地的基准信息
     * @return == ture 代表处理完成
     */
    @WorkerThread
    public boolean onDownloadCompleteRear(@NonNull DynamicFileSolder fileSolderInfo);
}
