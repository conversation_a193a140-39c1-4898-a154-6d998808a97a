package com.xmhaibao.gift.effect;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Point;
import android.os.Build;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.LinearInterpolator;
import android.widget.FrameLayout;

import androidx.annotation.IdRes;
import androidx.annotation.LayoutRes;

import hb.utils.Loger;
import hb.utils.ScreenUtils;
import hb.xstatic.utils.XUtils;


/**
 * 直播-手写礼物动效基础类
 *
 * @author: wusongyuan
 * @date: 2016.08.10
 * @desc:
 */
public abstract class LiveGiftBaseBuilder {

    protected static final String TAG = "LiveGiftBaseBuilder";
    // 屏幕的宽度
    protected int mParentWidth;
    // 屏幕的高度
    public static int mParentHeight;

    protected OnGiftAnimatorListener mGiftAnimatorListener;

    private AnimatorSet mAnimatorSet;
    private long mDuration;

    private Context mContext;

    private ViewGroup mParentView;
    private View mContentView;

    private int mRepeatCount = 0;
    private boolean mIsFinishing = false;
    private int mCurrentIteration;
    @RepeatMode
    private int mRepeatMode = RepeatMode.RESTART;

    private boolean mIsAnimationCanceled = false;

    private boolean mIsRunning = false;
    protected float mScaleX;
    protected float mScaleY;
    private Handler mmHandler;

    /**
     * 是否允许后台播放，左右滑动不影响动效
     */
    private boolean isEnableRunningInBackground = false;

    public @interface RepeatMode {
        int RESTART = 1;
        int REVERSE = 2;
        int MIRROR = 3; // 镜像
        int TOGGLE = 4; // 镜像切换
    }

    /** 是否运行在后台 */
    private boolean mIsBackground;

    protected Context getContext() {
        return mContext;
    }

    private LiveGiftBaseBuilder the() {
        return LiveGiftBaseBuilder.this;
    }

    protected static LinearInterpolator LINEAR_INTERPOLATOR = new LinearInterpolator();

    public LiveGiftBaseBuilder(ViewGroup viewGroup) {
        this(viewGroup, 2000);
    }

    public LiveGiftBaseBuilder(ViewGroup viewGroup, long animDuration) {
        if (viewGroup == null) {
            return;
        }
        mmHandler = new Handler();
        mContext = viewGroup.getContext();
        mParentView = viewGroup;
        mParentWidth = ScreenUtils.getScreenWidth();
        //mParentHeight = getScreenHeight(viewGroup.getContext()) - getStatusBarHeight(mContext);
        //mParentWidth = 720;
        //mParentHeight = 1280;
        if (mParentHeight == 0) {
            mParentHeight = getScreenHeight(viewGroup.getContext());
        }
        mScaleX = mParentWidth / 640.0f;
        mScaleY = mParentHeight / 1136.0f;
        mAnimatorSet = new AnimatorSet();
        mDuration = animDuration;
        if (mDuration > 0) {
            mAnimatorSet.setDuration(mDuration);
        }
        //mAnimatorSet.addListener(mAnimatorListener);
        mAnimatorSet.setStartDelay(10);
        setupAnimators(mAnimatorSet);
        onCreate();
        Loger.i(TAG, "onCreate()");
        Loger.i(TAG, "Screen dp2px: 1->" + ScreenUtils.dp2px(getContext(), 1));
        Loger.i(TAG, "ScaleX:" + getScaleX() + " ScaleY:" + getScaleY());
    }

    protected abstract void onCreate();

    protected void setContentView(@LayoutRes int res) {
        mContentView = LayoutInflater.from(mContext).inflate(res, mParentView, false);
        mContentView.setLayoutParams(new FrameLayout.LayoutParams(mParentWidth, mParentHeight));
        mContentView.setVisibility(View.INVISIBLE);
        attachToWindow();
    }

    protected void setContentView(View guideView) {
        mContentView = guideView;
        mContentView.setLayoutParams(new FrameLayout.LayoutParams(mParentWidth, mParentHeight));
        mContentView.setVisibility(View.INVISIBLE);
        attachToWindow();
    }

    public View getContentView() {
        return mContentView;
    }

    private void attachToWindow() {
        Loger.d(TAG, "attachToWindow() start");
        if (mContentView.getParent() != null) {
            Loger.d(TAG, "attachToWindow() mContentView.getParent()!= null");
            ((ViewGroup) mContentView.getParent()).removeView(mContentView);
        }
        if (mParentView != null && mContentView != null) {
            mParentView.addView(mContentView);
        }
    }

    private void detachFromWindow(){
        if (mContentView.getParent() != null) {
            Loger.d(TAG, "detachFromWindow() mContentView.getParent()!= null");
            ((ViewGroup) mContentView.getParent()).removeView(mContentView);
        }
    }

    protected View findViewById(@IdRes int id) {
        if (mContentView == null) {
            throw new IllegalStateException("layout required, setContentView() set layout.");
        }
        return mContentView.findViewById(id);
    }

    public boolean isRunning() {
        return mIsRunning;
    }

    protected void setupAnimators(AnimatorSet animatorSet) {
        final ValueAnimator valueAnimator = ValueAnimator.ofFloat(0.0f, 1.0f);
        valueAnimator.setInterpolator(LINEAR_INTERPOLATOR);
        animatorSet.playTogether(valueAnimator);
    }

    private Animator.AnimatorListener mAnimatorListener = new AnimatorListenerAdapter() {
        @Override
        public void onAnimationStart(Animator animation) {
            Loger.i(TAG, "onAnimationStart()");
            mIsAnimationCanceled = false;
            mIsRunning = true;
        }

        @Override
        public void onAnimationEnd(Animator animation) {
            Loger.i(TAG, "onAnimationEnd()");
            mIsRunning = false;
            if (!mIsFinishing && mCurrentIteration < mRepeatCount && !mIsAnimationCanceled) {
                Loger.i(TAG, "onAnimationEnd() RepeatMode:" + mRepeatMode + " CurrentIteration:" + mCurrentIteration);
                if (mRepeatMode == RepeatMode.RESTART) {
                    start(true);
                } else if (mRepeatMode == RepeatMode.MIRROR) {
                    start(true);
                } else if (mRepeatMode == RepeatMode.TOGGLE) {
                    start(true);
                }
                if(!mIsBackground) the().onAnimationRepeat(mCurrentIteration, mRepeatMode);// 运行在非后台,则重复动画
                mCurrentIteration++;
            } else {
                reset();
                if(!mIsBackground) the().animEnd();// 运行在非后台,则结束动画
                if (mGiftAnimatorListener != null) {
                    mGiftAnimatorListener.onAnimatorEnd();
                }
            }
        }

        @Override
        public void onAnimationCancel(Animator animation) {
            Loger.i(TAG, "onAnimationCancel() ");
            mIsRunning = false;
            mIsAnimationCanceled = true;
        }

    };


    protected void animStart() {
        Loger.i(TAG, "animStart() ");
        mContentView.setVisibility(View.VISIBLE);
        attachToWindow();
        onAnimationStart();
    }
    protected void animEnd() {
        Loger.i(TAG, "animEnd() ");
        mContentView.setVisibility(View.INVISIBLE);
        mContentView.setAlpha(1);
        detachFromWindow();
        onAnimationEnd();
    }

    protected abstract void onAnimationStart();
    protected abstract void onAnimationEnd();

    protected void onAnimationRepeat(int currentIteration, @RepeatMode int repeatMode) {
    }


    public void start() {
        start(false);
    }

    private void start(boolean isRepeat) {
        if (isRunning()) {
            Loger.i(TAG, "start() isRunning:" + isRunning());
            return;
        }
        if (!isRepeat) {
            if(!mIsBackground) animStart(); // 运行在非后台,则运行动画
            if (mGiftAnimatorListener != null) {
                mGiftAnimatorListener.onAnimatorStart();
            }
        } else {
            Loger.i(TAG, "start() repeat ");
        }
        if (mAnimatorSet != null) {
            if (mAnimatorSet.getChildAnimations() == null || mAnimatorSet.getChildAnimations().size() == 0) {
                Loger.i(TAG, "start() need a child animation");
                return;
            }
            Loger.i(TAG, "start() start ");
            mAnimatorSet.removeAllListeners();
            mAnimatorSet.addListener(mAnimatorListener);
            mAnimatorSet.start();
        }
        mIsAnimationCanceled = false;
    }

    private void reset() {
        mCurrentIteration = 0;
    }


    public void cancel() {
        Loger.i(TAG, "cancel() ");
        if (!isRunning()) {
            Loger.i(TAG, "cancel() return. isRunning:" + isRunning());
            return;
        }
        mIsRunning = false; // 必须在mGiftAnimatorListener.onAnimatorEnd()前面
        mIsAnimationCanceled = true;
        if (mAnimatorSet != null) {
            Loger.i(TAG, "cancel() true");
            mAnimatorSet.removeAllListeners();
            mAnimatorSet.cancel();
            if (!mIsBackground) animEnd();
        }
        if (mGiftAnimatorListener != null) {
            Loger.i(TAG, "cancel() onAnimatorEnd");
            mGiftAnimatorListener.onAnimatorEnd();
        }
    }

    /** 运行在后台, 不执行子类动画 */
    public void runInBackground(boolean isBackground) {
        mIsBackground = isBackground;
    }

    protected void cancelAnim(Animator... animators) {
        if (animators == null || animators.length == 0) {
            return;
        }
        for (int i = 0; i < animators.length; i++) {
            if (animators[i] != null && animators[i].isStarted()) {
                animators[i].cancel();
            }
        }
    }

    protected void startAnim(Animator... animators) {
        if (animators == null || animators.length == 0) {
            return;
        }
        for (int i = 0; i < animators.length; i++) {
            if (animators[i] != null && !animators[i].isStarted()) {
                animators[i].start();
            }
        }
    }

    public interface OnGiftAnimatorListener {
        void onAnimatorStart();

        void onAnimatorEnd();
    }

    public void release() {
        onDestroy();
    }

    protected void onDestroy() {
        mGiftAnimatorListener = null;
        if (mAnimatorSet != null) {
            mIsFinishing = true;
            mAnimatorSet.removeAllListeners();
            cancel();
            mAnimatorSet = null;
        }

        if (mContentView.getParent() != null) {
            ((ViewGroup) mContentView.getParent()).removeView(mContentView);
        }
        if (mmHandler != null) {
            mmHandler.removeCallbacksAndMessages(null);
            mmHandler = null;
        }
    }

    public static void measure(View view) {
        if (view.getMeasuredHeight() == 0 || view.getMeasuredWidth() == 0) {
            int width = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
            int height = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
            view.measure(width, height);
        }
    }

    public void setGiftAnimatorListener(OnGiftAnimatorListener giftAnimatorListener) {
        mGiftAnimatorListener = giftAnimatorListener;
    }


    public int getRepeatCount() {
        return mRepeatCount;
    }

    protected LiveGiftBaseBuilder setRepeatCount(int repeatCount) {
        mRepeatCount = repeatCount;
        return this;
    }

    public int getRepeatMode() {
        return mRepeatMode;
    }

    public LiveGiftBaseBuilder setRepeatMode(@RepeatMode int repeatMode) {
        mRepeatMode = repeatMode;
        return this;
    }

    public float getScaleX() {
        return mScaleX;
    }

    /** 减掉状态栏后的比例 */
    public float getScaleY() {
        return mScaleY;
    }

    protected int getScreenWidth() {
        return mParentWidth;
    }

    protected int getScreenHeight() {
        return mParentHeight;
    }

    public static int getStatusBarHeight(Context context) {
        return XUtils.getStatusBarHeight();
    }

    public void bringToFront() {
        mParentView.bringChildToFront(mContentView);
    }

    /** 按750宽屏幕适配，6s屏 */
    protected void adapter750P(){
        mScaleX = mParentWidth / 750.0f; // 以75UI适配
        mScaleY = mParentHeight / 1334.0f;
    }

    /** 对根布局创建透明度0-1渐变动画
     * @param duration 时长
     * @return 动画
     * <AUTHOR>
     */
    protected ObjectAnimator createFadeIn(int duration) {
        return createFadeIn(mContentView, duration);
    }

    /** 创建透明度0-1渐变动画
     * @param view 控件
     * @param duration 时长
     * @return 动画
     * <AUTHOR>
     */
    protected ObjectAnimator createFadeIn(View view, int duration) {
        ObjectAnimator fadeIn = ObjectAnimator.ofFloat(view, View.ALPHA, 0f, 1f).setDuration(duration);
        fadeIn.setInterpolator(LINEAR_INTERPOLATOR);
        return fadeIn;
    }

    /** 创建透明度1-0渐变动画
     * @param duration 时长
     * @return 动画
     * <AUTHOR>
     */
    protected ObjectAnimator createFadeOut(int duration) {
        return createFadeOut(mContentView, duration);
    }

    /** 创建透明度1-0渐变动画
     * @param duration 时长
     * @param view 控件
     * @return 动画
     * <AUTHOR>
     */
    protected ObjectAnimator createFadeOut(View view, int duration) {
        ObjectAnimator fadeOut = ObjectAnimator.ofFloat(view, View.ALPHA, 1f, 0f).setDuration(duration);
        fadeOut.setInterpolator(LINEAR_INTERPOLATOR);
        return fadeOut;
    }

    /**
     * 等Width比例缩放
     */
    protected ViewGroup.LayoutParams setAdapter750PByRatioX(View view, int originalWidth, int originalHeight){
        ViewGroup.LayoutParams params = view.getLayoutParams();
        params.width = (int) (originalWidth * getScaleX());
        params.height = (int) (originalHeight * getScaleX());
        view.requestLayout();
        return params;
    }

    public final boolean post(Runnable r){
        if (mmHandler != null) {
            mmHandler.removeCallbacks(r);
            return mmHandler.post(r);
        }
        return false;
    }

    public final boolean postDelayed(Runnable r, long delayMillis) {
        if (mmHandler != null) {
            mmHandler.removeCallbacks(r);
            return mmHandler.postDelayed(r, delayMillis);
        }
        return false;
    }

    public void setData(Object object) {

    }

    public void setRightSize(int rightSize) {

    }

    public void setRightMargin() {

    }

    public void setDuration(long duration) {
        mDuration = duration;
        if (mAnimatorSet != null) {
            mAnimatorSet.setDuration(duration);
        }
    }

    public static int getScreenHeight(Context context) {
        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Point point = new Point();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            //noinspection ConstantConditions
            wm.getDefaultDisplay().getRealSize(point);
        } else {
            //noinspection ConstantConditions
            wm.getDefaultDisplay().getSize(point);
        }
        return point.y;
    }

    public void setEnableRunningInBackground(boolean enableRunningInBackground) {
        isEnableRunningInBackground = enableRunningInBackground;
    }

    public boolean isEnableRunningInBackground() {
        return isEnableRunningInBackground;
    }
}
