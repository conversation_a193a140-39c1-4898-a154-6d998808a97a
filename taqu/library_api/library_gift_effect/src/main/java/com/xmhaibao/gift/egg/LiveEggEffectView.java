package com.xmhaibao.gift.egg;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.AttrRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.xmhaibao.gift.effect.PeriodInterpolator;
import com.xmhaibao.pins.lib.api.R;
import com.xmhaibao.gift.model.LiveEggItemBean;

import java.io.File;

import hb.ximage.fresco.BaseDraweeView;
import hb.ximage.fresco.utils.FrescoUtils;
import hb.utils.Loger;
import hb.utils.ScreenUtils;

/**
 * <AUTHOR>
 * @date 2017.04.27
 * @desc 直播-彩蛋动效
 */

public class LiveEggEffectView extends FrameLayout implements EggEffectView {

    private static final String TAG = "LiveEggEffectView";

    private BaseDraweeView mBgIv;
    private BaseDraweeView mCenterIv;


    private Handler mHandler;
    private ObjectAnimator mBgFadeOut;
    private ObjectAnimator mBgRolationY;
    private AnimatorSet mBgScaleAnimSet;
    private AnimatorSet mCenterAnimSet;
//    private ObjectAnimator mTextAlpha;
//    private AnimatorSet mTextScaleAnimSet;
    private AnimatorListenerAdapter mAnimationListener;

    private boolean mIsEggEffecting;

    public LiveEggEffectView(@NonNull Context context) {
        this(context, null);
    }

    public LiveEggEffectView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LiveEggEffectView(@NonNull Context context, @Nullable AttributeSet attrs, @AttrRes int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        mHandler = new Handler();
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        LayoutInflater.from(getContext()).inflate(R.layout.live_egg_effect_layout, this, true);

        mBgIv = findViewById(R.id.live_egg_base_bg_iv);
        mBgIv.getHierarchy().setPlaceholderImage(R.color.transparent);
        mBgIv.setImageFromResource(R.drawable.live_egg_base_bg); // 非必须的
        mCenterIv = findViewById(R.id.live_egg_base_center_iv);
        mCenterIv.getHierarchy().setPlaceholderImage(R.color.transparent);
        mCenterIv.setImageFromResource(R.drawable.live_egg_base_center_bg); // 非必须的


        // 背景动画
        mBgIv.setPivotX(ScreenUtils.dp2px(getContext(), 250) / 2.0f);
        mBgIv.setPivotY(ScreenUtils.dp2px(getContext(), 185));
        ObjectAnimator bgAnimScaleX = ObjectAnimator.ofFloat(mBgIv, View.SCALE_X, 0f, 1f).setDuration(300);
        ObjectAnimator bgAnimScaleY = ObjectAnimator.ofFloat(mBgIv, View.SCALE_Y, 0f, 1f).setDuration(300);
        mBgScaleAnimSet = new AnimatorSet();
        mBgScaleAnimSet.playTogether(bgAnimScaleX, bgAnimScaleY);

        mBgRolationY = ObjectAnimator.ofFloat(mBgIv, View.ROTATION_Y, 0f, 5f, 0f, -5f, 0f, 5f, 0f).setDuration(3000);
        mBgRolationY.setInterpolator(new PeriodInterpolator(3000, 0, 500, 1000, 1500, 2000, 2500, 3000));

        mBgFadeOut = ObjectAnimator.ofFloat(this, View.ALPHA, 1f, 0f).setDuration(500);

        // 中心动画
        ObjectAnimator centerAnimScaleX = ObjectAnimator.ofFloat(mCenterIv, View.SCALE_X, 0.1f, 1.2f, 1f).setDuration(
                300);
        centerAnimScaleX.setInterpolator(new PeriodInterpolator(300, 0, 200, 300));
        ObjectAnimator centerAnimScaleY = ObjectAnimator.ofFloat(mCenterIv, View.SCALE_Y, 0.1f, 1.2f, 1f).setDuration(
                300);
        centerAnimScaleY.setInterpolator(new PeriodInterpolator(300, 0, 200, 300));
        AnimatorSet centerScaleAnimSet = new AnimatorSet();
        centerScaleAnimSet.playTogether(centerAnimScaleX, centerAnimScaleY);


        ObjectAnimator centerRolationY = ObjectAnimator.ofFloat(mCenterIv, View.ROTATION_Y, 0f, 8f, 0f, -8f, 0f, 8f, 0f)
                .setDuration(3000);
        centerRolationY.setInterpolator(new PeriodInterpolator(3000, 0, 500, 1000, 1500, 2000, 2500, 3000));
        ObjectAnimator centerTransX = ObjectAnimator.ofFloat(mCenterIv, View.TRANSLATION_X, 0f, -20f, 0f, 20f, 0f, -20f,
                0f).setDuration(3000);
        centerTransX.setInterpolator(new PeriodInterpolator(3000, 0, 500, 1000, 1500, 2000, 2500, 3000));
        AnimatorSet centerRolaTransAnimSet = new AnimatorSet();
        centerRolaTransAnimSet.playTogether(centerRolationY, centerTransX);


        mCenterAnimSet = new AnimatorSet();
        mCenterAnimSet.playSequentially(centerScaleAnimSet, centerRolaTransAnimSet);


    }

    @Override
    public void start(LiveEggItemBean bean) {
        mIsEggEffecting = true;
        File bgFile = null;
        File picFile = null;
        if (bean != null) {
            bgFile = FrescoUtils.getDownloadedImage(bean.getBgUrl());
            picFile = FrescoUtils.getDownloadedImage(bean.getBgUrl());
        }
        // 图片不存在本地，则使用默认动画效果
        if (bean != null && bgFile != null && picFile != null && bgFile.exists() && picFile.exists()) {
            mBgIv.setImageFromUrl(bean.getBgUrl());
            mCenterIv.setImageFromUrl(bean.getPicUrl());
            Loger.i(TAG, "start: egg effect");
        } else { // 默认动效
            Loger.i(TAG, "start: normal effect");
            if (bean != null ) {
                // 不存在则下载图片
                if (bgFile == null || !bgFile.exists()) {
                    FrescoUtils.downloadImage(bean.getBgUrl());
                }

                // 不存在则下载图片
                if (picFile == null || !picFile.exists()) {
                    FrescoUtils.downloadImage(bean.getPicUrl());
                }
            }
            mBgIv.setImageFromResource(R.drawable.live_egg_base_bg);
            mCenterIv.setImageFromResource(R.drawable.live_egg_base_center_bg);
        }
        start();
    }

    private void start() {
        setVisibility(VISIBLE);
        setAlpha(1f);
        mBgScaleAnimSet.start();
        mCenterAnimSet.start();
//        mTextScaleAnimSet.start();
//        mTextAlpha.start();
        if (mHandler != null) {
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    mBgRolationY.start();
                }
            }, 500);
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    mBgFadeOut.start();
                }
            }, 2800);
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    stopAnim();
                }
            }, 3300);
        }
        mIsEggEffecting = true;
        if (mAnimationListener != null) {
            mAnimationListener.onAnimationStart(new ValueAnimator());
        }
    }

    public void stopAnim() {
        mIsEggEffecting = false;
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
        }
        cancelAnim(mBgScaleAnimSet, mCenterAnimSet, mBgRolationY, mBgFadeOut);
        setVisibility(GONE);

        // 放到本段代码最后
        if (mAnimationListener != null) {
            mAnimationListener.onAnimationEnd(new ValueAnimator());
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        setAnimationListener(null);
        stopAnim();
        mHandler = null;
    }

    @Override
    public boolean isEggEffecting() {
        return mIsEggEffecting;
    }

    @Override
    public void setAnimationListener(AnimatorListenerAdapter animationListener) {
        mAnimationListener = animationListener;
    }

    protected void cancelAnim(Animator... animators) {
        if (animators == null || animators.length == 0) {
            return;
        }
        for (int i = 0; i < animators.length; i++) {
            if (animators[i] != null && animators[i].isStarted()) {
                animators[i].cancel();
            }
        }
    }
}
