<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/rlPropInfo"
        android:layout_width="280dp"
        android:layout_height="345dp"
        android:layout_gravity="center"
        android:background="@drawable/prop_bg_prop_info"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/imgClose"
            android:layout_width="38dp"
            android:layout_height="38dp"
            android:layout_alignParentRight="true"
            android:layout_marginTop="34dp"
            android:layout_marginRight="7dp"
            android:padding="5dp"
            android:src="@drawable/common_ic_close_white" />


        <RelativeLayout
            android:layout_width="252dp"
            android:layout_height="180dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="93dp"
            android:background="@drawable/bg_white_radio_8">

            <hb.ximage.fresco.BaseDraweeView
                android:id="@+id/imgIcon"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_marginLeft="14dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="16dp"
                app:placeholderImage="@drawable/prop_bg_prop_default"
                app:placeholderImageScaleType="fitXY" />

            <TextView
                android:id="@+id/tvPropName"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_marginTop="20dp"
                android:layout_toRightOf="@id/imgIcon"
                android:includeFontPadding="false"
                android:textColor="@color/g1"
                android:textSize="@dimen/t6"
                tools:text="新春礼包" />

            <TextView
                android:id="@+id/tvPropNum"
                android:layout_width="wrap_content"
                android:layout_height="15dp"
                android:layout_below="@+id/tvPropName"
                android:layout_toRightOf="@id/imgIcon"
                android:includeFontPadding="false"
                android:textColor="@color/c2"
                android:textSize="@dimen/t7"
                tools:text="数量：2" />

            <TextView
                android:id="@+id/tvPropTime"
                android:layout_width="wrap_content"
                android:layout_height="15dp"
                android:layout_below="@+id/tvPropNum"
                android:layout_marginTop="4dp"
                android:layout_toRightOf="@id/imgIcon"
                android:includeFontPadding="false"
                android:textColor="@color/g3"
                android:textSize="@dimen/t7"
                tools:text="数量：2" />

            <ScrollView
                android:id="@+id/svProp"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_below="@id/imgIcon"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="29dp"
                android:layout_marginRight="16dp"
                android:background="@drawable/prop_detail_desc_bg"
                android:paddingLeft="6dp"
                android:paddingTop="8dp"
                android:paddingRight="6dp"
                android:paddingBottom="8dp"
                android:scrollbars="none">

                <TextView
                    android:id="@+id/tvPropDesc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/g2"
                    tools:text="这是一个很长的描述啊这是一个很长的描述啊这是一个很长的描述啊，非常长哦，嗯嗯嗯嗯嗯" />
            </ScrollView>


        </RelativeLayout>

        <TextView
            android:id="@+id/tvPropUse"
            android:layout_width="240dp"
            android:layout_height="44dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="18dp"
            android:layout_marginBottom="14dp"
            android:background="@drawable/prop_use_bg"
            android:gravity="center"
            android:text="立即使用"
            android:textColor="@color/g1"
            android:textSize="@dimen/t6" />

    </RelativeLayout>
</FrameLayout>