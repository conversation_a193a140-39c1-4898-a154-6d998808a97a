package com.xmhaibao.hbchat.bottominput.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import java.io.Serializable

/**
 * description:私信emojis表情包动图 单位
 *
 * <AUTHOR>
 * @date 2020/12/15 11:18
 */
data class MessageEmojisGifBean(
    /**
         * 图片的唯一ID
         */
        var id: String?,
    /**
         * 图片文件md5值
         */
        var md5: String?,
    var origin: Origin?,
    var thumb: Thumb?
) : Serializable, IDoExtra {
    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        origin?.doExtra(response)
        thumb?.doExtra(response)
    }
}

/**
 * 大图相关数据
 */
data class Origin(
        /**
         * gif格式图片url地址
         */
        var gif: String?,
        /**
         * 高
         */
        var h: Int?,
        /**
         * 宽
         */
        var w: Int?,
        /**
         * webp格式图片url地址
         */
        var webp: String?
) : Serializable, IDoExtra {
    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        if (webp.isNullOrEmpty()) {
            webp = gif
        }
    }
}

/**
 * 缩略图
 */
data class Thumb(
        var gif: String?,
        var gif_still: String?,
        var h: Int?,
        var w: Int?,
        var webp: String?,
        var webp_still: String?
) : Serializable, IDoExtra {
    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        if (webp.isNullOrEmpty()) {
            webp = gif
        }

    }
}