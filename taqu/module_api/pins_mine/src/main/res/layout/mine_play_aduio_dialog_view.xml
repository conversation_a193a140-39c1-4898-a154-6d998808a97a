<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="48dp"
    android:background="@drawable/mine_play_audio_none_dialog_bg"
    android:gravity="center_vertical"
    android:orientation="horizontal">


    <FrameLayout
        android:id="@+id/flPlay"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginLeft="4dp"
        android:background="@drawable/bg_c1_radius_360">

        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/ivPlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:src="@drawable/mine_play_audio_normal"
            app:placeholderImage="@color/transparent" />


    </FrameLayout>


    <TextView
        android:id="@+id/tvTime"
        style="@style/Text.T5_2.C1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="13dp"
        android:layout_marginRight="16dp"
        android:gravity="right"
        tools:text="15s" />
</LinearLayout>