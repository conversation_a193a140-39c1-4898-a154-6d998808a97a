package com.xmhaibao.live.api.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import hb.common.helper.HostHelper


/**
 * 获取主播开播信息接口（群聊展示使用）
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
data class LiveMultiChatHostInfoForChatBean(
    @SerializedName("live_status")
    var liveStatus: String?,
    @SerializedName("avatar")
    var avatar: String?,
    var message: String?,
    var relation: String?
) : IDoExtra {
    /**
     * 是否开播
     */
    val isLiveOpen: Boolean
        get() = liveStatus == "1"

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        avatar = HostHelper.getAvatarHost().getWebpUrl_4_1(avatar)
    }

}