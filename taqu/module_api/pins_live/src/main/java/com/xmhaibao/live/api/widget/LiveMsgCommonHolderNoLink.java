package com.xmhaibao.live.api.widget;

import android.graphics.Color;
import android.text.style.ForegroundColorSpan;

import hb.utils.ColorUtils;
import hb.utils.SizeUtils;

/**
 * 直播-聊天消息-公共
 * <p/>
 * 存放一些常量、公共用到的
 * 优化：去掉了LiveMsgCommonHolder原有的静态内部类MyLinkMovementMethod
 *
 * <AUTHOR>
 * @date 2023.3.1
 * @desc
 */
public class LiveMsgCommonHolderNoLink {
    private int mOrangeColor = 0;
    private int mNameColor = 0;
    private int mHuangDiNameColor = 0;
    private int mAtColor = 0;
    private int mIntoRoomColor = 0;
    private int mWhiteColor = 0;
    private int mBlueColor;
    private int mAdGiftColor;
    private int mRedColor;

    private ForegroundColorSpan mAtYellowColorSpan;
    private ForegroundColorSpan mWhiteColorSpan;
    private ForegroundColorSpan mIntoRoomColorSpan;
    private ForegroundColorSpan mOrangeColorSpan;
    private ForegroundColorSpan mNameColorSpan;
    private ForegroundColorSpan mHuangDiSpan;
    private ForegroundColorSpan mBlueColorSpan;
    private ForegroundColorSpan mAdGiftColorSpan;
    private ForegroundColorSpan mRedColorSpan;
    private ForegroundColorSpan mFreeExtraColorSpan;


    private int mTextPaddingLeft;
    private int mTextPaddingRight;
    private int mTextPaddingRightForVip;
    private int mTextNormalPaddingTop;

    private LiveMsgCommonHolderNoLink() {
        mNameColor = ColorUtils.getColor("#FFDC6D");
        mHuangDiNameColor = ColorUtils.getColor("#FEFF39");
        mAtColor = ColorUtils.getColor("#FFD9C8");
        mOrangeColor = ColorUtils.getColor("#B7FFDA");
        mBlueColor = ColorUtils.getColor("#91E5FF");
        mIntoRoomColor = ColorUtils.getColor("#DCDCDC");
        mWhiteColor = ColorUtils.getColor("#FFFFFF");

        mRedColor = ColorUtils.getColor("#FFBECF");
        mAdGiftColor = ColorUtils.getColor("#FFEE97");

        mAtYellowColorSpan = new ForegroundColorSpan(mAtColor);
        mWhiteColorSpan = new ForegroundColorSpan(Color.WHITE);
        mOrangeColorSpan = new ForegroundColorSpan(mOrangeColor);
        mIntoRoomColorSpan = new ForegroundColorSpan(mIntoRoomColor);
        mNameColorSpan = new ForegroundColorSpan(mNameColor);
        mHuangDiSpan = new ForegroundColorSpan(mHuangDiNameColor);
        mBlueColorSpan = new ForegroundColorSpan(mBlueColor);
        mAdGiftColorSpan = new ForegroundColorSpan(mAdGiftColor);
        mRedColorSpan = new ForegroundColorSpan(mRedColor);
        mFreeExtraColorSpan = new ForegroundColorSpan(ColorUtils.getColor("#ff4456"));
        mTextPaddingLeft = SizeUtils.dp2px(8);
        mTextPaddingRight = SizeUtils.dp2px(8);
        mTextPaddingRightForVip = SizeUtils.dp2px(18);
        mTextNormalPaddingTop = SizeUtils.dp2px(4);
    }

    private static class Singleton {
        private static LiveMsgCommonHolderNoLink instance = new LiveMsgCommonHolderNoLink();
    }

    public static LiveMsgCommonHolderNoLink getInstance() {
        return Singleton.instance;
    }

    public int getOrangeColor() {
        return mOrangeColor;
    }

    public int getNameColor() {
        return mNameColor;
    }

    public int getHuangDiNameColor() {
        return mHuangDiNameColor;
    }

    public int getIntoRoomColor() {
        return mIntoRoomColor;
    }

    public int getWhiteColor() {
        return mWhiteColor;
    }

    public int getBlueColor() {
        return mBlueColor;
    }

    public int getAtColor() {
        return mAtColor;
    }

    public int getAdGiftColor() {
        return mAdGiftColor;
    }

    public int getRedColor() {
        return mRedColor;
    }

    public ForegroundColorSpan getWhiteColorSpan() {
        return mWhiteColorSpan;
    }

    public ForegroundColorSpan getAtYellowColorSpan() {
        return mAtYellowColorSpan;
    }

    public ForegroundColorSpan getIntoRoomColorSpan() {
        return mIntoRoomColorSpan;
    }

    public ForegroundColorSpan getOrangeColorSpan() {
        return mOrangeColorSpan;
    }

    public ForegroundColorSpan getNameColorSpan() {
        return mNameColorSpan;
    }

    public ForegroundColorSpan getHuangdiColorSpan() {
        return mHuangDiSpan;
    }

    public ForegroundColorSpan getBlueColorSpan() {
        return mBlueColorSpan;
    }

    public ForegroundColorSpan getAdGiftColorSpan() {
        return mAdGiftColorSpan;
    }

    public ForegroundColorSpan getRedColorSpan() {
        return mRedColorSpan;
    }

    public ForegroundColorSpan getFreeExtraColorSpan() {
        return mFreeExtraColorSpan;
    }

    public void setFlowerExtraColorSpan(ForegroundColorSpan flowerExtraColorSpan) {
        mFreeExtraColorSpan = flowerExtraColorSpan;
    }

    public int getTextPaddingLeft() {
        return mTextPaddingLeft;
    }

    public int getTextPaddingRight() {
        return mTextPaddingRight;
    }

    public int getTextPaddingRightForVip() {
        return mTextPaddingRightForVip;
    }

    public int getTextNormalPaddingTop() {
        return mTextNormalPaddingTop;
    }


}
