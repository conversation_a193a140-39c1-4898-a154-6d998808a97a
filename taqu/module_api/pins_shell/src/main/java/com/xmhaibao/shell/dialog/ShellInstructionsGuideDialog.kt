package com.xmhaibao.shell.dialog

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import cn.taqu.lib.base.router.RouterLaunch
import cn.taqu.lib.base.utils.AppQuCoinHelper
import com.xmhaibao.pins.module.api.R
import com.xmhaibao.pins.module.api.databinding.ShellExchangeConfirmDialogBinding
import com.xmhaibao.pins.module.api.databinding.ShellInstructionsGuideDialogBinding
import hb.utils.ActivityUtils
import hb.xstyle.xdialog.XLifecycleDialog


/**
 * 二级货币-贝壳，说明弹窗
 *
 * <AUTHOR>
 * @date 2024-09-08
 */
class ShellInstructionsGuideDialog(
    context: Context,
    private var mShellIntroRelation: String?,
) :
    XLifecycleDialog(context) {

    companion object {
        @JvmStatic
        fun showDialog(
            context: Context?,
            shellIntroRelation: String?,
        ): ShellInstructionsGuideDialog? {
            if (context != null && ActivityUtils.isActivityAlive(context)) {
                val dialog = ShellInstructionsGuideDialog(
                    context, shellIntroRelation
                )
                dialog.show()
                return dialog
            }
            return null
        }
    }

    private lateinit var binding: ShellInstructionsGuideDialogBinding


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ShellInstructionsGuideDialogBinding.inflate(LayoutInflater.from(context))
        setContentView(binding.root)
        val lp = window?.attributes
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        lp?.height = WindowManager.LayoutParams.WRAP_CONTENT
        lp?.gravity = Gravity.BOTTOM
        window?.attributes = lp
        binding.apply {
            bdvGuideImg.setImageFromResource(R.drawable.shell_instructions_guide_ic)
            bdvGuideImg.setOnClickListener {
                if (!mShellIntroRelation.isNullOrEmpty()) {
                    RouterLaunch.dealJumpData(context, mShellIntroRelation)
                }
                dismiss()
            }
        }
    }


}