package com.xmhaibao.account.api.viewholder

import android.view.ViewGroup
import com.xmhaibao.account.api.bean.PersonalEditProfileIdealTypeBean
import com.xmhaibao.pins.module.api.R
import com.xmhaibao.pins.module.api.databinding.PersonalEditProfileIdealTypeViewHolderBinding
import hb.xadapter.XBaseViewHolder

/**
 * 我的理想型编辑页viewHolder
 * [飞书需求](https://project.feishu.cn/haibao/story/detail/**********)
 * <AUTHOR>
 * @date 2024-11-27
 */
class PersonalEditProfileIdealTypeViewHolder(private val parent: ViewGroup) : XBaseViewHolder<PersonalEditProfileIdealTypeBean>(
    parent,
    R.layout.personal_edit_profile_ideal_type_view_holder
) {
    private val binding by lazy {
        PersonalEditProfileIdealTypeViewHolderBinding.bind(itemView)
    }

    /**
     * 目标用户uuid
     */
    private var targetUuid = ""

    /**
     * 是否为预览页
     */
    private var isPreviewPage = true

    /**
     * 设置用户信息
     */
    fun setViewInfo( targetUuid: String, isPreviewPage: Boolean) {
        this.targetUuid = targetUuid
        this.isPreviewPage = isPreviewPage
    }

    override fun onBindView(item: PersonalEditProfileIdealTypeBean?) {
        item?.run {
            binding.viewIdealType.setData(this, targetUuid,  isPreviewPage)
        }

    }

}