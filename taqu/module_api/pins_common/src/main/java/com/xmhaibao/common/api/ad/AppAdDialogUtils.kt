package com.xmhaibao.common.api.ad

import android.app.Activity
import android.content.Context
import androidx.lifecycle.LifecycleOwner
import cn.taqu.lib.base.interf.DefaultOnShowMessageTabGuideListener
import cn.taqu.lib.base.interf.INavigationAdDialogShowCallback
import cn.taqu.lib.base.router.ARouterManager
import hb.advertising.AdBaseBean
import hb.advertising.HBAdHelper
import hb.advertising.IAdDialogShowInterceptor
import hb.advertising.IAdView
import hb.advertising.IAdViewCallback
import hb.common.data.AccountHelper
import hb.utils.Loger

/**
 * description:应用级弹窗
 *
 * 支持筛选通过数仓筛选人群，并指定人群弹出应用级弹窗
 *
 * <AUTHOR>
 * @date 2021/07/16 13:53
 */
class AppAdDialogUtils {


    companion object {

        private val TAG = "AppAdDialogUtils"

        @Volatile
        private var isListening = false

        /**
         * 记录下已经展示过的fragment，做单次启动应用频控
         */
        private val alreadyBindFragment = mutableSetOf<String>()


        /**
         * 广告弹窗是否要拦截
         *
         * 目前只有在首页-交友tab下才需要拦截，因为那里有个消息tab引导（丘比特动效）要展示
         * 如果要展示丘比特动效，则不显示广告了，不要显示丘比特动效时，才继续走显示广告的逻辑
         */
        private fun needIntercept(context: Context): Boolean {
            return ARouterManager.appService().isNavigationActivity(context)
                    && ARouterManager.appService().isFriendTabSelected
                    && AccountHelper.isFemale()
        }

        /**
         * 到达首页tab指定界面 检查是否可以显示广告
         * @param context navigationActivity
         * @param fragmentName 首页显示的fragment
         */
        fun showInNavigationTab(context: Activity, fragmentName: String?) {
            if (ARouterManager.appService().isNavigationActivity(context)) {
                if (isListening) {
                    return
                }
                isListening = true
                //优质女专项-补贴提示引导弹窗
                ARouterManager.messageForBaseService().showQualityFemaleAwardDialog(context, object : INavigationAdDialogShowCallback {
                    override fun onCallback(isShow: Boolean) {
                        isListening = false
                        if (isShow.not()) {
                            return
                        }
                        showNewFemaleMvpDialog(context, fragmentName)
                    }
                })
            } else {
                showNewFemaleMvpDialog(context, fragmentName)
            }
        }

        /**
         * 注册首日提现弹窗
         */
        private fun showNewFemaleMvpDialog(context: Activity, fragmentName: String?) {
            if (ARouterManager.appService().isNavigationActivity(context)
                && ARouterManager.appService().isFriendTabSelected
            ) {
                if (isListening) {
                    return
                }
                isListening = true
                // 弹窗优先级：注册首日提现>生日>签到>流量中台
                // 需求来源：https://o15vj1m4ie.feishu.cn/wiki/Yqu9wCFz5i43Ffk2ZI5c9rgXnlh
                ARouterManager.accountForBaseService().checkNewFemaleMvpDialog(object : INavigationAdDialogShowCallback {
                    override fun onCallback(isShow: Boolean) {
                        isListening = false
                        if (isShow.not()) {
                            return
                        }
                        // 显示生日弹窗
                        ARouterManager.messageForBaseService().checkNavigationBirthdayDialog(context, object : INavigationAdDialogShowCallback {
                            override fun onCallback(isShow: Boolean) {
                                if (isShow) {
                                    showInNavigationTabByCheckLucyGuide(context, fragmentName)
                                }
                            }
                        })
                    }
                })
                return
            } else {
                showInNavigationTabByCheckLucyGuide(context, fragmentName)
            }
        }

        /**
         *
         * 到达首页tab指定界面 检查是否可以显示广告(丘比特动效完成后才能显示广告)
         * @param context navigationActivity
         * @param fragmentName 首页显示的fragment
         */
        private fun showInNavigationTabByCheckLucyGuide(context: Activity, fragmentName: String?) {
            if (needIntercept(context)) {
                Loger.i(TAG,"show intercept!")
                //只有当前是在首页，且是交友tab下，才会走进这里
                if (isListening) {
                    //检查的监听只设置一次，已经设置过了，则忽略此次调用，等待上一次的回调回来
                    Loger.w(TAG,"show intercept ignore: isListening = true")
                    return
                }
                isListening = true
                //检查消息tab引导（丘比特动效）是否已经展示完了(或者是不需要展示)
                ARouterManager.messageForBaseService()
                    .isCheckMessageTabCompleted(object : DefaultOnShowMessageTabGuideListener() {
                        override fun onIgnore() {
                            Loger.i(TAG,"show intercept callback, call realShow!")
                            //重置标志位
                            isListening = false
                            if (needIntercept(context)) {
                                showSignDialog(context, fragmentName)
                            }
                        }

                        override fun onShowCompleted() {
                            Loger.i(TAG,"show intercept callback, onShowCompleted")
                            isListening = false
                            HBAdHelper.delayRequestByFriendHome = false
                        }
                    })
            } else {
                isListening = false
                showSignDialog(context, fragmentName)
            }
        }

        /**
         * 显示签到弹框
         *
         * @param context 上下文
         * @param fragmentName fragment名称
         */
        private fun showSignDialog(context: Activity, fragmentName: String?) {
            if (isListening) {
                //检查的监听只设置一次，已经设置过了，则忽略此次调用，等待上一次的回调回来
                Loger.w(TAG,"show intercept ignore: isListening = true")
                return
            }
            isListening = true
            ARouterManager.mineForBaseService().showSignDialogNavigationHomeTab(fragmentName, object : INavigationAdDialogShowCallback {
                override fun onCallback(isShow: Boolean) {
                    isListening = false
                    if (isShow) {
                        showInNavigationAdDialog(context, fragmentName)
                    }
                }
            })
        }

        /**
         * 到达首页tab指定界面 检查是否可以显示广告
         * @param context navigationActivity
         * @param fragmentName 首页显示的fragment
         */
        fun showInNavigationAdDialog(context: Activity, fragmentName: String?) {
            fragmentName?.let {
                if (alreadyBindFragment.contains(it)) {
                    return
                }
                HBAdHelper.bindDialogAd(
                    context,
                    context as LifecycleOwner,
                    it,
                    object : IAdDialogShowInterceptor {
                        override fun showInterceptor(adBaseBean: AdBaseBean?): Boolean {
                            // 素材是否为空
                            val isEmptyMaterial =
                                adBaseBean?.materials?.isEmpty() ?: true
                            // 在当前tab回调才显示 , 素材为空也拦截不显示
                            return it != ARouterManager.appService()
                                .currentSelectedTabClazz().simpleName || isEmptyMaterial
                        }
                    }, object : IAdViewCallback {
                        override fun onCreateSuccess(
                            adCode: String,
                            viewType: HBAdHelper.AdViewType,
                            adView: IAdView
                        ) {
                            //保证广告位只弹出一次
                            if (alreadyBindFragment.contains(fragmentName)) {
                                return
                            }
                            alreadyBindFragment.add(fragmentName)
                            adView.show()
                        }

                    })
            }
        }

        /**
         * 应用启动后的请求，放在navigation显示
         *
         *
         * 触发点在navigationActivity的onResume，但是也会去重，确保有且只请求一次
         * 切换账号清空本地记录的广告，保证切账号也能正常弹出广告
         */
        fun clearBindFragment() {
            alreadyBindFragment.clear()
        }
    }
}