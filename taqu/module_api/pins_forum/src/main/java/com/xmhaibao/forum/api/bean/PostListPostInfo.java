package com.xmhaibao.forum.api.bean;

import com.google.gson.annotations.SerializedName;
import com.xmhaibao.forum.api.interf.IPostImageSupportRead;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import cn.taqu.lib.base.bean.ImageInfo;
import hb.utils.CollectionUtils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/5.
 */
public class PostListPostInfo extends PostListCommonInfo implements IPostImageSupportRead, Serializable {

    @SerializedName("content")
    private String content;// 正文概要
    private String imgUrl;// 图片
    private int width;
    private int height;
    //    @SerializedName("review_count")
//    private String reviewNum;//回帖，评论的数量总和
    private ArrayList<ForumReviewInfo> reviewList;
    @SerializedName("post_media")
    private ForumMediaInfo mediaInfo; //多媒体
    private TopReviewInfo topReviewInfo;
    private List<ForumTopicListBean> topicList;
    private ImageInfo coverImg;//封面
    private boolean isMySelf;
    private boolean isShowPersonalPostBtn; //是否显示今天发布帖子按钮布局
    private boolean isShowPersonalPostDes; //是否显示今天发布帖子按钮描述

    public boolean isShowPersonalPostDes() {
        return isShowPersonalPostDes;
    }

    public void setShowPersonalPostDes(boolean showPersonalPostDes) {
        isShowPersonalPostDes = showPersonalPostDes;
    }

    public boolean isShowPersonalPostBtn() {
        return isShowPersonalPostBtn;
    }

    public void setShowPersonalPostBtn(boolean showPersonalPostBtn) {
        isShowPersonalPostBtn = showPersonalPostBtn;
    }

    public boolean isMySelf() {
        return isMySelf;
    }

    public void setMySelf(boolean mySelf) {
        isMySelf = mySelf;
    }

    @SerializedName("host_info")
    private PostListHostInfo mPostListHostInfo;//直播

    public PostListHostInfo getPostListHostInfo() {
        return mPostListHostInfo;
    }

    public void setPostListHostInfo(PostListHostInfo mPostListHostInfo) {
        this.mPostListHostInfo = mPostListHostInfo;
    }

    public List<ForumTopicListBean> getTopicList() {
        return topicList;
    }

    public void setTopicList(List<ForumTopicListBean> topicList) {
        this.topicList = topicList;
    }

    public TopReviewInfo getTopReviewInfo() {
        return topReviewInfo;
    }

    public void setTopReviewInfo(TopReviewInfo topReviewInfo) {
        this.topReviewInfo = topReviewInfo;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public ArrayList<ForumReviewInfo> getReviewList() {
        return reviewList;
    }

    public void setReviewList(ArrayList<ForumReviewInfo> reviewList) {
        this.reviewList = reviewList;
    }

    public ImageInfo getCoverImg() {
        return coverImg;
    }

    public void setCoverImg(ImageInfo coverImg) {
        this.coverImg = coverImg;
    }

    @Override
    public String[] getIdentity() {
        return identity;
    }

    @Override
    public void setIdentity(String[] identity) {
        this.identity = identity;
    }

    public ForumMediaInfo getMediaInfo() {
        return mediaInfo;
    }

    public void setMediaInfo(ForumMediaInfo mediaInfo) {
        this.mediaInfo = mediaInfo;
    }

    @Override
    public PostListItemType getListItemType() {
        if (mPostListHostInfo != null) {
            return PostListItemType.POST_TYPE_IS_HOST;
        }
        return PostListItemType.POST_TYPE_IS_POST;
    }

    @Override
    public PostListType getPostListType() {
        return super.getPostListType();
    }

    @Override
    public boolean supportRead() {
        return true;
    }

    @Override
    public String getTopicId() {
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isNotEmpty(topicList)) {
            for (int i = 0; i < topicList.size(); i++) {
                if (i != 0) {
                    sb.append(",");
                }
                sb.append(topicList.get(i).getTopicUuid());
            }
        }
        return sb.toString();
    }
}
