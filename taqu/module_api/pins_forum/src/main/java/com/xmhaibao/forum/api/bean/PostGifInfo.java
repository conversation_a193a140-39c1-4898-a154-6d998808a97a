package com.xmhaibao.forum.api.bean;

import java.io.Serializable;

import cn.taqu.lib.base.common.XjbApplicationHelper;
import com.xmhaibao.forum.api.constants.PostAdapterItemType;

public class PostGifInfo implements Serializable, PostDisplayItem {

    private String postUuid;
    private String gifUrl;
    private String relaction;
    private String description;
    private String coverImgUrl;
    private int size;
    private int width;
    private int height;
    private int imageDeviceHeight;// 根据设备的宽度，计算得到的展示图片时的高度
    int imgDeviceWith = XjbApplicationHelper.getInstance().getWidth();

    public String getPostUuid() {
        return postUuid;
    }

    public void setPostUuid(String postUuid) {
        this.postUuid = postUuid;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public String getGifUrl() {
        return gifUrl;
    }

    public void setGifUrl(String gifUrl) {
        this.gifUrl = gifUrl;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRelaction() {
        return relaction;
    }

    public void setRelaction(String relaction) {
        this.relaction = relaction;
    }

    public String getCoverImgUrl() {
        return coverImgUrl;
    }

    public void setCoverImgUrl(String coverImgUrl) {
        this.coverImgUrl = coverImgUrl;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getImageDeviceHeight() {
        if (imageDeviceHeight == 0) {
            if (width == 0) {
                imageDeviceHeight = imgDeviceWith;
            } else {
                imageDeviceHeight = imgDeviceWith * height / width;
            }
        }
        return imageDeviceHeight;
    }

    /*
     * 必须有宽高值的时候才能设置
     */
    public void setImageDeviceHeight() {

        if (width == 0) {
            this.imageDeviceHeight = height;
        } else {
            this.imageDeviceHeight = imgDeviceWith * height / width;
        }
    }

    /**
     * 根据给定的宽度计算图片应该显示的高度
     *
     * @param imgDeviceWith
     */
    public void setImageDeviceHeight(int imgDeviceWith) {
        if (width == 0) {
            this.imageDeviceHeight = height;
        } else {
            this.imageDeviceHeight = imgDeviceWith * height / width;
        }
    }

    @Override
    public int getItemType() {
        return PostAdapterItemType.POST_MODULE_TYPE_GIF;
    }
}
