<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/flVideoWindow"
    android:layout_width="90dp"
    android:layout_height="160dp"
    android:layout_alignParentRight="true"
    android:layout_alignParentBottom="true"
    android:layout_marginRight="16dp"
    android:layout_marginBottom="90dp"
    android:background="@drawable/forum_small_video_window_bg_ic"
    android:paddingStart="4dp"
    android:paddingTop="6dp"
    android:paddingEnd="4dp"
    tools:visibility="visible"
    android:visibility="gone">

    <cn.taqu.lib.base.widget.RoundFrameLayout
        android:id="@+id/videoParent"
        android:layout_width="match_parent"
        android:layout_height="148.5dp"
        android:layout_gravity="top"
        app:roundLayoutRadius="4dp" />

    <ImageView
        android:layout_width="41dp"
        android:layout_height="20dp"
        android:layout_marginStart="3dp"
        android:layout_marginTop="5dp"
        android:src="@drawable/forum_small_video_hint"
        android:text="直播" />

    <ImageView
        android:id="@+id/ivVideoClose"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_gravity="right"
        android:src="@drawable/forum_small_window_close_ic" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottieLive"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_gravity="bottom"
        android:layout_marginBottom="3.5dp"
        app:lottie_autoPlay="true"
        app:lottie_fileName="lottie/forum_personal_live/forum_personal_live.json"
        app:lottie_imageAssetsFolder="lottie/forum_personal_live/"
        app:lottie_loop="true" />

    <ImageView
        android:id="@+id/ivLiveTips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal|bottom"
        android:layout_marginBottom="35dp"
        android:background="@drawable/forum_live_entrance_tips_ic"
        android:visibility="gone" />
</FrameLayout>