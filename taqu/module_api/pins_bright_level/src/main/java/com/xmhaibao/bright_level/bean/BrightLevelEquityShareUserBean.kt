package com.xmhaibao.bright_level.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import hb.common.helper.HostHelper

/**
 * 璀璨等级-权益共享用户信息
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
class BrightLevelEquityShareUserBean : IDoExtra {
    @SerializedName("avatar")
    var avatar: String? = null

    @SerializedName("uuid")
    var userId: String? = null

    @SerializedName("account_name")
    var nickName: String? = null

    @SerializedName("age")
    var age: String? = null

    @SerializedName("sex_type")
    var sexType: String? = null

    @SerializedName("ip_city")
    var city: String? = null

    @SerializedName("online_status")
    var onlineStatus: String? = null

    fun isOnline() = onlineStatus == "1"

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        avatar = HostHelper.getAvatarHost().getWebpUrl_4_1(avatar)
    }
}