package com.xmhaibao.bright_level.utils

import android.graphics.LinearGradient
import android.graphics.Shader
import android.text.SpannableString
import android.text.Spanned
import android.widget.TextView


/**
 * textview渐变色工具累
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
object GradientColorsUtils {

    /**
     * 设置渐变色文案，渐变色从左往右
     * @param textView 文本控件
     * @param textStr 文本
     * @param colors 渐变色数组
     */
    fun setColorsText(textView: TextView?, textStr: String? = null, colors: IntArray?) {
        textView?.apply {
            textStr?.let {
                if (colors == null) {
                    this.text = it
                    return@let
                }
                if (colors.isNotEmpty()) {
                    if (colors.size == 1) {
                        this.setTextColor(colors[0])
                    } else if (measuredWidth == 0) {
                        post { realSetColorsText(this, it, colors) }
                    } else {
                        realSetColorsText(this, it, colors)
                    }

                } else {
                    this.text = textStr
                }
            }
        }
    }

    /**
     * 设置渐变色文案，渐变色从左往右
     * @param textView 文本控件
     * @param textStr 文本
     * @param colors 渐变色数组
     */
    private fun realSetColorsText(textView: TextView, textStr: String, colors: IntArray) {
        textView.apply {
            val paint = this.paint.also {
                it.isAntiAlias = true
            }
            // 获取View的宽度和高度
            val viewWidth = measuredWidth
            val textWidth = paint.measureText(textStr)
            // 计算文本居中绘制的起始x
            var dx = (viewWidth - textWidth) / 2
            if (dx < 0) {
                dx = 0F
            }
            val spannableString = SpannableString(textStr)
            val textShader =
                LinearGradient(dx, 0f, dx + textWidth, 0f, colors, null, Shader.TileMode.CLAMP)
            spannableString.setSpan(
                ChangeColorShaderSpan(textShader),
                0,
                textStr.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            this.text = spannableString
        }
    }
}