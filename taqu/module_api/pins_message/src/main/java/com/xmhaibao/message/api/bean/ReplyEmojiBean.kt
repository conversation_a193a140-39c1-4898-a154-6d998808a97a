package com.xmhaibao.message.api.bean
import com.google.gson.annotations.SerializedName


/**
 * 表情回复模型
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
data class ReplyEmojiBean(
    @SerializedName("emoji_key")
    var emojiKey: String? = "", // emoji1
    @SerializedName("user_info")
    var userInfo: UserInfo? = UserInfo()
)

data class UserInfo(
    @SerializedName("msg_id")
    var msgId: String? = "", // 1632745600000
    @SerializedName("user_name")
    var userName: String? = "", // user1Name
    @SerializedName("user_uuid")
    var userUuid: String? = "" // user1UUID
)