package com.xmhaibao.message.api.router.service.redpacket;

import android.content.Context;

import com.alibaba.android.arouter.facade.template.IProvider;
import com.xmhaibao.message.api.callback.OnRedPackageSendSuccessListener;

public interface MessageRedPacketService extends IProvider {

    void showMessageRedPacketDialog(Context context, String familyId, boolean isPlaza, OnRedPackageSendSuccessListener listener);
}
