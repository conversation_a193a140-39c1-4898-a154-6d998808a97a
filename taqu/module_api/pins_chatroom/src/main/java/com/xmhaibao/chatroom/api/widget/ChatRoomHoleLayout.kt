package com.xmhaibao.chatroom.api.widget

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout

/**
 * 引导浮层挖洞布局
 * <AUTHOR>
 * @date 2023/9/27
 */
class ChatRoomHoleLayout(context: Context, attrs: AttributeSet) : ConstraintLayout(context, attrs) {

    private val paint = Paint().apply {
        xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
    }
    private val holeList by lazy { mutableListOf<HoleBean>() }

    override fun draw(canvas: Canvas) {
        val id = canvas?.saveLayer(0f, 0f, width.toFloat(), height.toFloat(), null)
        super.draw(canvas)
        holeList.forEach {
            if (it.holeChild) canvas?.drawPath(it.path, paint)
        }
        id?.let { canvas.restoreToCount(it) }
    }

    override fun onDraw(canvas: Canvas) {
        holeList.forEach {
            if (!it.holeChild) canvas?.drawPath(it.path, paint)
        }
        super.onDraw(canvas)
    }

    /**
     * 添加洞口
     * @param path 需要挖洞的位置
     * @param holeChild 如果需要挖洞的位置与子View重叠，是否将重叠部分也挖掉
     */
    fun addHole(path: Path,holeChild: Boolean, invalidate: Boolean = true) {
        holeList.add(HoleBean(path,holeChild))
        if (invalidate) {
            invalidate()
        }
    }

    /**
     * 移除指定的洞口
     */
    fun removeHole(path: Path, invalidate: Boolean = true) {
        var success = false
        val iterator = holeList.iterator()
        while (iterator.hasNext()) {
            if (iterator.next().path == path) {
                iterator.remove()
                success = true
            }
        }
        if (success && invalidate) {
            invalidate()
        }
    }

    /**
     * 清除所有洞口
     */
    fun clearHole(invalidate: Boolean = true) {
        if (holeList.isNotEmpty()) {
            holeList.clear()
            if (invalidate) {
                invalidate()
            }
        }
    }

    private data class HoleBean(
        val path: Path,
        val holeChild: Boolean
    )
}