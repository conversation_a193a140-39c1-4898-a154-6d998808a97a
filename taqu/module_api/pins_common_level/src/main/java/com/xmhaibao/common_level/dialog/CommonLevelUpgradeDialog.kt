package com.xmhaibao.common_level.dialog

import android.os.Bundle
import android.view.View
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import cn.taqu.lib.base.constants.PushConstants
import cn.taqu.lib.base.router.RouterLaunch
import com.xmhaibao.common_level.helper.CommonLevelSpecialInfoHelper
import com.xmhaibao.common_level.holder.CommonLevelDescribeHolder
import com.xmhaibao.common_level.model.CommonLevelDescribeBean
import com.xmhaibao.common_level.model.CommonLevelSpecialLocalBean
import com.xmhaibao.common_level.repository.CommonLevelRepository
import com.xmhaibao.pins.module.api.R
import com.xmhaibao.pins.module.api.databinding.CommonLevelUpgradeDialogBinding
import com.xmhaibao.imganim.lottie.XLottiePlayer
import hb.xadapter.XBaseAdapter
import hb.xrequest.RequestCallback
import hb.xstyle.xdialogfragment.XDialogFragment

/**
 * 类描述：新版财富等级升级弹窗
 *
 * 1等级图标 2等级徽章 3免费鲜花 4升级提醒 5聊天气泡 6资料页装扮 7昵称变色 8专属礼物
 *
 * <AUTHOR>
 * @date 2022-07-27
 */
class CommonLevelUpgradeDialog : XDialogFragment(), View.OnClickListener {
    private lateinit var mViewBinding: CommonLevelUpgradeDialogBinding
    private val KEY_LEVEL = "KEY_LEVEL"
    private var mLevel: String? = null
    private var mRelation: String? = null

    companion object {
        fun getInstance(level: String) = CommonLevelUpgradeDialog().apply {
            setConfig(ConfigStyle.CENTER.config().setCancelable(false)
                .setCanceledOnTouchOutside(false).setDimAmount(0.7f))
            val args = Bundle()
            args.putString(KEY_LEVEL, level)
            arguments = args
        }
    }

    override fun onCreateContentView(): Any {
        mViewBinding = CommonLevelUpgradeDialogBinding.inflate(layoutInflater)
        return mViewBinding.root
    }

    override fun initConfig(argumentBundle: Bundle) {
    }

    override fun initViews(rootView: View?) {
        mViewBinding.rvUnlockContent.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mViewBinding.ivScanMore.setOnClickListener(this)
        mViewBinding.ivClose.setOnClickListener(this)
    }

    override fun initData(savedInstanceState: Bundle?) {
        arguments?.getString(KEY_LEVEL)?.let { mLevel = it }
        mLevel?.let {
            mViewBinding.tvUpgradeLevel.text = "Lv.$it"
            CommonLevelRepository.getLevelDescribeInfo(it)
                .execute(object : RequestCallback<CommonLevelDescribeBean>() {
                    override fun onSuccess(
                        isCache: Boolean,
                        describeBean: CommonLevelDescribeBean?
                    ) {
                        describeBean?.run { handleResponseDescribeInfo(this) }
                    }
                })
        }
    }

    private fun handleResponseDescribeInfo(describeBean: CommonLevelDescribeBean) {
        mRelation = describeBean.relaction
        if (describeBean.lottieId.isNullOrEmpty() || "0" == describeBean.lottieId) {
            mViewBinding.animView.setImageResource(R.drawable.common_level_lottie_pre)
        } else {
            XLottiePlayer.playById(
                mViewBinding.animView,
                describeBean.lottieId,
                R.drawable.common_level_lottie_pre
            )
        }

        if (describeBean.noticeItem.isNullOrEmpty()) {
            mViewBinding.vSplitLineLeft.isVisible = false
            mViewBinding.tvUpgradeTip.isVisible = false
            mViewBinding.vSplitLineRight.isVisible = false
            return
        }

        val adapter = XBaseAdapter(context)
        adapter.register(
            CommonLevelSpecialLocalBean::class.java,
            CommonLevelDescribeHolder::class.java
        )
        adapter.setOnViewHolderCreatedListener {
            it as CommonLevelDescribeHolder
            it.bindLevel(describeBean.level)
        }
        describeBean.noticeItem?.mapNotNull { CommonLevelSpecialInfoHelper.getSpecialInfoByType(it) }
            ?.let {
                adapter.items = it
                mViewBinding.rvUnlockContent.adapter = adapter
            }
    }

    override fun onClick(v: View) {
        when (v) {
            mViewBinding.ivClose -> dismiss()
            mViewBinding.ivScanMore -> mRelation?.let {
                if (!it.contains(PushConstants.PUSH_TYPE_SOURCE)) {
                    "${it}&${PushConstants.PUSH_TYPE_SOURCE}=11"
                } else {
                    it
                }.run {
                    RouterLaunch.dealJumpData(context, this)
                }
            }
        }
    }
}