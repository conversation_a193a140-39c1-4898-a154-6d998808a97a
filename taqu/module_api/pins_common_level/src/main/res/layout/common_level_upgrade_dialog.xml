<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    tools:background="@color/gray">

    <TextView
        android:id="@+id/tvUpgradeTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="恭喜你升为"
        android:textColor="#FACA7B"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/tvUpgradeLevel"
        app:layout_constraintLeft_toRightOf="parent"
        app:layout_constraintRight_toLeftOf="@id/tvUpgradeLevel" />

    <TextView
        android:id="@+id/tvUpgradeLevel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:textColor="#FACA7B"
        android:textSize="28sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toRightOf="@id/tvUpgradeTitle"
        app:layout_constraintRight_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="LV.5" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/animView"
        android:layout_width="375dp"
        android:layout_height="375dp"
        android:layout_marginTop="19dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvUpgradeLevel"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        android:scaleType="centerInside"
        tools:background="@drawable/common_level_lottie_pre" />

    <View
        android:id="@+id/vSplitLineLeft"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginLeft="27dp"
        android:background="#75DCC387"
        app:layout_constraintBottom_toBottomOf="@id/tvUpgradeTip"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/tvUpgradeTip"
        app:layout_constraintTop_toTopOf="@id/tvUpgradeTip" />

    <TextView
        android:id="@+id/tvUpgradeTip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="19dp"
        android:layout_marginRight="8dp"
        android:text="以下特权成功解锁"
        android:textColor="#FBD28E"
        android:textSize="14sp"
        app:layout_constraintLeft_toRightOf="@id/vSplitLineLeft"
        app:layout_constraintRight_toLeftOf="@id/vSplitLineRight"
        app:layout_constraintTop_toBottomOf="@id/animView" />

    <View
        android:id="@+id/vSplitLineRight"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginRight="27dp"
        android:background="#75DCC387"
        app:layout_constraintBottom_toBottomOf="@id/tvUpgradeTip"
        app:layout_constraintLeft_toRightOf="@id/tvUpgradeTip"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvUpgradeTip" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvUnlockContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvUpgradeTip"
        tools:itemCount="1"
        tools:listitem="@layout/common_level_unlock_privilege_item" />

    <ImageView
        android:id="@+id/ivScanMore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="34dp"
        android:background="@drawable/common_level_upgrade_more"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rvUnlockContent" />

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginTop="12dp"
        android:background="@drawable/icon_float_web_close"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivScanMore" />

</androidx.constraintlayout.widget.ConstraintLayout>