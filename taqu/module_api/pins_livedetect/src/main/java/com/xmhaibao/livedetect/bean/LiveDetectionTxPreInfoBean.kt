package com.xmhaibao.livedetect.bean

import com.google.gson.annotations.SerializedName

/**
 * description:腾讯活体认证调用sdk前请求的必备信息体
 *
 * <AUTHOR>
 * @date 2021/05/31 15:06
 */
class LiveDetectionTxPreInfoBean : ILiveDetectionPreInfo {


    /**
     * verify_limit : false
     */

    /**
     * false-未超过验证限制次数，true-超过了验证限制次数
     */
    @SerializedName("verify_limit")
    val verifyLimit = true

    /**
     *订单号，合作方订单的唯一标识
     */
    @SerializedName("orderno")
    var orderNo: String? = ""

    /**
     *合作方后台服务器通过 ticket 计算出来的签名信息
     */
    var sign: String? = ""

//    /**
//     *刷脸 ID 号，由合作方向人脸识别后台拉取获得
//     */
//    var faceId: String? = ""

    /**
     *32位随机数由服务端提供
     */
    var nonce: String? = ""

    /**
     *标识唯一用户的userId
     */
    @SerializedName("userid")
    var userUuid: String? = ""


    override fun isVerifyLimit(): Bo<PERSON>an {
        return verifyLimit
    }


}