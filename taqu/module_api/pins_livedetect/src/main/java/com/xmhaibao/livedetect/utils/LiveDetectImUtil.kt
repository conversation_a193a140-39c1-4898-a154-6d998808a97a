package com.xmhaibao.livedetect.utils

import cn.taqu.lib.base.common.AppAccountBean
import com.xmhaibao.livedetect.bean.EventLiveDetectSuccess
import hb.common.data.AccountHelper
import hb.qim.base.QimClient
import hb.qim.base.listener.BaseEmListener
import hb.utils.Loger
import hb.utils.ToastUtils
import hb.xstatic.tools.XEventBus

/**
 * description:真人认证正常im
 *
 * <AUTHOR>
 * @date 2021/11/29 10:01
 */
class LiveDetectImUtil {


    companion object {

        const val TAG = "LiveDetectImUtil"

        private var isRegisterIm = false

        /**
         * 真人认证成功im
         */
        fun registerRealPersonalCertificationSuccessImEvent() {
            if (!AccountHelper.isUserLogined()) {
                return
            }
            if (AppAccountBean.get().isAvatarRealPhotoAuth) {
                return
            }
            if (QimClient.isInited()) {
                registerRealPersonalCertificationSuccessIm()
                return
            }
            QimClient.addInitedListener {
                registerRealPersonalCertificationSuccessIm()
            }
        }


        /**
         * 真人认证成功
         */
        private fun registerRealPersonalCertificationSuccessIm() {
            if (!AccountHelper.isUserLogined()) {
                return
            }
            if (AppAccountBean.get().isAvatarRealPhotoAuth) {
                return
            }
            if (isRegisterIm) {
                return
            }
            Loger.d(TAG, "真人认证im注册")
            isRegisterIm = true
            QimClient.register("real_person_certification_success",
                    object : BaseEmListener() {
                        override fun callback(vararg args: Any) {
                            try {
                                if (args.size >= 1) {
//                                    val success = args[0] as String
                                    AppAccountBean.get().isAvatarRealPhotoAuth = true
                                    AppAccountBean.get().toRealPersonCertification = "0"
                                    AppAccountBean.get().toRealPersonCertificationMsg =""
                                    XEventBus.post(EventLiveDetectSuccess().apply {
                                        source = EventLiveDetectSuccess.SOURCE_SUCCESS_IM
                                    })
                                }
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                        }
                    })
        }

        /**
         * 反注册真人认证成功
         */
        fun unregisterRealPersonalCertificationSuccessIm() {
            isRegisterIm = false
            QimClient.unregister("real_person_certification_success")
        }
    }
}