package com.xmhaibao.starshine.ui.center

import android.annotation.SuppressLint
import android.content.res.ColorStateList
import android.graphics.PorterDuff
import android.graphics.Typeface
import android.os.Bundle
import android.util.SparseArray
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import cn.taqu.lib.base.bean.UiState.Companion.doFailed
import cn.taqu.lib.base.bean.UiState.Companion.doSuccess
import cn.taqu.lib.base.bean.UiState.Companion.getSuccessOrNull
import cn.taqu.lib.base.router.RouterLaunch
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayout
import com.xmhaibao.pins.module.api.R
import com.xmhaibao.pins.module.api.databinding.StarShineCenterFragmentBinding
import com.xmhaibao.pins.module.api.databinding.StarShineDanTimeLimitedBinding
import com.xmhaibao.starshine.bean.StarShineDanItemInfoBean
import com.xmhaibao.starshine.bean.StarShineTaskItemInfoBean
import com.xmhaibao.starshine.bean.StarShineTimeLimitedBean
import com.xmhaibao.starshine.bean.TIME_LIMITED_PROGRESS
import com.xmhaibao.starshine.bean.TIME_LIMITED_UNLOCK
import com.xmhaibao.starshine.bean.TIME_LIMITED_UPGRADED
import com.xmhaibao.starshine.trackter.StarShineTracker
import com.xmhaibao.starshine.ui.StarShineCenterContainerFragment
import com.xmhaibao.starshine.ui.center.viewholder.StarShineTaskDescViewHolder
import com.xmhaibao.starshine.ui.center.viewholder.StarShineTaskItemViewHolder
import com.xmhaibao.starshine.ui.center.viewmodel.StarShineCenterSharedViewModel
import com.xmhaibao.starshine.ui.center.viewmodel.StarShineCenterTaskListViewModel
import com.xmhaibao.starshine.ui.center.viewmodel.StarShineCenterViewModel
import com.xmhaibao.starshine.util.SimpleOnTabSelectedListener
import com.xmhaibao.starshine.util.highlightText
import com.xmhaibao.starshine.util.parentViewModels
import dp
import hb.common.xstatic.fragment.BaseMVVMFragment
import hb.drawable.shape.view.HbView
import hb.kotlin_extension.gone
import hb.kotlin_extension.visible
import hb.utils.BarUtils
import hb.utils.ColorUtils
import hb.utils.Loger
import hb.utils.SpanUtils
import hb.xadapter.XBaseAdapter
import sp

/**
 *
 * 星耀中心管理页面-用户评级
 * 收礼用户成长体系
 *
 * <AUTHOR>
 * @date 2024-11-12
 */
class StarShineCenterFragment : BaseMVVMFragment<StarShineCenterViewModel>() {
    private lateinit var binding: StarShineCenterFragmentBinding
    private var headBinding: StarShineDanTimeLimitedBinding? = null

    companion object {

        private const val IS_USER_SELF = "isUserSelf"
        fun newInstance(
            targetUUID: String?,
            isUserSelf: Boolean,
            source: String? = null,
            scene: String?,
            sceneUUID: String?
        ): StarShineCenterFragment {
            val fragment = StarShineCenterFragment().apply {
                arguments = bundleOf(
                    StarShineCenterContainerFragment.ACCOUNT_UUID_KEY to targetUUID,
                    StarShineCenterContainerFragment.SCENE_KEY to scene,
                    StarShineCenterContainerFragment.SCENE_UUID_KEY to sceneUUID,
                    StarShineCenterContainerFragment.SOURCE_KEY to source,
                    IS_USER_SELF to isUserSelf,
                )
            }
            return fragment
        }
    }

    private var targetUUID: String = ""
    private var source: String? = null
    private var scene: String? = null
    private var sceneUUID: String? = null

    /**
     * 是否为主态，默认为客态
     */
    private var isUserSelf: Boolean = false

    private val sharedViewModel by parentViewModels<StarShineCenterSharedViewModel>()
    private val viewModel by viewModels<StarShineCenterViewModel>()
    private val listViewModel by viewModels<StarShineCenterTaskListViewModel>()


    private val tracker by lazy { StarShineTracker() }

    /**
     *  段位任务 列表
     */
    private val adapter by lazy {
        XBaseAdapter(this.context).apply {
            register(
                StarShineTaskItemInfoBean::class.java, StarShineTaskItemViewHolder::class.java
            )
            register(
                String::class.java, StarShineTaskDescViewHolder::class.java
            )
        }


    }

    override fun onCreateContentView(): Any {
        binding = StarShineCenterFragmentBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initViews() {
        super.initViews()
        arguments?.apply {
            targetUUID = getString(StarShineCenterContainerFragment.ACCOUNT_UUID_KEY, "")
            source = getString(StarShineCenterContainerFragment.SOURCE_KEY, null)
            scene = getString(StarShineCenterContainerFragment.SCENE_KEY, null)
            sceneUUID = getString(StarShineCenterContainerFragment.SCENE_UUID_KEY, null)
            isUserSelf = getBoolean(IS_USER_SELF, false)
        }

        binding.clRoot.updateLayoutParams<MarginLayoutParams> {
            val statusBarHeight = if (sharedViewModel.inHalfScreen.value) {
                0
            } else {
                BarUtils.getStatusBarHeight()
            }
            topMargin = statusBarHeight + 45.dp
        }
        binding.ivHostCenterBg.setActualImageResource(R.drawable.star_shine_center_bg)
        adapter.setOnViewHolderCreatedListener { viewHolder ->
            if (viewHolder is StarShineTaskItemViewHolder) {
                // 判断是否为主态
                viewHolder.isUserSelf = isUserSelf

            }
        }
        binding.rvTaskList.setAdapter(adapter)
        binding.rvTaskList.setHasFixedSize(false)

    }


    /**
     * 限时福利 未解锁进度
     */
    private fun timeLimitedShowProgress(data: StarShineTimeLimitedBean) {
        headBinding?.let {
            // time
            it.tvTime.text = data.timeRange
            // progress
            it.bdvCircleBg.visible()
            it.circleProgress.visible()
            it.circleProgress.progressStyle()
            val progress: Int = if (data.current <= 0 || data.total <= 0) {
                0
            } else {
                val percent = data.current * 1.0f / data.total * 1.0f
                val value = if (percent >= 1.0f) {
                    100
                } else {
                    percent * 100
                }

                value.toInt()
            }
            it.circleProgress.setProgressValue(progress)
            it.tvProgressText.visible()
            it.tvProgressText.text = data.currentStr
            it.tvProgressDesc.visible()
            it.tvProgressDesc.text = "已获得"
            it.tvTotal.visible()
            it.tvTotal.text = "总额度${data.totalStr}"
            it.ivUpgraded.gone()
            // ruleRelation
            it.ivTips.setOnClickListener {
                RouterLaunch.dealJumpData(requireContext(), data.ruleRelation)
            }
            // title detail desc
            data.title?.let { info ->
                it.tvTitle.highlightText(info.text ?: "", info.highlight, info.color)
            }

            data.detail?.let { info ->
                it.tvDetail.highlightText(info.text ?: "", info.highlight, info.color)
            }
            if (data.desc == null) {
                it.tvDesc.gone()
            } else {
                it.tvDesc.visible()
            }
            data.desc?.let { info ->
                it.tvDesc.highlightText(info.text ?: "", info.highlight, info.color)
            }

        }
    }

    /**
     * 限时福利 未解锁进度
     */
    private fun timeLimitedShowUnlock(data: StarShineTimeLimitedBean) {
        headBinding?.let {
            // time
            it.tvTime.text = data.timeRange
            // progress
            it.bdvCircleBg.visible()
            it.circleProgress.visible()
            it.circleProgress.unlockStyle()
            it.tvProgressText.visible()
            it.tvProgressText.text = "未解锁"
            it.tvProgressDesc.visible()
            it.tvProgressDesc.text = "当前进度"
            it.tvTotal.gone()
            it.ivUpgraded.gone()
            // ruleRelation
            it.ivTips.setOnClickListener {
                RouterLaunch.dealJumpData(requireContext(), data.ruleRelation)
            }
            // title detail desc
            data.title?.let { info ->
                it.tvTitle.highlightText(info.text ?: "", info.highlight, info.color)
            }

            data.detail?.let { info ->
                it.tvDetail.highlightText(info.text ?: "", info.highlight, info.color)
            }
            if (data.desc == null) {
                it.tvDesc.gone()
            } else {
                it.tvDesc.visible()
            }
            data.desc?.let { info ->
                it.tvDesc.highlightText(info.text ?: "", info.highlight, info.color)
            }

        }
    }

    /**
     * 限时福利 已升段
     */
    private fun timeLimitedShowUpgraded(data: StarShineTimeLimitedBean) {
        headBinding?.let {
            it.tvTime.text = data.timeRange
            it.bdvCircleBg.gone()
            it.circleProgress.gone()
            it.tvProgressText.gone()
            it.tvProgressDesc.gone()
            it.tvTotal.gone()
            it.ivUpgraded.visible()
            // ruleRelation
            it.ivTips.setOnClickListener {
                RouterLaunch.dealJumpData(requireContext(), data.ruleRelation)
            }
            // title detail desc
            data.title?.let { info ->
                it.tvTitle.highlightText(info.text ?: "", info.highlight, info.color)
            }

            data.detail?.let { info ->
                it.tvDetail.highlightText(info.text ?: "", info.highlight, info.color)
            }
            if (data.desc == null) {
                it.tvDesc.gone()
            } else {
                it.tvDesc.visible()
            }
            data.desc?.let { info ->
                it.tvDesc.highlightText(info.text ?: "", info.highlight, info.color)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // tab 切换的时候，会触发 onResume，但是不会触发 onHiddenChanged
        if (!isHidden) {
            tracker.startShineCenterExpose(source)
        }
    }

    private fun enableHeaderScroll() {
        if (Loger.isDebug()) {
            Loger.d(TAG, "enableHeaderScroll")
        }
        val layoutParams = binding.llHeader.layoutParams
        if (layoutParams is AppBarLayout.LayoutParams) {
            val appbarLayoutParams: AppBarLayout.LayoutParams = layoutParams
            appbarLayoutParams.scrollFlags =
                AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL or AppBarLayout.LayoutParams.SCROLL_FLAG_ENTER_ALWAYS
            binding.llHeader.layoutParams = appbarLayoutParams
            if (Loger.isDebug()) {
                Loger.d(TAG, "SCROLL_FLAG_SCROLL")
            }
        }
    }

    /**
     * 初始化 段位列表相关信息 view pager
     */
    private fun initViewPager(danItemList: List<StarShineDanItemInfoBean>) {
        val pagerAdapter = InnerPagerAdapter(childFragmentManager, danItemList)
        binding.viewPager.adapter = pagerAdapter
        binding.stageTabLayout.setupWithViewPager(binding.viewPager)
        var currentSelect = 0
        for (i in 0..binding.stageTabLayout.tabCount) {
            binding.stageTabLayout.getTabAt(i)?.let {
                val danItem = danItemList.getOrNull(i)
                // 有下发当前位置，就进行滚动
                if (danItem?.isCurrentLevel == true) {
                    currentSelect = i
                }
                it.customView = createTabView(danItem, binding.stageTabLayout, i)
                it.view.isLongClickable = false
            }
        }
        //非第一个的时候进行滚动
        if (currentSelect > 0) {
            val preSelectedView = binding.stageTabLayout.getTabAt(0)?.customView
            preSelectedView?.let {
                changeTabTextSize(it, false)
            }
            val currentSelectView = binding.stageTabLayout.getTabAt(currentSelect)?.customView
            currentSelectView?.let {
                changeTabTextSize(it, true)
            }
            binding.viewPager.setCurrentItem(currentSelect, false)
        }
        // 修改监听在 滚动之后，避免第一次滚动的时候上报 埋点
        binding.stageTabLayout.addOnTabSelectedListener(object : SimpleOnTabSelectedListener() {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                val holder = tab?.customView?.tag as? TabViewHolder
                val text = holder?.tvRankTabTitle?.text
                tracker.startShineCenterDanTabClick(text?.toString() ?: "")
                changeTabTextSize(tab?.customView, true)
                val dan = danItemList.getOrNull(tab?.position ?: -1)?.dan
                dan?.let {
                    viewModel.updateSelectDan(it)
                }

            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                changeTabTextSize(tab?.customView, false)
            }
        })


    }

    private fun createTabView(
        danItem: StarShineDanItemInfoBean?, tabLayout: TabLayout, position: Int
    ): View? {

        val newContentView: View? = LayoutInflater.from(tabLayout.context).inflate(
            R.layout.star_shine_ranks_tab_item, tabLayout, false
        )
        val holder = TabViewHolder(newContentView)

        newContentView?.tag = holder
        // 主态才展示 当前等级标签
        if (danItem?.isCurrentLevel == true && isUserSelf) {
            holder.tvCurrentLevel?.visible()
        } else {
            holder.tvCurrentLevel?.gone()
        }
        holder.tvRankTabTitle?.text = danItem?.danName
        changeTabTextSize(newContentView, tabLayout.selectedTabPosition == position)
        return newContentView
    }

    /**
     * 改变Tab中的TextSize
     */
    private fun changeTabTextSize(customView: View?, isSelected: Boolean) {
        if (customView != null) {
            val holder = customView.tag as? TabViewHolder
            if (holder != null) {

                if (isSelected) {
                    holder.viewUnderline?.visibility = View.VISIBLE
                    holder.tvRankTabTitle?.setTextColor(
                        ContextCompat.getColor(
                            requireContext(), cn.taqu.lib.base.R.color.TH_Gray990
                        )
                    )
                    holder.tvRankTabTitle?.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD))
                    holder.tvRankTabTitle?.textSize = 18f
                } else {
                    holder.viewUnderline?.visibility = View.GONE
                    holder.tvRankTabTitle?.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL))
                    holder.tvRankTabTitle?.textSize = 14f
                    holder.tvRankTabTitle?.setTextColor(
                        ContextCompat.getColor(
                            requireContext(), cn.taqu.lib.base.R.color.TH_Gray600
                        )
                    )

                }
            }
        }
    }


    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        sharedViewModel.getUserInfo(targetUUID)
        viewModel.getDanInfo(targetUUID)
        collectFlow()
    }

    /**
     * 动态更新 secondaryProgress 颜色
     */
    private fun updateProgressbarColor(colorString: String) {
        val progressBar = binding.pbStarShine
        val color = ColorUtils.getColor(colorString)
        progressBar.secondaryProgressTintList = ColorStateList.valueOf(color)
        progressBar.secondaryProgressTintMode = PorterDuff.Mode.SRC_IN
    }

    private fun collectFlow() {
        // 用户相关信息，段位等级，历史最高等
        collectUserInfo()
        // 用户段位列表相关信息
        collectDanList()
        // 当前段位任务列表
        collectDanTaskList()
        // 当前段位的限时福利
        collectTimeLimited()
    }

    /**
     * 用户相关信息，段位等级，历史最高等
     */
    private fun collectUserInfo() {
        lifecycleScope.launchWhenResumed {
            sharedViewModel.userInfoFlow.collect { state ->
                state.doSuccess { data ->
                    // 个人基础信息
                    data.info?.bg?.let { binding.bdvBackground.setImageFromUrl(it) }
                    binding.avatar.setImageFromUrl(data.info?.avatar)
                    // 等级 icon level
                    binding.bdvLevelIc.setImageFromUrl(data.info?.currentLevel?.danLevelIcon)
                    // 段位图标
                    binding.bdvIc.setImageFromUrl(data.info?.currentLevel?.danIcon)
                    binding.tvNickName.text = data.info?.nickname
                    // 本月最高
                    binding.bdvMonthMaxIcon.setImageFromUrl(data.info?.monthMaxLevel?.danIcon)
                    val monthMaxName = data.info?.monthMaxLevel?.danName
                    val tvMonthMaxLevelDesc = if (monthMaxName.isNullOrEmpty()) {
                        ""
                    } else {
                        val monthMaxLevel = data.info?.monthMaxLevel?.level ?: 0
                        monthMaxName + "LV" + monthMaxLevel
                    }

                    binding.tvMonthMaxLevel.text = tvMonthMaxLevelDesc
                    // 历史最高
                    binding.bdvHistoryIcon.setImageFromUrl(data.info?.historyMaxLevel?.danIcon)
                    val historyMaxName = data.info?.historyMaxLevel?.danName
                    val historyMaxLevelDesc = if (monthMaxName.isNullOrEmpty()) {
                        ""
                    } else {
                        val historyMaxLevel = data.info?.historyMaxLevel?.level ?: 0
                        historyMaxName + "LV" + historyMaxLevel
                    }

                    binding.tvHistoryMaxLevel.text = historyMaxLevelDesc
                    // 星耀值 进度 百分比
                    val levelScore = data.info?.currentLevel?.levelScore ?: 0
                    val currentScore = data.info?.currentLevel?.currentScore ?: 0
                    val scoreProgress: Int = if (levelScore <= 0 || currentScore == 0) {
                        0
                    } else {
                        ((currentScore * 1.0f / levelScore * 1.0f) * 100).toInt()

                    }
                    binding.pbStarShine.progress = scoreProgress
                    val currentLevel = data.info?.currentLevel?.level ?: 0
                    binding.tvLevelStart.text = "LV.$currentLevel"
                    if (data.info?.nextLevel == null) {
                        binding.tvLevelEnd.gone()
                    } else {
                        binding.tvLevelEnd.visible()
                        val currentLevelDan = data.info?.currentLevel?.dan ?: -1
                        val nextLevelDan = data.info?.nextLevel?.dan ?: -2
                        val nextLevelDesc = if (currentLevelDan != nextLevelDan) {
                            (data.info?.nextLevel?.danName
                                ?: "") + "LV." + data.info?.nextLevel?.level
                        } else {
                            "LV." + data.info?.nextLevel?.level
                        }
                        binding.tvLevelEnd.text = nextLevelDesc
                    }
                    // 星耀值进度数值
                    val levelProgress = "${currentScore}/${levelScore}"
                    val spanText = SpanUtils().append("星耀值").setFontSize(11f.sp.toInt())
                        .append(levelProgress).setFontSize(10f.sp.toInt()).create()
                    binding.tvLevelProgress.text = spanText
                    // 进度条颜色
                    data.info?.progressColor?.let { updateProgressbarColor(it) }
                    listViewModel.getTaskInfo(
                        data.info?.currentLevel?.dan?.toString() ?: "", targetUUID
                    )

                }

            }
        }
    }

    /**
     * 用户段位列表相关信息
     * 段位列表，段位奖励
     */
    private fun collectDanList() {
        lifecycleScope.launchWhenResumed {
            viewModel.userDanInfoFlow.collect { state ->
                state.doSuccess { data ->
                    showContentLayout()
                    val danItemList = data.list
                    if (!danItemList.isNullOrEmpty()) {
                        enableHeaderScroll()
                        initViewPager(danItemList)
                    }
                    data.current?.dan?.let {
                        viewModel.updateSelectDan(it)
                    }

                }
                state.doFailed {
                    showEmptyView(
                        cn.taqu.lib.base.R.drawable.base_new_empty_new_ic, "加载失败", "重试"
                    ) {
                        if (sharedViewModel.userInfoFlow.value.getSuccessOrNull() == null) {
                            sharedViewModel.getUserInfo(targetUUID)
                        }
                        viewModel.getDanInfo(targetUUID)

                    }
                }
            }
        }
        if (isUserSelf) {
            lifecycleScope.launchWhenResumed {
                viewModel.currentSelectDan.collect {
                    if (it != null) {
                        viewModel.getDanTimeLimited(it.toString(), scene, sceneUUID)
                    } else {
                        Loger.d(TAG, "collect currentSelectDan init")
                    }
                }
            }
        }

    }

    /**
     * 当前段位任务列表
     */
    private fun collectDanTaskList() {
        lifecycleScope.launchWhenResumed {
            listViewModel.danTaskInfoFlow.collect { state ->
                state.doSuccess { data ->
                    val listCompat: ArrayList<Any> = ArrayList()
                    listCompat.add(data.title ?: "")
                    data.list?.let {
                        listCompat.addAll(it)
                    }
                    adapter.submitList(listCompat)

                }
            }
        }
    }

    /**
     * 当前段位的限时福利
     */
    @SuppressLint("NotifyDataSetChanged")
    private fun collectTimeLimited() {
        lifecycleScope.launchWhenResumed {
            viewModel.userDanTimeLimitedRewards.collect { state ->
                state.doSuccess { data ->
                    if (data != null) {
                        if (adapter.headersCount <= 0) {
                            headBinding = StarShineDanTimeLimitedBinding.inflate(layoutInflater)
                            headBinding?.let {
                                adapter.addHeaderView(it.root)
                                adapter.notifyDataSetChanged()
                            }
                        }
                        when (data.status) {
                            TIME_LIMITED_UNLOCK -> {
                                timeLimitedShowUnlock(data)
                            }

                            TIME_LIMITED_PROGRESS -> {
                                timeLimitedShowProgress(data)
                            }

                            TIME_LIMITED_UPGRADED -> {
                                timeLimitedShowUpgraded(data)
                            }
                        }

                    } else {
                        if (adapter.headersCount > 0) {
                            headBinding?.let {
                                adapter.removeHeaderView(it.root)
                                adapter.notifyDataSetChanged()
                            }
                        }

                    }

                }
                state.doFailed {
                    if (adapter.headersCount > 0) {
                        headBinding?.let {
                            adapter.removeHeaderView(it.root)
                            adapter.notifyDataSetChanged()
                        }
                    }
                }
            }
        }
    }

    private inner class TabViewHolder(contentView: View?) {
        val tvRankTabTitle by lazy { contentView?.findViewById<TextView>(R.id.tvRankTabTitle) }
        val viewUnderline by lazy { contentView?.findViewById<HbView>(R.id.viewUnderline) }
        val tvCurrentLevel by lazy { contentView?.findViewById<TextView>(R.id.tvCurrentLevel) }

    }

    /**
     * 段位 对应的 FragmentPagerAdapter
     */
    inner class InnerPagerAdapter(
        fragmentManager: FragmentManager,
        private val danItemList: List<StarShineDanItemInfoBean>,
        behavior: Int = BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT,
    ) : FragmentPagerAdapter(fragmentManager, behavior) {
        private val fragmentCache = SparseArray<Fragment>()


        override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
            super.destroyItem(container, position, `object`)
            fragmentCache.remove(position)
        }

        override fun getItem(position: Int): Fragment {
            val fragment: Fragment? = fragmentCache.get(position)
            if (fragment != null) {
                return fragment
            } else {
                // create new one
                val danItemInfoBean = danItemList.getOrNull(position)
                val fragmentNew: Fragment = StarShineCenterTaskListFragment.newInstance(
                    danItemInfoBean, targetUUID, isUserSelf
                )
                fragmentCache.set(position, fragmentNew)
                return fragmentNew
            }
        }

        override fun getCount(): Int = danItemList.size

        override fun getPageTitle(position: Int): CharSequence {
            return danItemList.getOrNull(position)?.danName ?: ""
        }
    }
}