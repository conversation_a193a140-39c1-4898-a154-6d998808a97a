<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout"
    tools:background="@color/black_alpha_50"
    android:layout_width="103dp"
    android:layout_height="57dp">

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/imgBg"
        app:placeholderImage="@color/transparent"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/imgAvatarAccount"
        app:roundingBorderColor="@color/white"
        app:roundingBorderWidth="1dp"
        app:roundingBorderPadding="1dp"
        android:layout_marginEnd="5dp"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/imgAvatarHost"
        android:layout_width="29dp"
        android:layout_height="29dp" />

    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/imgAvatarHost"
        app:roundingBorderColor="@color/white"
        app:roundingBorderWidth="1dp"
        app:roundingBorderPadding="1dp"
        app:layout_constraintStart_toEndOf="@id/imgAvatarAccount"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="29dp"
        android:layout_height="29dp" />

    <!--   中间爱心图片 valentine_day_love_ic-->
    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivCenterLove"
        android:layout_width="18dp"
        android:layout_height="18dp"
        app:placeholderImage="@color/transparent"
        app:layout_constraintBottom_toBottomOf="@id/imgAvatarAccount"
        app:layout_constraintEnd_toEndOf="@id/imgAvatarHost"
        app:layout_constraintStart_toStartOf="@id/imgAvatarAccount"
        app:layout_constraintTop_toTopOf="@id/imgAvatarAccount" />

    <hb.xemoji.view.XEmojiTextView
        android:id="@+id/tvContent"
        style="@style/Text.N3.White"
        android:singleLine="true"
        android:ellipsize="end"
        android:gravity="center"
        tools:text="用户名用户名用户名用户名"
        android:layout_marginTop="1dp"
        android:textColor="#B338CA"
        android:layout_marginLeft="2dp"
        android:layout_marginRight="2dp"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/imgAvatarAccount"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

</merge>