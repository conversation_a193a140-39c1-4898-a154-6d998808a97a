package com.xmhaibao.noble.api.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;

import com.xmhaibao.noble.api.activity.NobleListActivity;
import com.xmhaibao.noble.api.contants.LiveNoblePrivType;
import com.xmhaibao.noble.api.contants.NoblePinsConstants;
import com.xmhaibao.noble.api.helper.NobleHelper;
import com.xmhaibao.noble.api.model.MyAllNobleBean;
import com.xmhaibao.noble.api.model.NobleListBean;
import com.xmhaibao.noble.api.model.NobleStyleListBean;
import com.xmhaibao.noble.api.viewmodel.NobleListViewModel;
import com.xmhaibao.pins.module.api.R;
import com.xmhaibao.pins.module.api.databinding.NobleMyPrivilegeLayBinding;

import java.util.List;

import androidx.lifecycle.Observer;
import cn.taqu.lib.base.router.ARouterManager;
import hb.common.data.AccountHelper;
import hb.common.xstatic.fragment.BaseMVVMFragment;
import hb.utils.CollectionUtils;
import hb.utils.StringUtils;
import hb.utils.ToastUtils;

/**
 * 贵族列表 我的特权
 *
 * <AUTHOR>
 * @date 2021-05-19
 */
public class NobleMyPrivilegeFragment extends BaseMVVMFragment<NobleListViewModel> implements View.OnClickListener {

    /***
     * 我的贵族信息
     */
    private MyAllNobleBean myAllNobleBean;
    private NobleMyPrivilegeLayBinding mBinding;
    private NobleListViewModel mViewModel;

    public static NobleMyPrivilegeFragment newInstance(MyAllNobleBean nobleBean) {
        Bundle args = new Bundle();
        args.putParcelable(NoblePinsConstants.NOBLE_INFO, nobleBean);
        NobleMyPrivilegeFragment fragment = new NobleMyPrivilegeFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initData(Bundle savedInstanceState) {
        super.initData(savedInstanceState);
        if (getArguments() != null) {
            myAllNobleBean = getArguments().getParcelable(NoblePinsConstants.NOBLE_INFO);
        }
        if (myAllNobleBean == null || mBinding == null) {
            return;
        }
        mViewModel = getViewModel(getActivity(), NobleListViewModel.class);
        mBinding.ivAvatar.setImageFromUrl(AccountHelper.getAccountAvatar());
        mBinding.tvSurplusDay.setText(String.format("剩余%s天", myAllNobleBean.getLeftDay()));
        mBinding.tvExpireDay.setText(String.format("%s过期", myAllNobleBean.getExpireTime()));
        mBinding.ivNobleLevel.setImageFromUrl(myAllNobleBean.getIconMedalNormal());
        mBinding.tvNobleLevel.setText(myAllNobleBean.getNobleTitle());
        mBinding.llMyTqBean.setVisibility(View.GONE);
        mBinding.tvNobleHide.setVisibility(NobleHelper.getInstance().isSupportNoblePriv(LiveNoblePrivType.PRIV_TY_HIDDEN, StringUtils.stringToInt(myAllNobleBean.getNobleCode())) ? View.VISIBLE : View.GONE);
        mBinding.tvBuy.setOnClickListener(this);
        mBinding.tvNobleHide.setOnClickListener(this);
        initObserve();
    }

    private void initObserve() {
        mViewModel.getNobleListLiveData().observe(this, new Observer<NobleListBean>() {
            @Override
            public void onChanged(NobleListBean listBean) {
                if (listBean == null || mBinding == null) {
                    return;
                }
                mBinding.tvBuy.setVisibility(CollectionUtils.isEmpty(listBean.getStyleList()) ? View.GONE : View.VISIBLE);
            }
        });
    }

    @Override
    protected Object onCreateContentView() {
        mBinding = NobleMyPrivilegeLayBinding.inflate(LayoutInflater.from(getContext()));
        return mBinding.getRoot();
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tvBuy) {
            NobleListBean nobleListBean = mViewModel.getNobleListInfo();
            if (nobleListBean == null) {
                return;
            }
            if (nobleListBean.isDisable()) {
                ToastUtils.show("暂时无法开通比当前等级更低的贵族");
                return;
            }
            List<NobleStyleListBean> styleList = nobleListBean.getStyleList();
            if (CollectionUtils.isNotEmpty(styleList)) {
                NobleListActivity activity = (NobleListActivity) getActivity();
                if (activity == null || activity.isFinishing()) {
                    return;
                }
                activity.doNoblePurchase(styleList.get(0).getMonth(), styleList.get(0).getCurrent(),
                        nobleListBean.getNobleCode(), nobleListBean.applyText(), nobleListBean.getNobleTitle(), nobleListBean.isMoneyPay());
            }
        } else if (id == R.id.tvNobleHide) {
            ARouterManager.nobleService().launchNobleInvisibleActivity();
        }
    }
}
