<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="138dp"
    android:layout_gravity="center_horizontal"
    android:orientation="vertical">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="22dp"
        android:layout_gravity="bottom"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:background="@drawable/noble_card_shadow_ic" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="122dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:background="@drawable/noble_my_privilege_card_bg">

        <ImageView
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginStart="18dp"
            android:layout_marginTop="32dp"
            android:background="@drawable/noble_my_avatar_bg" />

        <hb.ximage.fresco.AvatarDraweeView
            android:id="@+id/ivAvatar"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginStart="23dp"
            android:layout_marginTop="37dp"
            android:layout_marginEnd="10dp" />

        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/ivNobleLevel"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="18dp"
            app:placeholderImage="@color/transparent" />

        <TextView
            android:id="@+id/tvNobleLevel"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="87dp"
            android:layout_marginEnd="30dp"
            android:background="@drawable/noble_card_label_ic"
            android:gravity="center"
            android:textColor="#543600"
            android:textSize="@dimen/t6_2"
            tools:text="骑士" />

        <LinearLayout
            android:layout_marginTop="24dp"
            android:layout_toEndOf="@id/ivAvatar"
            android:layout_centerVertical="true"
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/tvSurplusDay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#543600"
                    android:textSize="@dimen/t6"
                    tools:text="剩余9999天" />

                <TextView
                    android:id="@+id/tvBuy"
                    android:layout_width="42dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/noble_btn_xufei_ic"
                    android:gravity="center"
                    android:text="续费"
                    android:textColor="#FFE2AF"
                    android:textSize="@dimen/t6_2" />
            </LinearLayout>


            <TextView
                android:id="@+id/tvExpireDay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/tvSurplusDay"
                android:layout_marginTop="2dp"
                android:layout_toEndOf="@id/ivAvatar"
                android:textColor="#644000"
                android:textSize="@dimen/t6_2"
                tools:text="2021-9-21到期" />

            <LinearLayout
                android:id="@+id/llMyTqBean"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/tvExpireDay"
                android:layout_marginTop="2dp"
                android:layout_toEndOf="@id/ivAvatar"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:background="@drawable/noble_left_tqbean_ic" />

                <TextView
                    android:id="@+id/tvTqBeanNum"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-1dp"
                    android:textColor="#644000"
                    android:textSize="@dimen/t6_2"
                    tools:text="1000000" />

                <TextView
                    android:id="@+id/tvTqBeanExpire"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-2dp"
                    android:textColor="#644000"
                    android:textSize="@dimen/t8"
                    tools:text="（2021.12.8到期）" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvNobleHide"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/tvExpireDay"
                android:layout_marginTop="2dp"
                android:layout_toEndOf="@id/ivAvatar"
                android:visibility="gone"
                tools:visibility="visible"
                android:drawableEnd="@drawable/noble_icon_arrow_right"
                android:text="贵族隐身设置"
                android:textColor="#644000"
                android:textSize="@dimen/t6_2" />
        </LinearLayout>


    </RelativeLayout>

</FrameLayout>