<?xml version="1.0" encoding="utf-8"?>
<hb.drawable.shape.view.HbConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="68dp"
    android:layout_height="75dp"
    app:corner="9dp"
    app:stroke_color="#FFEEF0"
    app:stroke_width="1dp">

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivPrivilege"
        android:layout_width="30dp"
        android:layout_height="30dp"
        app:layout_constraintBottom_toTopOf="@id/tvPrivilegeName"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:placeholderImage="@color/transparent"
        tools:background="#2f00" />

    <TextView
        android:id="@+id/tvPrivilegeName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="#323232"
        android:textSize="10sp"
        app:layout_constraintBottom_toTopOf="@id/tvPrivilegeDesc"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivPrivilege"
        tools:text="免费消息" />

    <TextView
        android:id="@+id/tvPrivilegeDesc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/TH_Black40"
        android:textSize="9sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvPrivilegeName"
        tools:text="每月100句" />

</hb.drawable.shape.view.HbConstraintLayout>