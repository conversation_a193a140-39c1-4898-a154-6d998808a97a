package com.xmhaibao.report.holder

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.xmhaibao.pins.module.api.databinding.ReportPostListItemBinding
import com.xmhaibao.report.bean.ReportPostItemBean
import hb.utils.CollectionUtils
import hb.utils.StringUtils
import hb.utils.TimeUtils
import hb.xadapter.XBaseViewHolder

/**
 * 社区 热门话题 viewHolder
 *
 * <AUTHOR>
 * @date 2022-03-29
 */
class ReportPostViewHolder(val binding: ReportPostListItemBinding) :
    XBaseViewHolder<ReportPostItemBean>(binding.root) {

    constructor(parent: ViewGroup) :
            this(
                ReportPostListItemBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

    override fun onBindView(item: ReportPostItemBean?) {
        item?.let {
            setPostTitle(it)
            setPostTime(it)
            if (it.mediaInfo != null) {
                setPostMediaInfo(it)
            } else {
                setPostImageShow(it)
            }
            setPostSelected(it)

            //判断为视频帖或图片贴，并且没有发帖内容，内容补充
            if (binding.ivPost.visibility == View.VISIBLE && it.content.isNullOrBlank()) {
                binding.tvContent.text = if (it.mediaInfo != null) "该用户仅发表影片" else "该用户仅发表图片"
            }
            binding.ivVideoLabel.visibility = if (it.mediaInfo != null) View.VISIBLE else View.GONE
        }
    }

    /**
     * 设置帖子发布内容
     */
    private fun setPostTitle(item: ReportPostItemBean) {
        binding.tvContent.text = item.content
    }

    /**
     * 设置帖子发布时间
     */
    private fun setPostTime(item: ReportPostItemBean) {
        binding.tvPostTime.text = TimeUtils.millis2String(StringUtils.stringToLong(item.createTime) * 1000, "yyyy-MM-dd HH:mm")
    }

    /**
     * 设置帖子视频
     */
    private fun setPostMediaInfo(item: ReportPostItemBean) {
        if (item.mediaInfo == null) {
            binding.ivPost.visibility = View.GONE
            return
        }
        item.mediaInfo?.run {
            binding.ivPost.visibility = View.VISIBLE
            binding.ivPost.setImageFromUrl(this.imageUrl)
        }
    }

    /**
     * 设置帖子图片
     */
    private fun setPostImageShow(item: ReportPostItemBean) {
        if (CollectionUtils.isEmpty(item.imgList)) {
            binding.ivPost.visibility = View.GONE
            return
        }
        binding.ivPost.visibility = View.VISIBLE
        binding.ivPost.setImageFromUrl(item.imgList[0].picUrl)
    }

    /**
     * 设置选中
     */
    private fun setPostSelected(item: ReportPostItemBean) {
        binding.cbReportEvidence.isChecked = item.isSelected
    }
}

