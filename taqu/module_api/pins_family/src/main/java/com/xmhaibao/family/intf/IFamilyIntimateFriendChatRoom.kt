package com.xmhaibao.family.intf

import android.view.ViewStub
import androidx.lifecycle.LifecycleOwner
import com.xmhaibao.family.event.EventFamilyIntimateFriendChatRoomAgree

/**
 * 家族密友 聊天室相关接口
 *
 * <AUTHOR>
 * @date 2025/4/7
 */
interface IFamilyIntimateFriendChatRoom {
    /**
     * 绑定消息提示ViewStub
     */
    fun bindMessageTipsStub(messageTipsStub: ViewStub): IFamilyIntimateFriendChatRoom

    /**
     * 绑定新人提示ViewStub
     */
    fun bindNewerTipsStub(newerTipsStub: ViewStub): IFamilyIntimateFriendChatRoom

    /**
     * 绑定LifecycleOwner
     */
    fun bindLifecycleOwner(owner: LifecycleOwner)


    /**
     * 请求密友消息提示
     *
     * @param chatUuid 聊天室UUID
     */
    fun requestIntimateFriendTips(chatUuid: String?)

    /**
     * 点击带Ta处理
     * @param roomUuid 聊天室UUID
     * @param targetUuid 目标用户UUID
     * @param scene 场景类型
     */
    fun takeHim(roomUuid:String?, targetUuid: String, scene: String)

    /**
     * 显示聊天室房间内达成密友弹框
     *
     * @param event 事件
     */
    fun showIntimateFriendChatroomBuildSuccessDialog(event: EventFamilyIntimateFriendChatRoomAgree?)

    /**
     * 显示新的密友邀请提示
     *
     * @param message 消息
     */
    fun showNewInviteTips(message:String?)


    /**
     * 新人求带按钮曝光埋点
     *
     * @param sceneUuid 聊天室UUID
     * @param view 视图
     * @param bizScene 场景
     */
    fun trackerNewerCarryButtonExp(sceneUuid: String?, view: String, bizScene: String)

    /**
     * 隐藏底部提示消息气泡
     */
    fun hideBottomTips()

    fun release()
}