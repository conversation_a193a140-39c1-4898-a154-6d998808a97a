package com.xmhaibao.family.api.router

import cn.taqu.lib.base.router.ARouterDefaultService
import cn.taqu.lib.base.router.ARouterManager
import com.alibaba.android.arouter.facade.template.IProvider
import com.alibaba.android.arouter.launcher.ARouter
import com.xmhaibao.family.api.router.service.FamilyIntimateFriendService
import com.xmhaibao.family.api.router.service.FamilyIntimateFriendServiceDefaultImpl
import com.xmhaibao.family.api.router.service.FamilyMemberService
import com.xmhaibao.family.api.router.service.FamilyRelationService
import com.xmhaibao.family.api.router.service.FamilyService
import com.xmhaibao.family.api.router.service.FamilyServiceDefaultImpl
import com.xmhaibao.family.api.router.service.MessageFamilyService
import com.xmhaibao.family.constants.FamilyPinsConstants

/**
 * @desc:家族pins路由
 *
 * <AUTHOR>
 * @date 2023/10/18
 */
object FamilyPinsRouter {


    private val sProviderMap: Map<Class<out IProvider>, IProvider> =
        HashMap<Class<out IProvider>, IProvider>().apply {
            put(FamilyService::class.java, FamilyServiceDefaultImpl())
            put(FamilyMemberService::class.java, FamilyServiceDefaultImpl())
            put(FamilyIntimateFriendService::class.java, FamilyIntimateFriendServiceDefaultImpl())
        }

    /**
     * 家族公共跳转服务
     */
    @JvmStatic
    fun familyRelationService(): FamilyRelationService {
        return ARouterManager.provide<FamilyRelationService>(
            FamilyRelationService::class.java,
            FamilyRouterPath.FAMILY_RELATION_SERVICE
        )
    }

    /**
     * 获取家族通用 service
     */
    fun familyService(): FamilyService {
        val familyService = ARouter.getInstance().navigation(FamilyService::class.java)
        return familyService ?: sProviderMap[FamilyService::class.java] as FamilyService
    }

    /**
     * 获取家族成员列表 service
     */
    fun familyMemberService(): FamilyMemberService {
        val familyMemberService = ARouter.getInstance().navigation(FamilyMemberService::class.java)
        return familyMemberService
            ?: sProviderMap[FamilyMemberService::class.java] as FamilyMemberService
    }

    /**
     * 家族密友服务
     */
    @JvmStatic
    fun familyIntimateFriendService(): FamilyIntimateFriendService {
        return ARouterManager.provide<FamilyIntimateFriendService>(
            FamilyIntimateFriendService::class.java,
            FamilyRouterPath.FAMILY_INTIMATE_FRIEND_SERVICE
        )
    }

    /**
     * 私信模块中家族相关功能
     */
    @JvmStatic
    fun messageFamilyService(): MessageFamilyService {
        var service: MessageFamilyService? = ARouter.getInstance().navigation(MessageFamilyService::class.java)
        if (service == null) {
            service = sProviderMap[MessageFamilyService::class.java] as MessageFamilyService?
        }
        if (service == null) {
            service = ARouterDefaultService.get(MessageFamilyService::class.java)
        }
        return service!!
    }

    /**
     * 家族大厅页
     */
    fun launchMessageFamilyHomeActivity() {
        //校验当前是否有存在聊天室，如果有进入家族广场之前，需要最小化
        if (!ARouterManager.chatRoomForBaseService().currentChatRoomUuid.isNullOrEmpty()) {
            ARouterManager.chatRoomForBaseService().onChatRoomToMinimize{
                ARouter.getInstance().build(FamilyRouterPath.MessageFamilyHomeActivity)
                    .navigation()
            }
        } else {
            ARouter.getInstance().build(FamilyRouterPath.MessageFamilyHomeActivity)
                .navigation()
        }
    }

    /**
     * 家族详情页面
     *
     * @param familyUuid 家族uuid
     */
    fun launchMessageFamilyDetailActivity(familyUuid: String?) {
        launchMessageFamilyDetailActivity(familyUuid, "")
    }

    /**
     * 家族详情页面
     *
     * @param familyUuid        家族uuid
     * @param inviteAccountUuid 邀请人account_uuid
     */
    fun launchMessageFamilyDetailActivity(
        familyUuid: String?,
        inviteAccountUuid: String?
    ) {
        launchMessageFamilyDetailActivity(familyUuid, inviteAccountUuid, "")
    }

    /**
     * 家族详情页面
     *
     * @param familyUuid        家族uuid
     * @param inviteAccountUuid 邀请人account_uuid
     * @param from              来源
     */
    fun launchMessageFamilyDetailActivity(
        familyUuid: String?,
        inviteAccountUuid: String?,
        from: String?
    ) {
        //校验当前是否有存在聊天室，如果有进入家族主页之前，需要最小化
        if (!ARouterManager.chatRoomForBaseService().currentChatRoomUuid.isNullOrEmpty()) {
            ARouterManager.chatRoomForBaseService().onChatRoomToMinimize{
                ARouter.getInstance().build(FamilyRouterPath.FAMILY_DETAIL_ACTIVITY)
                    .withString(FamilyPinsConstants.INTENT_FAMILY_UUID, familyUuid)
                    .withString(
                        FamilyPinsConstants.MESSAGE_INTENT_FAMILY_INVITE_ACCOUNT_UUID,
                        inviteAccountUuid
                    )
                    .withString(FamilyPinsConstants.INTENT_FAMILY_HALL_SOURCE_KEY, from)
                    .navigation()
            }
        }else{
            ARouter.getInstance().build(FamilyRouterPath.FAMILY_DETAIL_ACTIVITY)
                .withString(FamilyPinsConstants.INTENT_FAMILY_UUID, familyUuid)
                .withString(
                    FamilyPinsConstants.MESSAGE_INTENT_FAMILY_INVITE_ACCOUNT_UUID,
                    inviteAccountUuid
                )
                .withString(FamilyPinsConstants.INTENT_FAMILY_HALL_SOURCE_KEY, from)
                .navigation()
        }
    }

    /**
     * 家族设置界面
     *
     * @param conversationId 用于跳转至家族聊天界面
     */
    fun launchMessageFamilySettingActivity(conversationId: String?) {
        ARouter.getInstance().build(FamilyRouterPath.FAMILY_SETTING_ACTIVITY)
            .withString(FamilyPinsConstants.INTENT_FAMILY_CONVERSATION_ID, conversationId)
            .navigation()
    }

    /**
     * 跳转到家族编辑页面
     *
     * @param familyUuid 家族uuid
     */
    fun launchMessageEditActivity(familyUuid: String?) {
        ARouter.getInstance()
            .build(FamilyRouterPath.FAMILY_EDIT_ACTIVITY)
            .withString(FamilyPinsConstants.INTENT_FAMILY_UUID, familyUuid)
            .navigation()
    }

    /**
     * 家族设置-修改宣言/修改名称 页面
     *
     * @param isInputFamilyName 是否是修改家族名称
     * @param defContent        默认内容
     * @param editNameNum       名称剩余修改次数
     */
    fun launchMessageFamilySettingInputStrActivity(
        isInputFamilyName: Boolean,
        defContent: String?,
        editNameNum: String?
    ) {
        ARouter.getInstance().build(FamilyRouterPath.FAMILY_SETTING_INPUT_STR_ACTIVITY)
            .withBoolean(
                FamilyPinsConstants.INTENT_IS_CRATE_FAMILY_INPUT_NAME,
                isInputFamilyName
            )
            .withString(FamilyPinsConstants.INTENT_FAMILY_INPUT_CONTENT, defContent)
            .withString(FamilyPinsConstants.INTENT_FAMILY_INPUT_NAME_NUMBER, editNameNum)
            .navigation()
    }
}