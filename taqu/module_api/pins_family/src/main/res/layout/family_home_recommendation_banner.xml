<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="200dp">

    <View
        android:id="@+id/ivBannerTop"
        android:layout_width="0dp"
        android:layout_height="100dp"
        android:background="@drawable/family_home_recommend_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <hb.drawable.shape.view.HbView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:corner_bottom_left="8dp"
        app:corner_bottom_right="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivBannerTop"
        app:solid="#F0EDFF">


    </hb.drawable.shape.view.HbView>


    <TextView
        android:id="@+id/tvMore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="14dp"
        style="@style/Text.B4"
        android:textColor="#4c0cba"
        android:drawableRight="@drawable/family_purple_arrow_right_ic"
        android:text="更多"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.xmhaibao.family.widget.ClickableRecyclerView
        android:id="@+id/rvRecommend"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="40dp"
        android:layout_marginBottom="12dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>