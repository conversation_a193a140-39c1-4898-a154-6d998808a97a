package com.xmhaibao.call.api.bean

import com.google.gson.annotations.SerializedName
import hb.utils.StringUtils
import java.io.Serializable

/**
 * 男用户是否可匹配
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
open class CallMaleMatchConfigBean : Serializable {
    /**
     * 卡通脸贴纸资源id
     */
    @SerializedName("face_uid")
    var cartoonFaceUid: String? = ""

    /**
     * 卡通脸功能是否可用
     */
    @SerializedName("is_face_user")
    var isCartoonFaceEnable: String? = ""

    /**
     * 获取优质女数量
     */
    @SerializedName("high_quality_num")
    var highQualityNum: String? = "0"


    /**
     * 是否开启卡通脸
     */
    fun isCartoonFaceEnable(): Boolean {
        return "1" == isCartoonFaceEnable
    }

    /**
     * 获取优质女数量
     */
    fun getHighQualityCount(): Int {
        return StringUtils.stringToInt(highQualityNum)
    }
}