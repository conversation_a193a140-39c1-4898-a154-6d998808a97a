package com.xmhaibao.call.api.bean

import cn.taqu.lib.base.constants.CommonConstants
import java.io.Serializable

/**
 * 拨打方结束页面参数bean
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
class CallCallerFinishData : Serializable {

    /**
     * 通话时间
     */
    var callTime: Long = 0

    /**
     * 是否sm结束
     */
    var callSmEnd: Boolean = false
    /**
     * 通话来源
     */
    var fromSource:String?=""

    /**
     * channelInfo
     */
    var channelInfo: CallChannelInfo? = null

    /**
     * 是否为standby通话
     */
    fun isStandby(): Boolean {
        return CommonConstants.CALL_SOURCE_STANDBY == fromSource
                || CommonConstants.CALL_SOURCE_FEED_STANDBY == fromSource
    }

}