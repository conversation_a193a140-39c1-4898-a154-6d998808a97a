package com.xmhaibao.call.api.event;

import com.xmhaibao.call.api.bean.CallHostOnlineStatusInfo;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/11/16.
 */

public class EventCallHostOnline {

    CallHostOnlineStatusInfo info;
    boolean hideOnlineView;
    boolean stopService;

    public EventCallHostOnline(CallHostOnlineStatusInfo info) {
        this.info = info;
    }

    public EventCallHostOnline(boolean hideOnlineView, boolean stopService) {
        this.hideOnlineView = hideOnlineView;
        this.stopService = stopService;
    }

    public boolean isHideOnlineView() {
        return hideOnlineView;
    }

    public void setHideOnlineView(boolean hideOnlineView) {
        this.hideOnlineView = hideOnlineView;
    }

    public boolean isStopService() {
        return stopService;
    }

    public void setStopService(boolean stopService) {
        this.stopService = stopService;
    }

    public CallHostOnlineStatusInfo getInfo() {
        return info;
    }

    public void setInfo(CallHostOnlineStatusInfo info) {
        this.info = info;
    }
}
