package com.xmhaibao.mybean.model;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

import cn.taqu.lib.okhttp.model.IDoExtra;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import hb.utils.StringUtils;
import hb.utils.TimeUtils;

/**
 * author: wusongyuan
 * date: 2016.10.28
 * desc:
 */

public class LiveTqBeanItemInfo implements Serializable, IDoExtra {

    // 流水记录编码
    @SerializedName("journal_sn")
    private String journalSn;
    // 流水记录类型 充值 礼物 弹幕 香蕉船等
    private String type;
    // 充值趣豆数
    private String tqbean;
    @SerializedName("create_time")
    private String createTime;
    private String tqbeanTitle;

    private int num;

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getJournalSn() {
        return journalSn;
    }

    public void setJournalSn(String journalSn) {
        this.journalSn = journalSn;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTqbean() {
        return tqbean;
    }

    public void setTqbean(String tqbean) {
        this.tqbean = tqbean;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getTqbeanTitle() {
        return tqbeanTitle;
    }

    public void setTqbeanTitle(String tqbeanTitle) {
        this.tqbeanTitle = tqbeanTitle;
    }

    @Override
    public void doExtra(IResponseInfo response) {
        if (!TextUtils.isEmpty(createTime)) {
            setCreateTime(TimeUtils.millis2String(StringUtils.stringToLong(getCreateTime()) * 1000));
        }
    }
}
