package com.xmhaibao.mybean.helper

import com.xmhaibao.mybean.model.LiveTqBeanPayTypeItemBean
import hb.common.data.AccountHelper
import hb.utils.kvcache.KVCacheUtils

/**
 * 支付方式折叠逻辑Helper
 * @link https://o15vj1m4ie.feishu.cn/wiki/wikcnHO6jx9k9zTLemX23Vc4VUb
 * <AUTHOR>
 **/
class MyBeanPayFoldHelper {

    companion object {
        private const val MY_BEAN_KEY_MMKV_ALIPAY_COUNT = "my_bean_key_mmkv_alipay_count"
        private const val MY_BEAN_THRESHOLD_NUM_ALIPAY_HAD_PAY = 1

        /**
         * 支付成功时，MMKV写入支付宝连续支付成功次数
         * */
        @JvmStatic
        fun onPaySuccess(isAliPay : Boolean) {
            if(isAliPay) {
                val last = KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).getInt(
                    MY_BEAN_KEY_MMKV_ALIPAY_COUNT, 0)
                KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).putInt(
                    MY_BEAN_KEY_MMKV_ALIPAY_COUNT, last + 1)
            } else {
                KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).putInt(
                    MY_BEAN_KEY_MMKV_ALIPAY_COUNT, 0)
            }
        }

        /**
         * 是否需要折叠支付宝支付以外的支付方式
         * */
        @JvmStatic
        fun isNeedFoldOtherPay() :Boolean{
            return KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).getInt(
                MY_BEAN_KEY_MMKV_ALIPAY_COUNT, 0) >= MY_BEAN_THRESHOLD_NUM_ALIPAY_HAD_PAY
        }

        /**
         * 生成只有支付宝支付的Bean
         * */
        @JvmStatic
        fun generateFoldedArray(origin : List<LiveTqBeanPayTypeItemBean>): List<LiveTqBeanPayTypeItemBean> {
            if(origin.any {
                it.isAliPay
                }) {
                return listOf(origin.first {
                    it.isAliPay
                })
            }
            return origin
        }

    }
}