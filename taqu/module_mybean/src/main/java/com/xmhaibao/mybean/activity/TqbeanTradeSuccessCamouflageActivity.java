package com.xmhaibao.mybean.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.xmhaibao.mybean.R;

import cn.taqu.lib.base.constants.CommonConstants;
import com.xmhaibao.mine.api.router.MyBeanRouterPath;

import cn.taqu.lib.base.utils.AppQuCoinHelper;
import hb.common.widget.webview.XWeb;
import hb.common.xstatic.activity.BaseActivity;

/**
 * @author：杨浩 项目：taqu
 * 创建日期：2019-06-18
 * 功能简介：趣币充值成功伪装
 */

@Route(path = MyBeanRouterPath.TqBeanTradeSuccessCamouflage)
public class TqbeanTradeSuccessCamouflageActivity extends BaseActivity {

    TextView mTvMybeanTradeSuccessCamouflageComplete;
    TextView mTvMybeanTradeSuccessCamouflageComplaints;
    TextView mTvMybeanTradeSuccessCamouflageMoney;
    View mRoot;
    private TextView mTvCamouflageTitle;


    private String mMoney = null;
    private String mHost = null;
    private String mOrder = null;

    @Override
    protected Object onCreateContentView() {
        return R.layout.mybean_trade_success_camouflage_activity;
    }

    @Override
    public void initConfig(Bundle savedInstanceState) {
        super.initConfig(savedInstanceState);
        if (getIntent() == null) {
            return;
        }
        mOrder = getIntent().getStringExtra(CommonConstants.PAY_CAMOUFLAGE_ORDER_ID);
        mMoney = getIntent().getStringExtra(CommonConstants.PAY_CAMOUFLAGE_MONEY);
        mHost = getIntent().getStringExtra(CommonConstants.PAY_COMPLAINTS_HOST);
    }


    @Override
    public void initViews() {
        super.initViews();
        mTvMybeanTradeSuccessCamouflageComplete = findViewById(R.id.tvMybeanTradeSuccessCamouflageComplete);
        mTvMybeanTradeSuccessCamouflageComplaints = findViewById(R.id.tvMybeanTradeSuccessCamouflageComplaints);
        mTvMybeanTradeSuccessCamouflageMoney = findViewById(R.id.tvMybeanTradeSuccessCamouflageMoney);
        mTvCamouflageTitle = findViewById(R.id.tvCamouflageTitle);
        mRoot = findViewById(R.id.viewMybeanTradeSuccessCamouflageRoot);
        hideToolbar();
        setPageTitle("趣币充值成功-微信");
        mTvMybeanTradeSuccessCamouflageMoney.setText(mMoney);
        mTvMybeanTradeSuccessCamouflageComplete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        mTvMybeanTradeSuccessCamouflageComplaints.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                XWeb.launch(v.getContext(), mHost + "?inpour_sn=" + mOrder, "", true);
                finish();
            }
        });
        if (mTvCamouflageTitle != null) {
            mTvCamouflageTitle.setText(String.format("%s在线充值", AppQuCoinHelper.getRatioUnit()));
        }
    }
}
