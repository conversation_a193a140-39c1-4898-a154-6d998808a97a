package com.xmhaibao.mybean.rechargedialog.viewholder

import android.graphics.Paint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.xmhaibao.mybean.R
import com.xmhaibao.mybean.bean.RechargeVipPayOptionBean
import com.xmhaibao.mybean.databinding.MybeanVipPayOptionItemBinding
import hb.skin.support.SkinCompatManager
import hb.utils.ScreenUtils
import hb.utils.SizeUtils
import hb.utils.SpanUtils
import hb.xadapter.XBaseViewHolder

/**
 * 会员档位viewholder
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
class RechargeVipPayOptionViewHolder(
    private val binding: MybeanVipPayOptionItemBinding
) : XBaseViewHolder<RechargeVipPayOptionBean>(binding.root) {

    lateinit var clickInvoke: (position: String?) -> Unit

    constructor(parent: ViewGroup) : this(
        MybeanVipPayOptionItemBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ).apply {
            val params = clItem.layoutParams as RecyclerView.LayoutParams
            params.width = ((ScreenUtils.getScreenWidth() * 0.84 - SizeUtils.dp2px(18f)) / 3).toInt()
            clItem.layoutParams = params

        }
    )


    override fun onBindView(item: RechargeVipPayOptionBean?) {
        item?.run {
            SkinCompatManager.getInstance().applySkin(binding.viewBg) {
                applySkin(this)
            }
            applySkin(item)

            //有效时长
            binding.tvDuration.text = this.duration

            //折扣价
            binding.tvDiscountPrice.text = this.price

            //原价
            binding.tvOriginPrice.text = "￥${this.originPrice}"
            binding.tvOriginPrice.paint.flags = Paint.STRIKE_THRU_TEXT_FLAG

            //日均价
            binding.tvDayPrice.text = this.dayPriceDesc

            //超划算
            binding.tvBubble.isVisible = this.showLabel?.isNotEmpty() == true
            binding.tvBubble.text = this.showLabel

            binding.clItem.setOnClickListener {
                //更新选中
                if (::clickInvoke.isInitialized && !this.isSelect) {
                    clickInvoke.invoke(this.id)
                }

            }
        }
    }

    private fun applySkin(item: RechargeVipPayOptionBean) {
        //背景
        binding.viewBg.shaper()
            .solid(
                ContextCompat.getColor(
                    binding.viewBg.context,
                    if (item.isSelect) R.color.mybean_vip_solid_color else cn.taqu.lib.base.R.color.TH_Gray100
                )
            )
            .stroke(
                SizeUtils.dp2px(1f),
                ContextCompat.getColor(
                    binding.viewBg.context,
                    if (item.isSelect) R.color.mybean_vip_stroke_color else cn.taqu.lib.base.R.color.TH_Navy003
                )
            )


        //有效时长
        binding.tvDuration.setTextColor(
            ContextCompat.getColor(
                binding.tvDuration.context,
                if (item.isSelect) cn.taqu.lib.base.R.color.TH_Navy001_Normal else cn.taqu.lib.base.R.color.TH_Gray990
            )
        )
        binding.tvDuration.shaper()
            .solid(
                ContextCompat.getColor(
                    binding.tvDuration.context,
                    if (item.isSelect) R.color.mybean_vip_stroke_color else cn.taqu.lib.base.R.color.TH_Gray200
                )
            ).stroke(
                SizeUtils.dp2px(1f),
                ContextCompat.getColor(
                    binding.viewBg.context,
                    if (item.isSelect) R.color.mybean_vip_stroke_color else cn.taqu.lib.base.R.color.TH_Navy003
                )
            )

        //折扣单位
        binding.tvUnit.setTextColor(
            ContextCompat.getColor(
                binding.tvUnit.context,
                if (item.isSelect) R.color.mybean_vip_discount_color else cn.taqu.lib.base.R.color.TH_Gray990
            )
        )

        //折扣价
        binding.tvDiscountPrice.setTextColor(
            ContextCompat.getColor(
                binding.tvDiscountPrice.context,
                if (item.isSelect) R.color.mybean_vip_discount_color else cn.taqu.lib.base.R.color.TH_Gray990
            )
        )

        //原价
        binding.tvOriginPrice.setTextColor(
            ContextCompat.getColor(
                binding.tvOriginPrice.context,
                if (item.isSelect) R.color.mybean_vip_origin_color else cn.taqu.lib.base.R.color.TH_Gray400
            )
        )

        //日均价
        binding.tvDayPrice.setTextColor(
            ContextCompat.getColor(
                binding.tvDayPrice.context,
                if (item.isSelect) R.color.mybean_vip_discount_color else cn.taqu.lib.base.R.color.TH_Gray990
            )
        )

        //节省xx%
        binding.tvSave.text = SpanUtils()
            .append("节省")
            .setForegroundColor(
                ContextCompat.getColor(
                    binding.tvSave.context,
                    if (item.isSelect) R.color.mybean_vip_discount_color else cn.taqu.lib.base.R.color.TH_Gray770
                )
            )
            .append(item.saveDesc ?: "")
            .setForegroundColor(ContextCompat.getColor(binding.tvSave.context, cn.taqu.lib.base.R.color.TH_Red600))
            .create()
        binding.tvSave.shaper().solid(
            ContextCompat.getColor(
                binding.tvSave.context,
                if (item.isSelect) hb.alive.R.color.white_alpha_95 else cn.taqu.lib.base.R.color.TH_Gray200
            )
        )
    }

}