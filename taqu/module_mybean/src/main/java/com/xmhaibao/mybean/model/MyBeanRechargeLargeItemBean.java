package com.xmhaibao.mybean.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * 促销充值更多bean
 *
 * <AUTHOR>
 * @date 2021-04-09
 */
public class MyBeanRechargeLargeItemBean implements Serializable {
    @SerializedName("promotion_text")
    private String promotionText;
    @SerializedName("limit_text")
    private String limitText;
    @SerializedName("radio_text")
    private String radioText;
    @SerializedName("min_amount")
    private String minAmount;
    @SerializedName("max_amount")
    private String maxAmount;
    private String title;
    private String radio;
    private boolean isSelected;

    public String getPromotionText() {
        return promotionText;
    }

    public void setPromotionText(String promotionText) {
        this.promotionText = promotionText;
    }

    public String getLimitText() {
        return limitText;
    }

    public void setLimitText(String limitText) {
        this.limitText = limitText;
    }

    public String getRadioText() {
        return radioText;
    }

    public void setRadioText(String radioText) {
        this.radioText = radioText;
    }

    public String getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(String minAmount) {
        this.minAmount = minAmount;
    }

    public String getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(String maxAmount) {
        this.maxAmount = maxAmount;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getRadio() {
        return radio;
    }

    public void setRadio(String radio) {
        this.radio = radio;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }
}
