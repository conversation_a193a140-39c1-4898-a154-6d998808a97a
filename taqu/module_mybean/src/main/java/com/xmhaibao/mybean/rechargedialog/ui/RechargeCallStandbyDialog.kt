package com.xmhaibao.mybean.rechargedialog.ui

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import cn.taqu.lib.base.router.RouterLaunch
import cn.taqu.lib.base.utils.AppQuCoinHelper
import cn.taqu.lib.base.utils.GIORechargePosition
import cn.taqu.lib.base.widget.Item_decoration.HorizontalSpaceItemDecoration
import com.xmhaibao.mine.api.model.EventTradeSuccess
import com.xmhaibao.mybean.bean.RechargeCallStandbyBean
import com.xmhaibao.mybean.bean.RechargeCallStandbyPackBean
import com.xmhaibao.mybean.databinding.MybeanCallStandbyRechargeDialogBinding
import com.xmhaibao.mybean.helper.RechargeOptPayHelper
import com.xmhaibao.mybean.helper.RechargeTracker
import com.xmhaibao.mybean.model.LiveTqBeanPayTypeItemBean
import com.xmhaibao.mybean.rechargedialog.viewholder.RechargeCallStandbyViewHolder
import com.xmhaibao.mybean.rechargedialog.viewholder.RechargeFirstOptPayMethodViewHolder
import dp
import hb.thirdtools.xpay.PayUtil
import hb.utils.ActivityUtils
import hb.utils.ColorUtils
import hb.utils.Loger
import hb.utils.SpanUtils
import hb.utils.StringUtils
import hb.xadapter.XBaseAdapter
import hb.xstatic.tools.XEventBus
import hb.xstyle.xdialog.XLifecycleDialog
import hb.xtoast.XToastUtils

/**
 * 提高免费匹配功能付费转化需求
 * [飞书需求](https://project.feishu.cn/haibao/story/detail/5458588956)
 * <AUTHOR>
 */
class RechargeCallStandbyDialog(
    context: Context,
    private var rechargeCallStandbyBean: RechargeCallStandbyBean? = null,
) :
    XLifecycleDialog(context), PayUtil.OnPayListener, OnClickListener {

    companion object {
        const val TAG = "RechargeCallStandbyDialog"
    }

    private val binding by lazy {
        MybeanCallStandbyRechargeDialogBinding.inflate(LayoutInflater.from(context))
    }

    private var payMethodAdapter: XBaseAdapter? = null

    private var curSelectPayMethod: LiveTqBeanPayTypeItemBean? = null
    private var payHelper: RechargeOptPayHelper? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        window?.apply {
            setLayout(315.dp, 462.dp)
            setGravity(Gravity.CENTER)
            setDimAmount(0.5f)
        }
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        initViews()
        initData()
        RechargeTracker.trackCallMatchRechargePopExp()
    }

    private fun initViews() {
        rechargeCallStandbyBean?.let { data ->
            binding.cbAgreement.isChecked = data.checkAgreement == "1"
            binding.tvPayAmount.text = SpanUtils().append(data.btnAmount)
                .setForegroundColor(ColorUtils.getColor("#FFED7A")).append("元特价").create()
            binding.tvPayOriginAmount.text = SpanUtils().append(data.originAmount)
                .setStrikethrough().create()
            val packAdapter = XBaseAdapter(context, data.rewardList)
            packAdapter.register(
                RechargeCallStandbyPackBean::class.java,
                RechargeCallStandbyViewHolder::class.java
            )

            binding.rvPackList.layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            binding.rvPackList.addItemDecoration(
                HorizontalSpaceItemDecoration.Builder(context)
                    .size(12.dp).transparentColor().build()
            )
            binding.rvPackList.adapter = packAdapter

            curSelectPayMethod = RechargeOptPayHelper.handlerPayMethodInfo(data.payMethodInfo)
            payMethodAdapter = XBaseAdapter(context, data.payMethodInfo?.payMethod.orEmpty())
            payMethodAdapter?.register(
                LiveTqBeanPayTypeItemBean::class.java,
                RechargeFirstOptPayMethodViewHolder::class.java
            )
            payMethodAdapter?.setOnItemClickListener { _, position ->
                updatePayChecked(position)
            }
            binding.rvPayMethod.layoutManager = GridLayoutManager(context, 2)
            binding.rvPayMethod.adapter = payMethodAdapter
        }
        initListener()
    }

    private fun initListener() {
        binding.ivPayBtn.setOnClickListener(this)
        binding.tvAgreement.setOnClickListener(this)
        binding.tvAgreementTitle.setOnClickListener(this)
        binding.ivClose.setOnClickListener(this)
    }

    private fun initData() {
        ActivityUtils.getActivity(context)?.let {
            payHelper = RechargeOptPayHelper(
                activity = it,
                <EMAIL>,
                GIORechargePosition.POSITION_CALL_STANDBY_RECHARGE_GUIDE,
                this@RechargeCallStandbyDialog
            )
        }
    }

    private fun updatePayChecked(checkPos: Int) {
        rechargeCallStandbyBean?.payMethodInfo?.payMethod?.forEachIndexed { index, item ->
            if (index == checkPos) {
                item.isSelect = true
                payMethodAdapter?.notifyItemChanged(index)
                curSelectPayMethod = item
            } else if (item.isSelect) {
                item.isSelect = false
                payMethodAdapter?.notifyItemChanged(index)
            }
        }
    }

    private fun pay() {
        val method = rechargeCallStandbyBean?.payMethodInfo?.payMethod?.find { it.isSelect }
        if (method == null) {
            XToastUtils.show("请选择支付方式")
            return
        }

        trackCallMatchRechargePopClick("去充值")
        when {
            method.isWxPay -> {
                requestPayInfo()
            }

            method.isAliPay -> {
                requestPayInfo()
            }

            else -> {
                XToastUtils.show("暂不支持该支付方式")
            }
        }
    }

    private fun trackCallMatchRechargePopClick(position: String?) {
        RechargeTracker.trackCallMatchRechargePopClick(position, if (binding.cbAgreement.isChecked) "是" else "否")
    }

    private fun requestPayInfo() {
        val amount = StringUtils.stringToInt(rechargeCallStandbyBean?.amount)
        if (!binding.cbAgreement.isChecked) {
            XToastUtils.show("请阅读并同意协议")
            return
        }
        if (amount <= 0) {
            XToastUtils.show("充值%s不能小于0".format(AppQuCoinHelper.ratioUnit))
            return
        }
        payHelper?.pay(
            curSelectPayMethod,
            null,
            amount = amount.toString(),
            type = rechargeCallStandbyBean?.origin,
            source = GIORechargePosition.POSITION_CALL_STANDBY_RECHARGE_GUIDE,
            "",
            rechargeCallStandbyBean?.amount
        )
    }

    override fun onClick(v: View?) {
        when (v) {
            binding.ivPayBtn -> pay()
            binding.tvAgreementTitle -> {
                binding.cbAgreement.isChecked = true
            }
            binding.tvAgreement -> RouterLaunch.dealJumpData(context,
                rechargeCallStandbyBean?.rechargeAgreementRelation)

            binding.ivClose -> {
                dismiss()
                trackCallMatchRechargePopClick("关闭")
            }
        }
    }

    fun onEventMainThread(event: EventTradeSuccess?) {
        Loger.d(TAG, "onEventMainThread EventTradeSuccess: $event")
        dismiss()
    }

    override fun onSuccess() {
        dismiss()
    }

    override fun onCancel() {
        //取消支付
    }

    override fun onFailed(error: String?, errorCode: String?) {
        //支付失败
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        XEventBus.register(this)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        XEventBus.unregster(this)
    }

    override fun onDestroy(owner: LifecycleOwner?) {
        super.onDestroy(owner)
        payHelper?.destroy()
        payHelper = null
    }

}