package com.xmhaibao.mybean.member.dialog

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import cn.taqu.lib.base.BaseApplication
import cn.taqu.lib.base.utils.XPermissionsHelper
import com.xmhaibao.mine.api.tracker.MyBeanTracker
import com.xmhaibao.mybean.databinding.MybeanRewardDetailDialogBinding
import com.xmhaibao.mybean.member.bean.LiveTqMemberRewardPropBean
import com.xmhaibao.mybean.member.viewmodel.LiveTqMemberDetailsViewModel
import dp
import hb.kotlin_extension.gone
import hb.kotlin_extension.invisible
import hb.kotlin_extension.visible
import hb.utils.ScreenUtils
import hb.xpermission.XPermissions
import hb.xstyle.xdialog.XLifecycleDialog
import hb.xtoast.XToastUtils

/**
 * VIP 领取弹窗
 * <AUTHOR>
 * @date 2025-04-22
 */
class MyBeanVipRewardDialog(context: Context,val viewModel: LiveTqMemberDetailsViewModel,val propInfo:LiveTqMemberRewardPropBean): XLifecycleDialog(context){


    private val binding: MybeanRewardDetailDialogBinding = MybeanRewardDetailDialogBinding.inflate(layoutInflater)



    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        binding.ivClose.setOnClickListener {
            MyBeanTracker.Instance.trackMemberItemClaimClick("关闭",propInfo.propName.orEmpty())
            dismiss()
        }
        binding.tvRewardDesc.text = propInfo.propDesc
        binding.tvSubTitle.text = propInfo.subTitle
        binding.ivPropIcon.setImageFromUrl(propInfo.propIcon)
        binding.ivPropName.text = propInfo.propName
        binding.ivPropTime.text = propInfo.propLabel

       if (viewModel.hasCalendarInfo()){
           binding.ivRewardButton.invisible()
           binding.viewRewardButton.visible()
           binding.tvRewardButton.text = "已开启提醒"
       }else{
           binding.ivRewardButton.visible()
           binding.viewRewardButton.gone()
           binding.tvRewardButton.text = "提醒我领取"
       }

        MyBeanTracker.Instance.trackMemberItemClaimExpo(propInfo.propName.orEmpty())

        binding.ivRewardButton.setOnClickListener {
            rewardButtonClick()
        }
    }


    private fun rewardButtonClick(){
        if (viewModel.hasCalendarInfo()){
            //有日历信息，点击没反应
            return
        }
        MyBeanTracker.Instance.trackMemberItemClaimClick("提醒我领取",propInfo.propName.orEmpty())
        val hasPermissions = XPermissionsHelper.checkCalendar(BaseApplication.getInstance())
        if (hasPermissions){
            MyBeanTracker.Instance.trackMemberCenterReminderClick("开启","成功","道具领取弹窗")
            viewModel.writeRewardCalender()
            XToastUtils.show("每日领取提醒已开通")
            dismiss()
            return
        }
        XPermissionsHelper.createCalendar(
            context,
            true,
            "开启日历权限",
            "授权日历权限，可每日免费领趣币道具卡",
            object : XPermissions.OnPermissionListener {
                override fun onGranted(perms: List<String?>?) {
                    viewModel.writeRewardCalender()
                    MyBeanTracker.Instance.trackMemberCenterReminderClick("开启","成功","道具领取弹窗")
                    XToastUtils.show("每日领取提醒已开通")
                    dismiss()
                }

                override fun onDenied(perms: List<String?>?) {
                    MyBeanTracker.Instance.trackMemberCenterReminderClick("开启","失败","道具领取弹窗")

                }
            })
    }


    override fun onStart() {
        super.onStart()
        window?.apply {
            setLayout((ScreenUtils.getScreenWidth() - 66.dp).toInt(),
                ViewGroup.LayoutParams.WRAP_CONTENT)
            setGravity(Gravity.CENTER)
        }
    }

}