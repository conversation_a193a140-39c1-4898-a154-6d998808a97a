<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/mIvCertIcon"
        android:layout_width="288dp"
        android:layout_height="328dp"
        app:placeholderImage="@color/transparent"/>

    <ImageView
        android:id="@+id/mIvClose"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_20"
        android:src="@drawable/mybean_certification_close"/>
</LinearLayout>