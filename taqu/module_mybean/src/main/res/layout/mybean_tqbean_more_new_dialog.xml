<?xml version="1.0" encoding="utf-8"?>
<hb.drawable.shape.view.HbRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:corner_top_left="15dp"
    app:corner_top_right="15dp"
    app:solid="@color/TH_Navy110">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:paddingBottom="20dp">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:layout_marginTop="13dp"
            android:text="更多金额"

            android:textColor="@color/black"
            android:textSize="@dimen/TH_FONT_H3"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvBean"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:layout_marginTop="3dp"
            android:textColor="@color/TH_Gray990"
            android:textSize="@dimen/t6"
            tools:text="我的趣豆" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_white_radio_8"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_live_tqbean_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="19dp"
                android:layout_marginBottom="10dp"
                android:gravity="center_vertical"
                android:textColor="@color/TH_Gray990"
                android:textSize="@dimen/t7"
                android:text="对应趣币: -" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="25dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:text="￥"
                    android:textColor="@color/TH_Gray990"
                    android:textSize="@dimen/TH_FONT_D2"
                    android:textStyle="bold" />

                <EditText
                    android:id="@+id/edit_live_tqbean_more"
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:layout_marginEnd="40dp"
                    android:background="@null"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:hint="输入人民币金额"
                    android:inputType="number"
                    android:paddingLeft="5dp"
                    android:singleLine="true"
                    android:textColor="@color/TH_Gray990"

                    android:textColorHint="@color/TH_Gray300"
                    android:textSize="@dimen/TH_FONT_D2" />
            </LinearLayout>


        </LinearLayout>


        <TextView
            android:id="@+id/tv_live_tqbean_hint1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="· 仅支持￥1000~￥50000的充值金额"
            android:textColor="@color/g3"
            android:textSize="@dimen/t7" />

        <TextView
            android:id="@+id/tv_live_tqbean_hint2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:text="·  RMB与趣豆的充值比率是1:100"
            android:textColor="@color/TH_Gray400"
            android:textSize="@dimen/TH_FONT_N1" />

        <TextView
            android:id="@+id/tv_live_tqbean_hint3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:text="· 特惠：额外赠送N%趣豆"
            android:textColor="@color/TH_Gray400"
            android:textSize="@dimen/TH_FONT_N1"
            android:visibility="gone"
            tools:visibility="visible" />

        <Button
            android:id="@+id/btn_live_tqbean_confirm"
            style="@style/BtnA.Large"
            android:layout_width="match_parent"
            android:layout_height="49dp"
            android:layout_marginTop="15dp"
            android:enabled="false"
            android:stateListAnimator="@null"
            android:text="确认充值"
            tools:enabled="true" />
    </LinearLayout>
</hb.drawable.shape.view.HbRelativeLayout>