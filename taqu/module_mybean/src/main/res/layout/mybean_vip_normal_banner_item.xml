<?xml version="1.0" encoding="utf-8"?>
<hb.drawable.shape.view.HbLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/llContentRoot"
    android:layout_width="match_parent"
    android:layout_height="160dp"
    android:background="@drawable/mybean_normal_banner_item_selected"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:layout_marginHorizontal="16dp"
    android:padding="8dp"
    app:corner="12dp"
    >


    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvTitle"
        style="@style/Text.B1.B100"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="每月100条免费消息"
        android:textStyle="bold" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvSbuTitle"
        style="@style/Text.B3.B100"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:ellipsize="marquee"
        android:gravity="center"
        android:singleLine="true"
        android:text="打铁要趁热，快和附近的异性聊天吧！" />

    <FrameLayout
        android:layout_width="327dp"
        android:layout_height="90dp"
        android:layout_marginTop="8dp">

        <hb.drawable.shape.view.HbView
            android:id="@+id/viewBannerBg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:corner="12dp"
            app:solid="@color/white_alpha_45" />

        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/ivIcon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:placeholderImage="@color/transparent"
            />
    </FrameLayout>

</hb.drawable.shape.view.HbLinearLayout>