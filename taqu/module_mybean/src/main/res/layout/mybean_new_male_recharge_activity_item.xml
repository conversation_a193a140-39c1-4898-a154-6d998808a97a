<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <hb.drawable.shape.view.HbView
        android:id="@+id/vContentBg"
        android:layout_width="match_parent"
        android:layout_height="84dp"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="12dp"
        android:layout_marginBottom="8dp"
        app:corner="11dp"
        app:layout_constraintTop_toTopOf="parent"
        app:solid="@color/white"
        app:stroke_color="#FAE2AD"
        app:stroke_width="1dp" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvTitleTip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="8dp"
        android:paddingTop="2dp"
        android:paddingRight="8dp"
        android:paddingBottom="2dp"
        android:textColor="@color/white"
        android:textSize="11sp"
        android:visibility="gone"
        app:corner_bottom_right="11dp"
        app:corner_top_left="11dp"
        app:gradient_color_end="#EF5414"
        app:gradient_color_start="#FE1633"
        app:layout_constraintLeft_toLeftOf="@id/vContentBg"
        app:layout_constraintTop_toTopOf="@id/vContentBg"
        tools:text="恋爱特惠"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvBeanCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="4dp"
        android:includeFontPadding="false"
        android:textColor="@color/TH_Gray700"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/tvDescribe"
        app:layout_constraintLeft_toLeftOf="@id/vContentBg"
        app:layout_constraintTop_toTopOf="@id/vContentBg"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="106趣币" />

    <TextView
        android:id="@+id/tvDescribe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:textColor="@color/TH_Gray600"
        android:textSize="11sp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/tvExplain"
        app:layout_constraintLeft_toLeftOf="@id/tvBeanCount"
        app:layout_constraintTop_toBottomOf="@id/tvBeanCount"
        tools:text="50+加送688趣币+通话卡"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvExplain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/TH_Gray500"
        android:textSize="9sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/vContentBg"
        app:layout_constraintLeft_toLeftOf="@id/tvBeanCount"
        app:layout_constraintTop_toBottomOf="@id/tvDescribe"
        tools:text="恋爱特惠限时特惠，赠送价值x元的聊天卡+趣聊通话卡。（通话卡1分钟）"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="12dp"
        android:includeFontPadding="false"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintRight_toRightOf="@id/vContentBg"
        app:layout_constraintTop_toTopOf="@id/tvBeanCount"
        tools:text="298" />

    <TextView
        android:id="@+id/tvPriceUnit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="￥"
        android:textSize="12sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/tvPrice"
        app:layout_constraintRight_toLeftOf="@id/tvPrice"
        app:layout_constraintTop_toTopOf="@id/tvPrice" />

    <TextView
        android:id="@+id/tvOriginalPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:textColor="@color/TH_Gray500"
        android:textSize="10sp"
        android:textStyle="bold"
        app:layout_constraintRight_toRightOf="@id/tvPrice"
        app:layout_constraintTop_toBottomOf="@id/tvPrice"
        tools:text="原价￥300" />

    <View
        android:id="@+id/vDeleteLine"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginBottom="4dp"
        android:background="@color/TH_Gray500"
        app:layout_constraintBottom_toBottomOf="@id/tvOriginalPrice"
        app:layout_constraintLeft_toLeftOf="@id/tvOriginalPrice"
        app:layout_constraintRight_toRightOf="@id/tvOriginalPrice" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupOriginalPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvOriginalPrice,vDeleteLine"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>