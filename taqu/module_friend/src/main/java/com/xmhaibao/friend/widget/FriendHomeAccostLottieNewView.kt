package com.xmhaibao.friend.widget

import android.content.Context
import android.util.AttributeSet
import cn.taqu.lib.base.utils.appdiff.AppDifferentiationUtils
import com.airbnb.lottie.LottieAnimationView
import hb.skin.support.SkinCompatManager
import hb.skin.support.widget.SkinCompatSupportable
import hb.utils.StringUtils

/**
 * 交友首页搭讪按钮适配深色模式动态切换
 *
 * <AUTHOR>
 * @date 2023-03-08
 */
class FriendHomeAccostLottieNewView(context: Context?, attrs: AttributeSet?) :
    LottieAnimationView(context, attrs), SkinCompatSupportable {

    init {
        applySkin()
    }

    override fun applySkin() {
        initLottiePath()
    }

    /**
     * 初始化lottie路径
     */
    private fun initLottiePath() {
        if (AppDifferentiationUtils.isQiaLiaoApp()) {
            if (!StringUtils.equalsIgnoreCase(
                    this.imageAssetsFolder,
                    "lottie/qialiao/friend_home_accost/images"
                )
            ) {
                //恰聊搭讪lottie
                this.setAnimation("lottie/qialiao/friend_home_accost/data.json")
                this.imageAssetsFolder = "lottie/qialiao/friend_home_accost/images"
            }

        } else {
            if (isApplySkin && SkinCompatManager.getInstance().isSkinDark) {
                if (!StringUtils.equalsIgnoreCase(
                        this.imageAssetsFolder,
                        "lottie/friend_home_page_new_accost_dark/images"
                    )
                ) {
                    this.imageAssetsFolder = "lottie/friend_home_page_new_accost_dark/images"
                    this.setAnimation("lottie/friend_home_page_new_accost_dark/data.json")
                }
            } else {
                if (!StringUtils.equalsIgnoreCase(
                        this.imageAssetsFolder,
                        "lottie/friend_home_page_new_accost_light/images"
                    )
                ) {
                    this.imageAssetsFolder = "lottie/friend_home_page_new_accost_light/images"
                    this.setAnimation("lottie/friend_home_page_new_accost_light/data.json")
                }
            }
        }
    }


    override fun isApplySkin(): Boolean {
        return SkinCompatManager.getInstance().isEnableSkin
    }

}