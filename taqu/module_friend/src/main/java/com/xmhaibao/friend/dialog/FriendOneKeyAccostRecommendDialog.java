package com.xmhaibao.friend.dialog;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ushengsheng.widget.recyclerview.GridDividerItemDecoration;
import com.xmhaibao.forum.api.tracker.ForumHomePageTracker;
import com.xmhaibao.forum.api.util.ForumPreferencesUtils;
import com.xmhaibao.friend.R;
import com.xmhaibao.friend.adapter.FriendOneKeyAccostDialogAdapter;
import com.xmhaibao.friend.bean.FriendOneKeyAccostDialogItemBean;
import com.xmhaibao.friend.databinding.FriendOneKeyAccostDialogBinding;
import com.xmhaibao.friend.viewholder.FriendOneKeyAccostDialogViewHolder;
import com.xmhaibao.message.api.router.MessagePinsRouter;

import java.util.ArrayList;
import java.util.List;

import cn.taqu.lib.base.interf.RequestAccostCallback;
import cn.taqu.lib.base.utils.GrowingIOUtils;
import cn.taqu.lib.base.utils.appdiff.AppDifferentiationUtils;
import cn.taqu.lib.base.utils.timeguide.config.ScheduleTimeConfig2;
import hb.actionqueue.annotation.XActionQueue;
import hb.message.db.entity.HBMessageContentDO;
import hb.utils.ScreenUtils;
import hb.utils.SizeUtils;
import hb.xadapter.XBaseAdapter;

/**
 * Created by Yangjm on 2020年3月17日 0017 上午 11:02:30
 * <p>
 * 交友    一键搭讪推荐弹框(只有男性才有)
 */
@XActionQueue(type = XActionQueue.TYPE_DIALOG)
public class FriendOneKeyAccostRecommendDialog extends Dialog {
    private FriendOneKeyAccostDialogBinding mBinding;
    private FriendOneKeyAccostDialogAdapter mAdapter;
    private Context mContext;
    private ArrayList<FriendOneKeyAccostDialogItemBean> mDataList = new ArrayList<>();

    public FriendOneKeyAccostRecommendDialog(@NonNull Context context) {
        super(context, cn.taqu.lib.base.R.style.xjbDialog);
        mContext = context;
        getWindow().setWindowAnimations(R.style.friend_one_key_accost_recommend_dialog_animation);
        setCanceledOnTouchOutside(false);
        init();
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (AppDifferentiationUtils.isPeiPeiApp()) {
            if (getWindow() != null) {
                WindowManager.LayoutParams lp = getWindow().getAttributes();
                lp.width = ScreenUtils.getScreenWidth();
                getWindow().setAttributes(lp);
            }
        }
    }

    private void init() {
        mBinding = FriendOneKeyAccostDialogBinding.inflate(LayoutInflater.from(mContext));
        setContentView(mBinding.getRoot());
        mAdapter = new FriendOneKeyAccostDialogAdapter(mContext, mDataList);
        mAdapter.register(FriendOneKeyAccostDialogItemBean.class, FriendOneKeyAccostDialogViewHolder.class);
        mBinding.recyclerView.setItemAnimator(null);
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener(new XBaseAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(View view, int position) {
                FriendOneKeyAccostDialogItemBean friendOneKeyAccostDialogItemBean = mDataList.get(position);
                friendOneKeyAccostDialogItemBean.setSelected(!friendOneKeyAccostDialogItemBean.isSelected());
                mAdapter.notifyItemChanged(position);
                for (FriendOneKeyAccostDialogItemBean bean : mDataList) {
                    if (bean.isSelected()) {
                        mBinding.btConfirm.setEnabled(true);
                        return;
                    }
                }
                mBinding.btConfirm.setEnabled(false);
            }
        });
        mBinding.recyclerView.addItemDecoration(new GridDividerItemDecoration(2, SizeUtils.dp2px(16), false));
        if (!AppDifferentiationUtils.isPeiPeiApp()) {
            ViewGroup.LayoutParams layoutParams = mBinding.getRoot().getLayoutParams();
            layoutParams.width = (int) (ScreenUtils.getScreenWidth() * 0.84);
        }
        mBinding.cbFriendOneKeyAccostIgnore.setOnCheckedChangeListener((buttonView, isChecked) ->
                ForumPreferencesUtils.setIgnoreFriendOneKeyAccostDialog(isChecked));

        mBinding.ivClose.setOnClickListener(this::onViewClicked);
        mBinding.btConfirm.setOnClickListener(this::onViewClicked);
    }


    public void show(List<FriendOneKeyAccostDialogItemBean> list, @Nullable ScheduleTimeConfig2 scheduleTimeConfig) {
        if (scheduleTimeConfig != null && scheduleTimeConfig.pass()) {
            //如果等-1或者不是在同一天
            scheduleTimeConfig.saveSelf();
            mDataList.clear();
            mDataList.addAll(list);
            mAdapter.notifyDataSetChanged();
            show();
        }
    }

    public void onViewClicked(View view) {
        int id = view.getId();
        if (id == R.id.ivClose) {
            dismiss();
        } else if (id == R.id.btConfirm) {
            onClickAccost();
        }
    }

    /**
     * 点击搭讪
     */
    private void onClickAccost() {
        accost();
    }


    /**
     * 开始搭讪
     */
    private void accost() {
        ForumHomePageTracker.Companion.getInstance().trackFriendMessage("同城", "一键搭讪","");
        ArrayList<String> list = new ArrayList<>();
        for (FriendOneKeyAccostDialogItemBean bean : mDataList) {
            if (bean != null && bean.isSelected()) {
                list.add(bean.getAccountUuid());
            }
        }
        String[] s = new String[]{};
        String[] uuids = list.toArray(s);
        if (uuids == null || uuids.length == 0) {
            return;
        }
        MessagePinsRouter.messageService().requestAccost(mContext, true, new RequestAccostCallback<List<HBMessageContentDO>>() {
            @Override
            public void onSuccess(@Nullable List<HBMessageContentDO> hbMessageContentDO) {
                dismiss();
            }
        }, uuids);
    }
}
