package com.xmhaibao.friend.adapter;


import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.xmhaibao.forum.api.bean.FriendHomeListBean;
import com.xmhaibao.forum.api.bean.INewFriendListItemType;
import com.xmhaibao.friend.bean.FriendHomeInfoGuideImageBean;
import com.xmhaibao.friend.bean.FriendHomeListBannerBean;
import com.xmhaibao.friend.helper.FriendHomeVoiceCenter;
import com.xmhaibao.friend.interf.IFriendListTrackHelper;
import com.xmhaibao.friend.page.feed.viewholder.FriendFeedCardViewHolder;
import com.xmhaibao.friend.tracker.FriendHomeListTracker;
import com.xmhaibao.friend.viewholder.FriendHomeListInfoBannerViewHolder;
import com.xmhaibao.friend.viewholder.FriendHomeListInfoGuideViewHolder;
import com.xmhaibao.friend.viewholder.FriendHomeListViewNewHolder;

import java.util.List;

import cn.taqu.lib.base.helper.NewLocationHelper;
import cn.taqu.lib.base.holder.EmptyViewHolder;
import hb.utils.CollectionUtils;

/**
 * Created by hqy on 2017/3/7
 * <p>
 * 交友--新朋友列表 底部list适配器
 */

public class FriendHomeListAdapter extends RecyclerView.Adapter implements IFriendListTrackHelper {



    /**
     * 操作类型
     */
    private int mOperateType = 0;


    private final List<INewFriendListItemType> mDataList;
    private final Context mContext;

    /**
     * findFriendFragment的区分用途字段
     */
    private final int mFindFriendType;
    /**
     * 交友首页语音统一管理
     */
    @Nullable
    private final FriendHomeVoiceCenter mFriendHomeVoiceCenter;

    /**
     * 首页定位新方案监听
     */
    private NewLocationHelper.OnNewLocationPermissionListener mNewLocationPermissionListener;

    /**
     * 是否显示feed流卡片新样式
     */
    private boolean showFeedNewUi = false;

    public void setFeedNewUi(boolean showNewUi){
        this.showFeedNewUi = showNewUi;
    }

    public void setNewLocationPermissionListener(NewLocationHelper.OnNewLocationPermissionListener newLocationPermissionListener) {
        mNewLocationPermissionListener = newLocationPermissionListener;
    }

    public FriendHomeListAdapter(Context context, @NonNull List<INewFriendListItemType> userList, int findFriendType,
                                 @Nullable FriendHomeVoiceCenter friendHomeVoiceCenter) {
        super();
        mContext = context;
        mDataList = userList;
        mFindFriendType = findFriendType;
        mFriendHomeVoiceCenter = friendHomeVoiceCenter;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        switch (viewType) {
            case INewFriendListItemType.TYPE_FRIEND_NEW_MALE:
                return new FriendHomeListViewNewHolder(parent, mFindFriendType, mFriendHomeVoiceCenter, mNewLocationPermissionListener,this);
            case INewFriendListItemType.TYPE_FRIEND_FEED_CARD:
                return new FriendFeedCardViewHolder(parent, mFindFriendType,this);
            case INewFriendListItemType.TYPE_GUIDE_IMAGE:
                FriendHomeListInfoGuideViewHolder guideViewHolder = new FriendHomeListInfoGuideViewHolder(parent);
                FriendHomeListTracker.Companion.getInstance().trackJiaoYouBanner("1");
                return guideViewHolder;
            case INewFriendListItemType.TYPE_GUIDE_BANNER:
                return new FriendHomeListInfoBannerViewHolder(parent);
            default:
                return EmptyViewHolder.createViewHolder(parent);
        }
    }




    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        switch (getItemViewType(position)) {
            case INewFriendListItemType.TYPE_FRIEND_NEW_MALE:
                FriendHomeListBean info = (FriendHomeListBean) mDataList.get(position);
                if (info != null) {
                    ((FriendHomeListViewNewHolder) holder).onBindView(info);
                }
                break;
            case INewFriendListItemType.TYPE_FRIEND_FEED_CARD:
                FriendHomeListBean feedInfo = (FriendHomeListBean) mDataList.get(position);
                if (feedInfo != null) {
                    ((FriendFeedCardViewHolder) holder).onBindView(feedInfo);
                }
                break;
            case INewFriendListItemType.TYPE_GUIDE_IMAGE:
                FriendHomeInfoGuideImageBean imgInfo = (FriendHomeInfoGuideImageBean) mDataList.get(position);
                if (imgInfo != null) {
                    ((FriendHomeListInfoGuideViewHolder) holder).onBindView(imgInfo);
                }
                break;
            case INewFriendListItemType.TYPE_GUIDE_BANNER:
                FriendHomeListBannerBean friendHomeListBannerBean = (FriendHomeListBannerBean) mDataList.get(position);
                if (friendHomeListBannerBean != null) {
                    FriendHomeListInfoBannerViewHolder holder1 = (FriendHomeListInfoBannerViewHolder) holder;
                    holder1.onBindView(friendHomeListBannerBean);
                    holder1.updateBannerUiSize(showFeedNewUi);
                }
            default:
                break;
        }
    }


    @Override
    public int getItemViewType(int position) {
        int type = mDataList.get(position).getItemType();
        if (type == INewFriendListItemType.TYPE_GUIDE_IMAGE
                || type == INewFriendListItemType.TYPE_GUIDE_BANNER
                || type == INewFriendListItemType.TYPE_FRIEND_EMPTY) {
            return type;
        }
        if (showFeedNewUi) {
            return INewFriendListItemType.TYPE_FRIEND_FEED_CARD;
        }
        return INewFriendListItemType.TYPE_FRIEND_NEW_MALE;
    }


    @Override
    public int getItemCount() {
        return CollectionUtils.isEmpty(mDataList) ? 0 : mDataList.size();
    }

    /**
     * 更新操作类型
     * @param operateType
     */
    public void updateOperateType(int operateType) {
        this.mOperateType = operateType;
    }

    @NonNull
    @Override
    public String getFriendOperateType() {
        if (mOperateType == IFriendListTrackHelper.FRIEND_LIST_OPERATE_TYPE_REFRESH) {
            return "refresh";
        }
        if (mOperateType == IFriendListTrackHelper.FRIEND_LIST_OPERATE_TYPE_SLIDE) {
            return "slide";
        }
        return "";
    }
}
