package com.xmhaibao.friend.adapter;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.xmhaibao.friend.fragment.FriendHomeNearFragment;
import com.xmhaibao.friend.fragment.FriendHomeSameTownFragment;
import java.util.ArrayList;

/**
 * description:交友首页-FragmentPagerAdapter
 *
 * <AUTHOR>
 * @date 2021/07/30 11:55
 */
public class FriendHomePagerAdapter extends FragmentPagerAdapter {

    private FriendHomeSameTownFragment mFriendHomeSameTownFragment;
    private FriendHomeNearFragment mFriendHomeNearFragment;

    private final ArrayList<String> mTitles;

    public FriendHomePagerAdapter(ArrayList<String> title, FragmentManager fm) {
        super(fm,BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
        mTitles = title;
    }

    @NonNull
    @Override
    public Fragment getItem(int position) {
        //第一个tab
        if (position == 0) {
            if (mFriendHomeSameTownFragment == null) {
                mFriendHomeSameTownFragment = new FriendHomeSameTownFragment();
            }
            return mFriendHomeSameTownFragment;
        } else {
            //第二个tab
            if (mFriendHomeNearFragment == null) {
                mFriendHomeNearFragment = new FriendHomeNearFragment();
            }
            return mFriendHomeNearFragment;
        }
    }

    @Nullable
    public FriendHomeSameTownFragment getFriendHomeSameTownFragment() {
        return mFriendHomeSameTownFragment;
    }

    @Nullable
    public FriendHomeNearFragment getFriendHomeNearFragment() {
        return mFriendHomeNearFragment;
    }

    public void setFriendHomeSameTownFragment(FriendHomeSameTownFragment mFriendHomeSameTownFragment) {
        this.mFriendHomeSameTownFragment = mFriendHomeSameTownFragment;
    }

    public void setFriendHomeNearFragment(FriendHomeNearFragment mFriendHomeNearFragment) {
        this.mFriendHomeNearFragment = mFriendHomeNearFragment;
    }

    @Override
    public int getCount() {
        return mTitles.size();
    }

    @Nullable
    @Override
    public CharSequence getPageTitle(int position) {
        if (position < 0 || position >= mTitles.size()) {
            return "附近～";
        }
        return mTitles.get(position);
    }
}
