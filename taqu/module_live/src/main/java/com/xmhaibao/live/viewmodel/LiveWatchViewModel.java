package com.xmhaibao.live.viewmodel;


import static com.xmhaibao.live.constants.LiveConstants.LIVE_GIFT_PACK_POP_NOTICE_KEY;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.xmhaibao.account.api.router.AccountPinsRouter;
import com.xmhaibao.gift.GiftSceneType;
import com.xmhaibao.live.api.helper.LiveAccountHelper;
import com.xmhaibao.live.api.helper.LiveRoomInfoHelper;
import com.xmhaibao.live.constants.LiveConstants;
import com.xmhaibao.live.dresswatch.model.EventMsgRemindBadgeHall;
import com.xmhaibao.live.helper.LiveGuiDanceHelper;
import com.xmhaibao.live.living.activity.LiveBaseActivity;
import com.xmhaibao.live.living.common.ask.model.EventLiveMsgChat;
import com.xmhaibao.live.living.common.gift.model.LiveGiftPackBean;
import com.xmhaibao.live.living.common.hotrank.repository.LiveHotRankRepository;
import com.xmhaibao.live.living.common.pklevel.bean.EventMsgWelcomeBean;
import com.xmhaibao.live.living.common.pklevel.bean.LivePkLevelRoomInfo;
import com.xmhaibao.live.living.common.pklevel.repository.LivePkLevelRepository;
import com.xmhaibao.live.living.common.qnrtc.model.LiveH2aInfo;
import com.xmhaibao.live.living.common.qnrtc.repository.LiveQnRtcRepository;
import com.xmhaibao.live.living.watch.alliance.LiveAllianceListDialog;
import com.xmhaibao.live.living.watch.alliance.LiveAllianceMainDialog;
import com.xmhaibao.live.living.watch.alliance.model.LiveAllianceInfoBean;
import com.xmhaibao.live.living.watch.alliance.repository.LiveAllianceRepository;
import com.xmhaibao.live.model.LiveAccountNotice;
import com.xmhaibao.live.model.LiveRecentMessageBean;
import com.xmhaibao.live.model.LiveRecentMsgAndNoticeBean;
import com.xmhaibao.live.model.Notice;
import com.xmhaibao.live.repository.LiveRepository;
import com.xmhaibao.live.shell.model.LiveShellStatus;
import com.xmhaibao.live.shell.repository.LiveShellRepository;
import com.xmhaibao.live.utils.LiveUtil;

import cn.taqu.lib.okhttp.callback.GsonCallBack;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import hb.common.data.AccountHelper;
import hb.utils.ActivityUtils;
import hb.utils.TimeUtils;
import hb.utils.kvcache.KVCacheUtils;
import hb.xrequest.RequestCallback;
import hb.xtoast.XToastUtils;

/**
 * 观众端ViewModel
 *
 * <AUTHOR>
 * @date 2020-12-22
 */
public class LiveWatchViewModel extends ViewModel {
    /**
     * 礼物背包过期提醒data
     */
    private final MutableLiveData<String> mGiftPackNoticeData = new MutableLiveData<>();

    public static LiveWatchViewModel getInstance(Context context) {
        return new ViewModelProvider((FragmentActivity) ActivityUtils.getActivity(context)).get(LiveWatchViewModel.class);
    }

    /**
     * 礼物背包过期提醒data
     *
     * @return
     */
    public MutableLiveData<String> getGiftPackNoticeData() {
        return mGiftPackNoticeData;
    }

    /**
     * 获取背包礼物信息
     */
    public void requestGiftPackInfo() {
        LiveRepository.getGiftBalancePackList("1", GiftSceneType.TYPE_LIVE, "1")
                .execute(new GsonCallBack<LiveGiftPackBean>() {
                    @Override
                    public void onSuccess(boolean isFromCache, @Nullable LiveGiftPackBean packBean, @NonNull IResponseInfo response) {
                        if (packBean != null) {
                            mGiftPackNoticeData.postValue(packBean.getWaitingNotice());
                        }
                    }

                    @Override
                    public void onFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {

                    }
                });
    }

    /**
     * 获取背包礼物过期信息
     */
    public void requestGiftExpirationNotice() {
        long noticeTime = KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).getLong(LIVE_GIFT_PACK_POP_NOTICE_KEY, 0L);
        if (TimeUtils.isToday(noticeTime)) {
            return;
        }
        LiveRepository.getGiftBalancePackList("1", GiftSceneType.TYPE_LIVE, "1")
                .execute(new GsonCallBack<LiveGiftPackBean>() {
                    @Override
                    public void onSuccess(boolean isFromCache, @Nullable LiveGiftPackBean packBean, @NonNull IResponseInfo response) {
                        if (packBean != null && !TextUtils.isEmpty(packBean.getExpirationNotice())) {
                            LiveGuiDanceHelper.getInstance().putGuiDanceId(LiveGuiDanceHelper.GUI_GIFT_EXPRI_NOTICE, packBean.getExpirationNotice());
                        }
                    }

                    @Override
                    public void onFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {

                    }
                });
    }

    /**
     * 获取连麦信息
     * @param msg
     * @param hostUuid
     */
    public void getConferenceInfo(String msg, String hostUuid) {
        LiveQnRtcRepository.Companion.getMeetingUuid(hostUuid)
                .execute(new RequestCallback<LiveH2aInfo>() {
                    @Override
                    public void onSuccess(boolean isCache, LiveH2aInfo obj) {
                        if (obj != null) {
                            if (TextUtils.equals(AccountHelper.getAccountUuid(), obj.getUuid())) {
                                XToastUtils.show(msg, Toast.LENGTH_LONG);
                            }
                        }
                    }

                    @Override
                    public void onFailure(int code, String responseStatus, String responseMsg) {
                    }
                });
    }

    /**
     * 上报直播间停留300秒用户
     */
    private String preHostUuid;
    public void submitWatchFiveMinutes(long time, String hostUuid) {
        if (time < 300) {
            return;
        }
        if (!TextUtils.equals(preHostUuid, hostUuid)) {
            preHostUuid = hostUuid;
            LiveHotRankRepository.Companion.reportUsersRetention(hostUuid).execute(null);
        }
    }

    /**
     * 上报观看时长
     *
     */
    public void submitWatchTime(String hostUuid) {
        // 每分钟上报一次
        LiveRepository.submitWatchTime(hostUuid).execute(null);
    }

    /**
     * 获取最近十分钟数据
     */
    public void getRoomRecentMessage() {
        LiveRepository.getRoomRecentMessage(LiveAccountHelper.getInstance().getHostUuid())
                .execute(new GsonCallBack<LiveRecentMsgAndNoticeBean>() {

                    @Override
                    public void onSuccess(boolean isCache, @Nullable LiveRecentMsgAndNoticeBean recentMsgAndNoticeBean,
                                          @NonNull IResponseInfo response) {
                        if (recentMsgAndNoticeBean == null) {
                            return;
                        }
                        Activity topActivity = ActivityUtils.getTopActivity();
                        LiveBaseActivity tempLiveBaseActivity = null;
                        if (topActivity instanceof LiveBaseActivity) {
                            tempLiveBaseActivity = (LiveBaseActivity) topActivity;
                            if (tempLiveBaseActivity.mMsgRecycler != null) {
                                tempLiveBaseActivity.mMsgRecycler.initAutoScroll();
                            }
                        }
                        // 优先加载历史消息
                        if (recentMsgAndNoticeBean.getRecentMsgList() != null && !recentMsgAndNoticeBean.getRecentMsgList().isEmpty()) {
                            for (LiveRecentMessageBean bean : recentMsgAndNoticeBean.getRecentMsgList()) {
                                EventLiveMsgChat chat = bean.converToLiveMsgChat();
                                if (tempLiveBaseActivity != null) {
                                    tempLiveBaseActivity.addMsg(chat);
                                }
                            }
                        }
                        // 然后加载系统消息
                        if (recentMsgAndNoticeBean.getNoticeList() != null && !recentMsgAndNoticeBean.getNoticeList().isEmpty()) {
                            for (Notice bean : recentMsgAndNoticeBean.getNoticeList()) {
                                if (tempLiveBaseActivity != null) {
                                    tempLiveBaseActivity.addMsg(bean.conver2LiveMsgSystemBroadcast());
                                }
                            }
                        }

                        if (tempLiveBaseActivity != null && tempLiveBaseActivity.mMsgRecycler != null) {
                            tempLiveBaseActivity.mMsgRecycler.startAutoScroll();
                        }
                    }

                    @Override
                    public void onFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {

                    }
                });
    }

    /**
     * 获取联盟信息
     */
    public void requestAllianceInfo() {
        LiveAllianceRepository.Companion.allianceGetAllianceInfo(AccountHelper.getTicketId())
                .execute(new GsonCallBack<LiveAllianceInfoBean>() {
                    @Override
                    public void onSuccess(boolean isFromCache, @Nullable LiveAllianceInfoBean obj, @NonNull IResponseInfo response) {
                        //createFlag = obj.getFamilyStatus();
                        if (obj != null) {
                            showAllianceInfoDialog(obj);
                        }
                    }

                    @Override
                    public void onFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {
                        if ("not_alliance".equals(response.getResponseStatus())) {
                            showAllianceInfoDialog(null);
                        }
                    }
                });
    }

    /**
     * 显示联盟弹窗
     *
     * @param liveAllianceInfoBean 信息
     */
    private void showAllianceInfoDialog(LiveAllianceInfoBean liveAllianceInfoBean) {
        if (!AccountHelper.isUserLogined()) {
            AccountPinsRouter.accountService().launchLogin();
            return;
        }
        LiveBaseActivity activity = LiveUtil.getLiveActivity();
        if (!ActivityUtils.isActivityAlive(activity)) {
            return;
        }
        if (liveAllianceInfoBean != null) {
            LiveAllianceMainDialog liveAllianceMainDialog = LiveAllianceMainDialog.newInstance(activity.getHost_uuid(), liveAllianceInfoBean);
            liveAllianceMainDialog.showDialog(activity.getSupportFragmentManager().beginTransaction());
            activity.registerDialog(liveAllianceMainDialog);
        } else {
            LiveAllianceListDialog liveAllianceMainDialog = LiveAllianceListDialog.newInstance(activity.getHost_uuid());
            liveAllianceMainDialog.showDialog(activity.getSupportFragmentManager().beginTransaction());
            activity.registerDialog(liveAllianceMainDialog);
        }
    }

    /**
     * 获取用户2分钟一次的提示信息
     */
    public void requestAccountNotice() {
        LiveRepository.getAccountNotice()
                .execute(new GsonCallBack<LiveAccountNotice>() {
                    @Override
                    public void onSuccess(boolean isCache, @Nullable LiveAccountNotice obj, @NonNull IResponseInfo response) {
                        if (obj != null && obj.getTitle() != null) {
                            LiveBaseActivity activity = LiveUtil.getLiveActivity();
                            if (!ActivityUtils.isActivityAlive(activity)) {
                                return;
                            }
                            activity.addMsg(new EventMsgRemindBadgeHall(obj.getTitle(), obj.getRelation()));
                        }
                    }

                    @Override
                    public void onFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {

                    }
                });
    }

    /**
     * 欢迎语data
     */
    private final MutableLiveData<EventMsgWelcomeBean> mWelcomeBeanData = new MutableLiveData<>();

    public MutableLiveData<EventMsgWelcomeBean> getWelcomeBeanData() {
        return mWelcomeBeanData;
    }


    private String prePkLevelHost = null;
    /**
     * 获取主播的pk段位信息
     *
     * @param hostUuid 主播uuid
     */
    public void requestHostPkLevelInfo(String hostUuid) {
        if (TextUtils.isEmpty(hostUuid)) {
            return;
        }
        if (TextUtils.equals(prePkLevelHost, hostUuid)) {
            return;
        }
        prePkLevelHost = hostUuid;
        long preTime = KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).getLong(LiveConstants.WELCOME_TIME + prePkLevelHost, 0L);
        int count = 0;
        if (TimeUtils.isToday(preTime)) {
            count = KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).getInt(LiveConstants.WELCOME_COUNT + prePkLevelHost, 0);
            if (count > 2) {
                return;
            }
        }
        int newCount = count + 1;
        new LivePkLevelRepository().getRoomRankInfo(hostUuid).execute(new GsonCallBack<LivePkLevelRoomInfo>() {
            @Override
            public void onSuccess(boolean isCache, @Nullable LivePkLevelRoomInfo bean, @NonNull IResponseInfo response) {
                if (bean != null) {
                    if (TextUtils.isEmpty(bean.getWelcomeText())) {
                        return;
                    }
                    KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).putLong(LiveConstants.WELCOME_TIME + prePkLevelHost, preTime);
                    KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).putInt(LiveConstants.WELCOME_COUNT + prePkLevelHost, newCount);
                    EventMsgWelcomeBean welcomeBean = new EventMsgWelcomeBean();
                    welcomeBean.setPkLevelName(bean.getPkLevelName());
                    welcomeBean.setPkLevelIcon(bean.getPkLevelIcon());
                    welcomeBean.setPkLevelColor(bean.getPkLevelColor());
                    welcomeBean.setWelcomeHint(bean.getWelcomeText());
                    mWelcomeBeanData.setValue(welcomeBean);
                }
            }

            @Override
            public void onFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {

            }
        });
    }

    public void requestShellStatus() {
        new LiveShellRepository().getShellCloseStatus().execute(new GsonCallBack<LiveShellStatus>() {
            @Override
            public void onSuccess(boolean isCache, @Nullable LiveShellStatus status, @NonNull IResponseInfo response) {
                if (status != null) {
                    LiveRoomInfoHelper.getInstance().setShellStatus(status.isClose());
                }
            }

            @Override
            public void onFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {

            }
        });
    }

}
