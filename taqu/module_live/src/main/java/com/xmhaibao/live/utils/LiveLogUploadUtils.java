package com.xmhaibao.live.utils;

import hb.logupload.XLogUpload;

/**
 * <AUTHOR>
 * @date 2020/9/21
 * @desc
 */
public class LiveLogUploadUtils {
    private static final String TYPE_PLAYERROR = "Monitor_Live_FaceUPlayError";

    /**
     * 控件为空上报
     */
    public static void keyboardChangeErrorUpload(String name) {
        XLogUpload.newBuilder("LiveKeyboardChange").params("NullPointerException", name).post();
    }

    /**
     * 资源更新失败
     */
    public static void upDateErrorUpload(String content) {
        XLogUpload.newBuilder("Monitor_Live_ResUpdateError")
                .params("msg", "更新资源失败，观众在直播间里送礼可能会看不见相应的效果")
                .params("content", content)
                .post();
    }


    /**
     * 直播美图贴纸文件不存在
     */
    public static void faceUPlayErrorUpload(String giftId, String effectId) {
        XLogUpload.newBuilder(TYPE_PLAYERROR)
                .text1("无效贴纸资源")
                .params("giftId", giftId)
                .params("effectId",effectId)
                .post();
    }

    /**
     * 直播贴纸文件不存在
     */
    public static void faceUPlayErrorUpload(String name) {
        XLogUpload.newBuilder(TYPE_PLAYERROR)
                .text1("文件检测异常")
                .params("content", name + " 本地资源不存在 请求下载").post();
    }

    /**
     * 直播贴纸文件不存在
     */
    public static void faceUFileNotExit(String path) {
        XLogUpload.newBuilder(TYPE_PLAYERROR)
                .text1("文件播放异常")
                .params("filePath", path)
                .post();
    }


    /**
     * 声网错误回调信息上报
     */
    public static void agoraLiveErrorLogUpload(boolean isFirst, int errorCode, int ownerUid, String channelName, String channelToken) {
        XLogUpload.newBuilder("Monitor_Live_AgoraError")
                .text1(isFirst ? "首次出现" : "重复上报")
                .text2(String.valueOf(errorCode))
                .params("time",String.valueOf(System.currentTimeMillis()))
                .params("ownerUid", String.valueOf(ownerUid))
                .params("channelName", channelName)
                .params("channelToken", channelToken)
                .post();
    }

    /**
     * 声网错误回调信息上报
     */
    public static void agoraLiveRtmpErrorLogUpload(int state, int errorCode, int ownerUid, String channelName, String channelToken,String publishUrl) {
        XLogUpload.newBuilder("Monitor_Live_AgoraRtmpError")
                .text1(String.valueOf(state))
                .text2(String.valueOf(errorCode))
                .params("time",String.valueOf(System.currentTimeMillis()))
                .params("ownerUid", String.valueOf(ownerUid))
                .params("channelName", channelName)
                .params("channelToken", channelToken)
                .params("publishUrl", publishUrl)
                .post();
    }

    /**
     * 声网错误回调信息上报
     */
    public static void agoraLiveMediaRelayError(int state, int errorCode) {
        XLogUpload.newBuilder("Monitor_Live_AgoraMediaRelayError")
                .params("time",String.valueOf(System.currentTimeMillis()))
                .text1(String.valueOf(state))
                .text2(String.valueOf(errorCode))
                .post();
    }

    /**
     * 声网日志回调信息上报
     */
    public static void agoraLiveWarnLogUpload(String key, String description,  int ownerUid, String channelName, String channelToken) {
        XLogUpload.newBuilder("AgoraLiveWarn")
                .params("time",String.valueOf(System.currentTimeMillis()))
                .params("key", "【 " + key + "】")
                .params("description", description)
                .params("ownerUid", String.valueOf(ownerUid))
                .params("channelName", channelName)
                .params("channelToken", channelToken)
                .post();
    }

    /**
     * 声网操作回调信息上报
     */
    public static void agoraLiveInfoLogUpload(String key, String description,  int ownerUid, String channelName, String channelToken) {
        XLogUpload.newBuilder("AgoraLiveInfo")
                .params("time",String.valueOf(System.currentTimeMillis()))
                .params("key", "【 " + key + "】")
                .params("description", description)
                .params("ownerUid", String.valueOf(ownerUid))
                .params("channelName", channelName)
                .params("channelToken", channelToken)
                .post();
    }

    /**
     * 声网错误日志文件上报
     */
    public static void rtcLogFileUpload(String fileUrl) {
        XLogUpload.newBuilder("AgoraRTCLogFile")
                .params("msg", "日志文件上报成功")
                .params("file_url", fileUrl)
                .post();
    }

    /**
     * 上报主播操作
     *
     * @param opUuid   对方主播uuid
     * @param position 来源位置
     */
    public static void hostAgoraOptions(String opUuid, String position) {
        XLogUpload.newBuilder("Live_Agora_Options")
                .params("time", String.valueOf(System.currentTimeMillis()))
                .params("opUuid", opUuid)
                .params("position", position)
                .post();
    }

    /**
     * 更新标题 错误上报
     *
     * @param status 错误码
     * @param msg    msg
     */
    public static void hostUpdateTitleError(String status, String msg) {
        XLogUpload.newBuilder("live_update_title_error")
                .params("time", String.valueOf(System.currentTimeMillis()))
                .params("status", status)
                .params("msg", msg)
                .post();
    }

    /**
     * 上报主播操作
     *
     * @param state   对方主播uuid
     * @param reason 来源位置
     */
    public static void musicError(String state, String reason) {
        XLogUpload.newBuilder("Monitor_Live_KgMusicError")
                .params("time", String.valueOf(System.currentTimeMillis()))
                .params("state", state)
                .params("reason", reason)
                .post();
    }

    /**
     * 声网日志回调信息上报
     */
    public static void videoBitrateLogUpload(int videoBitrate) {
        XLogUpload.newBuilder("Monitor_Live_Agora_VideoBitrate")
                .num1(videoBitrate)
                .post();
    }
}
