package com.xmhaibao.live.living.watch.watchguide.view

import android.animation.Animator
import android.animation.AnimatorSet
import android.view.ViewGroup
import android.widget.RelativeLayout
import com.xmhaibao.gift.effect.LiveGiftBaseBuilder
import com.xmhaibao.live.R
import com.xmhaibao.live.constants.LiveConstants
import com.xmhaibao.live.databinding.LiveGuideGameEnterBuilderBinding
import com.xmhaibao.live.living.activity.LiveWatchActivity
import com.xmhaibao.live.living.watch.bottommenu.model.LivePlayFeatureItemBean
import com.xmhaibao.live.living.watch.bottommenu.model.LiveWatchBottomMenuItem
import hb.common.data.AccountHelper
import hb.utils.Loger
import hb.utils.SizeUtils
import hb.utils.kvcache.KVCacheUtils

/**
 * 玩法入口优化 气泡引导
 *
 * <AUTHOR>
 * @date 2023/12/24
 */
class LiveGuideGameEnterBuilder (viewGroup: ViewGroup) : LiveGiftBaseBuilder(viewGroup, 3 * 1000) {

    private lateinit var mBinding: LiveGuideGameEnterBuilderBinding
    private var mAnimatorSet: AnimatorSet? = null
    private var mRightSize = 0
    private var mItemBean: LivePlayFeatureItemBean? = null

    override fun onCreate() {
        setContentView(R.layout.live_guide_game_enter_builder)
        mBinding = LiveGuideGameEnterBuilderBinding.bind(contentView)
        initAnimation()
    }

    /**
     * 初始化动画
     */
    private fun initAnimation() {
        mAnimatorSet = AnimatorSet()
        val fadeIn: Animator = createFadeIn(200)
        val fadeOut: Animator = createFadeOut(200)
        fadeOut.startDelay = 2800
        mAnimatorSet?.playTogether(fadeIn, fadeOut)
    }

    override fun setRightSize(rightSize: Int) {
        super.setRightSize(rightSize)
        this.mRightSize = rightSize

    }

    override fun setData(`object`: Any?) {
        super.setData(`object`)
        if (`object` is LiveWatchBottomMenuItem) {
            this.mItemBean = `object`
            setRightMargin()
        }
    }

    override fun setRightMargin() {
        super.setRightMargin()
        mItemBean?.run {
            if (context is LiveWatchActivity) {
                mRightSize =
                    (context as LiveWatchActivity).mBottomMenuLayout.getRightMenuVisibleSize(action) - 1
            }
            val rightMargin: Int = SizeUtils.dp2px(40f) * mRightSize
            if (rightMargin > 0) {
                val params = mBinding.ivIcon.layoutParams as RelativeLayout.LayoutParams
                params.rightMargin = rightMargin
                mBinding.ivIcon.layoutParams = params
            }
            KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
                .putLong(LiveConstants.LIVE_GAME_PLAY_GUI_TIME, System.currentTimeMillis())

            val showingCount =
                KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).getInt(
                    LiveConstants.LIVE_GAME_PLAY_GUI_COUNT,
                    0
                )
            KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).putInt(
                LiveConstants.LIVE_GAME_PLAY_GUI_COUNT,
                showingCount + 1
            )
        }
    }

    override fun onAnimationStart() {
        startAnim(mAnimatorSet)
    }

    override fun onAnimationEnd() {
        cancelAnim(mAnimatorSet)
    }

    override fun onDestroy() {
        super.onDestroy()
        cancelAnim(mAnimatorSet)
    }
}