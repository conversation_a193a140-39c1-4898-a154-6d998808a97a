package com.xmhaibao.live.widget;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.xmhaibao.live.R;

public class LightFlightLayout extends FrameLayout {
    private int mW;
    private int mH;
    ImageView imageView;
    ObjectAnimator objectAnimator;

    public LightFlightLayout(Context context) {
        super(context);
        initView(context, null);
    }

    public LightFlightLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView(context, attrs);
    }

    public LightFlightLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context, attrs);
    }

    private void initView(Context context, AttributeSet attrs) {

    }


    public void startAnim() {
        imageView.setVisibility(VISIBLE);
        objectAnimator = ObjectAnimator.ofFloat(imageView, View.X, 0, mW==0?800:mW);
        objectAnimator.setDuration(2000);
        objectAnimator.start();
        objectAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                cancelAnim();
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
    }

    public void cancelAnim() {
        if (objectAnimator == null) {
            return;
        }
        imageView.setVisibility(INVISIBLE);
        objectAnimator.cancel();
        objectAnimator = null;
    }

    @Override
    protected int getSuggestedMinimumHeight() {
        return 0;
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mW = w;
        mH = h;
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        imageView = findViewById(R.id.ivLight);
        //setBackgroundResource(R.drawable.live_msg_world_bg_shape_light);
    }
}