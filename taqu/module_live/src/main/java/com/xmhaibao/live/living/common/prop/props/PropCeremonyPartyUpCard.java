package com.xmhaibao.live.living.common.prop.props;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.xmhaibao.live.living.common.prop.helper.LivePropsModel;
import com.xmhaibao.live.living.common.prop.model.LivePropNumInfo;
import com.xmhaibao.prop.interf.IPropAction;
import com.xmhaibao.prop.interf.IPropActivity;
import com.xmhaibao.prop.interf.IPropView;
import com.xmhaibao.prop.model.PropInfo;

import cn.taqu.lib.okhttp.callback.GsonCallBack;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import hb.xstyle.xdialog.XLoadingDialog;
import hb.xtoast.XToastUtils;

/**
 * <AUTHOR>
 * @date 2021/10/29
 * @desc 年度盛典 金银票
 */
public class PropCeremonyPartyUpCard  implements IPropAction {

    /**
     * 使用道具
     *
     * @param view
     * @param info
     */
    @Override
    public void useProp(IPropView view, final PropInfo info, IPropActivity activity) {
        if (view == null || activity == null) {
            return;
        }

        LivePropsModel.getInstance().usePropForPkVideo(info.getPropId(), info.getPropCode(), activity.doGetHostUuid(), new GsonCallBack<LivePropNumInfo>() {
            @Override
            public void onSuccess(boolean isCache, @Nullable LivePropNumInfo obj, @NonNull IResponseInfo response) {
                XLoadingDialog.hideLoadingbar();
                //成功后提醒数据层已经使用了某个道具
                view.notifyList(info);
                view.hideDetail();
            }

            @Override
            public void onFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {
                XLoadingDialog.hideLoadingbar();
                view.hideDetail();
                XToastUtils.show(response.getResponseMsg());
            }
        });

    }
}