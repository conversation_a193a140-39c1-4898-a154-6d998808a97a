package com.xmhaibao.live.living.common.activities.qixi.holder

import android.annotation.SuppressLint
import android.view.View
import android.view.ViewGroup
import com.xmhaibao.live.R
import com.xmhaibao.live.databinding.LiveQixiRewardItemHolderLayoutBinding
import com.xmhaibao.live.living.common.activities.qixi.model.LiveQiXiRainRewardItem
import com.xmhaibao.live.utils.LiveUtil
import com.xmhaibao.shell.helper.ShellBalanceHelper
import hb.utils.StringUtils
import hb.xadapter.XBaseViewHolder

/**
 * 七夕鹊桥会 红包雨奖励结果
 *
 * <AUTHOR>
 * @date 2024/7/8
 */
class LiveQiXiRewardItemHolder(parent: ViewGroup) :
    XBaseViewHolder<LiveQiXiRainRewardItem>(
        parent,
        R.layout.live_qixi_reward_item_holder_layout
    ) {
    private val mBinding = LiveQixiRewardItemHolderLayoutBinding.bind(itemView)

    @SuppressLint("SetTextI18n")
    override fun onBindView(item: LiveQiXiRainRewardItem?) {
        mBinding.apply {
            item?.apply {
                if (isSign()) {
                    // 签语
                    tvMessage.text = "签语：$name"
                } else {
                    val realName = LiveUtil.dealMaxLenth(name, 16)
                    val price = StringUtils.stringToInt(value)
                    if (price > 0) {
                        ivBean.setImageResource(ShellBalanceHelper.shellIcon())
                        groupValue.visibility = View.VISIBLE
                        tvGiftPrice.text = value
                    } else {
                        groupValue.visibility = View.GONE
                    }
                    tvMessage.text = realName + "x" + num
                }
            }
        }
    }
}