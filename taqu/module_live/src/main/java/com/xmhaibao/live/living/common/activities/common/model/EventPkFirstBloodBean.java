package com.xmhaibao.live.living.common.activities.common.model;

import com.xmhaibao.live.living.common.activities.pkfreedom.helper.LivePkFreedomHelper;

import cn.taqu.lib.base.im.LiveIMLogHelper;
import hb.common.helper.HostHelper;
import hb.qim.base.core.IResponseParse;
import hb.utils.EventBusUtils;
import hb.utils.WebpUtils;

/**
 * PK首杀
 *
 * <AUTHOR>
 * @date 2021-08-04
 */
public class EventPkFirstBloodBean implements IResponseParse<EventPkFirstBloodBean> {

    private String uuid;
    private String avatar;
    private String magnification;
    private String activityType;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getMagnification() {
        return magnification;
    }

    public void setMagnification(String magnification) {
        this.magnification = magnification;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    @Override
    public EventPkFirstBloodBean parseResponse(String event, Object... args) {
        if (args.length >= 4) {
            LiveIMLogHelper.getInstance().logTag(LivePkFreedomHelper.TAG, event, args);
            setUuid(String.valueOf(args[0]));
            setAvatar(WebpUtils.getWebpUrl_2_1(String.valueOf(args[1]), HostHelper.getInstance().getHostAvatar()));
            setMagnification(String.valueOf(args[2]));
            setActivityType(String.valueOf(args[3]));
            EventBusUtils.post(this);
        }
        return this;
    }
}
