package com.xmhaibao.live.living.common.hostSign.repository

import cn.taqu.lib.base.api.UrlBase
import com.xmhaibao.live.api.helper.LiveRoomInfoHelper
import com.xmhaibao.live.living.common.hostSign.model.*
import hb.common.xstatic.HttpParams
import hb.xrequest.XRequest

/**
 * 类描述：主播签到-他趣模范生，网络请求管理
 *
 * <AUTHOR>
 * @date 2023-03-13
 */
object LiveHostSignRepository {
    /**
     * 获取签到相关信息
     */
    private val REQUEST_GET_HOST_SIGN_INFO = "${UrlBase.API_LIVE}/HostSign/getInfo"
    fun requestHostSignInfo(type: String): XRequest<LiveHostSignInfoBean> {
        HttpParams.newBuilder().get(REQUEST_GET_HOST_SIGN_INFO)
            .params("host_uuid", LiveRoomInfoHelper.getInstance().hostUuid)
            .params("type", type)
            .needTicketId(true)
            .build().apply {
                return XRequest.newRequest(this)
            }
    }

    /**
     * 获取榜单相关信息
     */
    private val REQUEST_GET_HOST_RANK_INFO = "${UrlBase.API_LIVE}/HostSign/getRankInfo"
    fun requestHostRankInfo(isYesterday: Boolean): XRequest<LiveHostRankInfoBean> {
        HttpParams.newBuilder().get(REQUEST_GET_HOST_RANK_INFO)
            .params("host_uuid", LiveRoomInfoHelper.getInstance().hostUuid)
            .params("type", if (isYesterday) "2" else "1") // type :1-今日；2-昨日
            .build().apply {
                return XRequest.newRequest(this)
            }
    }

    /**
     * 补签（普通/超级）
     */
    private val REQUEST_HOST_COMPLEMENT_SIGNED = "${UrlBase.API_LIVE}/HostSign/ComplementSigned"

    /**
     * 超级签到
     */
    private val REQUEST_HOST_SUPER_SIGNED = "${UrlBase.API_LIVE}/HostSign/superSign"
    fun requestHostSign(item: CalendarItem?, superSign: Boolean): XRequest<LiveHostReSignBean> {
        val url = if (superSign) {
            if (item?.status == HostSignStatus.HostSignResign.value) REQUEST_HOST_COMPLEMENT_SIGNED
            else REQUEST_HOST_SUPER_SIGNED
        } else {
            REQUEST_HOST_COMPLEMENT_SIGNED
        }

        HttpParams.newBuilder()
            .post(url)
            .params("date", item?.date)
            .params("type", if (superSign) "2" else "1")
            .needTicketId(true)
            .build().apply {
                return XRequest.newRequest(this)
            }
    }

    /**
     * 模范生日历
     */
    private val REQUEST_HOST_CALENDAR_INFO = "${UrlBase.API_LIVE}/HostSign/getCalendarInfo"
    fun requestCalendarInfo(year: Int, month: Int, type: String): XRequest<CalendarInfo> {
        HttpParams.newBuilder().get(REQUEST_HOST_CALENDAR_INFO)
            .params("host_uuid", LiveRoomInfoHelper.getInstance().hostUuid)
            .params("year", year.toString())
            .params("month", month.toString())
            .params("type", type)
            .needTicketId(true)
            .build().apply {
                return XRequest.newRequest(this)
            }
    }
}