package com.xmhaibao.live.model.event;

import com.xmhaibao.live.R;
import com.xmhaibao.live.event.EventMsgGuide;

/**
 * @auther <PERSON><PERSON><PERSON><PERSON>
 * @date 2020/4/30
 * @desc
 */
public class EventMsgReminWedding extends EventMsgGuide {

    private String hostName;
    private String hostUuid;

    public EventMsgReminWedding(String senderName, String hostName, String suffix, String hostUuid) {
        setIcon(com.xmhaibao.gift.R.drawable.gift_wedding_im_icon);
        setTitle(String.format(suffix, senderName, hostName));
        setHostName(hostName);
        setHostUuid(hostUuid);
    }

    public String getHostUuid() {
        return hostUuid;
    }

    private void setHostUuid(String hostUuid) {
        this.hostUuid = hostUuid;
    }

    public String getHostName() {
        return hostName;
    }

    private void setHostName(String hostName) {
        this.hostName = hostName;
    }
}
