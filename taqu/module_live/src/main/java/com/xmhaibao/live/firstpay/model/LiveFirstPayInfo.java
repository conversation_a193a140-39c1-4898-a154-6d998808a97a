package com.xmhaibao.live.firstpay.model;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.taqu.lib.okhttp.model.IDoExtra;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import hb.common.helper.HostHelper;
import hb.utils.WebpUtils;

/**
 * @auther SuZhanFeng
 * @date 2020/7/9
 * @desc
 */
public class LiveFirstPayInfo implements IDoExtra {
    @SerializedName("left_time")
    private long leftTime;
    @SerializedName("left_time_str")
    private String leftTimeStr;
    private List<LiveFirstPayItem> list;
    @SerializedName("is_display")
    private String isDisplay;

    public boolean isDisplay() {
        return "1".equals(isDisplay);
    }

    public void setIsDisplay(String isDisplay) {
        this.isDisplay = isDisplay;
    }

    public long getLeftTime() {
        return leftTime;
    }

    public void setLeftTime(long leftTime) {
        this.leftTime = leftTime;
    }

    public String getLeftTimeStr() {
        return leftTimeStr;
    }

    public void setLeftTimeStr(String leftTimeStr) {
        this.leftTimeStr = leftTimeStr;
    }

    public List<LiveFirstPayItem> getList() {
        return list;
    }

    public void setList(List<LiveFirstPayItem> list) {
        this.list = list;
    }

    @Override
    public void doExtra(IResponseInfo response) {
        if (list != null && !list.isEmpty()) {
            Pattern pattern = Pattern.compile("(?<=#).*(?=#)");
            for (LiveFirstPayItem item : list) {
                if (!TextUtils.isEmpty(item.getDesc())) {
                    Matcher matcher = pattern.matcher(item.getDesc());
                    if (matcher.find()) {
                        item.setSpecialStr(matcher.group());
                    }
                    item.setDesc(item.getDesc().replace(String.format("#%s#", item.getSpecialStr()), item.getSpecialStr()));
                }
                if (item.getPrizeList() != null && !item.getPrizeList().isEmpty()) {
                    for (LiveFirstPayAwardItem awardItem : item.getPrizeList()) {
                        awardItem.setIcon(WebpUtils.getWebpUrl(awardItem.getIcon(), HostHelper.getInstance().getHostImg()));
                    }
                }
            }
        }
    }
}
