package com.xmhaibao.live.fanschat.utils

import hb.common.data.AccountHelper
import hb.utils.kvcache.KVCacheUtils
import java.util.*

/**
 * 粉丝群相关sp 引导缓存
 *  key 简写 : fgc_xxxx
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
object LiveFansGroupChatSpUtils {

    /**
     * 粉丝群，开播创建引导
     */
    fun setCreateFansGroupChatGuide(count: Int) {
        KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
            .putInt("fgc_create_guide", count)
    }

    fun getCreateFansGroupChatGuide(): Int {
        return KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
            .getInt("fgc_create_guide", 0)
    }

    /**
     * 下播小结，粉丝群入口引导，一天只引导一
     * @param guideDay 引导时间
     */
    fun setLiveEndFansGroupChatGuideDay(guideDay: Int) {
        KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
            .putInt("fgc_live_end_guide_day", guideDay)
    }

    /**
     * 下播小结，粉丝群入口引导，历史最多引导三次
     * @param guideDay 引导时间，如果大于
     */
    fun setLiveEndFansGroupChatGuide(guideDay: Int) {
        KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
            .putInt("fgc_live_end_guide_count", guideDay)
    }


    /**
     * 下播小结，粉丝群入口引导，历史最多引导三次
     * @return 当前引导次数
     */
    fun getLiveEndFansGroupChatGuide(): Int {
        return KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
            .getInt("fgc_live_end_guide_count", 0)
    }


    /**
     * 下播小结，粉丝群入口引导，一天只引导一次，历史最多三次
     * @return true 需要引导
     */
    fun isNeedLiveEndFansGroupChatGuide(guideCount: Int): Boolean {
        if (guideCount >= 3) {
            return false
        }
        //当前时间不相等，引导
        val dayOfYear = Calendar.getInstance()[Calendar.DAY_OF_YEAR]
        val cacheDay = KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
            .getInt("fgc_live_end_guide_day", 0)

        return dayOfYear != cacheDay
    }

    /**
     * 保存粉丝群列表已选中的项
     */
    fun saveGroupChatSelectData(hostUuid: String?, groupUuid: String?) {
        if (hostUuid.isNullOrEmpty()) return
        KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
            .putString("fgc_live_select_${hostUuid}", groupUuid)
    }

    /**
     * 获取粉丝群列表已选中的项
     */
    fun getGroupChatSelectData(hostUuid: String?): String? {
        if (hostUuid.isNullOrEmpty()) return ""
        return KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid())
            .getString("fgc_live_select_${hostUuid}", "")
    }

}