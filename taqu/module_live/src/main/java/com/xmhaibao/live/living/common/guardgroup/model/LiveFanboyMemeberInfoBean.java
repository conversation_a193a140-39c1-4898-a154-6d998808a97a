package com.xmhaibao.live.living.common.guardgroup.model;

import com.google.gson.annotations.SerializedName;
import hb.common.helper.HostHelper;

import cn.taqu.lib.okhttp.model.IDoExtra;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import hb.utils.StringUtils;
import hb.utils.WebpUtils;

/**
 * Created by Sirius on 2018/12/26.
 */
public class LiveFanboyMemeberInfoBean implements IDoExtra {


    /**
     * host_info : {"host_uuid":"nbsplyoxekn","nickname":"cxh","avatar":"/taqu_ios_0_1998136908_1499909259.jpg","guard_group_name":"杀马特","icon_medal":"/index/2da9a9d361ce209a448eb04d1d87fef6.png","member_num":"1"}
     * my_info : {"rank":"1","account_uuid":"bvubjhtowfv","nickname":"小北大湿","avatar":"/taqu_android_post_01480594191107.jpg","guard_group_name":"杀马特","icon_level":"/index/2da9a9d361ce209a448eb04d1d87fef6.png","intimate":"0","intimate_max":"150"}
     */

    @SerializedName("host_info")
    private LiveFanboyHostInfoBean hostInfo;
    @SerializedName("my_info")
    private MyInfoBean myInfo;

    public LiveFanboyHostInfoBean getHostInfo() {
        return hostInfo;
    }

    public void setHostInfo(LiveFanboyHostInfoBean hostInfo) {
        this.hostInfo = hostInfo;
    }

    public MyInfoBean getMyInfo() {
        return myInfo;
    }

    public void setMyInfo(MyInfoBean myInfo) {
        this.myInfo = myInfo;
    }

    @Override
    public void doExtra(IResponseInfo response) {
        if(myInfo!=null) myInfo.doExtra(response);
        if(hostInfo!=null) hostInfo.doExtra(response);
    }

    public class LiveFanboyHostInfoBean implements IDoExtra {
        /**
         * host_uuid : nbsplyoxekn
         * nickname : cxh
         * avatar : /taqu_ios_0_1998136908_1499909259.jpg
         * guard_group_name : 杀马特
         * icon_medal : /index/2da9a9d361ce209a448eb04d1d87fef6.png
         * member_num : 1
         */

        @SerializedName("host_uuid")
        private String hostUuid;
        private String nickname;
        private String avatar;
        @SerializedName("fans_group_name")
        private String guardGroupName;
        @SerializedName("icon_medal")
        private String iconMedal;
        @SerializedName("member_num")
        private int memberNum;
        @SerializedName("manage_num")
        private int managerNum;
        @SerializedName("manage_max_num")
        private String managerMaxNum;

        public String getManagerMaxNum() {
            return managerMaxNum;
        }

        public int getManagerNum() {
            return managerNum;
        }

        public void setManagerMaxNum(String managerMaxNum) {
            this.managerMaxNum = managerMaxNum;
        }

        public void setManagerNum(int managerNum) {
            this.managerNum = managerNum;
        }

        public void addManager(){
            this.managerNum++;
        }

        public void reduceManager(){
            this.managerNum--;
        }

        public String getHostUuid() {
            return hostUuid;
        }

        public void setHostUuid(String hostUuid) {
            this.hostUuid = hostUuid;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getGuardGroupName() {
            return guardGroupName;
        }

        public void setGuardGroupName(String guardGroupName) {
            this.guardGroupName = guardGroupName;
        }

        public String getIconMedal() {
            return iconMedal;
        }

        public void setIconMedal(String iconMedal) {
            this.iconMedal = iconMedal;
        }

        public int getMemberNum() {
            return memberNum;
        }

        public void setMemberNum(int memberNum) {
            this.memberNum = memberNum;
        }

        @Override
        public void doExtra(IResponseInfo response) {
            avatar = WebpUtils.getWebpUrl(avatar, HostHelper.getInstance().getHostAvatar());
            iconMedal = WebpUtils.getWebpUrl(iconMedal, HostHelper.getInstance().getHostImg());
        }
    }

    public class MyInfoBean implements IDoExtra  {
        /**
         * rank : 1
         * account_uuid : bvubjhtowfv
         * nickname : 小北大湿
         * avatar : /taqu_android_post_01480594191107.jpg
         * guard_group_name : 杀马特
         * icon_level : /index/2da9a9d361ce209a448eb04d1d87fef6.png
         * intimate : 0
         * intimate_max : 150
         */

        private String rank;
        @SerializedName("accompany_rank")
        private String accompanyRank;
        @SerializedName("account_uuid")
        private String accountUuid;
        private String nickname;
        private String avatar;
        @SerializedName("fans_group_name")
        private String guardGroupName;
        @SerializedName("icon_level")
        private String iconLevel;
        private String intimate;
        @SerializedName("intimate_max")
        private String intimateMax;
        private String introduce;

        private String type;

        @SerializedName("intimate_left")
        private String intimateLeft;
        @SerializedName("accompany_day")
        private String accompanyDay;
        @SerializedName("handle_accompany_day")
        private String handleAccompanyDay;
        @SerializedName("day_left")
        private String dayLeft;
        @SerializedName("handle_intimate")
        private String handleIntimate;

        public String getHandleAccompanyDay() {
            return handleAccompanyDay;
        }

        public void setHandleAccompanyDay(String handleAccompanyDay) {
            this.handleAccompanyDay = handleAccompanyDay;
        }

        public String getHandleIntimate() {
            return handleIntimate;
        }

        public void setHandleIntimate(String handleIntimate) {
            this.handleIntimate = handleIntimate;
        }

        public String getAccompanyRank() {
            return accompanyRank;
        }

        public void setAccompanyRank(String accompanyRank) {
            this.accompanyRank = accompanyRank;
        }

        public String getIntimateLeft() {
            return intimateLeft;
        }

        public void setIntimateLeft(String intimateLeft) {
            this.intimateLeft = intimateLeft;
        }

        public String getAccompanyDay() {
            return accompanyDay;
        }

        public void setAccompanyDay(String accompanyDay) {
            this.accompanyDay = accompanyDay;
        }

        public String getDayLeft() {
            return dayLeft;
        }

        public void setDayLeft(String dayLeft) {
            this.dayLeft = dayLeft;
        }

        public boolean isManager(){

            return "2".equals(type);
        }

        public String getRank() {
            return rank;
        }

        public void setRank(String rank) {
            this.rank = rank;
        }

        public String getAccountUuid() {
            return accountUuid;
        }

        public void setAccountUuid(String accountUuid) {
            this.accountUuid = accountUuid;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getGuardGroupName() {
            return guardGroupName;
        }

        public void setGuardGroupName(String guardGroupName) {
            this.guardGroupName = guardGroupName;
        }

        public String getIconLevel() {
            return iconLevel;
        }

        public void setIconLevel(String iconLevel) {
            this.iconLevel = iconLevel;
        }

        public long getIntimateMax() {
            return StringUtils.stringToLong(intimateMax);
        }

        public long getIntimate() {
            return StringUtils.stringToLong(intimate);
        }

        public void setIntimate(String intimate) {
            this.intimate = intimate;
        }

        public void setIntimateMax(String intimateMax) {
            this.intimateMax = intimateMax;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        @Override
        public void doExtra(IResponseInfo response) {
            avatar = WebpUtils.getWebpUrl(avatar, HostHelper.getInstance().getHostAvatar());
            iconLevel = WebpUtils.getWebpUrl(iconLevel, HostHelper.getInstance().getHostImg());
        }

        public String getIntroduce() {
            return introduce;
        }

        public void setIntroduce(String introduce) {
            this.introduce = introduce;
        }
    }
}
