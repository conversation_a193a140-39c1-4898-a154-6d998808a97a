package com.xmhaibao.live.living.common.gift.fragment;

import static com.xmhaibao.live.constants.LiveConstants.LIVE_GIFT_PACK_RED_KEY;
import static com.xmhaibao.live.living.common.prop.fragment.LivePackListFragment.EXTRA_UUID;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager.widget.ViewPager;

import com.airbnb.lottie.LottieAnimationView;
import com.google.android.material.tabs.TabLayout;
import com.xmhaibao.common_level.model.EventMineWealthLevelUp;
import com.xmhaibao.gift.ConstGiftId;
import com.xmhaibao.gift.base.BaseGiftListPresenter;
import com.xmhaibao.gift.bean.GiftPackBindInfo;
import com.xmhaibao.gift.bean.LiveGiftDescInfo;
import com.xmhaibao.gift.bean.LiveGiftInfo;
import com.xmhaibao.gift.bean.LiveGiftSendResultBean;
import com.xmhaibao.gift.bean.LiveTabGiftItemean;
import com.xmhaibao.gift.bean.LiveTabGiftListBean;
import com.xmhaibao.gift.constants.GiftType;
import com.xmhaibao.gift.view.dao.ILiveGiftListSendListener;
import com.xmhaibao.gift.view.dao.ILiveGiftSelectListener;
import com.xmhaibao.gift.view.dao.ILiveUpdateTabAdViewListener;
import com.xmhaibao.gift.view.dao.OnGiftBatterSendListener;
import com.xmhaibao.gift.view.dao.OnGiftSentListener;
import com.xmhaibao.live.R;
import com.xmhaibao.live.api.bean.LiveBlindLuckyInfo;
import com.xmhaibao.live.api.helper.LiveRechargeHelper;
import com.xmhaibao.live.api.helper.LiveRoomInfoHelper;
import com.xmhaibao.live.base.adapter.LiveGiftViewPagerAdapter;
import com.xmhaibao.live.constants.LiveConstants;
import com.xmhaibao.live.constants.LiveGioConstants;
import com.xmhaibao.live.constants.LiveRoomType;
import com.xmhaibao.live.living.common.gift.fancyfish.model.FancyFishBean;
import com.xmhaibao.live.living.common.gift.fancyfish.widget.FancyFishInfoView;
import com.xmhaibao.live.living.common.gift.helper.LiveAdGiftDisplayHelper;
import com.xmhaibao.live.living.common.gift.helper.LiveFlowerHelper;
import com.xmhaibao.live.living.common.gift.helper.LiveFreeGiftListenerHelper;
import com.xmhaibao.live.living.common.gift.helper.LiveGiftGioHelper;
import com.xmhaibao.live.living.common.gift.helper.LiveGiftListHelper;
import com.xmhaibao.live.living.common.gift.interf.IPackGiftNoticeListener;
import com.xmhaibao.live.living.common.gift.interf.IPackPropListener;
import com.xmhaibao.live.living.common.gift.interf.OnBackPackViewChangeListener;
import com.xmhaibao.live.living.common.gift.interf.OnGiftDialogListener;
import com.xmhaibao.live.living.common.gift.interf.OnLiveGiftSelectNumDialogListener;
import com.xmhaibao.live.living.common.gift.model.EventLiveUpgradeGiftProgressBean;
import com.xmhaibao.live.living.common.gift.model.EventNotifyGiftById;
import com.xmhaibao.live.living.common.gift.model.LiveGiftUserInfo;
import com.xmhaibao.live.living.common.gift.tracker.LiveGiftTracker;
import com.xmhaibao.live.living.common.gift.view.LiveGiftSelectUserView;
import com.xmhaibao.live.living.common.gift.view.LiveGiftUpgradeInfoLayout;
import com.xmhaibao.live.living.common.gift.view.LiveMarqueeView;
import com.xmhaibao.live.living.common.gift.viewmodel.LiveGiftViewModel;
import com.xmhaibao.live.living.common.prop.fragment.LivePackListFragment;
import com.xmhaibao.live.living.common.prop.fragment.LivePropPackFragment;
import com.xmhaibao.live.living.common.starchallenge.widget.LiveStarChallengeMaskView;
import com.xmhaibao.live.shell.constants.LiveShellConstants;
import com.xmhaibao.live.shell.dialog.LiveShellExpiredGuideDialog;
import com.xmhaibao.live.shell.dialog.LiveShellInstructionsGuideDialog;
import com.xmhaibao.live.view.dao.IWatchLiveView;
import com.xmhaibao.live.viewmodel.LiveViewModel;
import com.xmhaibao.live.wealthlevel.view.LiveGiftVipShowView;
import com.xmhaibao.mine.api.model.EventTradeSuccess;
import com.xmhaibao.mine.api.router.MyBeanPinsRouter;
import com.xmhaibao.noble.api.helper.NobleHelper;
import com.xmhaibao.prop.router.PropRouter;
import com.xmhaibao.imganim.lottie.XLottiePlayer;

import java.util.ArrayList;
import java.util.List;

import cn.taqu.lib.base.common.XjbApplicationHelper;
import cn.taqu.lib.base.event.EventXjbLogin;
import cn.taqu.lib.base.event.EventXjbUserLoginOut;
import cn.taqu.lib.base.helper.BalanceHelper;
import cn.taqu.lib.base.interf.OnGiftDialogCallback;
import cn.taqu.lib.base.router.ARouterManager;
import cn.taqu.lib.base.router.RouterLaunch;
import cn.taqu.lib.base.utils.AppQuCoinHelper;
import cn.taqu.lib.base.utils.GIORechargePosition;
import com.xmhaibao.live.api.tracker.LiveTracker;
import com.xmhaibao.shell.bean.ShellBalanceBean;
import com.xmhaibao.shell.bean.ShellBalanceTipInfoBean;
import com.xmhaibao.shell.constants.ShellExChangeTrackerConstants;
import com.xmhaibao.shell.constants.ShellSceneConstants;
import com.xmhaibao.shell.dialog.ShellExChangeDialogFragment;
import com.xmhaibao.shell.helper.ShellBalanceHelper;
import com.xmhaibao.shell.listener.IShellBalanceListener;

import hb.actionqueue.annotation.XNotActionQueue;
import hb.common.data.AccountHelper;
import hb.common.helper.ServerTime;
import hb.logupload.XLogUpload;
import hb.utils.CollectionUtils;
import hb.utils.ColorUtils;
import hb.utils.EventBusUtils;
import hb.utils.Loger;
import hb.utils.ScreenUtils;
import hb.utils.SimpleAnimatorListener;
import hb.utils.SizeUtils;
import hb.utils.StringUtils;
import hb.utils.TimeUtils;
import hb.utils.kvcache.KVCacheUtils;
import hb.ximage.fresco.BaseDraweeView;
import hb.xstyle.xdialog.PatchedDialogFragment;
import hb.xtoast.XToastUtils;


/**
 * author: wusongyuan
 * date: 2016.11.15
 * desc: 直播-礼物列表Dialog
 */
@XNotActionQueue
public class LiveGiftListDialogFragment extends PatchedDialogFragment implements
        BalanceHelper.OnBalanceListener, LiveGiftListHelper.OnLiveAllGiftGetListListener,
        DialogInterface.OnShowListener, View.OnClickListener, OnGiftSentListener,
        ILiveGiftListSendListener, OnGiftDialogListener, OnLiveGiftSelectNumDialogListener,
        ILiveGiftSelectListener, IPackPropListener, IPackGiftNoticeListener, ILiveUpdateTabAdViewListener, IShellBalanceListener {

    public static final String TAG = "LiveGiftListDialogFrag";
    public static final String STATE_IS_SHOWING = "isShowing";
    public static final String ALL_IN = "All in";
    public static String preSelectUserUuid;

    private Context mContext;

    private LiveGiftBaseFragment mGiftListFragment;
    private LivePackListFragment mPackListFragment;

    private BaseGiftListPresenter.OnGiftListShowListener mOnGiftListShowListener;
    private List<OnGiftDialogCallback> mGiftDialogCallbacks = new ArrayList<>();
    private View mContentView;
    private Dialog mDialog;

    private LiveGiftListHelper mLiveGiftListHelper;
    private boolean mIsShowing = false;
    private long mTqBean;
    private ViewPager mViewPager;
    private TextView mTvTqbean;
    private TextView mTvGiftTab;
    private TextView mTvPropTab;
    private String mHostUuid;
    private ImageView mIvOpenNobleHint;

    private View mBgTopLay;
    //默认显示的界面的角标
    private int mDefaultPosition;
    /**
     * 默认选中指定的礼物
     */
    private String mDefaultGid;
    /**
     * 默认背包选中的tab下标
     */
    private int mPackTabIndex;
    /**
     * 默认礼物选中的tab下标
     */
    private String mGiftTabIndex;
    /**
     * 礼物说明
     */
    private BaseDraweeView mIvGiftDesc;
    private RelativeLayout mRLTvGiftDesc;
    private TextView mTvGiftDescTitle;
    private LiveMarqueeView mMarqueeGiftDesc;
    private LiveViewModel mLiveViewModel;
    private OnGiftBatterSendListener mGiftBatterSendListener;

    /**
     * 周星冠名礼物说明
     */
    private LiveStarChallengeMaskView mStarChallengeMaskView;

    //礼物说明动画
    private ObjectAnimator mRlTextDescAnimator;
    private ObjectAnimator mIvDescAnimator;
    private ObjectAnimator mFirstChargeIcAnimator;
    /**
     * 背包TabLayout
     */
    private TabLayout mVLivePackTabs;

    /**
     * 礼物TabLayout
     */
    private TabLayout mVLiveGiftTabs;

    /**
     * 财富等级显示控件
     */
    private LiveGiftVipShowView mVipShowView;


    /**
     * 首充广告位
     */
    private BaseDraweeView mIvFirstChargeIcon;
    /**
     * tab广告位-静态广告
     */
    private BaseDraweeView mIvTabAd;
    /**
     * tab广告位-动态态广告
     */
    private LottieAnimationView mIvTabAdLottie;

    /**
     * 道具使用记录
     */
    private TextView mTvPropRecord;
    /**
     * 右侧趣豆相关
     */
    private RelativeLayout mRlTqContent;
    /**
     * 当前选中的fragment
     */
    private Fragment mSelectedFragment;

    /**
     * 背包礼物过期提醒
     */
    private TextView mTvPackGiftNotice;
    private LiveGiftUpgradeInfoLayout mUpUpgradeInfoLayout;
    /**
     * 锦鲤礼物
     */
    private FancyFishInfoView mFancyFishInfoView;
    /**
     * 勇闯冒险岛特殊礼物id
     */
    private String mDuduBirdSpecialGid;

    private ViewStub mSelectUserViewStub;

    /**
     * 选择用户View
     */
    private LiveGiftSelectUserView mSelectUserView;

    /**
     * 房间类型
     */
    private int mRoomType = LiveRoomType.NORMAL;

    private int mOutPosition = -1;

    private int mInnerPosition = -1;

    private List<LiveGiftUserInfo> mMicUserList = new ArrayList<>();
    /**
     * 礼物面板相关的ViewModel
     */
    private LiveGiftViewModel mViewMode;

    private TextView tvShellNum;
    private boolean isHideShellLay = false;
    private View divider;
    /**
     * 是否冷启动
     */
    private boolean isColdBoot = false;
    /**
     * 调用 showDialog 方法时间
     */
    private long showDialogTime = 0L;

    public static LiveGiftListDialogFragment newInstance(Context context, String hostUuid) {
        Log.i(TAG, "newInstance: ");
        Bundle args = new Bundle();
        LiveGiftListDialogFragment fragment = new LiveGiftListDialogFragment();
        if (fragment.mGiftListFragment == null) {
            fragment.mGiftListFragment = LiveGiftBaseFragment.newInstance(context);
        }
        args.putString(EXTRA_UUID, hostUuid);
        fragment.setArguments(args);
        fragment.attach(context, hostUuid);
        return fragment;
    }

    public void attach(Context context, String hostUuid) {
        mContext = context;
        mHostUuid = hostUuid;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mContext = context;
    }


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (!EventBusUtils.isRegistered(this)) {
            EventBusUtils.register(this);
        }
        if (mContentView == null) {
            isColdBoot = true;
            Loger.i(TAG, "onCreateView: mContentView == null");
            mContentView = LayoutInflater.from(getContext()).inflate(R.layout.live_gift_list_layout, container, false);
            initView(mContentView);
        } else {
            Loger.i(TAG, "onCreateView: mContentView != null");
            ViewGroup parent = (ViewGroup) mContentView.getParent();
            if (null != parent) {
                parent.removeView(mContentView);
            }
        }
        mLiveViewModel = LiveViewModel.getInstance(getContext());
        initDatas(savedInstanceState);
        return mContentView;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        if (mDialog == null) {
            mDialog = new Dialog(getContext(), R.style.LiveGiftDialogThemeDefalut);
            mDialog.setOnShowListener(this);
            mDialog.setCanceledOnTouchOutside(true);
            mDialog.getWindow().requestFeature(Window.FEATURE_NO_TITLE);
            Window wm = mDialog.getWindow();
            WindowManager.LayoutParams p = wm.getAttributes();
            p.width = XjbApplicationHelper.getInstance().getWidth();
            p.gravity = Gravity.BOTTOM;
            wm.setWindowAnimations(cn.taqu.lib.base.R.style.BottomToTopAnim);
            wm.setAttributes(p);
        }
        return mDialog;
    }

    @Override
    public void onStart() {
        // 以下代码-------
        // ---解决dialog在onStart()时调用dialog.show()问题，导致页面被遮挡问题-----
        super.onStart();
        if (getDialog() != null && !mIsShowing) {
            getDialog().dismiss();
        }
        // 以上代码--------
        DisplayMetrics dm = new DisplayMetrics();
        getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
        getDialog().getWindow().setLayout(dm.widthPixels, getDialog().getWindow().getAttributes().height);
        mLiveViewModel.getIsGiftDialogDescShowLiveModel().postValue(mRLTvGiftDesc.getVisibility() == View.VISIBLE);
    }

    private void initView(View view) {

        Bundle bundle = getArguments();
        if (bundle != null) {
            mHostUuid = bundle.getString(EXTRA_UUID);
        }

        mViewPager = view.findViewById(R.id.live_gift_dialog_viewpaer);
        RelativeLayout.LayoutParams viewPagerParams = (RelativeLayout.LayoutParams) mViewPager.getLayoutParams();
        //viewPagerParams.height = height;
        viewPagerParams.height = ScreenUtils.dp2px(getContext(), 250);
        mTvTqbean = view.findViewById(R.id.tvTqBean);
        mTvTqbean.setOnClickListener(this);
        mIvOpenNobleHint = view.findViewById(R.id.ivOpenNobleHint);
        mIvOpenNobleHint.setOnClickListener(this);
        mBgTopLay = view.findViewById(R.id.topLay);
        mBgTopLay.setOnClickListener(this);
        mIvGiftDesc = view.findViewById(R.id.ivGiftDesc);
        mIvGiftDesc.setOnClickListener(this);
        mRLTvGiftDesc = view.findViewById(R.id.rLTvGiftDesc);
        mTvGiftDescTitle = view.findViewById(R.id.tvGiftDescTitle);
        mMarqueeGiftDesc = view.findViewById(R.id.marqueeGiftDesc);
        mTvGiftTab = view.findViewById(R.id.tabGift);
        mTvGiftTab.setOnClickListener(this);
        mTvPropTab = view.findViewById(R.id.tabProp);
        mTvPropTab.setOnClickListener(this);
        mTvPackGiftNotice = view.findViewById(R.id.tvNotice);
        mVLiveGiftTabs = view.findViewById(R.id.vLiveGiftTabs);
        mVLivePackTabs = view.findViewById(R.id.vLivePackTabs);
        mVipShowView = view.findViewById(R.id.giftViewShowView);
        mIvFirstChargeIcon = view.findViewById(R.id.ivFirstChargeIcon);
        mIvTabAd = view.findViewById(R.id.ivTabAd);
        mIvTabAd.setOnClickListener(this);
        mIvTabAdLottie = view.findViewById(R.id.ivTabAdLottie);
        mIvTabAdLottie.setOnClickListener(this);
        mIvFirstChargeIcon.setOnClickListener(this);
        mTvPropRecord = view.findViewById(R.id.tvPropRecord);
        mTvPropRecord.setOnClickListener(this);
        mRlTqContent = view.findViewById(R.id.rltTqContent);
        mStarChallengeMaskView = view.findViewById(R.id.starChallengeMaskView);
        mUpUpgradeInfoLayout = view.findViewById(R.id.upgradeLay);
        mUpUpgradeInfoLayout.setOnClickListener(this);
        mFancyFishInfoView = view.findViewById(R.id.fancyFishView);
        mFancyFishInfoView.setOnClickListener(this);
        mSelectUserViewStub = view.findViewById(R.id.selectUserViewStub);
        //首充优惠广告位
        initRechargeAdBanner();
        initViewModel();
        tvShellNum = view.findViewById(R.id.tvShellNum);
        tvShellNum.setOnClickListener(this);
        divider = view.findViewById(R.id.divider);
        resetShellStatus(LiveRoomInfoHelper.getInstance().isShellClose());
    }

    /**
     * 重置贝壳状态
     *
     * @param isClose 是否关闭
     */
    private void resetShellStatus(boolean isClose) {
        if (tvShellNum != null && divider != null) {
            isHideShellLay = isClose;
            tvShellNum.setVisibility(isClose ? View.GONE : View.VISIBLE);
            divider.setVisibility(isClose ? View.GONE : View.VISIBLE);
        }
    }

    private void initViewModel() {
        mViewMode = new ViewModelProvider(this).get(LiveGiftViewModel.class);
        mViewMode.getAccountInfoData().observe(this, info -> {
            if (mVipShowView != null) {
                mVipShowView.setLiveAccountInfo(info);
            }
        });
        mViewMode.getShellStatusData().observe(this, status -> resetShellStatus(status.isClose()));
    }

    /**
     * 设置房间类型
     */
    public void setRoomType(int roomType) {
        mRoomType = roomType;
        if (roomType == LiveRoomType.NORMAL) {
            if (mSelectUserView != null) {
                mSelectUserView.setVisibility(View.GONE);
            }
        } else if (roomType == LiveRoomType.MULTI_PLAYER) {
            initSelectUserView();
        }
    }

    /**
     * 设置送礼选择收礼人列表数据
     */
    public void setMicUserList(List<LiveGiftUserInfo> micUserList) {
        mMicUserList.clear();
        if (CollectionUtils.isNotEmpty(micUserList)) {
            mMicUserList.addAll(micUserList);
        }
        if (mIsShowing && mSelectUserView != null) {
            mSelectUserView.setData(mMicUserList, preSelectUserUuid);
            preSelectUserUuid = null;
        }
    }

    /**
     * 初始化首充优惠广告位
     */
    private void initRechargeAdBanner() {
        String banner = LiveRechargeHelper.getInstance().getBanner(AccountHelper.getAccountUuid());
        if (mIvFirstChargeIcon != null) {
            if (TextUtils.isEmpty(banner)) {
                mIvFirstChargeIcon.setVisibility(View.GONE);
            } else {
                showFirstRechargeAd();
                mIvFirstChargeIcon.setImageFromUrl(banner);
                //根据宽度自适应
                mIvFirstChargeIcon.setAdaptiveByFixedWidth(SizeUtils.dp2px(355f));
            }
        }
    }

    private void initDatas(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            mIsShowing = savedInstanceState.getBoolean(STATE_IS_SHOWING);
        }
        initFragment(savedInstanceState);
        if (mLiveGiftListHelper != null && mGiftListFragment != null) {
            mTqBean = mLiveGiftListHelper.getTqNobleBeanCount();
            setTvTqbean(String.valueOf(mLiveGiftListHelper.getTqbeanCount()));
            setTvNoblebean();
        }
    }


    private void initFragment(Bundle savedInstanceState) {
        Loger.i(TAG, "initFragment: ");
        if (savedInstanceState != null) {
            FragmentManager fragmentManager = getChildFragmentManager();
            if (fragmentManager != null) {
                List<Fragment> fragments = fragmentManager.getFragments();
                if (fragments != null && !fragments.isEmpty()) {
                    for (int i = 0; i < fragments.size(); i++) {
                        Fragment fragment = fragments.get(i);
                        if (fragment != null) {
                            if (fragment instanceof LiveGiftBaseFragment) {
                                if (mGiftListFragment == null) {
                                    mGiftListFragment = (LiveGiftBaseFragment) fragment;
                                }
                            } else if (fragment instanceof LivePackListFragment) {
                                mPackListFragment = (LivePackListFragment) fragment;
                            }
                        }
                    }
                }
            }
        }
        if (mGiftListFragment == null) {
            mGiftListFragment = LiveGiftBaseFragment.newInstance(mContext);
        }
        if (mLiveGiftListHelper != null) {
            mGiftListFragment.setLiveGiftListBean(mLiveGiftListHelper.getTabGiftListBean());
            mGiftListFragment.setPresentPacketPositionBean(mLiveGiftListHelper.getPresentPacketPositionBean());
        } else {
            Loger.i(TAG, "initFragment: mLiveGiftListHelper is null.");
        }
        mGiftListFragment.setTabLayout(mVLiveGiftTabs);
        mGiftListFragment.setOnLiveGiftSelectListener(this);
        mGiftListFragment.setOnLiveUpdateTabAdViewListener(this);
        mGiftListFragment.setOnLiveGiftListSendListener(this);
        mGiftListFragment.setGiftDialogListener(this);
        mGiftListFragment.setLiveGiftSelectNumDialogListener(this);
        if (mPackListFragment == null) {
            mPackListFragment = LivePackListFragment.newInstance(mContext, mHostUuid);
        }
        mPackListFragment.setTabLayout(mVLivePackTabs);
        mPackListFragment.setOnLiveGiftListSendListener(this);
        mPackListFragment.setOnLiveGiftSelectListener(this);
        mPackListFragment.setGiftDialogListener(this);
        mPackListFragment.setLiveGiftSelectNumDialogListener(this);
        mPackListFragment.setPackPropListener(this);
        mPackListFragment.setNoticeListener(this);
        mPackListFragment.setOnBackPackViewPageChangeListener(new OnBackPackViewChangeListener() {
            @Override
            public void onPageSelected(int position) {
                int outPosition = mViewPager == null ? 0 : mViewPager.getCurrentItem();
                onPageChange(outPosition, position);
            }
        });
        if (mLiveGiftListHelper != null) {
            mPackListFragment.setGiftMap(mLiveGiftListHelper.getLiveGiftMap());
        }
        List<Fragment> fragments = new ArrayList<>();
        fragments.add(mGiftListFragment);
        fragments.add(mPackListFragment);
        mSelectedFragment = mGiftListFragment;
        LiveGiftViewPagerAdapter viewPagerAdapter = new LiveGiftViewPagerAdapter(getChildFragmentManager(), fragments);
        mViewPager.setAdapter(viewPagerAdapter);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                int innerPosition = mPackListFragment == null ? 0 : mPackListFragment.getViewPagerCurrentItem();
                onPageChange(position, innerPosition);
                if (mPackListFragment != null && mGiftListFragment != null) {
                    if (position == 0) {
                        mSelectedFragment = mGiftListFragment;
                        //礼物tab不显示道具记录
                        showPropRecordView(false);
                        //设置首充优惠广告位显示
                        setChargeAdVisible(false);
                        setTagBg(true);
                        LiveGiftTracker.Companion.getInstance().trackLiveGiftDialogMenuClick(LiveGioConstants.GIFT);
                        LiveGiftTracker.Companion.getInstance().trackLiveSendGiftClick(LiveGioConstants.GIFT);
                        getChildFragmentManager().beginTransaction().hide(mPackListFragment).commitAllowingStateLoss();
                        getChildFragmentManager().beginTransaction().show(mGiftListFragment).commitAllowingStateLoss();
                        mGiftListFragment.updateSelectGift();
                    } else {
                        mSelectedFragment = mPackListFragment;
                        //根据当前选中的fragment判断是否显示道具记录
                        showPropRecordView(mPackListFragment.getSelectedFragment() instanceof LivePropPackFragment);
                        //设置首充优惠广告位隐藏
                        setChargeAdVisible(true);
                        setTagBg(false);
                        LiveGiftTracker.Companion.getInstance().trackLiveGiftDialogMenuClick(LiveGioConstants.PACK);
                        LiveGiftTracker.Companion.getInstance().trackLiveSendGiftClick(LiveGioConstants.PACK);
                        getChildFragmentManager().beginTransaction().hide(mGiftListFragment).commitAllowingStateLoss();
                        getChildFragmentManager().beginTransaction().show(mPackListFragment).commitAllowingStateLoss();
                        mPackListFragment.updateSelectGift();
                        if (mVipShowView != null) {
                            mVipShowView.updateGiftInfo();
                        }
                    }
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

    }

    private void setTagBg(boolean isSelectGiftTab) {
        mVLiveGiftTabs.setVisibility(isSelectGiftTab ? View.VISIBLE : View.GONE);
        mVLivePackTabs.setVisibility(isSelectGiftTab ? View.GONE : View.VISIBLE);
        mTvGiftTab.setBackgroundResource(isSelectGiftTab ? R.drawable.live_gift_dialog_tag_bg_w7 : 0);
        mTvGiftTab.setTextColor(ColorUtils.getColor(isSelectGiftTab ? hb.xstyle.R.color.white : hb.xstyle.R.color.w7));
        mTvPropTab.setBackgroundResource(isSelectGiftTab ? 0 : R.drawable.live_gift_dialog_tag_bg_w7);
        mTvPropTab.setTextColor(ColorUtils.getColor(isSelectGiftTab ? hb.xstyle.R.color.w7 : hb.xstyle.R.color.white));
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putBoolean(STATE_IS_SHOWING, mIsShowing);
    }

    @Override
    public void onShow(DialogInterface dialog) {
        Loger.i(TAG, "onShow:");
        if (!mIsShowing) {
            return;
        }
        LiveTracker.getInstance().trackLiveRoomClickPosition(LiveGioConstants.TIP);
        if (mOnGiftListShowListener != null && mIsShowing) {
            mOnGiftListShowListener.onGiftListShow(true);
        }
        if (mLiveGiftListHelper != null) {
            mLiveGiftListHelper.requestBalance();
            mLiveGiftListHelper.requestGlobalAdBean();
        }
        if (mPackListFragment != null) {
            mPackListFragment.refreshData();
        }
        boolean isNoble = NobleHelper.getInstance().getMyNobleCode() > 0;
        mIvOpenNobleHint.setVisibility(isNoble ? View.GONE : View.VISIBLE);

        //更新免费礼物信息
        LiveFreeGiftListenerHelper.getInstance().getLightStickNum(getContext(), mHostUuid);
        refreshDataAfterShow();
        showDefaultPosition();
        startMarQuee();
        if (mGiftListFragment != null) {
            //礼物面板礼物红包埋点上报
            LiveGiftGioHelper.INSTANCE.setListPagerNeedGio(true);
            mGiftListFragment.presentPacketGio();
            mGiftListFragment.setDuduBirdSpecialGid(mDuduBirdSpecialGid);
        }
        if (mPriSelectGiftInfo != null && LiveRoomInfoHelper.getInstance().isFancyFishGift(mPriSelectGiftInfo.getGid())) {
            if (mLiveGiftListHelper != null && mFancyFishInfoView.getVisibility() == View.VISIBLE) {
                mLiveGiftListHelper.requestFancyFishInfo(mPriSelectGiftInfo.getGid());
            }
        }
        if (mOutPosition != -1 && mInnerPosition != -1) {
            onPageChange(mOutPosition, mInnerPosition);
        }
        if (mSelectUserView != null) {
            mSelectUserView.setData(mMicUserList, preSelectUserUuid);
            preSelectUserUuid = null;
        }
        ShellBalanceHelper.INSTANCE.onAddListener(this);
        ShellBalanceHelper.INSTANCE.getShellBalanceInfo();
        reportOpenDialogTime();
        isColdBoot = false;
    }

    /**
     * 上报打开礼物面板弹窗时长
     */
    private void reportOpenDialogTime() {
        if (showDialogTime == 0) {
            return;
        }
        String tempKey = isColdBoot ? LiveConstants.LIVE_CLOD_OPEN_GIFT_DIALOG_KEY : LiveConstants.LIVE_OPEN_GIFT_DIALOG_KEY;
        long openTime = KVCacheUtils.getLong(tempKey, 0L);
        if (TimeUtils.isToday(openTime)) {
            return;
        }
        long currentTime = System.currentTimeMillis();
        long diffTime = currentTime - showDialogTime;
        XLogUpload.newBuilder(isColdBoot ? "LiveGiftDialogClodOpenTime" : "LiveGiftDialogWarmOpenTime")
                .num1(diffTime)
                .post();
        KVCacheUtils.putLong(tempKey, currentTime);
        Loger.i(TAG, "openDialogTime:" + diffTime);
    }

    private void refreshDataAfterShow() {
        refreshViewInfo();
        if (mViewMode != null) {
            mViewMode.requestShellCloseStatus();
        }
    }


    /**
     * 页面切换
     *
     * @param outPosition   礼物tab 选中的tab
     * @param innerPosition 这边是背包tab 选择页面：0 礼物背包，1 道具背包
     */
    private void onPageChange(int outPosition, int innerPosition) {
        mOutPosition = outPosition;
        mInnerPosition = innerPosition;
        if (mRoomType == LiveRoomType.MULTI_PLAYER) {
            boolean isInBackPack = outPosition == 1;
            if (isInBackPack && innerPosition == 1) {
                if (mSelectUserView != null) {
                    mSelectUserView.setVisibility(View.GONE);
                }
            } else {
                initSelectUserView();
            }
        } else {
            if (mSelectUserView != null) {
                mSelectUserView.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 更新财富等级信息
     * 为了防止在其页面更新信息没有及时同步，每次打开礼物面板都要重新刷新数据
     */
    private void refreshViewInfo() {
        if (mViewMode != null) {
            mViewMode.requestAccountInfo();
        }
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        Loger.i(TAG, "onDismiss: ");
        //super.onDismiss(dialog);
        try {
            FragmentManager fm = getFragmentManager();
            if (fm != null) {
                FragmentTransaction ft = fm.beginTransaction();
                if (ft != null) {
                    ft.hide(this);
                    ft.commitAllowingStateLoss();
                }
            }
            if (mIsShowing) {
                if (mOnGiftListShowListener != null) {
                    mOnGiftListShowListener.onGiftListShow(false);
                }
                if (CollectionUtils.isNotEmpty(mGiftDialogCallbacks)) {
                    for (OnGiftDialogCallback callback : mGiftDialogCallbacks) {
                        if (callback != null) {
                            callback.onCallback();
                        }
                    }
                }
            }
            mIsShowing = false;
        } catch (IllegalStateException e) {
            e.printStackTrace();
        }
        // 终止跑马灯
        stopMarQuee();
        ShellBalanceHelper.INSTANCE.onRemoveListener(this);
    }

    /**
     * 通过callback从列表移除
     *
     * @param callback
     */
    public void removeGiftDialogCallback(OnGiftDialogCallback callback) {
        if (CollectionUtils.isNotEmpty(mGiftDialogCallbacks)) {
            mGiftDialogCallbacks.remove(callback);
        }
    }

    @Override
    public void onGiftListBean(LiveTabGiftListBean bean) {
        if (mGiftListFragment != null && mLiveGiftListHelper != null) {
            mGiftListFragment.setLiveGiftListBean(mLiveGiftListHelper.getTabGiftListBean());
        }
        if (mPackListFragment != null && mLiveGiftListHelper != null) {
            mPackListFragment.giftPackRegroupList(mLiveGiftListHelper.getLiveGiftMap());
        }

        if (bean != null && bean.getGiftList() != null) {
            for (int i = 0; i < bean.getGiftList().size(); i++) {
                if (bean.getGiftList().get(i).getList() != null && bean.getGiftList().get(i).getList().size() > 0) {
                    List<LiveGiftInfo> liveGiftList = bean.getGiftList().get(i).getList();
                    for (LiveGiftInfo info : liveGiftList) {
                        if (info.isFreeFlower()) {
                            if (mContext != null) {
                                LiveFlowerHelper.getInstance().init(mContext);
                            }
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * 更新礼物升级玩法礼物信息
     *
     * @param info
     */
    @Override
    public void upgradeGamePlayInfo(LiveGiftInfo info) {
        if (info == null) {
            if (mUpUpgradeInfoLayout != null) {
                mUpUpgradeInfoLayout.setVisibility(View.GONE);
            }
            return;
        }
        if (mGiftListFragment != null) {
            mGiftListFragment.upgradeGamePlayInfo(info);
        }
        resetUpgradeInfo(info);
    }

    /**
     * 魔盒礼物玩法
     *
     * @param info
     */
    @Override
    public void upgradeBlindLuckyInfo(LiveBlindLuckyInfo info) {
        mBlindLuckyInfo = info;
        if (info == null) {
            return;
        }
        if (mPriSelectGiftInfo != null && LiveRoomInfoHelper.getInstance().isBlindBoxGift(mPriSelectGiftInfo.getGid())) {
            if (mUpUpgradeInfoLayout.getVisibility() == View.VISIBLE) {
                mUpUpgradeInfoLayout.setTag(cn.taqu.lib.base.R.id.click_rel, info.getRelation());
                mUpUpgradeInfoLayout.setBlindLuckyInfoData(info);
            }
        }
    }

    private LiveBlindLuckyInfo mBlindLuckyInfo;

    /**
     * 更新礼物升级玩法礼物信息
     *
     * @param info
     */
    public void upgradeGamePlaySendResult(EventLiveUpgradeGiftProgressBean info) {
        if (mUpUpgradeInfoLayout != null) {
            mUpUpgradeInfoLayout.setResultData(info);
        }
    }

    @Override
    public void upgradeFancyFishInfo(FancyFishBean bean) {
        mFancyFishBean = bean;
        if (mPriSelectGiftInfo != null && LiveRoomInfoHelper.getInstance().isFancyFishGift(mPriSelectGiftInfo.getGid())) {
            if (mFancyFishInfoView.getVisibility() != View.GONE) {
                mFancyFishInfoView.setTag(cn.taqu.lib.base.R.id.click_rel, bean.getRelation());
                mFancyFishInfoView.setData(bean);
            }
        }
    }

    private FancyFishBean mFancyFishBean;

    /**
     * 更新全局礼物广告
     *
     * @param info
     */
    @Override
    public void onGlobalAdBean(LiveGiftDescInfo info) {
        mGlobalAdInfo = info;
    }


    /**
     * 全局广告信息
     */
    private LiveGiftDescInfo mGlobalAdInfo;

    /**
     * @param info
     */
    private void resetUpgradeInfo(LiveGiftInfo info) {
        if (mPriSelectGiftInfo != null && mUpUpgradeInfoLayout != null) {
            if (TextUtils.equals(mPriSelectGiftInfo.getType(), GiftType.GIFT_TYPE_UPGRADE)
                    && TextUtils.equals(info.getType(), GiftType.GIFT_TYPE_UPGRADE)) {
                // 更新礼物升级玩法礼物布局信息
                mPriSelectGiftInfo.setGid(info.getGid());
                LiveGiftTracker.Companion.getInstance().liveRoomGiftPlayPopupExposure(mHostUuid, info.getGid(), info.getGiftUpgradeRelation());
                mUpUpgradeInfoLayout.setTag(cn.taqu.lib.base.R.id.click_rel, info.getGiftUpgradeRelation());
                mUpUpgradeInfoLayout.setData(info);
                showUpgradeInfoAd();
            } else if (LiveRoomInfoHelper.getInstance().isBlindBoxGift(info.getGid())) {
                // 幸运魔盒
                mLiveGiftListHelper.requestBlindLuckyInfo();
            } else {
                mUpUpgradeInfoLayout.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.ivOpenNobleHint) {
            ARouterManager.nobleService().launchNobleListActivity(mHostUuid);
            LiveGiftTracker.Companion.getInstance().trackLiveGiftDialogMenuClick(LiveGioConstants.OPEN_NOBLE);
            LiveGiftTracker.Companion.getInstance().trackLiveSendGiftClick(LiveGioConstants.OPEN_NOBLE);
        } else if (i == R.id.topLay) {
            mBgTopLay.setBackgroundResource(hb.xstyle.R.color.transparent);
            dismiss();

        } else if (i == R.id.tvTqBean) {
            //跳转充值
            LiveGiftTracker.Companion.getInstance().trackLiveGiftDialogMenuClick(LiveGioConstants.TQ_BEAN);
            LiveGiftTracker.Companion.getInstance().trackLivePanelBalanceClick(AppQuCoinHelper.getRatioUnit());
            MyBeanPinsRouter.launchTqBeanActivity(GIORechargePosition.POSITION_LIVE_SEND_GIFT);
        } else if (i == R.id.ivGiftDesc || i == R.id.upgradeLay || i == R.id.fancyFishView) {
            // 礼物跳转说明
            String rel = (String) v.getTag(cn.taqu.lib.base.R.id.click_rel);
            if (StringUtils.isNotEmpty(rel)) {
                RouterLaunch.dealJumpData(mContext, rel);
            }
            if (mPriSelectGiftInfo != null && !LiveRoomInfoHelper.getInstance().isBlindBoxGift(mPriSelectGiftInfo.getGid())) {
                LiveGiftTracker.Companion.getInstance().liveRoomGiftPlayPopupClick(mHostUuid, mPriSelectGiftInfo.getGid(), rel);
            }
        } else if (i == R.id.tabGift) {
            //选中礼物tab
            mViewPager.setCurrentItem(0);
        } else if (i == R.id.tabProp) {
            //选中背包tab
            selectGift(null);
            mViewPager.setCurrentItem(1);
        } else if (i == R.id.ivFirstChargeIcon) {
            MyBeanPinsRouter.launchTqBeanActivity(GIORechargePosition.POSITION_LIVE_SEND_GIFT);
        } else if (i == R.id.tvPropRecord) {
            //打开道具使用记录列表
            PropRouter.Companion.launchPropUsageRecordActivity();
        } else if (i == R.id.ivTabAd || i == R.id.ivTabAdLottie) {
            if (v.getTag(R.id.live_tab_ad_relation_id) instanceof String) {
                String relation = (String) v.getTag(R.id.live_tab_ad_relation_id);
                LiveGiftTracker.Companion.getInstance().liveRoomGiftTabAdClick(relation);
                RouterLaunch.dealJumpData(mContext, relation);
            }
        } else if (i == R.id.tvShellNum) {
            LiveGiftTracker.Companion.getInstance().trackLivePanelBalanceClick(ShellBalanceHelper.INSTANCE.shellUnit());
            ShellExChangeDialogFragment.Companion.newInstance(
                    v.getContext(),
                    ShellExChangeTrackerConstants.BUSINESS_LIVE,
                    ShellExChangeTrackerConstants.SCENE_PANEL,
                    ShellSceneConstants.SHELL_LIVE,
                    GIORechargePosition.POSITION_LIVE_SHELL_EXCHANGE
            );
        }
    }


    public void setTvTqbean(String tqbean) {
        if (mTvTqbean == null) {
            return;
        }
        if (!StringUtils.isEmpty(tqbean)) {
            mTvTqbean.setText(tqbean);
        } else {
            mTvTqbean.setText("0");
        }
    }

    public void setTvNoblebean() {
        boolean isNoble = NobleHelper.getInstance().getMyNobleCode() > 0;
        if (mIvOpenNobleHint != null) {
            mIvOpenNobleHint.setVisibility(isNoble ? View.GONE : View.VISIBLE);
        }
    }

    @Override
    public void onGiftSent(LiveGiftInfo info, LiveGiftSendResultBean bean) {
        if (info.isGuardGlowStick()) {
            //更新免费礼物信息
            LiveFreeGiftListenerHelper.getInstance().undateLightStickNum();
        }
        if (!info.isGiftPack() && mVipShowView != null) {
            mVipShowView.updateProgress(info);
        }
        if (LiveRoomInfoHelper.getInstance().isBlindBoxGift(info.getGid())) {
            // 幸运魔盒
            mLiveGiftListHelper.requestBlindLuckyInfo();
        } else if (LiveRoomInfoHelper.getInstance().isFancyFishGift(info.getGid())) {
            // 锦鲤礼物
            mLiveGiftListHelper.requestFancyFishInfo(info.getGid());
        }
    }

    public void setGiftBatterSendListener(OnGiftBatterSendListener listener) {
        mGiftBatterSendListener = listener;
    }

    /**
     * 修改选中礼物个数
     *
     * @param num 数量
     */
    @Override
    public void changeSelectNum(String num) {
        if (mVipShowView != null) {
            mVipShowView.updateGiftNum(num);
        }
    }

    int allNum;

    @Override
    public void sendBatchGift(LiveGiftInfo info, String num) {
        if (mLiveGiftListHelper != null) {
            if (info.getGid().equals(ConstGiftId.GID_SUPER_WEDDING_GIFT)) {
                // 世纪婚礼
                if (mContext instanceof IWatchLiveView) {
                    ((IWatchLiveView) mContext).showWeddingSelectDialog(info, this);
                }
                return;
            } else if (!info.isGiftPack()) {
                if (ALL_IN.equals(num)) {
                    if (StringUtils.stringToInt(info.getPrice()) > 0) {
                        if (info.isShellGift()) {
                            // 先判断是否是贝壳礼物
                            allNum = (int) (ShellBalanceHelper.INSTANCE.getShellBean() / StringUtils.stringToInt(info.getPrice()));
                            if (allNum == 0) {
                                allNum = 1;
                            }
                        } else if (LiveAdGiftDisplayHelper.isLuckyGift(info.getGid())) {
                            //判断如果是幸运礼物，只消耗普通趣豆，其他礼物则优先消耗贵族趣豆
                            allNum = (int) (mLiveGiftListHelper.getTqbeanCount() / StringUtils.stringToInt(info.getPrice()));
                            if (allNum == 0) {
                                allNum = 1;
                            }
                        } else {
                            if (StringUtils.stringToInt(info.getPrice()) <= mLiveGiftListHelper.getTqNobleBeanCount()) {
                                allNum = (int) (mLiveGiftListHelper.getTqNobleBeanCount() / StringUtils.stringToInt(info.getPrice()));
                            } else {
                                allNum = (int) (mLiveGiftListHelper.getTqbeanCount() / StringUtils.stringToInt(info.getPrice()));
                                if (allNum == 0) {
                                    allNum = 1;
                                }
                            }
                        }
                        info.setSendCount(allNum);
                    }
                } else {
                    info.setSendCount(StringUtils.stringToInt(num));
                }
            }
            mLiveGiftListHelper.sendGift(info, getSelectUserUuidStr(), this, mGiftBatterSendListener);
        }
    }

    /**
     * 是否允许赠送
     *
     * @param info
     * @return
     */
    @Override
    public boolean isAllowSend(LiveGiftInfo info) {
        if (info == null) {
            return false;
        }
        GiftPackBindInfo bindInfo = info.getBindInfo();
        if (bindInfo != null) {
            return checkBindInfo(getSelectUserUuidStr(), bindInfo);
        }
        if (isMultiRoom() && mSelectUserView != null && CollectionUtils.isEmpty(mSelectUserView.getSelectUserList())) {
            XToastUtils.show("请选择送礼对象");
            return false;
        }
        return true;
    }

    /**
     * 获取收礼的麦上用户个数
     * @return
     */
    @Override
    public int getReceiveHostSize() {
        if (isMultiRoom() && mSelectUserView != null && CollectionUtils.isNotEmpty(mSelectUserView.getSelectUserList())) {
            return mSelectUserView.getSelectUserList().size();
        }
        return 1;
    }

    /**
     * 检测绑定信息
     *
     * @param uuid     收礼uuid
     * @param bindInfo 礼物信息
     * @return
     */
    private boolean checkBindInfo(String uuid, GiftPackBindInfo bindInfo) {
        boolean result = false;
        if (!uuid.contains(",") && TextUtils.equals(uuid, bindInfo.getUuid())) {
            // 逗号说明是选中多人，无法赠送
            result = true;
        }
        if (!result) {
            XToastUtils.show("绑定礼物，仅能赠送给" + bindInfo.getNickname());
        }
        return result;
    }

    /**
     * 是否允许赠送免费礼物
     * 这里主要用在鲜花的判断上
     *
     * @return
     */
    @Override
    public boolean isAllowSendFreeGift() {
        if (isMultiRoom()) {
            if (mSelectUserView != null) {
                List<LiveGiftUserInfo> selectUserList = mSelectUserView.getSelectUserList();
                if (CollectionUtils.isNotEmpty(selectUserList)) {
                    boolean containRoomOwner = false;
                    for (int i = 0; i < selectUserList.size(); i++) {
                        if (TextUtils.equals(selectUserList.get(i).getUuid(), mHostUuid)) {
                            containRoomOwner = true;
                            break;
                        }
                    }
                    if (!containRoomOwner || selectUserList.size() > 1) {
                        XToastUtils.show("该礼物只能送给房主");
                        return false;
                    }
                    return true;
                }
            }
            return false;
        }
        return true;
    }

    private boolean isMultiRoom() {
        return mRoomType == LiveRoomType.MULTI_PLAYER;
    }

    @Override
    public void liveGiftSelectNumDialogDismiss() {
        if (mOnGiftListShowListener != null) {
            mOnGiftListShowListener.onGiftListShow(false);
        }
        dismiss();
    }


    public void setLiveGiftListHelper(LiveGiftListHelper liveGiftListHelper) {
        mLiveGiftListHelper = liveGiftListHelper;
        if (mLiveGiftListHelper != null) {
            mLiveGiftListHelper.addBalanceChangeListener(this);
            mLiveGiftListHelper.setAllGiftGetListListener(this);
        }
    }

    @Override
    public void dismiss() {
        //super.dismiss();  // 不能用super的方法
        if (getDialog() != null && getDialog().isShowing()) {
            getDialog().dismiss();
        }
    }

    public boolean isShowing() {
        return getDialog() != null && getDialog().isShowing();
    }

    public int showDialog(FragmentTransaction ft, String tag) {
        Loger.i(TAG,"showDialog");
        showDialogTime = System.currentTimeMillis();
        try {
            if (!this.isAdded()) {
                ft.remove(this);
                ft.add(this, tag);
            } else {
                if (getDialog() != null) {
                    getDialog().show();
                }
                ft.show(this);
            }
            mIsShowing = true;
            return ft.commitAllowingStateLoss();
        } catch (IllegalStateException e) {
            e.printStackTrace();
        }
        return 0;
    }

    public void resume() {
        if (mGiftListFragment != null) {
            mGiftListFragment.resume();
        }
    }

    public void pause() {
        if (mGiftListFragment != null) {
            mGiftListFragment.pause();
        }
    }

    public void destroy() {
        EventBusUtils.unregister(this);
        if (mGiftListFragment != null) {
            mGiftListFragment.destroy();
            mGiftListFragment = null;
        }
        if (mPackListFragment != null) {
            mPackListFragment.destroy();
            mPackListFragment = null;
        }
        //LiveFlowerHelper.getInstance().destroy();
        LiveFreeGiftListenerHelper.getInstance().destory();
        if (LiveFlowerHelper.getInstance() != null && LiveFlowerHelper.getInstance().getFlowerViewHolder() != null) {
            LiveFlowerHelper.getInstance().getFlowerViewHolder().resetItemSelect();
        }

        mOnGiftListShowListener = null;
        if (mContext != null) {
            mContext = null;
        }
        if (mBgTopLay != null) {
            mBgTopLay.setBackgroundResource(0);
            mBgTopLay.setOnClickListener(null);
            mBgTopLay = null;
        }
        if (mTvPackGiftNotice != null) {
            mTvPackGiftNotice = null;
        }
        preSelectUserUuid = null;
    }

    public void onEventMainThread(EventTradeSuccess event) {
        if (mLiveGiftListHelper != null) {
            mLiveGiftListHelper.requestBalance();
        }
    }

    /**
     * 刷新指定礼物
     *
     * @param event
     */
    public void onEventMainThread(EventNotifyGiftById event) {
        if (mGiftListFragment != null && event != null && !TextUtils.isEmpty(event.getGiftId())) {
            mGiftListFragment.setDefaultGid(event.getGiftId());
        }
    }

    /**
     * 财富等级提升
     *
     * @param event
     */
    public void onWealthLevelUp(EventMineWealthLevelUp event) {
        if (mGiftListFragment != null) {
            mGiftListFragment.onWealthLevelUp(event);
        }
        refreshViewInfo();
    }

    public void onLoginForLiveEvent(EventXjbLogin event) {
        if (mGiftListFragment != null) {
            mGiftListFragment.onLoginForLiveEvent(event);
            //更新免费礼物信息
            LiveFreeGiftListenerHelper.getInstance().getLightStickNum(getContext(), mHostUuid);
        }
    }

    public void onUserLoginOut(EventXjbUserLoginOut event) {
        if (mGiftListFragment != null) {
            //更新免费礼物信息
            //当登出的时候，把免费礼物的信息注销
            LiveFreeGiftListenerHelper.getInstance().destory();
            mGiftListFragment.onUserLoginOut(event);
        }
    }


    public void setOnGiftListShowListener(LiveGiftListHelper.OnGiftListShowListener onGiftListShowListener) {
        mOnGiftListShowListener = onGiftListShowListener;
    }

    /**
     * 添加callback至回调列表
     *
     * @param callback
     */
    public void setGiftDialogCallback(OnGiftDialogCallback callback) {
        if (mGiftDialogCallbacks != null && callback != null
                && !mGiftDialogCallbacks.contains(callback)) {
            mGiftDialogCallbacks.add(callback);
        }
    }

    public void setDefaultPosition(int defaultPosition) {
        this.mDefaultPosition = defaultPosition;
        if (mIsShowing) {
            showDefaultPosition();
            if (mPackListFragment != null) {
                mPackListFragment.refreshData();
            }
        }
    }


    public void setDefaultGid(String defaultGid) {
        this.mDefaultGid = defaultGid;
    }

    public void setDuduBirdSpecialGid(String specialGid) {
        this.mDuduBirdSpecialGid = specialGid;
    }

    public void setPackDefaultTab(int index) {
        this.mPackTabIndex = index;
    }

    public void setGiftDefaultTab(String tabId) {
        this.mGiftTabIndex = tabId;
    }

    /***
     * 显示默认tab
     */
    private void showDefaultPosition() {
        if (mViewPager != null) {
            mViewPager.post(new Runnable() {
                @Override
                public void run() {
                    if (mDefaultPosition < mViewPager.getChildCount()) {
                        mViewPager.setCurrentItem(mDefaultPosition, false);
                        if (mDefaultPosition == 0) {
                            if (mGiftListFragment != null) {
                                if (!TextUtils.isEmpty(mDefaultGid)) {
                                    mGiftListFragment.setDefaultGid(mDefaultGid);
                                    mDefaultGid = null;
                                } else if (!TextUtils.isEmpty(mGiftTabIndex)) {
                                    mGiftListFragment.setDefaultTab(mGiftTabIndex);
                                    mGiftTabIndex = null;
                                }
                            }
                        } else if (mDefaultPosition == 1) {
                            if (mPackListFragment != null) {
                                mPackListFragment.setTabIndex(mPackTabIndex);
                                mPackTabIndex = 1;
                            }
                        }
                        mDefaultPosition = 0;
                    }
                }
            });
        }
    }


    /**
     * 记录前一次选中的礼物 用来判断是否需要更新礼物描述信息
     */
    private LiveGiftInfo mPriSelectGiftInfo;

    @Override
    public boolean onPreSelectGift(LiveGiftInfo giftInfo) {
        return false;
    }

    @Override
    public void selectGift(LiveGiftInfo info) {
        if (info == null) {
            mPriSelectGiftInfo = null;
            hideGiftDesc(true);
            if (mVipShowView != null) {
                mVipShowView.updateGiftInfo();
            }
            return;
        }
        if (mPriSelectGiftInfo != null && TextUtils.equals(mPriSelectGiftInfo.getGid(), info.getGid())
                && mRLTvGiftDesc.getVisibility() == View.VISIBLE) {
            // 选中礼物没有改变 不更新礼物信息
            return;
        }
        mPriSelectGiftInfo = info;
        if (mStarChallengeMaskView != null && mPriSelectGiftInfo.getWeekStarTitleItem() != null) {
            showStarChallengeMaskAd();
        }
        if (info.isGiftPack() && mVipShowView != null) {
            mVipShowView.updateGiftInfo();
        } else if (mVipShowView != null) {
            mVipShowView.updateGiftInfo(info);
        }
        if (LiveRoomInfoHelper.getInstance().isFancyFishGift(info.getGid())) {
            mFancyFishInfoView.setVisibility(View.INVISIBLE);
            if (mFancyFishBean != null && mFancyFishInfoView != null) {
                showFancyFishInfoAd();
                mFancyFishInfoView.setTag(cn.taqu.lib.base.R.id.click_rel, mFancyFishBean.getRelation());
            }
            mLiveGiftListHelper.requestFancyFishInfo(info.getGid());
            hideGiftDesc(true);
        } else if (mBlindLuckyInfo != null && LiveRoomInfoHelper.getInstance().isBlindBoxGift(info.getGid())) {
            mLiveGiftListHelper.requestBlindLuckyInfo();
            if (mUpUpgradeInfoLayout != null) {
                showUpgradeInfoAd();
                mUpUpgradeInfoLayout.setBlindLuckyInfoData(mBlindLuckyInfo);
            }
            hideGiftDesc(true);
        } else if (TextUtils.equals(info.getType(), GiftType.GIFT_TYPE_UPGRADE)) {
            resetUpgradeInfo(info);
            hideGiftDesc(true);
        } else {
            setGiftAdDesc(info.getIntro(), info);
        }
    }

    // 设置礼物广告为
    private void setGiftAdDesc(LiveGiftDescInfo descInfo, LiveGiftInfo info) {
        if (descInfo == null && mGlobalAdInfo != null && !info.isGiftPack()) {
            descInfo = mGlobalAdInfo;
        }
        if (descInfo == null) {
            hideGiftDesc(false);
            resetUpgradeInfo(info);
            return;
        }
        mUpUpgradeInfoLayout.setVisibility(View.GONE);
        mFancyFishInfoView.setVisibility(View.GONE);
        if (descInfo.isShowText()) {
            if (mRLTvGiftDesc != null) {
                showGiftImgAndTxtDescAd();
                startTextGiftDescAnim();
                mLiveViewModel.getIsGiftDialogDescShowLiveModel().postValue(true);
            }
            if (mTvGiftDescTitle != null) {
                mTvGiftDescTitle.setText(descInfo.getTitle());
            }
            if (mMarqueeGiftDesc != null) {
                mMarqueeGiftDesc.setContent(descInfo.getContent());
            }
            if (mIvGiftDesc != null) {
                mIvGiftDesc.setVisibility(View.GONE);
            }
            //设置隐藏首充优惠广告位
            setChargeAdVisible(true);
        } else if (descInfo.isShowIcon()) {
            if (mRLTvGiftDesc != null) {
                mRLTvGiftDesc.setVisibility(View.GONE);
                mLiveViewModel.getIsGiftDialogDescShowLiveModel().postValue(false);
            }
            if (mMarqueeGiftDesc != null) {
                mMarqueeGiftDesc.stopRoll();
            }
            if (mIvGiftDesc != null) {
                showGiftImgDescAd();
                startIvGiftDescAnim();
                mIvGiftDesc.setImageFromUrl(descInfo.getBannerIcon());
                if (StringUtils.isNotEmpty(descInfo.getBannerSchema())) {
                    mIvGiftDesc.setTag(cn.taqu.lib.base.R.id.click_rel, descInfo.getBannerSchema());
                }
                LiveGiftTracker.Companion.getInstance().liveRoomGiftPlayPopupExposure(mHostUuid, info.getGid(), descInfo.getBannerSchema());
            }
            //设置隐藏首充优惠广告位
            setChargeAdVisible(true);
        } else {
            //设置显示首充优惠广告位
            setChargeAdVisible(false);
        }
    }

    /**
     * 开启文字说明动画
     */
    private void startTextGiftDescAnim() {
        if (mRlTextDescAnimator == null) {
            float bottomMargin = ((ViewGroup.MarginLayoutParams) mRLTvGiftDesc.getLayoutParams()).bottomMargin;
            int rlHeight = mRLTvGiftDesc.getHeight() == 0 ? SizeUtils.dp2px(41f) : mRLTvGiftDesc.getHeight();
            float startY = rlHeight + bottomMargin;
            mRlTextDescAnimator = ObjectAnimator.ofPropertyValuesHolder(mRLTvGiftDesc,
                    PropertyValuesHolder.ofFloat(View.TRANSLATION_Y, startY, 0),
                    PropertyValuesHolder.ofFloat(View.ALPHA, 0f, 1f));
            mRlTextDescAnimator.setDuration(200);
            mRlTextDescAnimator.setInterpolator(new LinearInterpolator());
        }
        mRlTextDescAnimator.cancel();
        mRlTextDescAnimator.start();
    }

    /**
     * 开启图片说明动画
     */
    private void startIvGiftDescAnim() {
        if (mIvDescAnimator == null) {
            float bottomMargin = ((ViewGroup.MarginLayoutParams) mIvGiftDesc.getLayoutParams()).bottomMargin;
            int ivHeight = mIvGiftDesc.getHeight() == 0 ? SizeUtils.dp2px(50f) : mIvGiftDesc.getHeight();
            float startY = ivHeight + bottomMargin;
            mIvDescAnimator = ObjectAnimator.ofPropertyValuesHolder(mIvGiftDesc,
                    PropertyValuesHolder.ofFloat(View.TRANSLATION_Y, startY, 0),
                    PropertyValuesHolder.ofFloat(View.ALPHA, 0f, 1f));
            mIvDescAnimator.setDuration(200);
            mIvDescAnimator.setInterpolator(new LinearInterpolator());
        }
        mIvDescAnimator.cancel();
        mIvDescAnimator.start();
    }

    /**
     * 开始首次广告动画
     */
    private void startFirstChargeIcAnim() {
        if (mFirstChargeIcAnimator != null) {
            mFirstChargeIcAnimator.cancel();
            mFirstChargeIcAnimator = null;
        }
        float bottomMargin = ((ViewGroup.MarginLayoutParams) mIvFirstChargeIcon.getLayoutParams()).bottomMargin;
        float startY = mIvFirstChargeIcon.getHeight() + bottomMargin;
        mFirstChargeIcAnimator = ObjectAnimator.ofPropertyValuesHolder(mIvFirstChargeIcon,
                PropertyValuesHolder.ofFloat(View.TRANSLATION_Y, startY, 0),
                PropertyValuesHolder.ofFloat(View.ALPHA, 0f, 1f));
        mFirstChargeIcAnimator.setDuration(200);
        mFirstChargeIcAnimator.setInterpolator(new LinearInterpolator());
        mFirstChargeIcAnimator.addListener(new SimpleAnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
                showFirstRechargeAd();
            }
        });
        mFirstChargeIcAnimator.start();
    }

    /**
     * 隐藏礼物说明
     *
     * @param isHideChargeAd true-隐藏首充优惠广告位
     */
    private void hideGiftDesc(boolean isHideChargeAd) {
        if (mIvGiftDesc != null) {
            mIvGiftDesc.setVisibility(View.GONE);
        }
        if (mRLTvGiftDesc != null) {
            mRLTvGiftDesc.setVisibility(View.GONE);
            mLiveViewModel.getIsGiftDialogDescShowLiveModel().postValue(false);
        }
        setChargeAdVisible(isHideChargeAd);
    }

    /**
     * 设置首次优惠广告位显隐
     *
     * @param isHideChargeAd
     */
    private void setChargeAdVisible(boolean isHideChargeAd) {
        if (mIvFirstChargeIcon == null) {
            return;
        }
        boolean isVisible = !isHideChargeAd &&
                StringUtils.isNotEmpty(LiveRechargeHelper.getInstance().getBanner(AccountHelper.getAccountUuid()));
        if (isVisible) {
            startFirstChargeIcAnim();
            mLiveViewModel.getIsGiftDialogDescShowLiveModel().postValue(true);
        } else {
            mIvFirstChargeIcon.setVisibility(View.GONE);
        }
    }

    /**
     * 获取当前选中用户uuid
     */
    public String getSelectUserUuidStr() {
        if (mRoomType == LiveRoomType.MULTI_PLAYER && mSelectUserView != null) {
            return mSelectUserView.getSelectUserUuidStr();
        }
        return mHostUuid;
    }

    /**
     * 判断是否符合条件 开启跑马灯
     */
    private void startMarQuee() {
        if (mRLTvGiftDesc != null && mRLTvGiftDesc.getVisibility() == View.VISIBLE) {
            if (mMarqueeGiftDesc != null && !TextUtils.isEmpty(mMarqueeGiftDesc.getContent())) {
                mMarqueeGiftDesc.continueRoll();
            }
        }
    }

    /**
     * 弹窗消失时候隐藏跑马灯
     */
    private void stopMarQuee() {
        if (mMarqueeGiftDesc != null) {
            mMarqueeGiftDesc.stopRoll();
        }
    }

    @Override
    public void onBalance(BalanceHelper.BalanceBean balance) {
        // 趣豆已更新
        if (balance.isTqbeanChanged()) {
            setTvTqbean(String.valueOf(balance.getTqbean()));
        }
        // 贵族趣豆已更新
        if (balance.isNobleTqbeanChanged()) {
            setTvNoblebean();
        }
    }

    @Override
    public void showPropRecordView(boolean isShowProp) {
        if (mSelectedFragment instanceof LivePackListFragment) {
            if (mTvPropRecord != null) {
                mTvPropRecord.setVisibility(isShowProp ? View.VISIBLE : View.GONE);
            }
            if (mRlTqContent != null) {
                mRlTqContent.setVisibility(isShowProp ? View.GONE : View.VISIBLE);
            }
        } else {
            if (mTvPropRecord != null) {
                mTvPropRecord.setVisibility(View.GONE);
            }
            if (mRlTqContent != null) {
                mRlTqContent.setVisibility(View.VISIBLE);
            }
        }
    }

    /**
     * 背包礼物过期提醒
     *
     * @param notice 文案
     */
    @Override
    public void showExpireNotice(String notice) {
        if (TextUtils.isEmpty(notice)) {
            return;
        }
        long noticeTime = KVCacheUtils.getLong(LIVE_GIFT_PACK_RED_KEY + AccountHelper.getAccountUuid(), 0L);
        if (TimeUtils.isToday(noticeTime)) {
            return;
        }
        if (mTvPackGiftNotice != null) {
            mTvPackGiftNotice.setText(notice);
            mTvPackGiftNotice.setVisibility(View.VISIBLE);
            mTvPackGiftNotice.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (mTvPackGiftNotice != null) {
                        mTvPackGiftNotice.setVisibility(View.GONE);
                    }
                }
            }, 5000);
            KVCacheUtils.putLong(LIVE_GIFT_PACK_RED_KEY + AccountHelper.getAccountUuid(), ServerTime.currentTimeMillis());
        }

    }

    /**
     * 显示首充广告位
     * 优先级：首充广告>礼物广告（礼物说明、周星冠名礼物、礼物升级玩法、锦鲤礼物）>tab广告
     */
    @Override
    public void showFirstRechargeAd() {
        if (mIvFirstChargeIcon.getVisibility() != View.VISIBLE) {
            mIvFirstChargeIcon.setVisibility(View.VISIBLE);
        }
        if (mIvGiftDesc.getVisibility() == View.VISIBLE) {
            mIvGiftDesc.setVisibility(View.GONE);
        }
        if (mIvTabAd.getVisibility() == View.VISIBLE) {
            mIvTabAd.setVisibility(View.GONE);
        }
        if (mIvTabAdLottie.getVisibility() == View.VISIBLE) {
            mIvTabAdLottie.setVisibility(View.GONE);
        }
        if (mRLTvGiftDesc.getVisibility() == View.VISIBLE) {
            mRLTvGiftDesc.setVisibility(View.GONE);
        }
        if (mStarChallengeMaskView.getVisibility() == View.VISIBLE) {
            mStarChallengeMaskView.setVisibility(View.GONE);
        }
        if (mUpUpgradeInfoLayout.getVisibility() == View.VISIBLE) {
            mUpUpgradeInfoLayout.setVisibility(View.GONE);
        }
        if (mFancyFishInfoView.getVisibility() == View.VISIBLE) {
            mFancyFishInfoView.setVisibility(View.GONE);
        }
    }

    /**
     * 显示礼物描述广告位(图文格式)
     * 优先级：首充广告>礼物广告（礼物说明、周星冠名礼物、礼物升级玩法、锦鲤礼物）>tab广告
     */
    private void showGiftImgAndTxtDescAd() {
        if (mRLTvGiftDesc.getVisibility() != View.VISIBLE) {
            mRLTvGiftDesc.setVisibility(View.VISIBLE);
        }
        if (mIvFirstChargeIcon.getVisibility() == View.VISIBLE) {
            mIvFirstChargeIcon.setVisibility(View.GONE);
        }
        if (mIvGiftDesc.getVisibility() == View.VISIBLE) {
            mIvGiftDesc.setVisibility(View.GONE);
        }
        if (mIvTabAd.getVisibility() == View.VISIBLE) {
            mIvTabAd.setVisibility(View.GONE);
        }
        if (mIvTabAdLottie.getVisibility() == View.VISIBLE) {
            mIvTabAdLottie.setVisibility(View.GONE);
        }
        if (mStarChallengeMaskView.getVisibility() == View.VISIBLE) {
            mStarChallengeMaskView.setVisibility(View.GONE);
        }
        if (mUpUpgradeInfoLayout.getVisibility() == View.VISIBLE) {
            mUpUpgradeInfoLayout.setVisibility(View.GONE);
        }
        if (mFancyFishInfoView.getVisibility() == View.VISIBLE) {
            mFancyFishInfoView.setVisibility(View.GONE);
        }
    }

    /**
     * 显示礼物描述广告位(图片格式)
     * 优先级：首充广告>礼物广告（礼物说明、周星冠名礼物、礼物升级玩法、锦鲤礼物）>tab广告
     */
    private void showGiftImgDescAd() {
        if (mIvGiftDesc.getVisibility() != View.VISIBLE) {
            mIvGiftDesc.setVisibility(View.VISIBLE);
        }
        if (mRLTvGiftDesc.getVisibility() == View.VISIBLE) {
            mRLTvGiftDesc.setVisibility(View.GONE);
        }
        if (mIvFirstChargeIcon.getVisibility() == View.VISIBLE) {
            mIvFirstChargeIcon.setVisibility(View.GONE);
        }
        if (mIvTabAd.getVisibility() == View.VISIBLE) {
            mIvTabAd.setVisibility(View.GONE);
        }
        if (mIvTabAdLottie.getVisibility() == View.VISIBLE) {
            mIvTabAdLottie.setVisibility(View.GONE);
        }
        if (mStarChallengeMaskView.getVisibility() == View.VISIBLE) {
            mStarChallengeMaskView.setVisibility(View.GONE);
        }
        if (mUpUpgradeInfoLayout.getVisibility() == View.VISIBLE) {
            mUpUpgradeInfoLayout.setVisibility(View.GONE);
        }
        if (mFancyFishInfoView.getVisibility() == View.VISIBLE) {
            mFancyFishInfoView.setVisibility(View.GONE);
        }
        if (mIvFirstChargeIcon.getVisibility() == View.VISIBLE) {
            mIvFirstChargeIcon.setVisibility(View.GONE);
        }
    }


    /**
     * 显示周星冠名礼物广告位
     * 优先级：首充广告>礼物广告（礼物说明、周星冠名礼物、礼物升级玩法、锦鲤礼物）>tab广告
     */
    private void showStarChallengeMaskAd() {
        if (mStarChallengeMaskView != null && mPriSelectGiftInfo != null) {
            mStarChallengeMaskView.setDta(mPriSelectGiftInfo.getWeekStarTitleItem());
        }
        if (mRLTvGiftDesc.getVisibility() == View.VISIBLE) {
            mRLTvGiftDesc.setVisibility(View.GONE);
        }
        if (mIvFirstChargeIcon.getVisibility() == View.VISIBLE) {
            mIvFirstChargeIcon.setVisibility(View.GONE);
        }
        if (mIvTabAd.getVisibility() == View.VISIBLE) {
            mIvTabAd.setVisibility(View.GONE);
        }
        if (mIvTabAdLottie.getVisibility() == View.VISIBLE) {
            mIvTabAdLottie.setVisibility(View.GONE);
        }
        if (mUpUpgradeInfoLayout.getVisibility() == View.VISIBLE) {
            mUpUpgradeInfoLayout.setVisibility(View.GONE);
        }
        if (mFancyFishInfoView.getVisibility() == View.VISIBLE) {
            mFancyFishInfoView.setVisibility(View.GONE);
        }
        if (mIvGiftDesc.getVisibility() == View.VISIBLE) {
            mIvGiftDesc.setVisibility(View.GONE);
        }
    }

    /**
     * 显示礼物升级玩法广告位
     * 优先级：首充广告>礼物广告（礼物说明、周星冠名礼物、礼物升级玩法、锦鲤礼物）>tab广告
     */
    private void showUpgradeInfoAd() {
        if (mUpUpgradeInfoLayout.getVisibility() != View.VISIBLE) {
            mUpUpgradeInfoLayout.setVisibility(View.VISIBLE);
        }
        if (mRLTvGiftDesc.getVisibility() == View.VISIBLE) {
            mRLTvGiftDesc.setVisibility(View.GONE);
        }
        if (mIvFirstChargeIcon.getVisibility() == View.VISIBLE) {
            mIvFirstChargeIcon.setVisibility(View.GONE);
        }
        if (mIvTabAd.getVisibility() == View.VISIBLE) {
            mIvTabAd.setVisibility(View.GONE);
        }
        if (mIvTabAdLottie.getVisibility() == View.VISIBLE) {
            mIvTabAdLottie.setVisibility(View.GONE);
        }
        if (mStarChallengeMaskView.getVisibility() == View.VISIBLE) {
            mStarChallengeMaskView.setVisibility(View.GONE);
        }
        if (mFancyFishInfoView.getVisibility() == View.VISIBLE) {
            mFancyFishInfoView.setVisibility(View.GONE);
        }
        if (mIvGiftDesc.getVisibility() == View.VISIBLE) {
            mIvGiftDesc.setVisibility(View.GONE);
        }
    }

    /**
     * 显示锦鲤礼物广告位
     * 优先级：首充广告>礼物广告（礼物说明、周星冠名礼物、礼物升级玩法、锦鲤礼物）>tab广告
     */
    private void showFancyFishInfoAd() {
        if (mFancyFishInfoView.getVisibility() != View.VISIBLE) {
            mFancyFishInfoView.setVisibility(View.VISIBLE);
        }
        if (mUpUpgradeInfoLayout.getVisibility() == View.VISIBLE) {
            mUpUpgradeInfoLayout.setVisibility(View.GONE);
        }
        if (mRLTvGiftDesc.getVisibility() == View.VISIBLE) {
            mRLTvGiftDesc.setVisibility(View.GONE);
        }
        if (mIvFirstChargeIcon.getVisibility() == View.VISIBLE) {
            mIvFirstChargeIcon.setVisibility(View.GONE);
        }
        if (mIvTabAd.getVisibility() == View.VISIBLE) {
            mIvTabAd.setVisibility(View.GONE);
        }
        if (mIvTabAdLottie.getVisibility() == View.VISIBLE) {
            mIvTabAdLottie.setVisibility(View.GONE);
        }
        if (mStarChallengeMaskView.getVisibility() == View.VISIBLE) {
            mStarChallengeMaskView.setVisibility(View.GONE);
        }
        if (mIvGiftDesc.getVisibility() == View.VISIBLE) {
            mIvGiftDesc.setVisibility(View.GONE);
        }
    }

    /**
     * 显示tab广告位
     * 优先级：首充广告>礼物广告（礼物说明、周星冠名礼物、礼物升级玩法、锦鲤礼物）>tab广告
     */
    @Override
    public void showTabInfoAd(@Nullable LiveTabGiftItemean selectTabGiftItem) {
        mIvTabAd.setVisibility(View.GONE);
        mIvTabAdLottie.setVisibility(View.GONE);
        if (selectTabGiftItem == null) {
            return;
        }
        if (mFancyFishInfoView.getVisibility() == View.VISIBLE) {
            mFancyFishInfoView.setVisibility(View.GONE);
        }
        if (mIvFirstChargeIcon.getVisibility() == View.VISIBLE) {
            mIvFirstChargeIcon.setVisibility(View.GONE);
        }
        if (mUpUpgradeInfoLayout.getVisibility() == View.VISIBLE) {
            mUpUpgradeInfoLayout.setVisibility(View.GONE);
        }
        if (mRLTvGiftDesc.getVisibility() == View.VISIBLE) {
            mRLTvGiftDesc.setVisibility(View.GONE);
        }
        if (mStarChallengeMaskView.getVisibility() == View.VISIBLE) {
            mStarChallengeMaskView.setVisibility(View.GONE);
        }
        if (mIvGiftDesc.getVisibility() == View.VISIBLE) {
            mIvGiftDesc.setVisibility(View.GONE);
        }
        // 先判断是否有动态广告，没有再显示静态广告
        String lottieId = selectTabGiftItem.getTabAdLottieId();
        String tabUrl = selectTabGiftItem.getTabAdUrl();
        if (!TextUtils.isEmpty(lottieId)) {
            // 动态广告
            LiveGiftTracker.Companion.getInstance().liveRoomGiftTabAdExposure(selectTabGiftItem.getTabAdRelation());
            mIvTabAdLottie.setTag(R.id.live_tab_ad_relation_id, selectTabGiftItem.getTabAdRelation());
            mIvTabAdLottie.setVisibility(View.VISIBLE);
            XLottiePlayer.playById(mIvTabAdLottie, lottieId, 0);
        } else if (!TextUtils.isEmpty(tabUrl)) {
            // 静态广告
            LiveGiftTracker.Companion.getInstance().liveRoomGiftTabAdExposure(selectTabGiftItem.getTabAdRelation());
            mIvTabAd.setTag(R.id.live_tab_ad_relation_id, selectTabGiftItem.getTabAdRelation());
            mIvTabAd.setVisibility(View.VISIBLE);
            mIvTabAd.setImageFromUrl(tabUrl);
        }
    }

    /**
     * 初始化选择用户布局
     */
    private void initSelectUserView() {
        if (mSelectUserView == null && mSelectUserViewStub != null) {
            View view = mSelectUserViewStub.inflate();
            mSelectUserView = view.findViewById(R.id.liveGiftSelectUserView);
        }
        if (mSelectUserView != null) {
            mSelectUserView.setVisibility(View.VISIBLE);
        }
    }

    private long currentShellBean = 0L;

    @Override
    public void onShellBalance(long shellBean) {
        if (isHideShellLay) {
            resetShellStatus(true);
            return;
        }
        if (currentShellBean < shellBean) {
            // 标识兑换贝壳成功，更新用户等级
            refreshViewInfo();
        }
        currentShellBean = shellBean;
        if (tvShellNum != null) {
            tvShellNum.setText(String.valueOf(shellBean));
        }
    }

    @Override
    public void onShellBalanceInfo(@Nullable ShellBalanceBean shellBeanInfo) {
        if (shellBeanInfo == null) {
            return;
        }
        if (isHideShellLay) {
            return;
        }
        dealShellGuide(shellBeanInfo.getShellIntroRelation(),shellBeanInfo.getTipInfo());
    }

    /**
     * 处理贝壳过期引导
     *
     * @param left    间距
     * @param builder 提示文案
     */
    private void dealShellExpiredGuide(int left, SpannableStringBuilder builder) {
        if (TextUtils.isEmpty(builder)) {
            return;
        }
        long showTime = KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).getLong(LiveShellConstants.KEY_LIVE_SHELL_TIP_EXPIRE, 0);
        if (TimeUtils.isToday(showTime)) {
            return;
        }
        KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).putLong(LiveShellConstants.KEY_LIVE_SHELL_TIP_EXPIRE, System.currentTimeMillis());
        new LiveShellExpiredGuideDialog(mContext, left, builder).show();
    }

    /**
     * 处理贝壳首次引导
     *
     * @param relation 跳转地址
     */
    private void dealShellGuide(String relation, ShellBalanceTipInfoBean bean) {
        if(tvShellNum == null){
            return;
        }
        boolean isShow = KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).getBoolean(LiveShellConstants.KEY_LIVE_SHELL_SHOW_GUIDE, false);
        if (isShow) {
            if (bean != null) {
                dealShellExpiredGuide(tvShellNum.getLeft(), bean.getTipSpan());
            }
            return;
        }
       int left = tvShellNum.getLeft();
        KVCacheUtils.getAccountKVStrategy(AccountHelper.getAccountUuid()).putBoolean(LiveShellConstants.KEY_LIVE_SHELL_SHOW_GUIDE, true);
        new LiveShellInstructionsGuideDialog(mContext, relation, left, bean.getTipSpan()).show();
    }

    @Override
    public void onShellClose() {

    }
}