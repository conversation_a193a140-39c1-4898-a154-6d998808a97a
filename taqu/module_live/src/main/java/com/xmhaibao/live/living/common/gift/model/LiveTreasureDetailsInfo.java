package com.xmhaibao.live.living.common.gift.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

import cn.taqu.lib.okhttp.model.IDoExtra;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import hb.common.helper.HostHelper;
import hb.utils.WebpUtils;

/**
 * @auther SuZhanFeng
 * @date 2020/6/11
 * @desc
 */
public class LiveTreasureDetailsInfo implements IDoExtra {
    private String popularity;
    @SerializedName("receiver_num")
    private String intoNum;
    @SerializedName("sender_uuid_list")
    private List<LiveTreasureSenderItemInfo> senderUuidList;
    @SerializedName("rank_list")
    private List<LiveTreasureSenderItemInfo> rankList;
    @SerializedName("special_uuid_list")
    private List<LiveTreasureSpecialItemInfo> specialUuidList;

    public String getPopularity() {
        return popularity;
    }

    public void setPopularity(String popularity) {
        this.popularity = popularity;
    }

    public String getIntoNum() {
        return intoNum;
    }

    public void setIntoNum(String intoNum) {
        this.intoNum = intoNum;
    }

    public List<LiveTreasureSenderItemInfo> getSenderUuidList() {
        return senderUuidList;
    }

    public void setSenderUuidList(List<LiveTreasureSenderItemInfo> senderUuidList) {
        this.senderUuidList = senderUuidList;
    }

    public List<LiveTreasureSenderItemInfo> getRankList() {
        return rankList;
    }

    public void setRankList(List<LiveTreasureSenderItemInfo> rankList) {
        this.rankList = rankList;
    }

    public List<LiveTreasureSpecialItemInfo> getSpecialUuidList() {
        return specialUuidList;
    }

    public void setSpecialUuidList(List<LiveTreasureSpecialItemInfo> specialUuidList) {
        this.specialUuidList = specialUuidList;
    }

    @Override
    public void doExtra(IResponseInfo response) {
        if (senderUuidList != null) {
            for (LiveTreasureSenderItemInfo itemInfo : senderUuidList) {
                itemInfo.setAvatar(WebpUtils.getWebpUrl_4_1(itemInfo.getAvatar(), HostHelper.getInstance().getHostAvatar()));
                itemInfo.setIcon(WebpUtils.getWebpUrl_4_1(itemInfo.getIcon(), HostHelper.getInstance().getHostImg()));
            }
        }

        if (specialUuidList != null) {
            for (LiveTreasureSpecialItemInfo itemInfo : specialUuidList) {
                if (itemInfo.isPet()) {
                    itemInfo.setAvatar(WebpUtils.getWebpUrl_4_1(itemInfo.getAvatar(), HostHelper.getInstance().getHostImg()));
                } else {
                    itemInfo.setAvatar(WebpUtils.getWebpUrl_4_1(itemInfo.getAvatar(), HostHelper.getInstance().getHostAvatar()));
                }
                itemInfo.setIcon(WebpUtils.getWebpUrl_4_1(itemInfo.getIcon(), HostHelper.getInstance().getHostImg()));
            }
        }

        if (rankList != null) {
            for (int i = 0; i < rankList.size(); i++) {
                LiveTreasureSenderItemInfo itemInfo = rankList.get(i);
                itemInfo.setIndex(i);
                if (itemInfo.isPet()) {
                    itemInfo.setAvatar(WebpUtils.getWebpUrl_4_1(itemInfo.getAvatar(), HostHelper.getInstance().getHostImg()));
                } else {
                    itemInfo.setAvatar(WebpUtils.getWebpUrl_4_1(itemInfo.getAvatar(), HostHelper.getInstance().getHostAvatar()));
                }
                itemInfo.setIcon(WebpUtils.getWebpUrl_4_1(itemInfo.getIcon(), HostHelper.getInstance().getHostImg()));

            }
        }
    }
}
