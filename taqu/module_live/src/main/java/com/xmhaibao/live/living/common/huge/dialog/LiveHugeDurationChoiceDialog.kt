package com.xmhaibao.live.living.common.huge.dialog

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import com.xmhaibao.live.databinding.LiveHugeDurationChoiceDialogBinding
import com.xmhaibao.live.living.common.huge.holder.LiveHugeDurationChoiceHolder
import com.xmhaibao.live.living.common.huge.model.PurchaseItem
import hb.utils.ScreenUtils
import hb.xadapter.XBaseAdapter
import hb.xstyle.xdialogfragment.XDialogFragment

/**
 * 类描述：开通/续费-时长选择弹窗
 *
 * <AUTHOR>
 * @date 2023-02-28
 */
class LiveHugeDurationChoiceDialog : XDialogFragment(), XBaseAdapter.OnItemClickListener {
    private val KEY_PARAMS_PURCHASE = "KEY_PARAMS_PURCHASE"
    private var mAdapter: XBaseAdapter? = null
    private var mItemSelectedBlock: ((item: PurchaseItem) -> Unit)? = null

    companion object {
        fun newInstance(
            data: ArrayList<PurchaseItem>,
            itemSelectedBlock: ((item: PurchaseItem) -> Unit)? = null
        ) =
            LiveHugeDurationChoiceDialog().apply {
                mItemSelectedBlock = itemSelectedBlock
                val params = Bundle()
                params.putSerializable(KEY_PARAMS_PURCHASE, data)
                arguments = params
            }
    }

    private val mViewBinding: LiveHugeDurationChoiceDialogBinding by lazy {
        LiveHugeDurationChoiceDialogBinding.inflate(
            layoutInflater
        )
    }

    override fun onCreateContentView() = mViewBinding.root

    override fun initConfig(argumentBundle: Bundle) {
        setConfig(ConfigStyle.CENTER.config().setHeight(ScreenUtils.getScreenHeight()))
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initViews(rootView: View?) {
        mViewBinding.root.setOnTouchListener { _, _ ->
            dismiss()
            false
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        arguments?.getSerializable(KEY_PARAMS_PURCHASE)?.let {
            it as ArrayList<PurchaseItem>
            mAdapter = XBaseAdapter(context).apply {
                register(PurchaseItem::class.java, LiveHugeDurationChoiceHolder::class.java)
                items = it
                setOnItemClickListener(this@LiveHugeDurationChoiceDialog)
            }
            mViewBinding.rvDurationSelect.adapter = mAdapter
        }
    }

    override fun onItemClick(view: View?, position: Int) {
        mAdapter?.getItem<PurchaseItem>(position)?.let {
            mItemSelectedBlock?.invoke(it)
            dismiss()
        }
    }

}