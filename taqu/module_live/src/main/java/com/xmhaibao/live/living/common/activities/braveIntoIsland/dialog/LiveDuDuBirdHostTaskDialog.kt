package com.xmhaibao.live.living.common.activities.braveIntoIsland.dialog

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import com.xmhaibao.live.R
import com.xmhaibao.live.databinding.LiveDudubirdHostTaskDialogLayBinding
import com.xmhaibao.live.living.common.activities.braveIntoIsland.helper.LiveBraveIntoIslandHelper
import com.xmhaibao.live.living.common.activities.braveIntoIsland.tracker.LiveDuDuBirdTracker
import hb.utils.SizeUtils
import hb.xstyle.xdialog.XLifecycleDialog


/**
 * 勇闯冒险岛 主播助力提示弹窗
 *
 * <AUTHOR>
 * @date 2024/3/12
 */
class LiveDuDuBirdHostTaskDialog(context: Context) : XLifecycleDialog(context, cn.taqu.lib.base.R.style.CustomTheme_Dialog) {

    private lateinit var mViewBinding: LiveDudubirdHostTaskDialogLayBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mViewBinding = LiveDudubirdHostTaskDialogLayBinding.inflate(LayoutInflater.from(context))
        setContentView(mViewBinding.root)
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        mViewBinding.ivBird.setImageFromResource(R.drawable.live_dudubird_host_task_dialog_ic)
        mViewBinding.ivBackground.setImageFromResource(hb.xstyle.R.drawable.xdialog_cover_bg)
        mViewBinding.ivClose.setOnClickListener {
            dismiss()
        }
        mViewBinding.tvBtn.setOnClickListener {
            // 助力主播
            LiveDuDuBirdTracker().trackHelpClk()
            LiveBraveIntoIslandHelper.hostJoin()
            dismiss()
        }
    }

    override fun onStart() {
        super.onStart()
        window?.run {
            attributes.width = SizeUtils.dp2px(270f)
        }
    }
}