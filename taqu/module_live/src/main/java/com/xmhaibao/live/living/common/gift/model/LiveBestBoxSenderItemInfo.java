package com.xmhaibao.live.living.common.gift.model;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;

/**
 * @auther <PERSON><PERSON>han<PERSON><PERSON>
 * @date 2020/3/13
 * @desc
 */
public class LiveBestBoxSenderItemInfo {


    /**
     * uuid : fh08ex6g9zb
     * num : 2
     * nickname : 五松果
     * avatar : /avatar/f14318068a9cd05a04e66e5fdb2febfc.png
     */

    private String uuid;
    private String num;
    private String nickname;
    private String avatar;
    @SerializedName("is_pet")
    private String isPet;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getIsPet() {
        return isPet;
    }

    public void setIsPet(String isPet) {
        this.isPet = isPet;
    }

    public boolean isPet() {
        return TextUtils.equals(isPet, "1");
    }
}
