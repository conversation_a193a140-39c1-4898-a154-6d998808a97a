package com.xmhaibao.live.living.common.intoroom.helper;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.xmhaibao.gift.ConstGiftId;
import com.xmhaibao.gift.helper.EffectCallback;
import com.xmhaibao.gift.helper.IEffectHelper;
import com.xmhaibao.into.room.inter.IIntoRoomAnimatorListener;
import com.xmhaibao.into.room.widget.IntoRoomAnimatorView;
import com.xmhaibao.live.living.common.intoroom.model.EventLiveMsgIntoRoom;
import com.xmhaibao.live.living.common.userinfodialog.view.LiveUserInfoDialog;
import com.xmhaibao.noble.api.helper.NobleHelper;
import com.xmhaibao.urd.URDManager;
import com.xmhaibao.urd.bean.ResourceItemBean;
import com.xmhaibao.urd.readytouse.interf.OnUseDownloadCallBack;

import java.util.ArrayList;
import java.util.List;

import hb.common.data.AccountHelper;
import hb.utils.CollectionUtils;
import hb.utils.Loger;
import hb.utils.SPUtils;
import hb.utils.StringUtils;

/**
 * @author: wusongyuan
 * @date: 2016-08-03
 * @desc:
 */
public class LiveIntoRoomAnimatorHelper implements EffectCallback, IIntoRoomAnimatorListener {

    private static final String TAG = "LiveIntoRoomAnimator";

    private List<EventLiveMsgIntoRoom> mIntoRoomList;
    private boolean mIsFirstIntoRoom = true;
    // 礼物类型海豹特效是否在播放，如果礼物海豹特效正在播放，抛弃掉当前需要播放的进场海豹特效
    private boolean mIsGiftHbEffectStarted = false;
    private boolean mIsIntoRoomHbEffectStarted = false;
    private boolean mIsIntoRoomArrivalStarted = false;
    private IntoRoomAnimatorView mIntoRoomAnimatorView;

    private IEffectHelper mEffectHelper;

    public LiveIntoRoomAnimatorHelper(String hostUuid, IntoRoomAnimatorView intoRoomAnimatorView, IEffectHelper effectHelper) {
        mIntoRoomAnimatorView = intoRoomAnimatorView;
        mIntoRoomAnimatorView.setIntoRoomListener(this);
        mIntoRoomAnimatorView.setTag(hostUuid);
        mIntoRoomAnimatorView.setArrivalClickListener(event -> {
            if (mIntoRoomAnimatorView != null) {
                LiveUserInfoDialog.showSeeNoHostDialog(mIntoRoomAnimatorView.getContext(), hostUuid, event.getAccountUuid());
            }
        });
        mEffectHelper = effectHelper;
        mEffectHelper.addHbEffectCallback(this);
    }

    private Runnable postLazyRunnable = new Runnable() {
        @Override
        public void run() {
            Looper.myQueue().addIdleHandler(() -> {
                if (timeOutRunnable != null) {
                    timeOutRunnable.run();
                }
                return false;
            });
        }
    };

    /**
     * 不管谁执行，都会 都会取消 timeOutRunnable 的持有
     * 所以这个任务只会执行一次，timeout 和 idle 相互竞争
     */
    private Runnable timeOutRunnable = new Runnable() {
        @Override
        public void run() {
            if (mStartIntoRoomRunnable != null) {
                mStartIntoRoomRunnable.run();
            }
            timeOutRunnable = null;
        }
    };

    /**
     * 土豪进房提醒
     */
    public void intoRoomByVip(EventLiveMsgIntoRoom event) {
        if (event == null) {
            return;
        }
        if (event.getDriveItemBean() != null) {
            String effectId = event.getDriveItemBean().getEffectId();
            if(TextUtils.isEmpty(effectId)){
                dealIntoRoomByVip(event);
            } else {
                URDManager.getInstance().getPagDataById(effectId, new OnUseDownloadCallBack() {
                    @Override
                    public void onUseDownloadSuccess(boolean isDownload, ResourceItemBean bean) {
                        if (event != null) {
                            event.setAdEffectBean(bean);
                            dealIntoRoomByVip(event);
                        }
                    }

                    @Override
                    public void onUseDownloadError(String id, String error) {
                        Loger.e(TAG, "onUseDownloadError: id: " + id + " error: " + error);
                        if (event != null) {
                            dealIntoRoomByVip(event);
                        }
                    }
                });
            }
        } else {
            dealIntoRoomByVip(event);
        }
    }

    /**
     * 土豪进房提醒
     */
    private void dealIntoRoomByVip(EventLiveMsgIntoRoom event) {
        if (mIntoRoomList == null) {
            mIntoRoomList = new ArrayList<>();
        }

        checkPriority(event);
        int index = 0;
        for (EventLiveMsgIntoRoom msgIntoRoom : mIntoRoomList) {
            // 值越低优先级越高
            if (msgIntoRoom.getIntoPriority() > event.getIntoPriority()) {
                break;
            }
            index++;
        }
        Loger.i(TAG, "intoRoomByVip() index:" + index);
        mIntoRoomList.add(index, event);
        if (mIsFirstIntoRoom) {
            // 避免Activity还在加载过程, 延迟执行
            mIsFirstIntoRoom = false;
            if (mIntoRoomAnimatorView != null && mStartIntoRoomRunnable != null) {
                // 这里需要包一层，destroy 的时候方便移除，跟之前的逻辑保持一致
                mIntoRoomAnimatorView.post(postLazyRunnable);
                mIntoRoomAnimatorView.postDelayed(() -> {

                    if (timeOutRunnable != null) {
                        timeOutRunnable.run();
                    }
                }, 2500L);
            }
        } else {
            if (mIntoRoomAnimatorView != null && mStartIntoRoomRunnable != null) {
                mIntoRoomAnimatorView.post(mStartIntoRoomRunnable);
            }
        }

    }

    private void checkPriority(EventLiveMsgIntoRoom event) {
        if (isUserSelf(event)) {
            // 自己的进房动效
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.OWNE);
        } else {
            // 别人的进房动效
            setPriority(event);
        }
    }

    public void setPriority(EventLiveMsgIntoRoom event) {
        if (event.getNobleCode() == NobleHelper.TYPE_HUANG_DI) {
            // 皇帝
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.NOBLE_HUANG);
        } else if (event.getNobleCode() == NobleHelper.TYPE_GUO_WANG) {
            // 国王
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.NOBLE_GUO);
        } else if (event.isFamousMan()) {
            // 名人堂特权
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.FAMOUS_MAN);
        } else if (event.getNobleCode() == NobleHelper.TYPE_GONG_JUE) {
            // 公爵
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.NOBLE_GONG);
        } else if (StringUtils.stringToInt(event.getCommonLevel()) > 28) {
            // 财富等级28以上
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.VIP_29);
        } else if (StringUtils.stringToInt(event.getCommonLevel()) > 22) {
            // 财富等级22以上
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.VIP_23);
        } else if (StringUtils.stringToInt(event.getCommonLevel()) > 15) {
            // 财富等级16到22
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.VIP_16);
        } else if (StringUtils.stringToInt(event.getCommonLevel()) > 10) {
            // 财富等级11到15
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.VIP_11);
        } else if (StringUtils.stringToInt(event.getFanBoyLevel()) > 6) {
            // 粉丝团7级以上
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.FANBOY_8);
        } else if (StringUtils.stringToInt(event.getCommonLevel()) > 6) {
            // 财富等级7到10
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.VIP_7);
        } else if (StringUtils.stringToInt(event.getFanBoyLevel()) > 3) {
            // 粉丝团5级到7级
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.FANBOY_5);
        } else if (StringUtils.stringToInt(event.getCommonLevel()) > 0) {
            // 财富等级1到6
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.VIP_1);
        } else if (event.isFirstPay()) {
            // 首充礼包用户
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.FIRST_PAY);
        } else if (StringUtils.stringToInt(event.getFanBoyLevel()) > 0) {
            // 粉丝团1级到4级
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.FANBOY_1);
        } else {
            // 普通用户（包含机器人）
            event.setPriority(EventLiveMsgIntoRoom.IntoRoomPriority.COMMON);
        }
    }

    private boolean isUserSelf(EventLiveMsgIntoRoom event) {
        return AccountHelper.isUserLogined() && event != null && TextUtils.equals(event.getAccountUuid(),
                AccountHelper.getAccountUuid());
    }


    private Runnable mStartIntoRoomRunnable = new Runnable() {
        @Override
        public void run() {
            Log.i(TAG, "run: ");
            startIntoRoomAnimator();
        }
    };

    private void startIntoRoomAnimator() {
        if (mIntoRoomList == null || mIntoRoomList.isEmpty() || mIsIntoRoomHbEffectStarted || mIsIntoRoomArrivalStarted) {
            return;
        }
        EventLiveMsgIntoRoom event = mIntoRoomList.get(0);
        Log.i(TAG, "startIntoRoomAnimator: " + event);
        mIntoRoomList.remove(0);
        if (event == null) {
            return;
        }

        boolean isOpenLowApproach = SPUtils.getInstance().getBoolean("LOW_APPROACH", false);
        // [低调进场]打开时，不显示座驾特效
        if (!isOpenLowApproach) {
            // 座驾海豹特效
            if (mEffectHelper != null && event.getDriveItemBean() != null && event.getAdEffectBean() != null) {
                Log.i(TAG, "startIntoRoomAnimator: 1");
                if (!mIsGiftHbEffectStarted && !isStopEffect && !isStopIntoRoomEffect) {
                    // 当前礼物特效没有播放
                    mEffectHelper.startAnimation(ConstGiftId.PRIORITY_CAR, event.getAdEffectBean(), false);
                }
                if (mIntoRoomAnimatorView != null) {
                    mIntoRoomAnimatorView.setData(event.createIntoRoomMessageBean(), true);
                }
                return;
            }
        }

        if (mIntoRoomAnimatorView != null) {
            Log.i(TAG, "startIntoRoomAnimator: 3");
            mIntoRoomAnimatorView.setData(event.createIntoRoomMessageBean(), true);
        }
    }

    public void destroy() {
        // 这里清 mStartIntoRoomRunnable postLazyRunnable ，避免动画执行
        if (mIntoRoomAnimatorView != null && mStartIntoRoomRunnable != null) {
            mIntoRoomAnimatorView.removeCallbacks(mStartIntoRoomRunnable);
        }
        if (mIntoRoomAnimatorView != null) {
            mIntoRoomAnimatorView.removeCallbacks(postLazyRunnable);
        }
        mStartIntoRoomRunnable = null;

        if (mIntoRoomAnimatorView != null) {
            mIntoRoomAnimatorView.destroy();
            mIntoRoomAnimatorView.setIntoRoomListener(null);
            mIntoRoomAnimatorView = null;
        }

    }

    @Override
    public void onHbEffectPrepare(int priority) {
        // do nothing
    }

    @Override
    public void onHbEffectError(String gid, int priority, int errorCode, String errorMsg) {
        if (priority != ConstGiftId.PRIORITY_CAR) {
            // 当前是礼物特效出错误
            mIsGiftHbEffectStarted = false;
            return;
        }
        mIsIntoRoomHbEffectStarted = false;

    }

    @Override
    public void onHbEffectEnd(int priority) {
        if (priority != ConstGiftId.PRIORITY_CAR) {
            //当前是礼物海豹特效播放结束
            mIsGiftHbEffectStarted = false;
            return;
        }

        mIsIntoRoomHbEffectStarted = false;
        if (!CollectionUtils.isEmpty(mIntoRoomList)) {
            Log.i(TAG, "onHbEffectEnd: ");
            startIntoRoomAnimator();
        }
    }

    @Override
    public void onHbEffectStart(int priority) {
        if (priority != ConstGiftId.PRIORITY_CAR) {
            // 当前是礼物海豹特效播放开始
            mIsGiftHbEffectStarted = true;
            mIsIntoRoomHbEffectStarted = false;
            return;
        }
        mIsIntoRoomHbEffectStarted = true;
    }

    @Override
    public void onHbEffectTime(int priority, int second) {

    }

    @Override
    public void onHbEffectNoFound(String gid, int priority, int errorCode, String errorMsg) {
        if (priority != ConstGiftId.PRIORITY_CAR) {
            // 当前是礼物海豹特效播放资源未找到
            mIsGiftHbEffectStarted = false;
            return;
        }
        mIsIntoRoomHbEffectStarted = false;

    }

    @Override
    public void onIntoRoomAnimStart() {
        mIsIntoRoomArrivalStarted = true;
    }

    @Override
    public void onIntoRoomAnimNext() {
        mIsIntoRoomArrivalStarted = false;
        Log.i(TAG, "onIntoRoomAnimNext: ");
        startIntoRoomAnimator();
    }

    /**
     * 是否需要停止播放海豹特效
     */
    private boolean isStopEffect;

    /**
     * 是否需要停止播放进场海豹特效
     */
    private boolean isStopIntoRoomEffect;

    public void stopEffect() {
        // 中断当前特效队列
        isStopEffect = true;
        if (mIsIntoRoomHbEffectStarted) {
            mIsIntoRoomHbEffectStarted = false;
            // 表示当前正在播放特效座驾
            if (mEffectHelper != null) {
                mEffectHelper.interruptAnim(ConstGiftId.PRIORITY_CAR);
            }
        }
    }

    public void reStart() {
        isStopEffect = false;
    }

    /***
     * 设置是否关闭进场海豹特效
     * @param isStopIntoRoomEffect
     */
    public void stopIntoRoomEffect(boolean isStopIntoRoomEffect) {
        this.isStopIntoRoomEffect = isStopIntoRoomEffect;
    }

    /**
     * 观众端设置座驾动效开关 不影响主播端
     * @param isOpen
     */
    public void setCarEffectSwitch(boolean isOpen) {
        stopIntoRoomEffect(!isOpen);
        if (isOpen) {
            reStart();
        } else {
            stopEffect();
            if (mIntoRoomList != null) {
                mIntoRoomList.clear();
            }
        }
    }
}
