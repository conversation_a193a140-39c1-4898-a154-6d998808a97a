package com.xmhaibao.live.living.common.userinfodialog.giftwall.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

import cn.taqu.lib.okhttp.model.IDoExtra;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import hb.common.helper.HostHelper;
import hb.utils.WebpUtils;

/**
 * @auther SuZhanFeng
 * @date 2020/4/15
 * @desc
 */
public class LiveGiftWallSoleData implements IDoExtra {
    @SerializedName("host_info")
    private LiveGiftWallSoleHostInfo hostInfo;
    @SerializedName("history_list")
    private List<LiveGiftWallSoleItem> historyList;

    public LiveGiftWallSoleHostInfo getHostInfo() {
        return hostInfo;
    }

    public void setHostInfo(LiveGiftWallSoleHostInfo hostInfo) {
        this.hostInfo = hostInfo;
    }

    public List<LiveGiftWallSoleItem> getHistoryList() {
        return historyList;
    }

    public void setHistoryList(List<LiveGiftWallSoleItem> historyList) {
        this.historyList = historyList;
    }

    @Override
    public void doExtra(IResponseInfo response) {
        if (hostInfo != null) {
            hostInfo.setHostAvatar(WebpUtils.getWebpUrl_4_1(hostInfo.getHostAvatar(), HostHelper.getInstance().getHostAvatar()));
        }

        if (historyList != null) {
            for (LiveGiftWallSoleItem item : historyList) {
                item.setGiftUrl(WebpUtils.getWebpUrl_4_1(item.getGiftUrl(), HostHelper.getInstance().getHostImg()));
                item.setAccountAvatar(WebpUtils.getWebpUrl_4_1(item.getAccountAvatar(), HostHelper.getInstance().getHostAvatar()));
            }
        }
    }
}
