package com.xmhaibao.live.music.widget

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.xmhaibao.live.R
import com.xmhaibao.live.databinding.LiveMusicListDialogLayBinding
import com.xmhaibao.live.music.holder.LiveMusicPlayListViewHolder
import com.xmhaibao.live.music.interf.IMusicAddListener
import com.xmhaibao.live.music.interf.IMusicPlayOptionListener
import com.xmhaibao.live.music.interf.IMusicPlayStateListener
import com.xmhaibao.live.music.interf.IMusicSoftInputModeListener
import com.xmhaibao.live.music.manager.LiveMusicSongManager
import com.xmhaibao.live.music.model.LiveSongListItemBean
import hb.utils.SizeUtils
import hb.xadapter.OnViewHolderCreatedListener
import hb.xadapter.XBaseAdapter
import hb.xstyle.xdialog.XDialogUtils

import hb.xstyle.xdialog.XLifecycleDialog

/**
 * @desc 音乐列表
 *
 * <AUTHOR>
 * @date 2022/3/31
 */
class LiveMusicListDialog(context: Context) :
    XLifecycleDialog(context, R.style.Live_Music_Search_Dialog), IMusicAddListener,
    IMusicPlayStateListener {

    private lateinit var mViewBinding: LiveMusicListDialogLayBinding
    private var softInputModeListener: IMusicSoftInputModeListener? = null
    private var playOptionListener: IMusicPlayOptionListener? = null

    private var adapter: XBaseAdapter? = null

    companion object {
        fun showDialog(
            context: Context,
            modeListener: IMusicSoftInputModeListener?,
            optionListener: IMusicPlayOptionListener?
        ) {
            LiveMusicListDialog(context).run {
                softInputModeListener = modeListener
                playOptionListener = optionListener
                show()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mViewBinding = LiveMusicListDialogLayBinding.inflate(LayoutInflater.from(context))
        setContentView(mViewBinding.root)
        mViewBinding.tvAdd.setOnClickListener {
            LiveMusicSearchDialog.showDialog(context, this, softInputModeListener)
        }
        mViewBinding.recyclerView.layoutManager = LinearLayoutManager(context)
        adapter = XBaseAdapter(context)
        adapter?.register(
            LiveSongListItemBean::class.java,
            LiveMusicPlayListViewHolder::class.java
        )
        adapter?.setOnViewHolderCreatedListener(OnViewHolderCreatedListener { holder ->
            if (holder is LiveMusicPlayListViewHolder) {
                holder.mViewBinding.ivDelete.setOnClickListener {
                    val song = LiveMusicSongManager.getSong(holder.adapterPosition)?: return@setOnClickListener
                    LiveMusicSongManager.remove(song)
                    if (holder.adapterPosition == 0) {
                        // 需要暂停音乐 然后开始播放下一首
                        playOptionListener?.stopPlaySong(song)
                        if (LiveMusicSongManager.isEmpty()) {
                            mViewBinding.recyclerView.visibility = View.GONE
                            mViewBinding.tvHint.visibility = View.VISIBLE
                        } else {
                            playOptionListener?.playSingleSong(LiveMusicSongManager.getNextSong())
                        }
                    }
                    adapter?.notifyDataSetChanged()
                }
            }
        })
        mViewBinding.recyclerView.adapter = adapter
        adapter?.items = LiveMusicSongManager.getList()
        if (LiveMusicSongManager.isEmpty()) {
            mViewBinding.tvHint.visibility = View.VISIBLE
            mViewBinding.recyclerView.visibility = View.GONE
        } else {
            mViewBinding.tvHint.visibility = View.GONE
            mViewBinding.recyclerView.visibility = View.VISIBLE
        }
        adapter?.notifyDataSetChanged()
    }

    override fun onStart() {
        super.onStart()
        window?.run {
            setLayout(ViewGroup.LayoutParams.MATCH_PARENT, SizeUtils.dp2px(496f))
            setGravity(Gravity.BOTTOM)
            attributes
        }
    }

    override fun show() {
        super.show()
        playOptionListener?.registerPlayStateListener(this)
        val isNeedRestart = playOptionListener?.isNeedRestartPlayMusic() ?: false
        if (isNeedRestart) {
            XDialogUtils.show(
                context, "当前存在未播放完的音乐列表，是否继续播放", null, "确定",
                { dialog, which ->
                    playOptionListener?.restartPlaySong()
                    dialog.dismiss()
                }, "取消"
            ) { dialog, which ->
                LiveMusicSongManager.clear()
                dialog.dismiss()
            }
        }
    }

    override fun dismiss() {
        super.dismiss()
        playOptionListener?.unRegisterPlayStateListener()
        softInputModeListener = null
    }

    /**
     * 添加音乐到列表当中
     */
    override fun addSong(data: LiveSongListItemBean?) {
        // 判断列表当中为空时候 直接播放歌曲  不为空添加到底部
        data?.run {
            if (LiveMusicSongManager.isEmpty()) {
                mViewBinding.recyclerView.visibility = View.VISIBLE
                mViewBinding.tvHint.visibility = View.GONE
                LiveMusicSongManager.addSong(data)
                adapter?.notifyDataSetChanged()
                playOptionListener?.playSingleSong(data)
            } else {
                LiveMusicSongManager.addSong(data)
                adapter?.notifyDataSetChanged()
            }
        }
    }

    /**
     * 开始播放
     */
    override fun onPlay() {
        mViewBinding?.recyclerView?.post {
            adapter?.notifyDataSetChanged()
        }
    }

    /**
     * 全部播放结束
     */
    override fun onCompletion() {
        mViewBinding?.recyclerView?.post {
            mViewBinding.recyclerView.visibility = View.GONE
            mViewBinding.tvHint.visibility = View.VISIBLE
        }
    }

}