package com.xmhaibao.live.living.common.talent.ui.rank

import android.graphics.Color
import android.os.Bundle
import android.view.View
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.lifecycle.lifecycleScope
import cn.taqu.lib.base.router.RouterLaunch
import com.xmhaibao.live.R
import com.xmhaibao.live.databinding.LiveTalentRankListDialogBinding
import com.xmhaibao.live.living.common.talent.repository.LiveTalentRepository
import dp
import hb.kotlin_extension.noneSyncLazy
import hb.utils.ColorUtils
import hb.utils.ScreenUtils
import hb.xrequest.awaitNullable
import hb.xrequest.launchHttp
import hb.xstyle.xdialogfragment.XDialogFragment

/**
 * 直播-上才艺-主播榜
 *
 * <AUTHOR>
 * @date 2024-08-19
 */
class LiveTalentRankListDialog : XDialogFragment() {
    private lateinit var binding: LiveTalentRankListDialogBinding
    private val titles = mutableListOf("本周榜", "上周榜","总榜")
    private var hostUuid: String? = null
    private val repo by noneSyncLazy { LiveTalentRepository() }

    companion object {
        const val TYPE_WEEK = 0
        const val TYPE_LAST_WEEK = 1
        const val TYPE_ALL = 2

        fun show(fragmentManager: FragmentManager, hostUuid: String?) {
            LiveTalentRankListDialog().apply {
                this.hostUuid = hostUuid
                setConfig(
                    ConfigStyle.BOTTOM.config()
                        .setHeight((ScreenUtils.getScreenHeight() * 0.65f).toInt())
                )
                showDialog(fragmentManager.beginTransaction())
            }
        }
    }

    override fun onCreateContentView(): Any {
        binding = LiveTalentRankListDialogBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initConfig(argumentBundle: Bundle) {
        
    }

    override fun initViews(rootView: View?) {
        binding.apply {
            initTabStrip()
            ivBack.setOnClickListener {
                dismissAllowingStateLoss()
            }
            imgBg.setImageFromResource(R.drawable.live_talent_top_bg)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        requestRule()
    }

    private fun initTabStrip() {
        binding.tabLayout.apply {
            // 设置Tab是自动填充满屏幕的
            shouldExpand = false
            // 设置Tab的分割线是透明的
            dividerColor = Color.TRANSPARENT
            indicatorColor = ColorUtils.getColor(hb.xstyle.R.color.TH_Black100)
            setIndicatorRadius(58.dp)
            indicatorHeight = 4.dp
            setIndicatorWidth(10.dp)
            setIsSelectScale(false)
            textSize = 15.dp
            setLineBottomPadding(4.dp)
            textColor = ColorUtils.getColor(hb.xstyle.R.color.TH_Gray600)
            setSelectedTextColor(ColorUtils.getColor(hb.xstyle.R.color.TH_Gray990))
            setSelectedTextBold(true)
            setBackgroundColor(Color.TRANSPARENT)
            tabPaddingLeftRight = 8.dp
        }
        val pagerAdapter = LiveTalentHostPagerAdapter(childFragmentManager)
        binding.viewPager.adapter = pagerAdapter
        binding.tabLayout.setViewPager(binding.viewPager)
    }

    inner class LiveTalentHostPagerAdapter(
        fragmentManager: FragmentManager, behavior: Int = BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT
    ) : FragmentPagerAdapter(fragmentManager, behavior) {

        override fun getItem(position: Int): Fragment {
            return when (position) {
                0 -> LiveTalentRankListFragment.newInstance(TYPE_WEEK, hostUuid)
                1 -> LiveTalentRankListFragment.newInstance(TYPE_LAST_WEEK, hostUuid)
                else -> LiveTalentRankListFragment.newInstance(TYPE_ALL, hostUuid)
            }
        }

        override fun getCount(): Int = titles.size

        override fun getPageTitle(position: Int): CharSequence = titles[position]
    }

    private fun requestRule() {
        lifecycleScope.launchHttp({
            val data = repo.getTalentRankRule(hostUuid).awaitNullable()
            if (data?.relaction.isNullOrEmpty().not()) {
                binding.ivRule.isVisible = true
                binding.ivRule.setOnClickListener {
                    RouterLaunch.dealJumpData(context, data?.relaction)
                }
            }
        }, {
            true
        })
    }

}