package com.xmhaibao.live.living.common.gift.adapter;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.xmhaibao.live.living.common.gift.holder.LiveGiftPackViewHolder;

import java.util.List;

import hb.xadapter.XBaseAdapter;

/**
 * @author：杨浩 项目：taqu
 * 创建日期：2020/9/14
 * 功能简介：
 */
public class LiveGiftPackAdapter extends XBaseAdapter {
    /***
     * 选中项
     */
    private int mSelect = 0;

    public LiveGiftPackAdapter(Context context, @NonNull List<?> items, int gridPosition) {
        super(context, items);
        if (gridPosition == 0) {
            mSelect = 0;
        } else {
            mSelect = -1;
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof LiveGiftPackViewHolder) {
            ((LiveGiftPackViewHolder) holder).setPosition(mSelect);
        }
        super.onBindViewHolder(holder, position);

    }

    /***
     * 刷新选中item背景方法
     * @param position
     */
    public void changeSelected(int position) {
        if (position != mSelect) {
            mSelect = position;
            notifyDataSetChanged();
        }
    }
}
    