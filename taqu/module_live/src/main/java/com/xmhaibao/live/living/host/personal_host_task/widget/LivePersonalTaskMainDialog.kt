package com.xmhaibao.live.living.host.personal_host_task.widget

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.xmhaibao.live.api.tracker.LiveTracker
import com.xmhaibao.live.R
import com.xmhaibao.live.databinding.LivePersonalTaskHomeLayBinding
import com.xmhaibao.live.living.host.personal_host_task.model.LivePersonalTaskHomeBean
import com.xmhaibao.live.living.host.personal_host_task.repository.LivePersonalHostTaskRepository
import com.xmhaibao.live.living.host.personal_host_task.tracker.LivePersonalHostTaskTracker
import com.xmhaibao.live.utils.LiveUtil
import hb.common.data.AccountHelper
import hb.kotlin_extension.isNull
import hb.utils.SizeUtils
import hb.xrequest.await
import hb.xrequest.launchHttp
import hb.xstyle.xdialog.XLifecycleDialog
import hb.xtoast.XToastUtils

/**
 * @desc 素人主播任务主页面
 *
 * <AUTHOR>
 * @date 2023/2/20
 */
class LivePersonalTaskMainDialog(context: Context) :
    XLifecycleDialog(context, cn.taqu.lib.base.R.style.Bottom_Dialog) {
    private lateinit var mViewBinding: LivePersonalTaskHomeLayBinding
    private var mBean: LivePersonalTaskHomeBean? = null

    companion object {
        @JvmStatic
        fun showDialog(context: Context): LivePersonalTaskMainDialog {
            val dialog = LivePersonalTaskMainDialog(context)
            (context as LifecycleOwner?)?.apply {
                lifecycleScope.launchHttp({
                    val data = LivePersonalHostTaskRepository.getPersonalHostTaskHomeInfo()
                        .await<LivePersonalTaskHomeBean>()
                    if (data.isNull()) {
                        XToastUtils.show("暂无活动")
                        return@launchHttp
                    }
                    dialog.setData(data)
                    dialog.show()
                })
            }
            return dialog
        }
    }

    private fun setData(bean: LivePersonalTaskHomeBean?) {
        this.mBean = bean
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mViewBinding = LivePersonalTaskHomeLayBinding.inflate(LayoutInflater.from(context))
        setContentView(mViewBinding.root)
        mViewBinding.ivTop.setImageFromResource(R.drawable.live_personal_task_top_bg)
        mViewBinding.tvHistory.setOnClickListener {
            LivePersonalHostTaskTracker.instance.trackLivePersonalHostTaskDetailClick(AccountHelper.getAccountUuid(), "历史奖励")
            LivePersonalTaskHistoryDialog.showDialog(LiveUtil.getLiveActivity())
        }
        mBean?.run {
            mViewBinding.tvAwardTitle.text = taskAward
            mViewBinding.tvTitleHint.text = taskTitle
            mViewBinding.tvAwardHint.text = taskHint
            if (rulesIconHeight > 0) {
                mViewBinding.ruleBg.aspectRatio = rulesIconWidth.toFloat() / rulesIconHeight
            }
            mViewBinding.ruleBg.setImageFromUrl(rulesIcon)
            if (list.isNullOrEmpty()) {
                return@run
            }
            if (list.size > 0) {
                mViewBinding.taskView1.setData(list[0])
            }
            if (list.size > 1) {
                mViewBinding.taskView2.setData(list[1])
            }
        }

    }

    override fun onStart() {
        super.onStart()
        window?.run {
            setLayout(
                ViewGroup.LayoutParams.MATCH_PARENT,
                SizeUtils.dp2px(580f)
            )
            setGravity(Gravity.BOTTOM)
        }
    }

}