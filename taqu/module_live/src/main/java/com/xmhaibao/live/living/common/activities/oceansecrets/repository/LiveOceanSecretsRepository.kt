package com.xmhaibao.live.living.common.activities.oceansecrets.repository

import cn.taqu.lib.base.api.UrlBase
import com.xmhaibao.live.living.common.activities.oceansecrets.model.LiveOceanSecretsHostResult
import com.xmhaibao.live.living.common.activities.oceansecrets.model.LiveOceanSecretsOpenResult
import com.xmhaibao.live.living.common.activities.oceansecrets.model.LiveOceanSecretsRoomInfo
import hb.common.xstatic.HttpParams
import hb.xrequest.XRequest
import hb.xrequest.awaitNullable

/**
 * 海洋探秘 数仓请求辅助类
 *
 * <AUTHOR>
 * @date 2024/5/11
 */
class LiveOceanSecretsRepository : UrlBase() {

    /**
     * 立即送出接口
     */
    suspend fun sendGift(hostUuid: String?): LiveOceanSecretsOpenResult? {
        val httpParams =
            HttpParams.newBuilder().post(API_LIVE.plus("/ShellOceanSecrets/createRelation"))
                .needTicketId(true)
                .params("host_uuid", hostUuid)
                .build()
        return XRequest.newRequest<LiveOceanSecretsOpenResult>(httpParams).awaitNullable()
    }


    /**
     * 海洋探秘-顶部轮播入口
     */
    suspend fun requestRoomInfo(hostUuid: String?): LiveOceanSecretsRoomInfo? {
        val httpParams =
            HttpParams.newBuilder().post(API_LIVE.plus("/ShellOceanSecrets/carousel"))
                .needTicketId(true)
                .params("host_uuid", hostUuid)
                .build()
        return XRequest.newRequest<LiveOceanSecretsRoomInfo>(httpParams).awaitNullable()
    }

    /**
     * 直播大厅获取海洋探秘info
     */
    fun getOceanSecretsInfo(): XRequest<LiveOceanSecretsHostResult?> {
        val httpParams = HttpParams.newBuilder().post(API_LIVE.plus("/ShellOceanSecrets/remind"))
            .needTicketId(true)
        return XRequest.newRequest(httpParams.build())
    }
}