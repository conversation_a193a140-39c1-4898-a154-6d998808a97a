package com.xmhaibao.live.living.common.share.model;

import hb.qim.base.core.IResponseParse;
import com.xmhaibao.live.bean.EventLiveMsgSystemBroadcast;

import cn.taqu.lib.base.im.LiveIMLogHelper;
import cn.taqu.lib.base.live.model.event.EventMsgBase;
import hb.utils.EventBusUtils;

/**
 * Created by Sirius on 2018/12/27.
 */
public class EventShareSuccessBean extends EventMsgBase implements IResponseParse<EventShareSuccessBean> {

    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public EventShareSuccessBean parseResponse(String event, Object... args) {
        LiveIMLogHelper.getInstance().log(event, args);
        if (args.length >= 1) {
            EventLiveMsgSystemBroadcast broadcast = new EventLiveMsgSystemBroadcast();
            broadcast.setMsgContent(String.valueOf(args[0]));
            EventBusUtils.post(broadcast);
        }
        return null;
    }
}
