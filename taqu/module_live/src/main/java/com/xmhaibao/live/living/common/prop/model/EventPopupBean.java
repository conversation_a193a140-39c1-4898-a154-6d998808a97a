package com.xmhaibao.live.living.common.prop.model;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.xmhaibao.live.api.helper.LiveAccountHelper;
import com.xmhaibao.live.api.helper.LiveRoomInfoHelper;
import com.xmhaibao.live.living.host.hostmenu.LiveHostPlayHelper;
import com.xmhaibao.live.living.watch.bottommenu.helper.LivePlayHelper;

import hb.common.data.AccountHelper;
import hb.qim.base.core.IResponseParse;

import hb.common.helper.HostHelper;
import cn.taqu.lib.base.im.LiveIMLogHelper;
import hb.utils.EventBusUtils;
import hb.utils.StringUtils;
import hb.utils.WebpUtils;
import hb.utils.kvcache.KVCacheUtils;

/**
 * <AUTHOR>
 * @date 2019/7/31
 * @desc
 */
public class EventPopupBean implements IResponseParse<EventPopupBean> {
    /**
     * hostUuid 如果为空就推全房
     * type 1-弹窗  2-飘屏 3-飘屏&弹窗
     * content - 飘屏内容
     * title - 弹窗标题
     * detail - 弹窗内容
     * popup_type  - 通用
     * url 弹窗背景图
     */
    private int type;
    private String content;
    private String title;
    private String detail;
    @SerializedName("activity_type")
    private String activityType;
    private String url;
    private String hostUuid;

    private String userUuid;

    public String getUserUuid() {
        return userUuid;
    }

    public void setUserUuid(String userUuid) {
        this.userUuid = userUuid;
    }

    public String getHostUuid() {
        return hostUuid;
    }

    public void setHostUuid(String hostUuid) {
        this.hostUuid = hostUuid;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public EventPopupBean parseResponse(String event, Object... args) {
        LiveIMLogHelper.getInstance().log(event, args);
        if (args.length >= 7) {
            setType(StringUtils.objectToInt(args[0]));
            setContent(String.valueOf(args[1]));
            setTitle(String.valueOf(args[2]));
            setDetail(String.valueOf(args[3]));
            setActivityType(String.valueOf(args[4]));
            setUrl(WebpUtils.getWebpUrl(String.valueOf(args[5]), HostHelper.getInstance().getHostImg()));
            setHostUuid(String.valueOf(args[6]));
            if (args.length >= 8) {
                setUserUuid(String.valueOf(args[7]));
            }
            // 有下发用户uuid 并且不是当前用户  不显示
            if (!TextUtils.isEmpty(getUserUuid()) && !TextUtils.equals(AccountHelper.getAccountUuid(), getUserUuid())) {
                if (LiveRoomInfoHelper.getInstance().isHostRoom() && !LiveAccountHelper.isGiftMessageHostOpen()) {
                    return this;
                }
                if (!LiveRoomInfoHelper.getInstance().isHostRoom() && !LiveAccountHelper.isGiftMessageOpen()) {
                    return this;
                }
            }
            EventBusUtils.post(this);
        }
        return this;
    }
}
