package com.xmhaibao.live.living.common.guardgroup.holder

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.xmhaibao.live.api.tracker.LiveTracker
import com.xmhaibao.live.R
import com.xmhaibao.live.databinding.LiveJoinFamilyOrGuardGuidItemBinding
import com.xmhaibao.live.living.common.guardgroup.model.EventMsgJoinFansGroup
import com.xmhaibao.live.living.common.guardgroup.tracker.LiveGuardGroupTracker
import com.xmhaibao.live.living.common.msg.interf.OnMsgItemClickListener
import hb.xadapter.XBaseViewHolder

/**
 * 类描述：加入粉丝团消息通知（仅观众端自己可见）
 *
 * <AUTHOR>
 * @date 2022-09-29
 */
class LiveSelfMsgJoinFansGroupViewHolder(val mViewBinding: LiveJoinFamilyOrGuardGuidItemBinding) :
    XBaseViewHolder<EventMsgJoinFansGroup>(mViewBinding.root), View.OnClickListener {
    var mOnMsgItemClickListener: OnMsgItemClickListener? = null

    constructor(parent: ViewGroup, listener: OnMsgItemClickListener? = null) : this(
        LiveJoinFamilyOrGuardGuidItemBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
    ) {
        mOnMsgItemClickListener = listener
    }

    override fun onBindView(item: EventMsgJoinFansGroup) {
        itemView.tag = item
        itemView.setOnClickListener(this)
        mViewBinding.ivAvatar.setImageFromResource(R.drawable.live_join_family_tip)
        mViewBinding.tvContent.text = item.msgContent
        mViewBinding.tvEnter.text = "去看看"
    }

    override fun onClick(v: View?) {
        LiveGuardGroupTracker.instance.liveFansImClick("加入反馈")
        mOnMsgItemClickListener?.onItemViewClick(v)
    }
}