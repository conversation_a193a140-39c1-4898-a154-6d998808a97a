package com.xmhaibao.live.mylive.holder

import androidx.annotation.NonNull
import android.view.ViewGroup
import com.xmhaibao.live.R
import com.xmhaibao.live.databinding.LiveRoomReportItemBinding
import com.xmhaibao.live.mylive.model.LiveRoomReportBean
import hb.xadapter.XBaseViewHolder

/**
 * @auther zhangzhaojie
 * @date 2020/8/3
 * @desc 举报理由
 */
class LiveRoomReportViewHolder(@NonNull parent: ViewGroup) : XBaseViewHolder<LiveRoomReportBean.ListBean?>(parent, R.layout.live_room_report_item) {
    val mViewBinding = LiveRoomReportItemBinding.bind(itemView)

    override fun onBindView(item: LiveRoomReportBean.ListBean?) {
        item?.run {
            mViewBinding.mIvReportCheck.tag = item
            mViewBinding.mTvReportReason.text = title
            mViewBinding.mIvReportCheck.setBackgroundResource(if (isCheck) cn.taqu.lib.base.R.drawable.ic_checked else cn.taqu.lib.base.R.drawable.ic_check_n)
        }
    }

}