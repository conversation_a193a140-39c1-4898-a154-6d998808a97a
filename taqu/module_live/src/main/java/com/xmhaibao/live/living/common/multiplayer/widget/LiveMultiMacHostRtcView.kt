package com.xmhaibao.live.living.common.multiplayer.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.SurfaceView
import androidx.constraintlayout.widget.ConstraintLayout
import cn.taqu.lib.base.helper.widescreen.AppScreenFitHelper.aspect
import cn.taqu.lib.base.helper.widescreen.AppScreenFitHelper.isWideScreen
import com.xmhaibao.gift.effect.LiveGiftBaseBuilder
import com.xmhaibao.live.databinding.LiveMultiHostRtcViewLayoutBinding
import hb.utils.ScreenUtils


/**
 * 多人娱乐房 主播端麦位推流控件
 * 包含当前主播预览以及其他
 *
 * <AUTHOR>
 * @date 2024/7/23
 */
class LiveMultiMacHostRtcView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private var binding =
        LiveMultiHostRtcViewLayoutBinding.inflate(LayoutInflater.from(context), this)

    init {
        var appHight = LiveGiftBaseBuilder.mParentHeight
        if (appHight == 0) {
            appHight = ScreenUtils.getScreenHeight()
        }
        var screenWidth = ScreenUtils.getScreenWidth().toFloat()
        if (isWideScreen(appHight.toFloat(), screenWidth)) {
            screenWidth = (appHight / aspect)
        }
        val width = (screenWidth / 3.0F).toInt()
        val height = (width * 1.36f).toInt()
        resetLayoutParams(binding.seatLayout1, width, height)
        resetLayoutParams(binding.seatLayout2, width, height)
        resetLayoutParams(binding.seatLayout3, width, height)
        resetLayoutParams(binding.seatLayout4, width, height)
        resetLayoutParams(binding.seatLayout5, width, height)
        resetLayoutParams(binding.seatLayout6, width, height)
    }

    private fun resetLayoutParams(itemView: LiveMultiMacRtcItemView?, width: Int, height: Int) {
        itemView?.let {
            val params = it.layoutParams
            params.width = width
            params.height = height
            it.layoutParams = params
        }
    }


    /**
     * 设置单个用户禁止视频状态
     * @param index 麦位下表
     * @param avatar 头像
     */
    fun setMuteVideo(index: Int, avatar: String?) {
        getView(index)?.setMuteVideo(avatar)
    }

    /**
     * 设置单个用户恢复默认状态
     * @param index 麦位下表
     */
    fun setSeatIdle(index: Int) {
        getView(index)?.setIdle()
    }

    /**
     * 单个用户视频播放中
     * @param index 麦位下表
     */
    fun setVideoPlaying(index: Int) {
        getView(index)?.setVideoPlaying()
    }


    /**
     * 添加采集控件
     */
    fun addSurfaceView(index: Int, surfaceView: SurfaceView?) {
        surfaceView?.let {
            getView(index)?.addSurfaceView(it)
        }
    }

    /**
     * 获取采集控件
     */
    fun getSurfaceView(index: Int): SurfaceView? {
        return getView(index)?.getSurfaceView()
    }

    /**
     * 销毁采集控件
     */
    fun removeSurfaceView(index: Int) {
        getView(index)?.removeSurfaceView()
    }

    fun destroy() {
        for (i in 0..5) {
            getView(i)?.destroy()
        }
    }


    /**
     * 根据下标获取麦位item
     */
    private fun getView(index: Int): LiveMultiMacRtcItemView? {
        when (index) {
            0 -> {
                return binding.seatLayout1
            }

            1 -> {
                return binding.seatLayout2
            }

            2 -> {
                return binding.seatLayout3
            }

            3 -> {
                return binding.seatLayout4
            }

            4 -> {
                return binding.seatLayout5
            }

            5 -> {
                return binding.seatLayout6
            }

            else -> return null
        }
    }

}