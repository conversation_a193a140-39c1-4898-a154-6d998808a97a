package com.xmhaibao.live.living.common.online.model;

/**
 * 金主信息item
 *
 * <AUTHOR>
 * @date 2021-04-21
 */
public class EventDevoteItem {
    private String avatar;
    private String accountUuid;
    private String hadDevote;
    private int resId;
    private int rank ;
    /**
     * 超粉类型
     */
    private String hugeFansType;

    public String getHugeFansType() {
        return hugeFansType;
    }

    public void setHugeFansType(String hugeFansType) {
        this.hugeFansType = hugeFansType;
    }
    /**
     * 等级徽章
     */
    private String commonLevel;

    public String getCommonLevel() {
        return commonLevel;
    }

    public void setCommonLevel(String commonLevel) {
        this.commonLevel = commonLevel;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }

    public String getAccountUuid() {
        return accountUuid;
    }


    public void setResid(int resId) {
        this.resId = resId;
    }

    public int getResId() {
        return resId;
    }

    public String getHadDevote() {
        return hadDevote;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public int getRank() {
        return rank;
    }

    public void setHadDevote(String hadDevote) {
        this.hadDevote = hadDevote;
    }

    @Override
    public String toString() {
        return "{" +
                "avatar='" + avatar + '\'' +
                ", accountUuid='" + accountUuid + '\'' +
                '}';
    }
}
