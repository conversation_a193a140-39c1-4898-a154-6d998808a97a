package com.xmhaibao.live.living.watch.freshBox.model;


import cn.taqu.lib.base.im.LiveIMLogHelper;
import hb.qim.base.core.IResponseParse;
import hb.utils.EventBusUtils;

/**
 * 获取自建卡event
 *
 * <AUTHOR>
 * @date 2021-08-13
 */
public class EventLiveGetRecommendOneselfCard implements IResponseParse<EventLiveGetRecommendOneselfCard> {
    private String uuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Override
    public EventLiveGetRecommendOneselfCard parseResponse(String event, Object... args) {
        if (args.length >= 1) {
            LiveIMLogHelper.getInstance().log(event, args);
            setUuid(String.valueOf(args[0]));
            EventBusUtils.post(this);
        }
        return this;
    }
}
