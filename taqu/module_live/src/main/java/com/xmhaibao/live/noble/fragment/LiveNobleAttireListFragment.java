package com.xmhaibao.live.noble.fragment;


import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.xmhaibao.live.R;
import com.xmhaibao.live.noble.model.LiveNobleAttireListBean;
import com.xmhaibao.live.noble.repository.LiveMyPrivilegeRepository;
import com.xmhaibao.live.repository.LiveRepository;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import cn.taqu.lib.base.common.XjbApplicationHelper;
import cn.taqu.lib.base.http.TQResponseInfo;
import cn.taqu.lib.okhttp.callback.GsonCallBack;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import cn.taqu.library.widget.PtrTaquFrameLayout;
import hb.common.data.AccountHelper;
import hb.common.helper.HostHelper;
import hb.common.xstatic.fragment.BaseFragment;
import hb.utils.ScreenUtils;
import hb.utils.SizeUtils;
import hb.utils.SpanUtils;
import hb.utils.StringUtils;
import hb.utils.WebpUtils;
import hb.xadapter.XBaseAdapter;
import hb.xadapter.XBaseViewHolder;
import hb.xadapter.XItemViewBinder;
import hb.ximage.fresco.AvatarDraweeView;
import hb.ximage.fresco.BaseDraweeView;
import hb.xrequest.XRequest;
import in.srain.cube.views.ptr.PtrDefaultHandler;
import in.srain.cube.views.ptr.PtrFrameLayout;
import in.srain.cube.views.ptr.PtrHandler;

import static com.xmhaibao.live.noble.fragment.LiveNobleAttireAndCodeFragment.USE_FORM_ATTIRE;

/**
 * Created by zhangzhaojie on 2018/12/6 14:56
 * 直播--资料装扮列表
 */
public class LiveNobleAttireListFragment extends BaseFragment implements View.OnClickListener {
    public static final String HOST_UUID = "HOST_UUID";
    private String mHostUuid;
    private static final String FOREVER = "永久";
    private static final String ATTIRETYPE = "attiretype";
    private static final String OPENNOBLE = "开通贵族";
    private PtrTaquFrameLayout mFrameLayout;
    private RecyclerView mRecyclerView;
    private XBaseAdapter mAdapter;
    private List<LiveNobleAttireListBean> mLiveAttireList;
    private String mType;

    public static LiveNobleAttireListFragment getInstance(String type, String hostUuid) {
        Bundle bundle = new Bundle();
        bundle.putString(ATTIRETYPE, type);
        bundle.putString(HOST_UUID, hostUuid);
        LiveNobleAttireListFragment fragment = new LiveNobleAttireListFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onClick(View v) {

    }

    @Override
    protected Object onCreateContentView() {
        return R.layout.live_noble_attire_code_fragment;
    }

    @Override
    public void initViews() {
        super.initViews();
        if (getArguments() != null) {
            mType = getArguments().getString(ATTIRETYPE);
            mHostUuid = getArguments().getString(HOST_UUID);
        }
        mFrameLayout = findViewById(R.id.prtFrameLayout);
        mRecyclerView = findViewById(R.id.recyclerViewNobleList);
        mFrameLayout.disableWhenHorizontalMove(true);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity(), LinearLayoutManager.VERTICAL, false));
        mLiveAttireList = new ArrayList<>();
        mAdapter = new XBaseAdapter(getContext());
        mAdapter.register(LiveNobleAttireListBean.class, new XItemViewBinder<LiveNobleAttireListBean, LiveNobleAttireListViewHolder>() {
            @Override
            protected LiveNobleAttireListViewHolder onCreateHolder(@NonNull LayoutInflater inflater, @NonNull ViewGroup parent) {
                return new LiveNobleAttireListViewHolder(parent);
            }

            @Override
            protected void onBindViewHolder(@NonNull LiveNobleAttireListViewHolder holder, @NonNull LiveNobleAttireListBean item) {
                holder.onBindView(item);
            }
        });
        mAdapter.setItems(mLiveAttireList);
        mRecyclerView.setAdapter(mAdapter);
        mFrameLayout.setPtrHandler(new PtrHandler() {
            @Override
            public boolean checkCanDoRefresh(PtrFrameLayout frame, View content, View header) {
                return PtrDefaultHandler.checkContentCanBePulledDown(frame, content, header);
            }

            @Override
            public void onRefreshBegin(PtrFrameLayout frame) {
                requestAttireList();
            }
        });
        ((ImageView) findViewById(R.id.ivEmpty)).setImageResource(R.drawable.live_noble_code_empty);
        if (USE_FORM_ATTIRE.equals(mType)) {
            ((TextView) findViewById(R.id.tvEmpty)).setText("暂无可使用装扮");
        } else {
            ((TextView) findViewById(R.id.tvEmpty)).setText("暂无已过期装扮");
        }
    }

    @Override
    public void initData(Bundle savedInstanceState) {
        super.initData(savedInstanceState);
        requestAttireList();
    }


    private void requestAttireList() {
        showLoadingBar(true);
        XRequest<List<LiveNobleAttireListBean>> request;
        if (USE_FORM_ATTIRE.equals(mType)) {
            //可装扮
            request = LiveRepository.getNobleAttireAvailableList(AccountHelper.getUserTicketId());
        } else {//已过期
            request = LiveMyPrivilegeRepository.getNobleAttireUnailableList(AccountHelper.getUserTicketId());
        }
        request.execute(new GsonCallBack<List<LiveNobleAttireListBean>>() {
                    @Override
                    public void onSuccess(boolean isFromCache, @Nullable List<LiveNobleAttireListBean> list, @NonNull IResponseInfo response) {
                        hideLoadingBar();
                        mFrameLayout.refreshComplete(true);
                        if (list != null && !list.isEmpty()) {
                            mLiveAttireList.clear();
                            mLiveAttireList.addAll(list);
                            mAdapter.notifyDataSetChanged();
                        } else {
                            findViewById(R.id.llEmpty).setVisibility(View.VISIBLE);
                            mFrameLayout.setVisibility(View.GONE);
                        }
                        mAdapter.setLoadMoreEndHide();
                    }

                    @Override
                    public void onFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {
                        hideLoadingBar();
                        mFrameLayout.refreshComplete(false);
                        TQResponseInfo tqResponseInfo = (TQResponseInfo) response;
                        if (tqResponseInfo.getNetErrorInfo() != null) {
                            showErrorView(tqResponseInfo.getNetErrorInfo().getStatusCode(), tqResponseInfo.getNetErrorInfo().getErrorData());
                        }
                    }
                });

    }

    /***
     * 使用皮肤
     */
    private void useSkin(String skinId) {
        LiveRepository.getNobleAttireUsed(AccountHelper.getUserTicketId(), skinId)
                .execute(new GsonCallBack<Object>() {
                    @Override
                    public void onSuccess(boolean isFromCache, @Nullable Object object, @NonNull IResponseInfo response) {
                        //重新刷新数据
                        requestAttireList();
                    }

                    @Override
                    public void onFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {

                    }
                });
    }


    @Override
    public void onRetryClicked(View view) {
        super.onRetryClicked(view);
        requestAttireList();
    }

    class LiveNobleAttireListViewHolder extends XBaseViewHolder<LiveNobleAttireListBean> {
        TextView tvTitle;
        TextView tvTime;
        TextView tvAttireState;
        CheckBox ckAttire;
        View ivBgDefault;
        BaseDraweeView nobleTopGradBg;
        BaseDraweeView ivTopBg;
        ImageView ivTopBg2;
        BaseDraweeView ivAvatarBg;
        BaseDraweeView ivAvatarNobleBg;
        BaseDraweeView ivNobleType;
        AvatarDraweeView ivAvatar;

        public LiveNobleAttireListViewHolder(ViewGroup parent) {
            super(parent, R.layout.live_noble_attire_list_item);
            tvTitle = itemView.findViewById(R.id.tvTitle);
            tvTime = itemView.findViewById(R.id.tvTime);
            tvAttireState = itemView.findViewById(R.id.tvAttireState);
            ckAttire = itemView.findViewById(R.id.ckAttire);
            ivBgDefault = itemView.findViewById(R.id.ivBgDefault);
            nobleTopGradBg = itemView.findViewById(R.id.nobleTopGradBg);
            ivTopBg = itemView.findViewById(R.id.ivTopBg);
            ivTopBg2 = itemView.findViewById(R.id.ivTopBg2);
            ivAvatarBg = itemView.findViewById(R.id.ivAvatarBg);
            ivAvatarNobleBg = itemView.findViewById(R.id.ivAvatarNobleBg);
            ivNobleType = itemView.findViewById(R.id.ivNobleType);
            ivAvatar = itemView.findViewById(R.id.ivAvatar);
        }


        @Override
        public void onBindView(final LiveNobleAttireListBean bean) {
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) tvAttireState.getLayoutParams();
            if (LiveNobleAttireAndCodeFragment.USE_FORM_ATTIRE.equals(mType)) {
                ivBgDefault.setVisibility(View.GONE);
                if (FOREVER.equals(bean.getExpire())) {
                    tvTime.setText(new SpanUtils()
                            .append("可使用时间：")
                            .append(bean.getExpire())
                            .setForegroundColor(getContext().getResources().getColor(hb.xstyle.R.color.g3))
                            .create()
                    );
                } else {
                    tvTime.setText(new SpanUtils()
                            .append("可使用时间：")
                            .append(bean.getExpire())
                            .setForegroundColor(getContext().getResources().getColor(hb.xstyle.R.color.c2))
                            .create()
                    );
                }

                tvAttireState.setText(bean.getUsed() ? "已装扮" : "闲置中");
                tvTitle.setTextColor(getContext().getResources().getColor(hb.xstyle.R.color.g1));
                ckAttire.setVisibility(View.VISIBLE);
                if (!bean.getUsed()) {
                    ckAttire.setChecked(false);
                    ckAttire.setClickable(true);
                    tvAttireState.setTextColor(getContext().getResources().getColor(hb.xstyle.R.color.g3));
                    ckAttire.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            useSkin(bean.getId());
                        }
                    });
                } else {
                    ckAttire.setChecked(true);
                    ckAttire.setClickable(false);
                    tvAttireState.setTextColor(getContext().getResources().getColor(hb.xstyle.R.color.c2));
                }
                layoutParams.rightMargin = ScreenUtils.dp2px(getContext(), 4);

            } else {
                ivBgDefault.setVisibility(View.GONE);
                tvAttireState.setText("已过期");
                tvTitle.setTextColor(getContext().getResources().getColor(hb.xstyle.R.color.g3));
                ckAttire.setVisibility(View.INVISIBLE);
                layoutParams.rightMargin = -ScreenUtils.dp2px(getContext(), 20);
                tvTime.setText(new SpanUtils()
                        .append("过期时间：")
                        .append(bean.getExpire())
                        .create()
                );
            }
            tvAttireState.setLayoutParams(layoutParams);
            tvTitle.setText(bean.getTitle());
            //皮肤（线框）
            if (!StringUtils.isEmpty(WebpUtils.getWebpUrl(bean.getSkinHead(), HostHelper.getInstance().getHostImg()))) {
                ivTopBg.setVisibility(View.VISIBLE);
               /* float ratio = XjbApplicationHelper.getInstance().getWidth() * 1f / ScreenUtils.dp2px(getContext(), 343);
                ivTopBg.setAspectRatio(ratio);*/
                final RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) ivTopBg.getLayoutParams();
                params.width = XjbApplicationHelper.getInstance().getWidth() - SizeUtils.dp2px(52);
                params.height = params.width * SizeUtils.dp2px(85) / SizeUtils.dp2px(343);
                ivTopBg.setLayoutParams(params);
                ivTopBg.setImageFromUrl(WebpUtils.getWebpUrl(bean.getSkinHead(), HostHelper.getInstance().getHostImg()));
            } else {
                ivTopBg.setVisibility(View.INVISIBLE);
            }
        }
    }

}
