package com.xmhaibao.live.high_quality_host.repository

import cn.taqu.lib.base.api.UrlBase
import com.xmhaibao.live.hall.model.LiveHostInfo
import com.xmhaibao.live.high_quality_host.model.LiveHighQualityHostRewardInfo
import hb.common.xstatic.HttpParams
import hb.xrequest.XRequest

/**
 * 优质主播承接数仓
 *
 * <AUTHOR>
 * @date 2024-3-1
 */
class LiveHighQualityHostRepository : UrlBase() {

    /**
     * 点击去送礼获取推荐主播uuid
     */
    fun getRecommendHost(): XRequest<LiveHostInfo?> {
        val httpParams =
            HttpParams.newBuilder().get(API_LIVE.plus("/HighGradeHost/getRecommendHost"))
                .needTicketId(true)
                .build()
        return XRequest.newRequest(httpParams)
    }

    /**
     * 进入直播间领取礼物包
     */
    fun getRewardInfo(currentUuid: String?): XRequest<LiveHighQualityHostRewardInfo?> {
        val httpParams =
            HttpParams.newBuilder().get(API_LIVE.plus("/HighGradeHost/getRewardInfo"))
                .needTicketId(true)
                .params("host_uuid", currentUuid)
                .build()
        return XRequest.newRequest(httpParams)
    }
}