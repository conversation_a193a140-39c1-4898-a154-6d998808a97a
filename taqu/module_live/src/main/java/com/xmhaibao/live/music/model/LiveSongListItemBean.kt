package com.xmhaibao.live.music.model

import com.google.gson.annotations.SerializedName

/** 背景音乐 资源列表
 *
 * <AUTHOR>
 * @date 2024-1-24
 */
class LiveSongListItemBean {
    /**
     * 歌曲名称
     */
    @SerializedName("name")
    var songName: String? = null

    /**
     * 歌手姓名
     */
    @SerializedName("singer")
    var singerName: String? = null

    /**
     * 歌曲id
     */
    @SerializedName("id")
    var songId: String? = null

    /**
     * 是否被选中
     */
    var select = false

    /**
     * 是否正在播放
     */
    var playing = false

    /**
     * 播放地址.mp3
     */
    var url: String? = null
}