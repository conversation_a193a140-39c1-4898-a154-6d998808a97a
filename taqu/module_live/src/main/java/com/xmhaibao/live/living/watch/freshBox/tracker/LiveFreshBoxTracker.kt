package com.xmhaibao.live.living.watch.freshBox.tracker

import hb.xtracker.XTrackBase
import org.json.JSONObject

/**
 * @auther su<PERSON><PERSON><PERSON>
 * @date 2023/9/25
 * @desc 新人宝箱埋点上报类
 */
class LiveFreshBoxTracker : XTrackBase() {

    companion object {
        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            LiveFreshBoxTracker()
        }
    }

    /**
     * 新人宝箱曝光
     * 埋点文档：https://o15vj1m4ie.feishu.cn/wiki/wikcn9idXvFSc9dsgIhaEqwk8tf
     * @param type
     */
    fun trackLiveBoxExplore(type: String?) {
        try {
            val jsonObject = JSONObject()
            jsonObject.put("type", type)
            trackEvent("liveroom_treasure_explore", jsonObject)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 新人引导宝箱展现
     *
     * @param type 类别
     * type：曝光类型
     *      - box2
     *      - box4
     * 埋点文档：https://o15vj1m4ie.feishu.cn/wiki/wikcn9idXvFSc9dsgIhaEqwk8tf
     */
    fun trackLiveNewUserBoxShow(type: String?) {
        try {
            val jsonObject = JSONObject()
            jsonObject.put("type", type)
            trackEvent("newuser_treasurebox_show", jsonObject)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 新人引导宝箱展现
     *
     * @param type     类别
     * @param position
     * type：曝光类型
     *      - box2
     *      - box4
     * position：点击位置
     *      - cancel
     *      - receive
     * 埋点文档：https://o15vj1m4ie.feishu.cn/wiki/wikcn9idXvFSc9dsgIhaEqwk8tf
     */
    fun trackLiveNewUserBoxClick(type: String?, position: String?) {
        try {
            val jsonObject = JSONObject()
            jsonObject.put("type", type)
            jsonObject.put("position", position)
            trackEvent("newuser_treasurebox_click", jsonObject)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}