package com.xmhaibao.live.living.common.multipk.helper

import android.content.Context
import android.content.DialogInterface
import com.xmhaibao.live.bean.EventLiveMsgSystemBroadcast
import com.xmhaibao.live.living.activity.LiveBaseActivity
import com.xmhaibao.live.living.activity.LiveStartActivity
import com.xmhaibao.live.living.common.activities.pkfreedom.model.EventPkFreedomMatchBean
import com.xmhaibao.live.living.common.multipk.bean.LiveMultiPkRoomStateBean.Companion.MATCH_PK_MATCHING
import com.xmhaibao.live.living.common.multipk.bean.LiveMultiPkRoomStateBean.Companion.UNION_PK_RUNNING
import com.xmhaibao.live.living.common.multipk.dialog.LiveMultiPkCountDownDialog
import com.xmhaibao.live.living.common.multipk.dialog.LiveMultiPkEntranceDialogFragment
import com.xmhaibao.live.living.common.multipk.dialog.LiveMultiPkMangeDialogFragment
import com.xmhaibao.live.living.common.multipk.dialog.LiveMultiPkMeetingMangeDialogFragment
import com.xmhaibao.live.living.common.multipk.dialog.LiveMultiPkTeamInviteDialogFragment
import com.xmhaibao.live.living.common.multipk.dialog.LiveMultiPkTeamLaunchDialogFragment
import com.xmhaibao.live.living.common.multipk.event.EventLiveMultiPkEnd
import com.xmhaibao.live.living.common.multipk.event.EventLiveMultiPkGetRoomState
import com.xmhaibao.live.living.common.multipk.event.EventLiveMultiPkInviteResult
import com.xmhaibao.live.living.common.multipk.event.EventLiveMultiPkRestartInvite
import com.xmhaibao.live.living.common.multipk.event.EventLiveMultiPkStartInvite
import com.xmhaibao.live.living.common.multipk.event.EventLiveMultiPkTeamInvite
import com.xmhaibao.live.living.common.multipk.event.EventLiveMultiPkTeamInviteResult
import com.xmhaibao.live.living.common.multipk.viewmodel.LiveMultiPkViewModel
import com.xmhaibao.live.model.event.EventMultiUnionBean
import com.xmhaibao.live.utils.LiveUtil
import hb.common.data.AccountHelper
import hb.utils.EventBusUtils
import hb.utils.StringUtils
import hb.xstyle.xdialog.XDialog
import hb.xstyle.xdialogfragment.XDialogFragment
import hb.xtoast.XToastUtils

/**
 * 直播-多人PK Helper
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
class LiveMultiPkHelper(
    val viewModel: LiveMultiPkViewModel
) {
    private var multiMeetingInviteDialog: LiveMultiPkCountDownDialog? = null

    init {
        EventBusUtils.register(this)
    }

    /**
     * 处理底部按钮点击
     */
    fun handleBottomBtnClick(context: Context) {
        val roomState = viewModel.roomStateChangeLiveData.value
        if (roomState?.isInMatching() == true) {
            // 匹配中
            showPkMeetingCancelMatchingDialog(context, roomState.matchState == MATCH_PK_MATCHING)
        } else if (roomState?.isInUnionRunning() == true) {
            // 联播、pk中
            showPkMeetingManageDialog(context, roomState.unionState == UNION_PK_RUNNING)
        } else {
            // 正常状态
            showPkMeetingEntranceDialog(context)
        }
    }

    /**
     * 展示pk联播入口弹窗
     */
    private fun showPkMeetingEntranceDialog(context: Context) {
        if (context is LiveStartActivity) {
            LiveMultiPkEntranceDialogFragment.show(context.supportFragmentManager, context.isGoldPk)
        }
    }

    /**
     * 展示pk联播取消匹配弹窗
     */
    private fun showPkMeetingCancelMatchingDialog(context: Context, isPk: Boolean) {
        XDialog.newBuilder(context)
            .title("提示")
            .message(if (isPk) "系统正在为您匹配pk对象，您可随时中断" else "系统正在为您寻找主播中，您可随时中断")
            .negativeButton("取消", null)
            .positiveButton(
                "中断"
            ) { _, _ ->
                if (isPk) {
                    viewModel.cancelRandomMultiPk()
                } else {
                    viewModel.cancelRandomMultiMeeting()
                }
            }
            .show()
    }

    /**
     * 展示pk联播管理弹窗
     */
    private fun showPkMeetingManageDialog(context: Context, isPk: Boolean) {
        if (context is LiveBaseActivity) {
            if (isPk) {
                LiveMultiPkMangeDialogFragment.show(context.supportFragmentManager)
            } else {
                LiveMultiPkMeetingMangeDialogFragment.show(context.supportFragmentManager)
            }
        }
    }

    /**
     * 联播流程Event
     */
    fun onEventMainThread(event: EventMultiUnionBean) {
        when (event.operation) {
            EventMultiUnionBean.INVITE -> {
                // 邀请
                if (multiMeetingInviteDialog?.isShowing() == true) {
                    return
                }
                LiveUtil.getLiveActivity()?.let {
                    multiMeetingInviteDialog?.destroy()
                    multiMeetingInviteDialog = LiveMultiPkCountDownDialog().apply {
                        setTitle("联播邀请")
                        setMessage(event.text)
                        setLeftTime(event.leftTime)
                        show(it) { _, which ->
                            if (which == DialogInterface.BUTTON_POSITIVE) {
                                viewModel.onAcceptMultiMeetingInvite(event.inviteId)
                                closeDialogFragment(LiveMultiPkEntranceDialogFragment::class.java.simpleName)
                            } else if (which == DialogInterface.BUTTON_NEGATIVE) {
                                viewModel.onRejectMultiMeetingInvite(event.inviteId)
                            }
                        }
                    }
                }
            }

            EventMultiUnionBean.CANCEL_INVITE -> {
                // 取消邀请
                dismissMeetingInviteDialog()
                XToastUtils.show(event.text)
            }

            EventMultiUnionBean.REJECT -> {
                // 拒绝邀请
                XToastUtils.show(event.text)
            }

            EventMultiUnionBean.ACCEPT -> {
                viewModel.getRoomStateNow()
            }

            EventMultiUnionBean.FINISH -> {
                // 联播结束
                viewModel.getRoomStateNow()
                closeDialogFragment(LiveMultiPkMeetingMangeDialogFragment::class.java.simpleName)
                closeDialogFragment(LiveMultiPkMangeDialogFragment::class.java.simpleName)
            }
        }
    }

    /**
     * 获取最新房间状态Event
     */
    fun onEventMainThread(event: EventLiveMultiPkGetRoomState) {
        viewModel.getRoomStateNow()
    }

    /**
     * pk开始Event
     */
    fun onEventMainThread(event: EventPkFreedomMatchBean) {
        viewModel.getRoomStateNow()
        closeDialogFragment(LiveMultiPkMeetingMangeDialogFragment::class.java.simpleName)
        closeDialogFragment(LiveMultiPkTeamLaunchDialogFragment::class.java.simpleName)
    }

    /**
     * 组队pk邀请Event
     */
    fun onEventMainThread(event: EventLiveMultiPkTeamInvite) {
        LiveUtil.getLiveActivity()?.let {
            LiveMultiPkTeamInviteDialogFragment.show(it.supportFragmentManager, event)
        }
    }

    /**
     * 组队pk邀请结果Event
     */
    fun onEventMainThread(event: EventLiveMultiPkTeamInviteResult) {
        if (event.hostUuid == AccountHelper.getAccountUuid()) {
            if (event.state == "2") {
                XToastUtils.show(event.content)
            }
        }
    }

    /**
     * pk邀请结果Event
     */
    fun onEventMainThread(event: EventLiveMultiPkInviteResult) {
        if (event.hostUuid == AccountHelper.getAccountUuid()) {
            if (event.state == "2") {
                XToastUtils.show(event.content)
            }
        }
    }

    /**
     * 重开邀请、结果Event
     */
    fun onEventMainThread(event: EventLiveMultiPkRestartInvite) {
        val activity = LiveUtil.getLiveActivity() as? LiveStartActivity ?: return
        when (event.status) {
            "1" -> {
                LiveMultiPkCountDownDialog()
                    .setTitle("pk重开一局")
                    .setMessage(event.content.orEmpty())
                    .setLeftTime(StringUtils.stringToLong(event.leftTime))
                    .show(activity) { _, which ->
                        if (which == DialogInterface.BUTTON_POSITIVE) {
                            viewModel.onRestartPkAgreeReject(event.pkUuid, true)
                        } else {
                            viewModel.onRestartPkAgreeReject(event.pkUuid, false)
                        }
                    }
            }
            "2" -> {
                XToastUtils.show(event.content)
                val eventMsgSystemBroadcast = EventLiveMsgSystemBroadcast()
                eventMsgSystemBroadcast.msgContent = event.content
                eventMsgSystemBroadcast.nickName = ""
                activity.addMsg(eventMsgSystemBroadcast)
            }
            "3" -> {
                val eventMsgSystemBroadcast = EventLiveMsgSystemBroadcast()
                eventMsgSystemBroadcast.msgContent = "对方主播同意了您的重开请求，记得加油哦"
                eventMsgSystemBroadcast.nickName = ""
                activity.addMsg(eventMsgSystemBroadcast)
            }
        }
    }

    /**
     * pk邀请Event
     */
    fun onEventMainThread(event: EventLiveMultiPkStartInvite) {
        val activity = LiveUtil.getLiveActivity() as? LiveStartActivity ?: return
        LiveMultiPkCountDownDialog()
            .setTitle("提示")
            .setMessage(event.content)
            .setLeftTime(StringUtils.stringToLong(event.leftTime))
            .show(activity) { _, which ->
                if (which == DialogInterface.BUTTON_POSITIVE) {
                    viewModel.onAcceptMultiPkInvite(event.meetUuid, event.pkUuid)
                } else {
                    viewModel.onRejectMultiPkInvite(event.meetUuid, event.pkUuid)
                }
            }
    }

    /**
     * pk结束Event
     */
    fun onEventMainThread(event: EventLiveMultiPkEnd) {
        viewModel.getRoomStateNow()
        closeDialogFragment(LiveMultiPkMangeDialogFragment::class.java.simpleName)
    }

    /**
     * 关闭DialogFragment
     */
    private fun closeDialogFragment(tag: String) {
        val fm = LiveUtil.getLiveActivity()?.supportFragmentManager ?: return
        val dialogFragment = (fm.findFragmentByTag(tag) as? XDialogFragment) ?: return
        if (dialogFragment.dialog?.isShowing == true) {
            dialogFragment.dismissAllowingStateLoss()
        }
    }

    private fun dismissMeetingInviteDialog() {
        multiMeetingInviteDialog?.destroy()
        multiMeetingInviteDialog = null
    }

    fun destroy() {
        EventBusUtils.unregister(this)
        dismissMeetingInviteDialog()
    }

}