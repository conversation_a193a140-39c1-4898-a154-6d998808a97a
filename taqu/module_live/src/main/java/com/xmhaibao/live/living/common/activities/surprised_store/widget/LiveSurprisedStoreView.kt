package com.xmhaibao.live.living.common.activities.surprised_store.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.annotation.AttrRes
import androidx.annotation.NonNull
import cn.iwgang.countdownview.CustomCountDownTimer
import com.xmhaibao.live.databinding.LiveSurprisedStoreLayoutBinding
import com.xmhaibao.live.living.common.activities.common.view.LiveRankBaseLayout
import com.xmhaibao.live.living.common.activities.surprised_store.model.EventSurprisedStoreBean
import com.xmhaibao.live.living.common.tracker.LiveOptionTracker
import com.xmhaibao.live.utils.LiveTimeUtils
import javax.annotation.Nullable

/**
 * 惊喜商店入口
 */
class LiveSurprisedStoreView  : LiveRankBaseLayout<EventSurprisedStoreBean> {

    private lateinit var mLayoutBinding: LiveSurprisedStoreLayoutBinding
    private var mCountDownTimer: CustomCountDownTimer? = null

    constructor(@NonNull context: Context) : this(context, null)

    constructor(@NonNull context: Context, @Nullable attributeSet: AttributeSet?) : this(
        context,
        attributeSet,
        0
    )

    constructor(
        @NonNull context: Context,
        @Nullable attributeSet: AttributeSet?,
        @AttrRes defStyleAttr: Int
    ) : super(context, attributeSet, defStyleAttr)

    override fun onCreate() {
        mLayoutBinding =
            LiveSurprisedStoreLayoutBinding.inflate(LayoutInflater.from(context), this, true)
        mLayoutBinding.ivIcon.setOnClickListener {
            LiveOptionTracker.Instance.trackLiveRightAdClick("惊喜商店", relation)
            jumpRankH5()
        }
    }


    override fun setData(data: EventSurprisedStoreBean?) {
        super.setData(data)
        data?.run {
            mLayoutBinding.ivIcon.setImageFromUrl(icon)
            startCountdownTime(leftTime)
        }
    }

    override fun onDestroy() {
        cancelCountdownTime()
    }

    override fun isDefaultClick(): Boolean {
        return false
    }

    override fun isNeedSlideshow(): Boolean {
        return true
    }

    /**
     * 开始倒计时
     */
    private fun startCountdownTime(totalTime: Long) {
        cancelCountdownTime()
        if (totalTime <= 0) {
            mLayoutBinding.tvTime.text = "00:00:00"
            return
        }
        mCountDownTimer = object : CustomCountDownTimer(totalTime * 1000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                mLayoutBinding.tvTime.text = LiveTimeUtils.parseHHMMSSString(millisUntilFinished)
            }

            override fun onFinish() {
            }
        }
        mCountDownTimer?.start()
    }

    /**
     * 关闭定时器
     */
    fun cancelCountdownTime() {
        if (mCountDownTimer != null) {
            mCountDownTimer?.stop()
            mCountDownTimer = null
        }
    }
}