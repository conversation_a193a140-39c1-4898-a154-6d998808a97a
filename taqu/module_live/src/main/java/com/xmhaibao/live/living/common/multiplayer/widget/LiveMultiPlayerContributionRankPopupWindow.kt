package com.xmhaibao.live.living.common.multiplayer.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.PopupWindow
import com.xmhaibao.live.R
import com.xmhaibao.live.databinding.LiveMultiPlayerContributionRankPupLayoutBinding

/**
 * 多人娱乐房贡献榜 规则提示
 *
 * <AUTHOR>
 * @date 2024/9/18
 */
@SuppressLint("SetTextI18n")
class LiveMultiPlayerContributionRankPopupWindow(val mContext: Context) : PopupWindow() {
    private var binding =
        LiveMultiPlayerContributionRankPupLayoutBinding.inflate(LayoutInflater.from(mContext))

    init {
        contentView = binding.root
        val dw = ColorDrawable(Color.TRANSPARENT)
        setBackgroundDrawable(dw)
        isOutsideTouchable = true
        width = ViewGroup.LayoutParams.WRAP_CONTENT
        height = ViewGroup.LayoutParams.WRAP_CONTENT
        binding.tvHint.setBackgroundResource(R.drawable.live_multi_player_contribution_pop_bg)
        binding.tvHint.text = "贡献榜取本场直播内总的用户送礼贡献值"
    }
}