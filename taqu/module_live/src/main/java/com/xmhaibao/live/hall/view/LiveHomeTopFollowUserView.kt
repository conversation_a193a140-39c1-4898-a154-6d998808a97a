package com.xmhaibao.live.hall.view

import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.xmhaibao.live.hall.holder.LiveHomeTopFollowUserViewHolder
import com.xmhaibao.live.hall.model.LiveHomeTopFollowUserBean
import com.xmhaibao.live.hall.tracker.LiveHomeTracker
import com.xmhaibao.live.hall.util.LiveHomeTopFollowUserDiffCallback
import hb.kotlin_extension.gone
import hb.kotlin_extension.noneSyncLazy
import hb.kotlin_extension.visible
import hb.xadapter.XBaseAdapter

/**
 * 直播顶部关注用户入口View
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
class LiveHomeTopFollowUserView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {
    private val tracker by noneSyncLazy { LiveHomeTracker() }
    private var adapter: XBaseAdapter? = null

    private fun initAdapter(list: MutableList<LiveHomeTopFollowUserBean>) {
        adapter = XBaseAdapter(context, list).apply {
            register(
                LiveHomeTopFollowUserBean::class.java,
                LiveHomeTopFollowUserViewHolder::class.java
            )
        }
        setHasFixedSize(true)
        setLayoutManager(
            LinearLayoutManager(
                context, LinearLayoutManager.HORIZONTAL, false
            )
        )
        setAdapter(adapter)
    }

    fun setData(list: MutableList<LiveHomeTopFollowUserBean>?) {
        if (list.isNullOrEmpty()) {
            gone()
            return
        }
        visible()
        tracker.trackLiveHomeTopFollowViewExp()
        if (adapter == null) {
            initAdapter(list)
        } else {
            adapter?.let {
                val result = DiffUtil.calculateDiff(
                    LiveHomeTopFollowUserDiffCallback(
                        it.getItems() as? MutableList<LiveHomeTopFollowUserBean>, list
                    )
                )
                it.getItems().clear()
                it.setItems(list)
                result.dispatchUpdatesTo(it)
            }
        }
    }

}