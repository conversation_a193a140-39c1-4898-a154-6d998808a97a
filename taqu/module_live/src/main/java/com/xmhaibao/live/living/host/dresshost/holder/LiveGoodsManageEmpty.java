package com.xmhaibao.live.living.host.dresshost.holder;

import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.xmhaibao.live.R;
import com.xmhaibao.live.living.host.dresshost.model.LiveGoodsInfo;

import hb.ximage.fresco.BaseDraweeView;
import hb.xadapter.XBaseViewHolder;

import static com.xmhaibao.live.living.host.dresshost.fragment.LiveGoodsManageFragment.TYPE_BADGE;
import static com.xmhaibao.live.living.host.dresshost.fragment.LiveGoodsManageFragment.TYPE_BUBBLES;
import static com.xmhaibao.live.living.host.dresshost.fragment.LiveGoodsManageFragment.TYPE_DRESS;
import static com.xmhaibao.live.living.host.dresshost.fragment.LiveGoodsManageFragment.TYPE_DRIVE;
import static com.xmhaibao.live.living.host.dresshost.fragment.LiveGoodsManageFragment.TYPE_TITLE;

/**
 * @author：杨浩 项目：taqu
 * 创建日期：2020/9/14
 * 功能简介：
 */
public class LiveGoodsManageEmpty extends XBaseViewHolder<LiveGoodsInfo.LiveMineGoods> {

    int type;
    BaseDraweeView mmImgIcon;
    TextView mmTvName;
    TextView mmTvTime;
    TextView mmTvUse;
    TextView mmTvUnUse;
    ImageView mmImgUseLabel;

    ImageView mmImgEmpty;
    TextView mmTvDesc;

    private int dialogType;

    public LiveGoodsManageEmpty(ViewGroup parent) {
        super(parent,  R.layout.live_mine_goods_empty_item);
    }

    public void setDialogType(int dialogType) {
        this.dialogType = dialogType;
    }

    @Override
    public void onBindView(LiveGoodsInfo.LiveMineGoods item) {
        itemView.setBackgroundResource(0);
        mmImgEmpty = itemView.findViewById(R.id.imgEmpty);
        mmTvDesc = itemView.findViewById(R.id.tvEmptyDesc);
        if (dialogType == TYPE_TITLE) {
            mmImgEmpty.setImageResource(R.drawable.live_ic_name_empty);
            mmTvDesc.setText("您还没有称号");
        }

        else if (dialogType == TYPE_DRIVE) {
            mmImgEmpty.setImageResource(R.drawable.live_ic_driver_empty);
            mmTvDesc.setText("您还没有座驾");
        }
        //家族徽章
        else if (dialogType == TYPE_BADGE) {
            mmImgEmpty.setImageResource(R.drawable.live_ic_empty_badge);
            mmTvDesc.setText("你还没有家族徽章");
        }
        //气泡
        else if (dialogType == TYPE_BUBBLES) {
            mmImgEmpty.setImageResource(R.drawable.live_ic_empty_bubbles);
            mmTvDesc.setText("你还没有气泡");
        }
        else if (dialogType == TYPE_DRESS) {
            mmImgEmpty.setImageResource(R.drawable.live_ic_dress_empty);
            mmTvDesc.setText("您还没有装扮");
        }
    }
}
    