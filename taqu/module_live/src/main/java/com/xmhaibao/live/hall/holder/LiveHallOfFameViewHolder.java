package com.xmhaibao.live.hall.holder;

import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.ViewSwitcher;

import com.xmhaibao.forum.api.router.ForumPinsRouter;
import com.xmhaibao.live.R;
import com.xmhaibao.live.hall.model.LiveHallOfFameItem;
import com.xmhaibao.live.hall.model.LiveHallOfData;
import com.xmhaibao.live.utils.LivePrePerencesUtils;
import com.xmhaibao.live.utils.LiveRouterUtils;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import cn.taqu.lib.base.constants.CommonConstants;
import cn.taqu.lib.base.constants.LiveSourceConstants;
import cn.taqu.lib.base.mvp.multeadapter.BaseViewHolder;
import cn.taqu.lib.base.router.RouterLaunch;
import cn.taqu.lib.base.utils.GrowingIOUtils;
import hb.utils.CollectionUtils;
import hb.utils.SizeUtils;
import hb.ximage.fresco.AvatarDraweeView;
import hb.utils.Loger;
import hb.utils.ScreenUtils;
import hb.utils.StringUtils;

public class LiveHallOfFameViewHolder extends BaseViewHolder<LiveHallOfData> implements ViewSwitcher.ViewFactory, View.OnClickListener {
    private ViewSwitcher mTvSwitcher;

    // 排行榜数据
    private List<LiveHallOfFameItem> rankList = new ArrayList<>();
    // 线上活动数据
    private List<LiveHallOfFameItem> activityOnlineList = new ArrayList<>();
    // 活动数据
    private List<LiveHallOfFameItem> activityList = new ArrayList<>();
    // 世界消息
    private List<LiveHallOfFameItem> worldList = new ArrayList<>();
    // 圈子数据
    private List<LiveHallOfFameItem> circleList = new ArrayList<>();
    // 活动数据和圈子数据合并集合
    private List<LiveHallOfFameItem> activityAndcCircleList = new ArrayList<>();
    // 合并集合最大值
    private final int MAX_INDEX = 80;
    // 合并集合当前正在展示的index
    private int mCurrentIndex;
    private boolean isPause;

    private TextSwitcherRunnable mTextSwitcherRunnable = new TextSwitcherRunnable();

    public LiveHallOfFameViewHolder(@NonNull ViewGroup parent) {
        super(parent, R.layout.live_hall_of_fame_layout);
        int dp24 = SizeUtils.dp2px(24);
        int height = (int) ((ScreenUtils.getScreenWidth() - dp24) / 5.72f);
        itemView.getLayoutParams().height = height;
        mTvSwitcher = itemView.findViewById(R.id.viewSwitcher);
        mTvSwitcher.setFactory(this);
        Animation in = AnimationUtils.loadAnimation(itemView.getContext(), hb.thirdtools.sharelogin.R.anim.sharedialog_bottom_in);
        Animation out = AnimationUtils.loadAnimation(itemView.getContext(), com.ushengsheng.widget.R.anim.push_bottom_out_notice);
        mTvSwitcher.setInAnimation(in);
        mTvSwitcher.setOutAnimation(out);
    }


    @Override
    public void onBindViewHolder(@NonNull BaseViewHolder holder, @Nullable LiveHallOfData data, int position) {
        if (data == null) {
            return;
        }
        activityList.clear();
        circleList.clear();
        activityAndcCircleList.clear();
        if (CollectionUtils.isNotEmpty(data.getActivity())) {
            activityList.addAll(data.getActivity());
        }
        if (CollectionUtils.isNotEmpty(data.getFollowed())) {
            circleList.addAll(data.getFollowed());
        }
        if (CollectionUtils.isNotEmpty(data.getUnFollowed())) {
            circleList.addAll(data.getUnFollowed());
        }
        if (circleList.size() > MAX_INDEX) {
            circleList.remove(MAX_INDEX);
        }
        mCurrentIndex = 0;
        if (!isStart) {
            isStart = true;
            itemView.removeCallbacks(mTextSwitcherRunnable);
            itemView.post(mTextSwitcherRunnable);
        }
    }

    private boolean isStart;


    public void addItemData(LiveHallOfFameItem item) {
        if (item == null) {
            return;
        }
        switch (item.getMsgType()) {
            case LiveHallOfFameItem.TYPE_RANK_HOST:
            case LiveHallOfFameItem.TYPE_RANK_USER:
                rankList.add(item);
                break;
            case LiveHallOfFameItem.TYPE_ACTIVITY_ONLINE:
                activityOnlineList.add(item);
                break;
            case LiveHallOfFameItem.TYPE_WORLD:
                worldList.add(item);
                break;
            default:
                break;
        }

    }

    public void resume() {
        if (itemView != null && isPause) {
            isPause = false;
            Loger.d("LiveHallOfFameViewHolder", "resume");
            itemView.removeCallbacks(mTextSwitcherRunnable);
            itemView.postDelayed(mTextSwitcherRunnable, 3000);
        }
    }

    public void pause() {
        if (itemView != null) {
            isPause = true;
            itemView.removeCallbacks(mTextSwitcherRunnable);
        }
    }

    public void onDestroy() {
        if (itemView != null) {
            itemView.removeCallbacks(mTextSwitcherRunnable);
        }
    }

    @Override
    public View makeView() {
        return View.inflate(itemView.getContext(), R.layout.live_hall_of_fame_item, null);
    }

    @Override
    public void onClick(View v) {
        if (v.getTag() instanceof LiveHallOfFameItem) {
            LiveHallOfFameItem item = (LiveHallOfFameItem) v.getTag();
            switch (item.getMsgType()) {
                case LiveHallOfFameItem.TYPE_RANK_HOST:
                case LiveHallOfFameItem.TYPE_RANK_USER:
                    ForumPinsRouter.launchPersonalHomePageActivity(v.getContext(), item.getUuid());
                    break;
                case LiveHallOfFameItem.TYPE_CIRCLE:
                    RouterLaunch.dealJumpData(v.getContext(), item.getRelation());
                    break;
                case LiveHallOfFameItem.TYPE_ACTIVITY:
                    RouterLaunch.dealJumpData(v.getContext(), item.getRelation());
                    break;
                case LiveHallOfFameItem.TYPE_ACTIVITY_ONLINE:
                    RouterLaunch.dealJumpData(v.getContext(), item.getRelation());
                    break;
                default:
                    break;
            }

        } else {
            String url = LivePrePerencesUtils.getFameRankUrl();
            if (StringUtils.isNotEmpty(url)) {
                RouterLaunch.dealJumpData(v.getContext(), url);
            }
        }
    }

    class TextSwitcherRunnable implements Runnable {

        @Override
        public void run() {
            LiveHallOfFameItem item = null;
            if (!rankList.isEmpty()) {
                item = rankList.remove(0);
            } else if (!activityOnlineList.isEmpty()) {
                item = activityOnlineList.remove(0);
            } else if (!activityList.isEmpty()) {
                item = activityList.remove(0);
                addActivityAndcCircleData(item);
            } else if (!worldList.isEmpty()) {
                item = worldList.remove(0);
            } else if (!circleList.isEmpty()) {
                item = circleList.remove(0);
                addActivityAndcCircleData(item);
            } else if (!activityAndcCircleList.isEmpty()) {
                item = activityAndcCircleList.get(mCurrentIndex);
                mCurrentIndex++;
                if (mCurrentIndex >= activityAndcCircleList.size()) {
                    mCurrentIndex = 0;
                }
            }
            if (item != null) {
                View lay = mTvSwitcher.getNextView();
                lay.findViewById(R.id.rightHolder).setOnClickListener(LiveHallOfFameViewHolder.this);
                lay.setTag(item);
                lay.setOnClickListener(LiveHallOfFameViewHolder.this);
                switch (item.getMsgType()) {
                    case LiveHallOfFameItem.TYPE_RANK_HOST:
                        setRankData(lay, true, item);
                        break;
                    case LiveHallOfFameItem.TYPE_RANK_USER:
                        setRankData(lay, false, item);
                        break;
                    case LiveHallOfFameItem.TYPE_ACTIVITY_ONLINE:
                        setActivityOnlineData(lay, item);
                        break;
                    case LiveHallOfFameItem.TYPE_ACTIVITY:
                        setActivityData(lay, item);
                        break;
                    case LiveHallOfFameItem.TYPE_WORLD:
                        setWorldData(lay, item);
                        break;
                    case LiveHallOfFameItem.TYPE_CIRCLE:
                        setCircleData(lay, item);
                        break;
                }
                mTvSwitcher.showNext();
                Loger.d("LiveHallOfFameViewHolder", "showNext");
                itemView.removeCallbacks(mTextSwitcherRunnable);
                itemView.postDelayed(mTextSwitcherRunnable, 3 * 1000);
            }

        }
    }

    private void setCircleData(View lay, LiveHallOfFameItem item) {
        lay.findViewById(R.id.icon).setVisibility(View.GONE);
        lay.findViewById(R.id.iconCover).setVisibility(View.GONE);
        TextView tvTitle = lay.findViewById(R.id.tvTitle);
        tvTitle.setTextColor(ContextCompat.getColor(mTvSwitcher.getContext(), R.color.live_hall_headline_title));
        tvTitle.setText(StringUtils.isNotEmpty(item.getTitle()) ? item.getTitle() : item.getNickname());
        tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        TextView tvContent = lay.findViewById(R.id.tvContent);
        tvContent.setTextColor(ContextCompat.getColor(mTvSwitcher.getContext(), R.color.live_hall_headline_content));
        tvContent.setText(StringUtils.isNotEmpty(item.getContent()) ? item.getContent() : "大家都在关注的主播");
        tvContent.setTextSize(TypedValue.COMPLEX_UNIT_SP, 11);
        lay.setBackgroundResource(R.drawable.live_hall_item_anchor_circle_bg);
    }

    private void setActivityOnlineData(View lay, LiveHallOfFameItem item) {
        lay.findViewById(R.id.icon).setVisibility(View.GONE);
        lay.findViewById(R.id.iconCover).setVisibility(View.GONE);
        TextView tvTitle = lay.findViewById(R.id.tvTitle);
        tvTitle.setTextColor(ContextCompat.getColor(mTvSwitcher.getContext(), R.color.live_hall_msg_title));
        tvTitle.setText(item.getTitle());
        tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        TextView tvContent = lay.findViewById(R.id.tvContent);
        tvContent.setTextColor(ContextCompat.getColor(mTvSwitcher.getContext(), R.color.live_hall_msg_content));
        tvContent.setText(item.getContent());
        tvContent.setTextSize(TypedValue.COMPLEX_UNIT_SP, 11);
        lay.setBackgroundResource(R.drawable.live_hall_item_activity_online_msg_bg);
    }

    private void setActivityData(View lay, LiveHallOfFameItem item) {
        lay.findViewById(R.id.icon).setVisibility(View.GONE);
        lay.findViewById(R.id.iconCover).setVisibility(View.GONE);
        TextView tvTitle = lay.findViewById(R.id.tvTitle);
        tvTitle.setTextColor(ContextCompat.getColor(mTvSwitcher.getContext(), R.color.live_hall_msg_title));
        tvTitle.setText(item.getTitle());
        tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        TextView tvContent = lay.findViewById(R.id.tvContent);
        tvContent.setTextColor(ContextCompat.getColor(mTvSwitcher.getContext(), R.color.live_hall_msg_content));
        tvContent.setText(item.getContent());
        tvContent.setTextSize(TypedValue.COMPLEX_UNIT_SP, 11);
        lay.setBackgroundResource(R.drawable.live_hall_item_activity_msg_bg);
    }

    private void setWorldData(View lay, LiveHallOfFameItem item) {
        lay.findViewById(R.id.icon).setVisibility(View.GONE);
        lay.findViewById(R.id.iconCover).setVisibility(View.GONE);
        TextView tvTitle = lay.findViewById(R.id.tvTitle);
        tvTitle.setTextColor(ContextCompat.getColor(mTvSwitcher.getContext(), R.color.live_hall_msg_content));
        tvTitle.setText(item.getNickname() + ":");
        tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 11);
        TextView tvContent = lay.findViewById(R.id.tvContent);
        tvContent.setTextColor(ContextCompat.getColor(mTvSwitcher.getContext(), R.color.live_hall_msg_title));
        tvContent.setText(item.getContent());
        tvContent.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        lay.setBackgroundResource(R.drawable.live_hall_item_worldnews_bg);
    }

    private void setRankData(View lay, boolean isHost, LiveHallOfFameItem item) {
        AvatarDraweeView avatarView = lay.findViewById(R.id.icon);
        avatarView.setImageURI(item.getAvatar());
        avatarView.setVisibility(View.VISIBLE);
        ImageView ivCover = lay.findViewById(R.id.iconCover);
        ivCover.setVisibility(View.VISIBLE);
        ivCover.setImageResource(isHost ? R.drawable.live_hall_item_ranking_avtar_host_bg : R.drawable.live_hall_item_ranking_avtar_rich_bg);
        int textColor = ContextCompat.getColor(mTvSwitcher.getContext(), isHost ? R.color.live_hall_rank_host : R.color.live_hall_rank_user);
        TextView tvTitle = lay.findViewById(R.id.tvTitle);
        tvTitle.setTextColor(textColor);
        tvTitle.setText(item.getTitle());
        tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        TextView tvContent = lay.findViewById(R.id.tvContent);
        tvContent.setTextColor(textColor);
        tvContent.setText(item.getContent());
        tvContent.setTextSize(TypedValue.COMPLEX_UNIT_SP, 11);
        lay.setBackgroundResource(isHost ? R.drawable.live_hall_item_ranking_anchor_bg : R.drawable.live_hall_item_ranking_rich_bg);
    }

    private void addActivityAndcCircleData(LiveHallOfFameItem item) {
        activityAndcCircleList.add(item);
        if (activityAndcCircleList.size() > MAX_INDEX) {
            activityAndcCircleList.remove(0);
        }
    }
}
