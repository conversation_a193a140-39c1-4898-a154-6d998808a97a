package com.xmhaibao.live.living.common.activities.moonpalaceadventure.test

import com.xmhaibao.live.living.common.activities.moonpalaceadventure.model.LiveMoonAdventureHostInfo
import com.xmhaibao.live.living.common.activities.moonpalaceadventure.model.LiveMoonAdventureUserInfo
import com.xmhaibao.live.living.common.activities.moonpalaceadventure.widget.LiveMoonAdventureReceiveRabbitDialog
import com.xmhaibao.live.utils.LiveUtil
import hb.utils.ActivityUtils


/**
 * 月宫奇遇 调试工具类
 *
 * <AUTHOR>
 * @date 2024/8/20
 */
object LiveMoonAdventureTest {

    /**
     * 活动开始领取兔子
     */
    fun testReceiveRabbit() {
        val result = LiveMoonAdventureUserInfo()
        val activity = LiveUtil.getLiveActivity()
        if (!ActivityUtils.isActivityAlive(activity)) {
            return
        }
        result.let {
            LiveMoonAdventureReceiveRabbitDialog(activity, it).show()
        }
    }

    /**
     * 主播端入口
     */
    fun testEnterHost() {
        val result = LiveMoonAdventureHostInfo()
        val activity = LiveUtil.getLiveActivity()
        if (!ActivityUtils.isActivityAlive(activity)) {
            return
        }
        result.let {
            it.rank = "99999"
            it.value = "99999"
            it.obtained = "99999"
            activity.mRankViewHelper?.setRankData(it)
        }
    }

    /**
     * 用户端入口
     */
    fun testEnterUser() {
        val result = LiveMoonAdventureUserInfo()
        val activity = LiveUtil.getLiveActivity()
        if (!ActivityUtils.isActivityAlive(activity)) {
            return
        }
        result.let {
            it.value = "99999"
            it.num = "99999"
            it.leftCount = "99999"
            activity.mRankViewHelper?.setRankData(it)
        }
    }
}