package com.xmhaibao.live.living.common.prop.view;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;


import com.xmhaibao.live.R;
import com.xmhaibao.prop.model.PropInfo;


import hb.ximage.fresco.SquareDraweeView;
import hb.utils.StringUtils;
import hb.xadapter.XBaseViewHolder;

/**
 * @auther su<PERSON><PERSON>
 * @date 2018/6/26
 * @desc 直播间道具背包
 */
public class LivePropPackViewHolder extends XBaseViewHolder<PropInfo> {

    SquareDraweeView mmImgIcon;
    TextView mmTvCount;
    TextView mTvName;
    TextView mTvTimeLimit;
    protected int lastPosition = -1;

    public LivePropPackViewHolder(ViewGroup viewGroup) {
        super(viewGroup, R.layout.live_pack_prop_item);
        mmImgIcon = (SquareDraweeView) itemView.findViewById(R.id.imgIcon);
        mmTvCount = (TextView) itemView.findViewById(R.id.tvCount);
//            mmTvCount.setStroke(1, itemView.getContext().getResources().getColor(R.color.g1));
        mTvName = itemView.findViewById(R.id.tvName);
        mTvTimeLimit = itemView.findViewById(R.id.tvTimeLimit);
    }

    @Override
    public void onBindView(PropInfo propInfo) {
        int position = getAdapterPosition();
        itemView.setTag(propInfo);
        if (propInfo != null && propInfo.getNum() > 0) {
            if (!StringUtils.isEmpty(propInfo.getIcon())) {
                mmImgIcon.setImageFromUrl(propInfo.getIcon());
            } else {
                mmImgIcon.setImageFromUrl("");
            }
            mmTvCount.setText(String.valueOf(propInfo.getNum()));
            mmTvCount.setVisibility(View.VISIBLE);
            mTvName.setText(propInfo.getName());
            mTvTimeLimit.setVisibility(TextUtils.isEmpty(propInfo.getExpireTime()) ? View.GONE : View.VISIBLE);
            mTvTimeLimit.setText(propInfo.getExpireTime());
        } else {
            mmImgIcon.setImageFromUrl("");
            mmTvCount.setVisibility(View.INVISIBLE);
            mTvName.setText("");
        }
    }
}
