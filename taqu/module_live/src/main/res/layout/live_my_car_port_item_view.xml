<?xml version="1.0" encoding="utf-8"?>
<cn.taqu.lib.base.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clContent"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginRight="7dp"
    android:layout_marginBottom="10dp">

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivBg"
        android:layout_width="80dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="0.84:1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:placeholderImage="@color/transparent" />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivCar"
        android:layout_width="50dp"
        android:layout_height="50dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivBg"
        app:layout_constraintLeft_toLeftOf="@+id/ivBg"
        app:layout_constraintRight_toRightOf="@+id/ivBg"
        app:layout_constraintTop_toTopOf="@+id/ivBg"
        app:layout_constraintVertical_bias="0.37"
        app:placeholderImage="@color/transparent" />

    <TextView
        android:id="@+id/tvName"
        style="@style/Text.T7.White"
        android:layout_width="70dp"
        android:layout_height="16dp"
        android:layout_marginTop="-3dp"
        android:background="@drawable/live_carport_text_bg"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        app:layout_constraintLeft_toLeftOf="@+id/ivBg"
        app:layout_constraintRight_toRightOf="@+id/ivBg"
        app:layout_constraintTop_toBottomOf="@+id/ivCar"
        tools:text="座驾名称最长..." />
</cn.taqu.lib.base.widget.RoundConstraintLayout>