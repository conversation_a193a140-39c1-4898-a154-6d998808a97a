<?xml version="1.0" encoding="utf-8"?>
<cn.taqu.lib.base.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="290dp"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="14dp"
            android:layout_marginRight="10dp"
            android:background="@drawable/live_ic_party_detail_level_bg">

            <TextView
                android:id="@+id/tvLivePartyLevelTitle"
                style="@style/Text.T6.G1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="40dp"
                android:text="派对等级"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvLivePartyNextLevel"
                style="@style/Text.T7.G2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="16dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvLivePartyLevelTitle"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvLivePartyLevelTitle"
                tools:text="@string/party_detail_point" />

            <ProgressBar
                android:id="@+id/pbLivePartyLevelProgress"
                style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                android:layout_width="0dp"
                android:layout_height="12dp"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="25dp"
                android:layout_marginRight="16dp"
                android:progressDrawable="@drawable/live_party_next_level_progress"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvLivePartyNextLevel"
                tools:secondaryProgress="39" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clLivePartyLevelProgress"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="24dp"
                app:layout_constraintBottom_toBottomOf="@+id/pbLivePartyLevelProgress"
                app:layout_constraintLeft_toLeftOf="@+id/pbLivePartyLevelProgress"
                app:layout_constraintRight_toRightOf="@+id/pbLivePartyLevelProgress"
                app:layout_constraintTop_toTopOf="@+id/pbLivePartyLevelProgress">

                <LinearLayout
                    android:id="@+id/llLivePartyDetailLevelOne"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintHorizontal_chainStyle="spread_inside"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@+id/llLivePartyDetailLevelTwo"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/ivLivePartyDetailLevelOne"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:background="@drawable/live_party_detail_level_selector" />

                    <TextView
                        style="@style/Text.T7.G1"
                        android:layout_width="wrap_content"
                        android:layout_height="24dp"
                        android:gravity="center"
                        android:text="1级" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llLivePartyDetailLevelTwo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintLeft_toRightOf="@+id/llLivePartyDetailLevelOne"
                    app:layout_constraintRight_toLeftOf="@+id/llLivePartyDetailLevelThree"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/ivLivePartyDetailLevelTwo"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:background="@drawable/live_party_detail_level_selector" />

                    <TextView
                        style="@style/Text.T7.G1"
                        android:layout_width="wrap_content"
                        android:layout_height="24dp"
                        android:gravity="center"
                        android:text="2级" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llLivePartyDetailLevelThree"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintLeft_toRightOf="@+id/llLivePartyDetailLevelTwo"
                    app:layout_constraintRight_toLeftOf="@+id/llLivePartyDetailLevelFour"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/ivLivePartyDetailLevelThree"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:background="@drawable/live_party_detail_level_selector" />

                    <TextView
                        style="@style/Text.T7.G1"
                        android:layout_width="wrap_content"
                        android:layout_height="24dp"
                        android:gravity="center"
                        android:text="3级" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llLivePartyDetailLevelFour"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintHorizontal_chainStyle="spread_inside"
                    app:layout_constraintLeft_toRightOf="@+id/llLivePartyDetailLevelThree"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/ivLivePartyDetailLevelFour"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:background="@drawable/live_party_detail_level_selector" />

                    <TextView
                        style="@style/Text.T7.G1"
                        android:layout_width="wrap_content"
                        android:layout_height="24dp"
                        android:gravity="center"
                        android:text="4级" />
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rlLivePartyLevelGift"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:layout_constraintLeft_toLeftOf="@+id/pbLivePartyLevelProgress"
                app:layout_constraintRight_toRightOf="@+id/pbLivePartyLevelProgress"
                app:layout_constraintTop_toBottomOf="@+id/clLivePartyLevelProgress" />

            <TextView
                android:id="@+id/tvLivePartyLevelBoost"
                style="@style/Text.T6.White"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="14dp"
                android:layout_marginRight="27dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/live_party_level_boost_bg"
                android:gravity="center"
                android:text="为TA助力"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rlLivePartyLevelGift" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:layout_width="wrap_content"
            android:layout_height="30dp" />
    </LinearLayout>
</cn.taqu.lib.base.widget.NestedScrollView>