<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:fresco="http://schemas.android.com/tools"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp">

    <View
        android:id="@+id/topLine"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/white_alpha_40" />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivActivity"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_below="@+id/topLine"
        android:layout_marginTop="9.5dp"
        fresco:roundedCornerRadius="8dp" />

    <TextView
        android:id="@+id/tvActivityName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="12dp"
        android:layout_toRightOf="@+id/ivActivity"
        android:ellipsize="end"
        android:singleLine="true"
        android:text="活动火热进行中"
        android:textColor="@color/white"
        android:textSize="@dimen/t6"
        tools:text="活动名称" />

    <TextView
        android:id="@+id/tvActivityDec"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tvActivityName"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="1dp"
        android:layout_toRightOf="@+id/ivActivity"
        android:ellipsize="end"
        android:singleLine="true"
        android:text="活动火热进行中"
        android:textColor="@color/white_alpha_50"
        android:textSize="@dimen/t6"
        tools:text="活动简介" />

</RelativeLayout>