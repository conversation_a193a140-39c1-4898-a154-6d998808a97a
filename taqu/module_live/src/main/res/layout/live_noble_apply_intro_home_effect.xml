<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/flRoot"
    android:background="#AF000000"
    android:layout_height="match_parent">


    <ImageView
        android:id="@+id/ivHalo"
        android:layout_width="284dp"
        android:layout_height="284dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="119dp"
        android:background="@drawable/live_noble_ordinary_halo_bg" />
    <LinearLayout
        android:id="@+id/llMain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="72dp"
        android:layout_gravity="center_horizontal"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="360dp"

            android:layout_height="360dp">

            <ImageView
                android:id="@+id/ivLight"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:background="@drawable/live_noble_ordinary_light" />
            <ImageView
                android:id="@+id/ivStar"
                android:layout_width="311dp"
                android:layout_height="346dp"
                android:layout_gravity="center"
                android:background="@drawable/live_noble_ordinary_star" />


            <ImageView
                android:id="@+id/tvIcon"
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:layout_gravity="center"
            />
            
            <ImageView
                android:id="@+id/ivSpot"
                android:src="@drawable/live_noble_senior_lightspot"
                android:layout_width="50dp"
                android:layout_marginLeft="144dp"
                android:layout_marginTop="104dp"
                android:layout_height="50dp" />
            
        </FrameLayout>

        <TextView
            android:id="@+id/tvDescribe"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="-26dp"
            android:gravity="center"
            android:lineSpacingExtra="5dp"
            tools:text="「集齐甜蜜告白系列礼物」成就\n获得勋章和称号"
            android:textColor="@color/c1"
            android:textSize="@dimen/t5_2" />

        <TextView
            android:id="@+id/tvTips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="11dp"
            android:textColor="@color/white"
            android:textSize="@dimen/t5_2"
            android:layout_gravity="center"
            tools:text="（ PS：称号在背包中查看呦 ）" />

    </LinearLayout>
</FrameLayout>
