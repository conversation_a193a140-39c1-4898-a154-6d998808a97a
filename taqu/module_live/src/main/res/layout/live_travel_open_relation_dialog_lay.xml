<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivBg"
        android:layout_width="270dp"
        android:layout_height="296dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:placeholderImage="@color/transparent" />

    <ImageView
        android:id="@+id/ivFemaleAvatarBg"
        android:layout_width="54dp"
        android:layout_height="54dp"
        android:layout_marginStart="47dp"
        android:layout_marginTop="7dp"
        app:layout_constraintStart_toStartOf="@+id/ivBg"
        app:layout_constraintTop_toTopOf="@+id/ivBg"
        android:src="@drawable/live_exciting_trip_avatar_bg_female"/>

    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/ivFemaleAvatar"
        android:layout_width="50dp"
        android:layout_height="50dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivFemaleAvatarBg"
        app:layout_constraintEnd_toEndOf="@+id/ivFemaleAvatarBg"
        app:layout_constraintStart_toStartOf="@+id/ivFemaleAvatarBg"
        app:layout_constraintTop_toTopOf="@+id/ivFemaleAvatarBg" />


    <TextView
        android:id="@+id/tvFemaleName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="@+id/ivFemaleAvatarBg"
        app:layout_constraintEnd_toEndOf="@+id/ivFemaleAvatarBg"
        app:layout_constraintTop_toBottomOf="@+id/ivFemaleAvatarBg"
        android:layout_marginTop="2dp"
        android:textSize="@dimen/TH_FONT_N1"
        android:textColor="@color/black"/>

    <ImageView
        android:id="@+id/ivMaleAvatarBg"
        android:layout_width="54dp"
        android:layout_height="54dp"
        android:layout_marginEnd="47dp"
        android:layout_marginTop="7dp"
        app:layout_constraintEnd_toEndOf="@+id/ivBg"
        app:layout_constraintTop_toTopOf="@+id/ivBg"
        android:src="@drawable/live_exciting_trip_avatar_bg_male"/>


    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/ivMaleAvatar"
        android:layout_width="50dp"
        android:layout_height="50dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivMaleAvatarBg"
        app:layout_constraintEnd_toEndOf="@+id/ivMaleAvatarBg"
        app:layout_constraintStart_toStartOf="@+id/ivMaleAvatarBg"
        app:layout_constraintTop_toTopOf="@+id/ivMaleAvatarBg" />

    <TextView
        android:id="@+id/tvMaleName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="@+id/ivMaleAvatarBg"
        app:layout_constraintEnd_toEndOf="@+id/ivMaleAvatarBg"
        app:layout_constraintTop_toBottomOf="@+id/ivMaleAvatarBg"
        android:layout_marginTop="2dp"
        android:textSize="@dimen/TH_FONT_N1"
        android:textColor="@color/black"/>


    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/btnOpen"
        android:layout_width="238dp"
        android:layout_height="48dp"
        app:layout_constraintStart_toStartOf="@+id/ivBg"
        app:layout_constraintEnd_toEndOf="@+id/ivBg"
        app:layout_constraintBottom_toBottomOf="@+id/ivBg"
        android:layout_marginBottom="12dp"
        app:placeholderImage="@color/transparent"/>

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_marginTop="55dp"
        android:padding="5dp"
        android:src="@drawable/base_close_white_32_ic"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivBg" />


</androidx.constraintlayout.widget.ConstraintLayout>