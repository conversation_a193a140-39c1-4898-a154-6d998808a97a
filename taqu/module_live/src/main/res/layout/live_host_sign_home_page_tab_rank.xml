<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="#FFB953">

        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/vRankAwardBg"
            android:layout_width="match_parent"
            android:layout_height="78dp"
            android:layout_margin="8dp"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:placeholderImage="@color/transparent"
            app:roundedCornerRadius="16dp"
            tools:background="@color/white"
            tools:visibility="visible" />

        <hb.drawable.shape.view.HbView
            android:id="@+id/vRankBg"
            android:layout_width="0dp"
            android:layout_height="359dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="20dp"
            app:corner="16dp"
            app:layout_constraintLeft_toLeftOf="@id/vRankAwardBg"
            app:layout_constraintRight_toRightOf="@id/vRankAwardBg"
            app:layout_constraintTop_toBottomOf="@id/vRankAwardBg"
            app:solid="@color/white" />

        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/ivRankGradual"
            android:layout_width="0dp"
            android:layout_height="108dp"
            app:actualImageScaleType="fitXY"
            app:layout_constraintLeft_toLeftOf="@id/vRankBg"
            app:layout_constraintRight_toRightOf="@id/vRankBg"
            app:layout_constraintTop_toTopOf="@id/vRankBg"
            app:placeholderImage="@color/transparent"
            tools:background="@drawable/live_host_sign_gradual_bg" />

        <hb.drawable.shape.view.HbRadioGroup
            android:id="@+id/rgRankType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal"
            android:padding="4dp"
            app:corner="20dp"
            app:layout_constraintLeft_toLeftOf="@id/vRankBg"
            app:layout_constraintRight_toRightOf="@id/vRankBg"
            app:layout_constraintTop_toTopOf="@id/vRankBg"
            app:solid="#1A000000">

            <RadioButton
                android:id="@+id/rbRankYesterday"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/live_host_sign_rank_title_bg"
                android:button="@null"
                android:gravity="center"
                android:paddingLeft="24dp"
                android:paddingTop="2dp"
                android:paddingRight="24dp"
                android:paddingBottom="2dp"
                android:text="昨日榜单"
                android:textColor="@color/live_host_sign_rank_title_tip"
                android:textSize="13sp" />

            <RadioButton
                android:id="@+id/rbRankToday"
                android:checked="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/live_host_sign_rank_title_bg"
                android:button="@null"
                android:gravity="center"
                android:paddingLeft="24dp"
                android:paddingTop="2dp"
                android:paddingRight="24dp"
                android:paddingBottom="2dp"
                android:text="今日榜单"
                android:textColor="@color/live_host_sign_rank_title_tip"
                android:textSize="13sp" />
        </hb.drawable.shape.view.HbRadioGroup>

        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/ivSignPodium"
            android:layout_width="0dp"
            android:layout_height="108dp"
            app:layout_constraintLeft_toLeftOf="@id/vRankBg"
            app:layout_constraintRight_toRightOf="@id/vRankBg"
            app:layout_constraintTop_toBottomOf="@id/tvFirstHostName"
            app:placeholderImage="@color/transparent"
            tools:background="@drawable/live_host_sign_podium" />

        <hb.ximage.fresco.AvatarDraweeView
            android:id="@+id/ivFirstAvatar"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:layout_marginTop="16dp"
            app:layout_constraintLeft_toLeftOf="@id/vRankAwardBg"
            app:layout_constraintRight_toRightOf="@id/vRankAwardBg"
            app:layout_constraintTop_toBottomOf="@id/rgRankType"
            app:placeholderImage="@color/transparent"
            app:roundingBorderColor="#FFF5AD"
            app:roundingBorderWidth="2dp"
            tools:placeholderImage="@drawable/live_host_sign_rank_avatar_default" />

        <ImageView
            android:id="@+id/ivFirstLiving"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/live_host_sign_rank_living"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/ivFirstAvatar"
            app:layout_constraintRight_toRightOf="@id/ivFirstAvatar"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvFirstHostName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="虚位以待"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintLeft_toLeftOf="@id/ivFirstAvatar"
            app:layout_constraintRight_toRightOf="@id/ivFirstAvatar"
            app:layout_constraintTop_toBottomOf="@id/ivFirstAvatar" />

        <TextView
            android:id="@+id/tvFirstDurationTip"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:text="- 直播时长 -"
            android:textColor="@color/TH_Gray600"
            android:textSize="9sp"
            app:layout_constraintBottom_toBottomOf="@id/ivSignPodium"
            app:layout_constraintLeft_toLeftOf="@id/ivFirstAvatar"
            app:layout_constraintRight_toRightOf="@id/ivFirstAvatar" />

        <TextView
            android:id="@+id/tvFirstDuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/TH_Gray990"
            android:textSize="13sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="@id/tvFirstDurationTip"
            app:layout_constraintRight_toRightOf="@id/tvFirstDurationTip"
            app:layout_constraintTop_toBottomOf="@id/tvFirstDurationTip"
            tools:text="-" />

        <hb.ximage.fresco.AvatarDraweeView
            android:id="@+id/ivSecondAvatar"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginLeft="24dp"
            android:layout_marginTop="36dp"
            app:layout_constraintLeft_toLeftOf="@id/vRankAwardBg"
            app:layout_constraintTop_toTopOf="@id/ivFirstAvatar"
            app:placeholderImage="@color/transparent"
            app:roundingBorderColor="#C4C4C4"
            app:roundingBorderWidth="2dp"
            tools:placeholderImage="@drawable/live_host_sign_rank_avatar_default" />

        <ImageView
            android:id="@+id/ivSecondLiving"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/live_host_sign_rank_living"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/ivSecondAvatar"
            app:layout_constraintRight_toRightOf="@id/ivSecondAvatar"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvSecondHostName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="虚位以待"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintLeft_toLeftOf="@id/ivSecondAvatar"
            app:layout_constraintRight_toRightOf="@id/ivSecondAvatar"
            app:layout_constraintTop_toBottomOf="@id/ivSecondAvatar" />

        <TextView
            android:id="@+id/tvSecondDurationTip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="28dp"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:text="- 直播时长 -"
            android:textColor="@color/TH_Gray600"
            android:textSize="9sp"
            app:layout_constraintBottom_toBottomOf="@id/ivSignPodium"
            app:layout_constraintLeft_toLeftOf="@id/vRankBg" />

        <TextView
            android:id="@+id/tvSecondDuration"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/TH_Gray990"
            android:textSize="13sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="@id/tvSecondDurationTip"
            app:layout_constraintRight_toRightOf="@id/tvSecondDurationTip"
            app:layout_constraintTop_toBottomOf="@id/tvSecondDurationTip"
            tools:text="-" />

        <hb.ximage.fresco.AvatarDraweeView
            android:id="@+id/ivThirdAvatar"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginTop="36dp"
            android:layout_marginRight="25dp"
            app:layout_constraintRight_toRightOf="@id/vRankAwardBg"
            app:layout_constraintTop_toTopOf="@id/ivFirstAvatar"
            app:placeholderImage="@color/transparent"
            app:roundingBorderColor="#DEB0A2"
            app:roundingBorderWidth="2dp"
            tools:placeholderImage="@drawable/live_host_sign_rank_avatar_default" />

        <ImageView
            android:id="@+id/ivThirdLiving"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/live_host_sign_rank_living"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/ivThirdAvatar"
            app:layout_constraintRight_toRightOf="@id/ivThirdAvatar"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvThirdHostName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="虚位以待"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintLeft_toLeftOf="@id/ivThirdAvatar"
            app:layout_constraintRight_toRightOf="@id/ivThirdAvatar"
            app:layout_constraintTop_toBottomOf="@id/ivThirdAvatar" />

        <TextView
            android:id="@+id/tvThirdDurationTip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="29dp"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:text="- 直播时长 -"
            android:textColor="@color/TH_Gray600"
            android:textSize="9sp"
            app:layout_constraintBottom_toBottomOf="@id/ivSignPodium"
            app:layout_constraintRight_toRightOf="@id/vRankBg" />

        <TextView
            android:id="@+id/tvThirdDuration"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/TH_Gray990"
            android:textSize="13sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="@id/tvThirdDurationTip"
            app:layout_constraintRight_toRightOf="@id/tvThirdDurationTip"
            app:layout_constraintTop_toBottomOf="@id/tvThirdDurationTip"
            tools:text="-" />

        <hb.ximage.fresco.AvatarDraweeView
            android:id="@+id/ivCurrentHostAvatar"
            android:layout_width="54dp"
            android:layout_height="54dp"
            android:layout_marginLeft="60dp"
            android:layout_marginBottom="19dp"
            app:layout_constraintBottom_toBottomOf="@id/vRankBg"
            app:layout_constraintLeft_toLeftOf="@id/vRankBg"
            app:roundingBorderColor="#FFEBCE"
            app:roundingBorderWidth="2dp" />

        <TextView
            android:id="@+id/tvCurrentHostRank"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:textColor="@color/black"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="@id/ivCurrentHostAvatar"
            app:layout_constraintLeft_toLeftOf="@id/vRankBg"
            app:layout_constraintTop_toTopOf="@id/ivCurrentHostAvatar"
            tools:text="100+" />

        <TextView
            android:id="@+id/tvCurrentHostName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@id/ivCurrentHostAvatar"
            app:layout_constraintLeft_toRightOf="@id/ivCurrentHostAvatar"
            app:layout_constraintTop_toTopOf="@id/ivCurrentHostAvatar"
            tools:text="主播昵称" />

        <TextView
            android:id="@+id/tvCurrentHostLiveTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="23dp"
            android:textColor="@color/black"
            android:textSize="11sp"
            app:layout_constraintBottom_toBottomOf="@id/ivCurrentHostAvatar"
            app:layout_constraintRight_toRightOf="@id/vRankBg"
            app:layout_constraintTop_toTopOf="@id/ivCurrentHostAvatar"
            tools:text="23.5小时" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>