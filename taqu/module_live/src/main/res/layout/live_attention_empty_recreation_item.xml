<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <hb.drawable.shape.view.HbView
        android:id="@+id/bgView"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="7dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        app:layout_constraintTop_toTopOf="parent"
        app:corner="12dp"
        app:solid="@color/TH_Navy100"/>

    <ImageView
        android:id="@+id/ivIcon"
        android:layout_width="56dp"
        android:layout_height="56dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/bgView"
        android:layout_marginStart="12dp"
        android:src="@drawable/live_bg_attention_item_icon" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintBottom_toTopOf="@+id/tvDEsc"
        app:layout_constraintStart_toEndOf="@+id/ivIcon"
        app:layout_constraintEnd_toEndOf="@+id/bgView"
        android:text="您还没有关注的主播"
        android:textStyle="bold"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/t6" />

    <TextView
        android:id="@+id/tvDEsc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivIcon"
        app:layout_constraintEnd_toEndOf="@+id/bgView"
        android:text="关注后可以在这里看到主播的开播房间哦~"
        android:textColor="@color/TH_Gray600"
        android:textSize="@dimen/t6" />

</androidx.constraintlayout.widget.ConstraintLayout>
