<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/ivBg"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:scaleType="fitXY"
        android:layout_marginStart="74dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/tvContent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:placeholderImage="@color/transparent"
        android:src="@drawable/live_ceremony_party_floating_bg" />

    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/imgHost"
        app:placeholderImage="@drawable/live_ceremony_party_floating_normal_avatar"
        android:layout_marginStart="11dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="34dp"
        android:layout_height="34dp" />

    <ImageView
        android:id="@+id/imgHostBg"
        android:layout_width="46dp"
        android:layout_height="44dp"
        android:src="@drawable/live_ceremony_party_floating_left_avatar_ic"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/imgUser"
        app:placeholderImage="@drawable/live_ceremony_party_floating_normal_avatar"
        android:layout_marginEnd="11dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/imgUserBg"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="34dp"
        android:layout_height="34dp" />

    <ImageView
        android:id="@+id/imgUserBg"
        android:layout_width="46dp"
        android:layout_height="44dp"
        android:src="@drawable/live_ceremony_party_floating_right_avatar_ic"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/imgHostBg"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvContent"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:layout_marginStart="92dp"
        android:gravity="center_vertical"
        android:paddingEnd="40dp"
        android:textColor="@color/white"
        android:textSize="@dimen/t6_2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="赢得年度盛典总决赛冠军赢得年度盛典总决赛冠军" />

</androidx.constraintlayout.widget.ConstraintLayout>