<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="56dp">

    <TextView
        android:id="@+id/tvDate"
        style="@style/Text.T7.G3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="11.15" />

    <TextView
        android:layout_width="4dp"
        android:layout_height="4dp"
        android:layout_marginStart="48dp"
        android:layout_marginTop="14dp"
        android:background="@drawable/live_alliance_event_point"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvContent"
        style="@style/Text.T7.G2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="25dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:lineSpacingExtra="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvDate"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="什么什么什么是你帮忙凄凄切切群群群群群群群群群群群群群群群群去加入了联盟" />
</androidx.constraintlayout.widget.ConstraintLayout>