<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tvDate"
        android:layout_width="70dp"
        android:layout_height="54dp"
        android:layout_marginStart="12dp"
        android:gravity="center_horizontal"
        android:paddingTop="12dp"
        android:text="日期"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_B4"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/tvTime"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.20" />

    <TextView
        android:id="@+id/tvTime"
        android:layout_width="60dp"
        android:layout_height="54dp"
        android:gravity="center_horizontal"
        android:paddingTop="12dp"
        android:text="上麦时长"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_B4"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/tvGiftValue"
        app:layout_constraintStart_toEndOf="@+id/tvDate"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.17" />

    <TextView
        android:id="@+id/tvTimeUnit"
        android:layout_width="60dp"
        android:layout_height="54dp"
        android:gravity="center_horizontal"
        android:paddingTop="29dp"
        android:text="(分钟)"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_N3"
        app:layout_constraintEnd_toEndOf="@+id/tvTime"
        app:layout_constraintStart_toStartOf="@+id/tvTime"
        app:layout_constraintTop_toTopOf="@+id/tvTime" />

    <TextView
        android:id="@+id/tvGiftValue"
        android:layout_width="wrap_content"
        android:layout_height="54dp"
        android:gravity="center_horizontal"
        android:paddingTop="12dp"
        android:text="趣币礼物收益"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_B4"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/tvShellValue"
        app:layout_constraintStart_toEndOf="@+id/tvTime"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.205" />

    <TextView
        android:id="@+id/tvGiftValueUnit"
        android:layout_width="wrap_content"
        android:layout_height="54dp"
        android:gravity="center_horizontal"
        android:paddingTop="29dp"
        android:text="(趣币)"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_N3"
        app:layout_constraintEnd_toEndOf="@+id/tvGiftValue"
        app:layout_constraintStart_toStartOf="@+id/tvGiftValue"
        app:layout_constraintTop_toTopOf="@+id/tvGiftValue" />

    <TextView
        android:id="@+id/tvShellValue"
        android:layout_width="0dp"
        android:layout_height="54dp"
        android:gravity="center_horizontal"
        android:minWidth="72dp"
        android:paddingTop="12dp"
        android:text="贝壳值"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_B4"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/tvRoomId"
        app:layout_constraintStart_toEndOf="@+id/tvGiftValue"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.205" />

    <TextView
        android:id="@+id/tvShellValueUnit"
        android:layout_width="wrap_content"
        android:layout_height="54dp"
        android:gravity="center_horizontal"
        android:paddingTop="29dp"
        android:text="(贝壳)"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_N3"
        app:layout_constraintEnd_toEndOf="@+id/tvShellValue"
        app:layout_constraintStart_toStartOf="@+id/tvShellValue"
        app:layout_constraintTop_toTopOf="@+id/tvShellValue" />

    <TextView
        android:id="@+id/tvRoomId"
        android:layout_width="0dp"
        android:layout_height="54dp"
        android:layout_marginEnd="12dp"
        android:gravity="center_horizontal"
        android:paddingTop="12dp"
        android:text="房主ID"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_B4"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvShellValue"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.219" />

    <FrameLayout
        android:id="@+id/flRank"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginTop="54dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/tvRoomId"
        app:layout_constraintStart_toStartOf="@+id/tvDate"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>