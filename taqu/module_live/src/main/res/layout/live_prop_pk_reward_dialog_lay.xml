<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <hb.drawable.shape.view.HbView
        android:id="@+id/viewBg"
        android:layout_width="270dp"
        android:layout_height="197dp"
        app:corner="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:solid="@color/TH_Navy002" />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivTop"
        android:layout_width="270dp"
        android:layout_height="100dp"
        app:layout_constraintEnd_toEndOf="@+id/viewBg"
        app:layout_constraintStart_toStartOf="@+id/viewBg"
        app:layout_constraintTop_toTopOf="@+id/viewBg"
        app:placeholderImage="@color/transparent" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="恭喜您获得"
        android:textColor="@color/TH_Gray990"
        android:textSize="@dimen/TH_FONT_H2"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/viewBg"
        app:layout_constraintStart_toStartOf="@+id/viewBg"
        app:layout_constraintTop_toTopOf="@+id/viewBg" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="57dp"
        app:layout_constraintEnd_toEndOf="@+id/viewBg"
        app:layout_constraintStart_toStartOf="@+id/viewBg"
        app:layout_constraintTop_toTopOf="@+id/viewBg" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/btn"
        android:layout_width="238dp"
        android:layout_height="40dp"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:text="开心收下"
        android:textColor="@color/white"
        android:textSize="@dimen/TH_FONT_H3"
        android:textStyle="bold"
        app:corner="40dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewBg"
        app:layout_constraintEnd_toEndOf="@+id/viewBg"
        app:layout_constraintStart_toStartOf="@+id/viewBg"
        app:solid="@color/TH_Red600" />

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:layout_margin="7dp"
        android:padding="5dp"
        android:src="@drawable/xdialog_close_ic"
        app:layout_constraintEnd_toEndOf="@+id/viewBg"
        app:layout_constraintTop_toTopOf="@+id/viewBg" />

</androidx.constraintlayout.widget.ConstraintLayout>