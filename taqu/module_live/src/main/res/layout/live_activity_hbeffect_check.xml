<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:orientation="vertical">


    <LinearLayout
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/searchPreLay"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="gone"
        tools:visibility="visible">

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="30dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_weight="1">

            <EditText
                android:id="@+id/editKeyword"
                android:layout_width="match_parent"
                android:layout_height="34dp"
                android:layout_gravity="center_vertical"
                android:background="@drawable/bg_g6_radius_6"
                android:drawableLeft="@drawable/ic_search_left"
                android:drawablePadding="5dp"
                android:hint="搜索资源名称或者id"
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:maxLines="1"
                android:paddingLeft="4dp"
                android:textColor="@color/g1"
                android:textColorHint="@color/g4"
                android:textSize="@dimen/t6" />

            <ImageView
                android:id="@+id/imgClearEdit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="5dp"
                android:src="@drawable/ic_clear_edittext"
                android:visibility="gone" />
        </RelativeLayout>

        <TextView
            android:id="@+id/btnCancel"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:paddingRight="16dp"
            android:text="取消"
            android:textColor="@color/g2"
            android:textSize="@dimen/t6" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/parentLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/searchPreLay" />

    <com.xmhaibao.imganim.XImageAnimation
        android:id="@+id/imgAnimLottieCheck"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/searchPreLay" />

</androidx.constraintlayout.widget.ConstraintLayout>