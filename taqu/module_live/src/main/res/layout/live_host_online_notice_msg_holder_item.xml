<?xml version="1.0" encoding="utf-8"?>
<hb.drawable.shape.view.HbConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="35dp"
    android:layout_marginTop="2dp"
    android:layout_marginRight="33dp"
    app:corner="10dp"
    app:solid="@color/black_alpha_30">


    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/ivAvatar"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="8dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvContent"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:textColor="@color/white"
        android:textSize="13sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/tvEnter"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="@id/ivAvatar"
        tools:text="关注主播用户昵称正在直播" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvEnter"
        android:layout_width="wrap_content"
        android:layout_height="27dp"
        android:layout_marginEnd="8dp"
        android:gravity="center"
        android:paddingLeft="6dp"
        android:paddingRight="6dp"
        android:textColor="@color/white"
        android:textSize="11sp"
        app:corner="6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:solid="#FF2076"
        android:text="去看看" />


</hb.drawable.shape.view.HbConstraintLayout>