<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/ivWinBg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/live_gift_red_packet_win" />

    <TextView
        android:id="@+id/tvWinTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/ivWinBg"
        app:layout_constraintRight_toRightOf="@id/ivWinBg"
        app:layout_constraintTop_toTopOf="@id/ivWinBg"
        tools:text="恭喜获得" />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivGiftIcon"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginTop="22dp"
        android:scaleType="fitXY"
        app:layout_constraintLeft_toLeftOf="@id/ivWinBg"
        app:layout_constraintRight_toRightOf="@id/ivWinBg"
        app:layout_constraintTop_toBottomOf="@id/tvWinTitle"
        app:placeholderImage="@color/translucent"
        tools:background="@drawable/live_gift_red_packet_unwin" />

    <TextView
        android:id="@+id/tvWinResultTip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:textColor="#FF0006"
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="@id/ivWinBg"
        app:layout_constraintRight_toRightOf="@id/ivWinBg"
        app:layout_constraintTop_toBottomOf="@id/ivGiftIcon"
        tools:text="礼物名称X99" />

    <TextView
        android:id="@+id/tvPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FF0006"
        android:textSize="10sp"
        app:layout_constraintLeft_toLeftOf="@id/ivWinBg"
        app:layout_constraintRight_toRightOf="@id/ivWinBg"
        app:layout_constraintTop_toBottomOf="@id/tvWinResultTip"
        tools:text="999趣币" />

    <Button
        android:id="@+id/btDoAction"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="76dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="76dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/live_gift_red_packet_result_btn_normal"
        android:gravity="center"
        android:paddingBottom="10dp"
        android:text="确定"
        android:textColor="#FF0006"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/ivWinBg"
        app:layout_constraintLeft_toLeftOf="@id/ivWinBg"
        app:layout_constraintRight_toRightOf="@id/ivWinBg" />

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginTop="24dp"
        android:src="@drawable/base_red_package_close_ic"
        app:layout_constraintLeft_toLeftOf="@id/ivWinBg"
        app:layout_constraintRight_toRightOf="@id/ivWinBg"
        app:layout_constraintTop_toBottomOf="@id/ivWinBg" />


</androidx.constraintlayout.widget.ConstraintLayout>