<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="80dp"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/upgradeLay"
        android:layout_width="80sp"
        android:layout_height="wrap_content"
        android:visibility="gone">

        <FrameLayout
            android:id="@+id/barLay"
            android:layout_width="80dp"
            android:layout_height="60dp"
            android:background="@drawable/bg_black_radius_4_alpha_50">

            <ProgressBar
                android:id="@+id/progressbar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="70dp"
                android:layout_height="2dp"
                android:layout_gravity="bottom|center_horizontal"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/tvHint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|center_horizontal"
                android:layout_marginBottom="8dp"
                android:textColor="@color/white"
                android:textSize="@dimen/t8" />


        </FrameLayout>

        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/icon"
            android:layout_width="80dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            app:placeholderImage="@color/transparent" />


    </FrameLayout>

    <FrameLayout
        android:id="@+id/seatLay"
        android:layout_width="60dp"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:visibility="gone">

        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/seatBg"
            android:layout_width="54dp"
            android:layout_height="54dp"
            android:layout_gravity="center_horizontal"
            app:placeholderImage="@color/transparent" />


        <hb.ximage.fresco.AvatarDraweeView
            android:id="@+id/ivmVp"
            android:layout_width="26dp"
            android:layout_height="26dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="14dp"
            android:visibility="gone"
            app:placeholderImage="@color/transparent" />


        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/tvSeatBg"
            android:layout_width="60dp"
            android:layout_height="12dp"
            android:layout_marginTop="54dp"
            app:placeholderImage="@color/transparent" />

        <TextView
            android:id="@+id/tvMvp"
            android:layout_width="60dp"
            android:layout_height="12dp"
            android:layout_marginTop="54dp"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="8sp" />

    </FrameLayout>

</LinearLayout>