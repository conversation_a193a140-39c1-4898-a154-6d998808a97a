package com.xmhaibao.multichat.impl

import cn.taqu.lib.base.voice.IVoiceRecordCallback
import com.xmhaibao.hbchat.bean.bean.MessageVoiceItemBean
import com.xmhaibao.message.api.constants.ImTypeConstants
import com.xmhaibao.multichat.message.viewmodel.MultiChatMessageViewModel
import com.xmhaibao.multichat.viewmodel.MultiChatViewModel
import com.xmhaibao.multichat.widget.multichat.MultiChatBottomInputView
import hb.message.HBChat
import hb.message.db.entity.HBMessageContentDO
import hb.utils.Loger
import hb.utils.ToastUtils

/**
 * @desc:群里-录制语音回调
 *
 * <AUTHOR>
 * @date 2023/5/24
 */
class MultiChatVoiceRecordCallback(
    private val viewModel: MultiChatViewModel,
    private val messageViewModel: MultiChatMessageViewModel,
    private val inputView: MultiChatBottomInputView?,
) :
    IVoiceRecordCallback {

    companion object {
        private const val TAG = "MultiChatVoiceRecordCallback"
    }

    override fun recordSuccess(
        voiceUrl: String,
        duration: Int,
        hbMessageContentDO: HBMessageContentDO?,
        localRecordPath: String
    ) {
        viewModel.hideLoadingBar()
        messageViewModel.sendVoiceMessage(voiceUrl, duration, hbMessageContentDO)
    }

    override fun recordTempVoice(seconds: Int, localRecordPath: String): HBMessageContentDO? {
        val voiceBean = MessageVoiceItemBean()
        voiceBean.voiceUrl = localRecordPath
        voiceBean.voiceTime = seconds
        val contentBean = messageViewModel.createMineSendMessageContentBean(ImTypeConstants.MESSAGE_VOICE_MESSAGE, voiceBean)
        HBChat.getMultiChatProvider().updateMessage(contentBean)
        return contentBean
    }

    override fun recordFail() {
        viewModel.hideLoadingBar()
        ToastUtils.show("语音发送失败")
    }

    override fun hideRecordDialog() {
        inputView?.hideRecordDialog()
    }

    override fun showRecordDialog() {
        //ignore
    }

    override fun hideLoading() {
        viewModel.hideLoadingBar()
    }

    override fun updateVoiceStatusPosition(position: Int) {
        Loger.d(TAG, " updateVoiceStatusPosition = $position")
    }
}