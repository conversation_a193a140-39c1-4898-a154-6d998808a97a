package com.xmhaibao.multichat.message.widget

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.core.view.isVisible
import com.xmhaibao.hbchat.bean.bean.MessageCommonAccountInfo
import com.xmhaibao.multichat.R
import com.xmhaibao.multichat.databinding.MultichatMessageUserViewBinding
import hb.utils.SizeUtils
import hb.utils.StringUtils

/**
 * 群聊-消息体用户信息
 *
 * <AUTHOR>
 * @date 2023/5/23
 */
class MultiChatMessageUserView : LinearLayout {

    private val mBinding: MultichatMessageUserViewBinding = MultichatMessageUserViewBinding.inflate(LayoutInflater.from(context), this)

    constructor(context: Context?) : super(context)
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    init {
        orientation = HORIZONTAL
        gravity = Gravity.CENTER_VERTICAL
    }

    fun setUserInfo(accountInfo: MessageCommonAccountInfo?) {
        accountInfo?.apply {
            mBinding.tvNickName.text = nickName

            if (sexType.isNullOrEmpty() && age.isNullOrEmpty()) {
                mBinding.tvSexAge.isVisible = false
            } else {
                mBinding.tvSexAge.isVisible = true
                mBinding.tvSexAge.updateData(sexType, age)
            }
            mBinding.tvRoleTagView.setRole(StringUtils.stringToInt(groupRole))
        }
        mBinding.bdvMatchmakerLevel.isVisible = accountInfo?.matchmakerLevelIcon.run {
            this?.let {
                mBinding.bdvMatchmakerLevel.setImageFromUrl(it)
                mBinding.bdvMatchmakerLevel.setAdaptiveByFixedHeight(SizeUtils.dp2px(16F))
            }
            !this.isNullOrBlank()
        }
    }

    override fun setLayoutDirection(layoutDirection: Int) {
        super.setLayoutDirection(layoutDirection)
        mBinding.tvSexAge.layoutDirection = LAYOUT_DIRECTION_LTR
        mBinding.tvRoleTagView.layoutDirection = LAYOUT_DIRECTION_LTR
        mBinding.bdvMatchmakerLevel.layoutDirection = LAYOUT_DIRECTION_LTR
    }
}