package com.xmhaibao.multichat.utils

import com.xmhaibao.multichat.activity.MultiChatActivity
import com.xmhaibao.multichat.activity.MultiChatNoticeListActivity
import com.xmhaibao.multichat.activity.MultiChatNoticeReleaseActivity
import com.xmhaibao.multichat.setting.activity.MultiChatMemberManageActivity
import com.xmhaibao.multichat.setting.activity.MultiChatSettingActivity
import hb.utils.ActivityUtils

/**
 * 群聊-activity工具类
 *
 * <AUTHOR>
 * @date 2023/5/19
 */
object MultiChatActivityUtils {

    /**
     * 关闭群聊相关所有页面（解散群聊、退出群聊）
     */
    fun closeAllMultiChatActivity() {
        val iterator = ActivityUtils.getActivityList().listIterator()
        while (iterator.hasNext()) {
            val next = iterator.next()
            if (next is MultiChatActivity
                || next is MultiChatSettingActivity
                || next is MultiChatNoticeListActivity
                || next is MultiChatNoticeReleaseActivity
                || next is MultiChatMemberManageActivity
            ) {
                next.finish()
            }
        }
    }
}