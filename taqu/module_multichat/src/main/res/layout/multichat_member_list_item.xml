<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="72dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/ivChoose"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:src="@drawable/multichat_choose_normal_ic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>


    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivChoose"
        android:layout_marginStart="12dp"
        android:layout_width="48dp"
        android:layout_height="48dp"/>

    <TextView
        android:id="@+id/tvNickName"
        tools:text="1111"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        android:layout_marginStart="8dp"
        style="@style/Text.B1.G990"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
    
    <com.xmhaibao.multichat.widget.MultiChatSexAgeView
        android:id="@+id/genderView"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvNickName"
        android:layout_marginStart="4dp"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_16"/>

</androidx.constraintlayout.widget.ConstraintLayout>