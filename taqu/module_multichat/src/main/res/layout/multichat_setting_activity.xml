<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/scrollView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/TH_Navy110"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <hb.drawable.shape.view.HbView
            android:id="@+id/viewBaseInfoBorder"
            android:layout_width="0dp"
            android:layout_height="199dp"
            android:layout_marginStart="7dp"
            android:layout_marginTop="7dp"
            android:layout_marginEnd="7dp"
            app:corner="12dp"
            app:gradient_color_end="@color/transparent"
            app:gradient_color_start="@color/multichat_setting_top_border"
            app:gradient_linear_orientation="top_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <hb.drawable.shape.view.HbView
            android:id="@+id/viewBaseInfoBg"
            android:layout_width="0dp"
            android:layout_height="199dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            app:corner="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:solid="@color/TH_Navy001" />

        <hb.drawable.shape.view.HbView
            android:id="@+id/viewBaseInfoGradientBg"
            android:layout_width="0dp"
            android:layout_height="199dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            app:corner="12dp"
            app:gradient_color_end="@color/transparent"
            app:gradient_color_start="@color/multichat_setting_top_bg_start_color"
            app:gradient_linear_orientation="top_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <hb.ximage.fresco.BaseDraweeView
            android:id="@+id/ivCover"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginStart="12dp"
            android:layout_marginTop="12dp"
            app:layout_constraintStart_toStartOf="@+id/viewBaseInfoGradientBg"
            app:layout_constraintTop_toTopOf="@+id/viewBaseInfoGradientBg"
            app:placeholderImage="@color/transparent"
            app:roundedCornerRadius="8dp" />

        <hb.drawable.shape.view.HbImageView
            android:id="@+id/ivCoverEdit"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:padding="6dp"
            app:stroke_color="@color/TH_Gray001"
            app:stroke_width="1dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:shape="oval"
            app:solid="@color/TH_Yellow600"
            android:src="@drawable/multichat_setting_cover_edit_ic"
            android:translationX="2dp"
            android:translationY="2dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivCover"
            app:layout_constraintEnd_toEndOf="@+id/ivCover" />

        <TextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:maxWidth="220dp"
            android:singleLine="true"
            android:textColor="@color/TH_Gray990"
            android:textSize="@dimen/TH_FONT_H3"
            app:layout_constraintBottom_toTopOf="@+id/tvId"
            app:layout_constraintStart_toEndOf="@+id/ivCover"
            app:layout_constraintTop_toTopOf="@+id/ivCover"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="群聊名称" />

        <hb.drawable.shape.view.HbView
            android:id="@+id/viewManageBg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            app:corner="12dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvManageTitle"
            app:layout_constraintEnd_toEndOf="@+id/viewBaseInfoGradientBg"
            app:layout_constraintStart_toStartOf="@+id/viewBaseInfoGradientBg"
            app:layout_constraintTop_toBottomOf="@+id/viewInfoBg"
            app:solid="@color/TH_Navy001" />


        <ImageView
            android:id="@+id/ivNameEdit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible"
            android:padding="4dp"
            android:src="@drawable/multichat_setting_name_edit_ic"
            app:layout_constraintBottom_toBottomOf="@+id/tvName"
            app:layout_constraintStart_toEndOf="@+id/tvName"
            app:layout_constraintTop_toTopOf="@+id/tvName" />

        <hb.drawable.shape.view.HbTextView
            android:id="@+id/tvCategorize"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingLeft="6dp"
            android:paddingTop="1dp"
            android:paddingRight="6dp"
            android:paddingBottom="1dp"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:visibility="gone"
            app:corner="39dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvId"
            app:layout_constraintStart_toStartOf="@+id/tvName"
            app:layout_constraintTop_toTopOf="@+id/tvId"
            app:solid="@color/TH_Pink600"
            tools:text="情绪发泄" />


        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrierCategorize"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="end"
            app:constraint_referenced_ids="tvCategorize" />

        <android.widget.Space
            android:id="@+id/spaceCategorize"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toEndOf="@+id/barrierCategorize" />

        <TextView
            android:id="@+id/tvId"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="6dp"
            android:textColor="@color/TH_Gray600"
            android:textSize="10sp"
            app:layout_constraintBottom_toBottomOf="@+id/ivCover"
            app:layout_constraintStart_toEndOf="@+id/spaceCategorize"
            app:layout_constraintTop_toBottomOf="@+id/tvName"
            app:layout_goneMarginStart="0dp"
            tools:text="ID: 1231231231" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvMemberList"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="4dp"
            app:layout_constraintEnd_toEndOf="@+id/viewBaseInfoGradientBg"
            app:layout_constraintStart_toStartOf="@+id/viewBaseInfoGradientBg"
            app:layout_constraintTop_toBottomOf="@+id/ivCover"
            tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:orientation="horizontal" />

        <TextView
            android:id="@+id/tvMemberMore"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:drawableEnd="@drawable/multichat_setting_member_arrow_ic"
            android:gravity="center"
            android:visibility="gone"
            android:textColor="@color/TH_Gray600"
            android:textSize="@dimen/t6_1"
            tools:visibility="visible"
            app:layout_constraintEnd_toEndOf="@+id/viewBaseInfoGradientBg"
            app:layout_constraintStart_toStartOf="@+id/viewBaseInfoGradientBg"
            app:layout_constraintTop_toBottomOf="@+id/rvMemberList"
            tools:text="查看全部成员（200）" />

        <hb.drawable.shape.view.HbView
            android:id="@+id/viewInfoBg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            app:corner="12dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvReport"
            app:layout_constraintEnd_toEndOf="@+id/viewBaseInfoGradientBg"
            app:layout_constraintStart_toStartOf="@+id/viewBaseInfoGradientBg"
            app:layout_constraintTop_toBottomOf="@+id/viewBaseInfoGradientBg"
            app:solid="@color/TH_Navy001" />

        <android.widget.Space
            android:id="@+id/spaceTitleWidth"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            app:layout_constraintEnd_toEndOf="@+id/viewInfoBg"
            app:layout_constraintStart_toStartOf="@+id/viewInfoBg" />

        <TextView
            android:id="@+id/tvNoticeTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:drawableEnd="@drawable/multichat_setting_arrow_right_ic"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:text="群公告"
            android:textColor="@color/TH_Gray990"
            android:textSize="@dimen/TH_FONT_H3"
            app:layout_constraintEnd_toEndOf="@+id/spaceTitleWidth"
            app:layout_constraintStart_toStartOf="@+id/spaceTitleWidth"
            app:layout_constraintTop_toTopOf="@+id/viewInfoBg" />

        <TextView
            android:id="@+id/tvNoticeTitleNoSet"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:text="未设置"
            android:layout_marginEnd="20dp"
            android:textColor="@color/TH_Gray600"
            android:textSize="@dimen/t6"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@+id/tvNoticeTitle"
            app:layout_constraintEnd_toEndOf="@+id/tvNoticeTitle"
            app:layout_constraintTop_toTopOf="@+id/tvNoticeTitle" />

        <TextView
            android:id="@+id/tvNoticeContent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="3"
            android:paddingBottom="12dp"
            android:textColor="@color/TH_Gray600"
            android:textSize="@dimen/TH_FONT_B2"
            app:layout_constraintEnd_toEndOf="@+id/tvNoticeTitle"
            app:layout_constraintStart_toStartOf="@+id/tvNoticeTitle"
            app:layout_constraintTop_toBottomOf="@+id/tvNoticeTitle"
            tools:text="福建省厦门市集美区软件园三期C区C08厦门海豹他趣有限公司福建省厦门市集美区软件园三期C区C08厦门海豹他趣有限公司福建省厦门市集美区软件园三期C区C08厦门海豹他趣有限公司" />

        <TextView
            android:id="@+id/tvIntroTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:text="群介绍"
            android:textColor="@color/TH_Gray990"
            android:textSize="@dimen/TH_FONT_H3"
            app:layout_constraintEnd_toEndOf="@+id/spaceTitleWidth"
            app:layout_constraintStart_toStartOf="@+id/spaceTitleWidth"
            app:layout_constraintTop_toBottomOf="@+id/tvNoticeContent" />

        <TextView
            android:id="@+id/tvIntroTitleNoSet"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:text="未设置"
            android:visibility="gone"
            tools:visibility="visible"
            android:textColor="@color/TH_Gray600"
            android:textSize="@dimen/t6"
            app:layout_constraintBottom_toBottomOf="@+id/tvIntroTitle"
            app:layout_constraintEnd_toEndOf="@+id/tvIntroTitle"
            app:layout_constraintTop_toTopOf="@+id/tvIntroTitle" />

        <TextView
            android:id="@+id/tvIntroContent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingBottom="8dp"
            android:textColor="@color/TH_Gray600"
            android:textSize="@dimen/TH_FONT_B2"
            app:layout_constraintEnd_toEndOf="@+id/tvIntroTitle"
            app:layout_constraintStart_toStartOf="@+id/tvIntroTitle"
            app:layout_constraintTop_toBottomOf="@+id/tvIntroTitle"
            tools:text="福建省厦门市集美区软件园三期C区C08厦门海豹他趣有限公司福建省厦门市集美区软件园三期C区C08厦门海豹他趣有限公司福建省厦门市集美区软件园三期C区C08厦门海豹他趣有限公司" />


        <TextView
            android:id="@+id/tvReport"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:drawableEnd="@drawable/multichat_setting_arrow_right_ic"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="举报"
            android:textColor="@color/TH_Gray990"
            android:textSize="@dimen/TH_FONT_H3"
            app:layout_constraintEnd_toEndOf="@+id/spaceTitleWidth"
            app:layout_constraintStart_toStartOf="@+id/spaceTitleWidth"
            app:layout_constraintTop_toBottomOf="@+id/tvIntroContent" />


        <TextView
            android:id="@+id/tvManageTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:drawableEnd="@drawable/multichat_setting_arrow_right_ic"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="群管理员"
            android:textColor="@color/TH_Gray990"
            android:textSize="@dimen/t5_1"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/spaceTitleWidth"
            app:layout_constraintStart_toStartOf="@+id/spaceTitleWidth"
            app:layout_constraintTop_toTopOf="@id/viewManageBg"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvManageContent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="84dp"
            android:layout_marginEnd="20dp"
            android:gravity="end"
            android:textColor="@color/TH_Gray600"
            android:textSize="@dimen/t6"
            app:layout_constraintBottom_toBottomOf="@+id/tvManageTitle"
            app:layout_constraintEnd_toEndOf="@+id/tvManageTitle"
            app:layout_constraintStart_toStartOf="@+id/tvManageTitle"
            app:layout_constraintTop_toTopOf="@+id/tvManageTitle"
            tools:text="未设置" />

        <hb.drawable.shape.view.HbTextView
            android:id="@+id/tvQuitMultiChat"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:drawableEnd="@drawable/multichat_setting_arrow_right_ic"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:text="退出群聊"
            android:textColor="@color/TH_Red600"
            android:textSize="@dimen/t5_1"
            android:visibility="gone"
            app:corner="12dp"
            app:layout_constraintEnd_toEndOf="@+id/viewBaseInfoGradientBg"
            app:layout_constraintStart_toStartOf="@+id/viewBaseInfoGradientBg"
            app:layout_constraintTop_toBottomOf="@+id/tvManageTitle"
            app:solid="@color/TH_Navy001"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>