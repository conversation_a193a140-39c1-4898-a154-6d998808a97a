package com.xmhaibao.chatroom.artistcenter.inter

/**
 * 一键送礼，私信 用户item点击操作，回调到
 *
 * <AUTHOR>
 * @date 2023-11-16
 */
interface IChatRoomCenterOneKeyItemOperation {

    /**
     * 用户信息点击Item
     * @param pageIndex  当前页面的角标
     * @param isSelectAll 是否全选，true 是
     */
    fun onItemClick(pageIndex: Int, isSelectAll: Boolean)


    /**
     * 当前页面数据加载成功
     * @param pageIndex  当前页面的角标
     */
    fun onPageSelect(pageIndex: Int)


    /**
     * 页面依附来源
     *   @return ChatRoomCenterConstants.SOURCE_END_SUMMARY 下播小结，ChatRoomCenterConstants.SOURCE_ARTIST_CENTER 艺人中心
     */
    fun getAttachedSource(): String? = ""

    /**
     * 页面统计纬度类型
     *@return ChatRoomCenterConstants.ROOM_STAT 房间纬度，ChatRoomCenterConstants.USER_STAT 个人纬度
     */
    fun getAttachedType(): String? = ""

    /**
     * 依附的页面名字，埋点用
     */
    fun getAttachedPageName(): String?


}