package com.xmhaibao.chatroom.roomplay.gobang.view;

import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;

import com.xmhaibao.chatroom.R;

import hb.utils.ColorUtils;
import hb.utils.ResourceUtils;
import hb.utils.SizeUtils;

/**
 * 聊天室-五子棋段位赛匹配页面倒计时控件
 *
 * <AUTHOR>
 * @date 2022/5/23
 */
public class ChatRoomGoBangRankMatchCountDownView extends View implements Runnable {

    //首位0
    private static final String ZERO_FORMAT = "0%s";
    //一分
    private static final int ONE_MINUTE = 60;
    //一小时
    private static final int ONE_HOUR = 60 * ONE_MINUTE;
    //文字背景图
    private Drawable mTextBGDrawable;
    //冒号图片
    private Drawable mColonDrawable;
    //时，分，秒
    private String mHourText = "00", mMinuteText = "00", mSecondText = "00";
    //文字Paint
    private Paint mTextPaint;
    //背景宽高
    private int mBgWidth, mBgHeight;
    //文字间隔
    private int margin;
    //区域Rect
    private Rect mRect;
    //文字宽高
    private int mTextWidth, mTextHeight;
    //冒号宽高
    private int mColonWidth, mColonHeight;
    private Paint.FontMetrics fm;
    //开始时间
    private long startSecond;
    private OnCountDownListener mCountDownListener;

    public interface OnCountDownListener {
        void onEnd();
    }

    public ChatRoomGoBangRankMatchCountDownView(Context context) {
        super(context);
        init();
    }

    public ChatRoomGoBangRankMatchCountDownView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ChatRoomGoBangRankMatchCountDownView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public ChatRoomGoBangRankMatchCountDownView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    /**
     * 初始化属性
     */
    private void init() {
        mTextPaint = new Paint();
        mTextPaint.setAntiAlias(true);
        mTextPaint.setColor(ColorUtils.getColor("#C0E6FF"));
        mTextPaint.setTextAlign(Paint.Align.CENTER);
        mTextPaint.setTextSize(SizeUtils.sp2px(30));
        mTextPaint.setFakeBoldText(true);
        mRect = new Rect();

        margin = SizeUtils.dp2px(6);
        mTextBGDrawable = ResourceUtils.getDrawable(R.drawable.chatroom_gobang_rank_match_count_time_bg);
        mColonDrawable = ResourceUtils.getDrawable(R.drawable.chatroom_gobang_rank_match_colon_ic);

        calculateValues();
    }

    /**
     * 计算值
     */
    private void calculateValues() {
        mTextWidth = (int) mTextPaint.measureText(mHourText);
        fm = mTextPaint.getFontMetrics();
        mTextPaint.getTextBounds("00", 0, 2, mRect);
        mTextHeight = mRect.height();

        if (mTextBGDrawable == null) {
            mBgWidth = mTextWidth;
            mBgHeight = mTextHeight;
        } else {
            mBgWidth = mTextBGDrawable.getIntrinsicWidth();
            mBgHeight = mTextBGDrawable.getIntrinsicHeight();
        }

        if (mColonDrawable != null) {
            mColonWidth = mColonDrawable.getIntrinsicWidth();
            mColonHeight = mColonDrawable.getIntrinsicHeight();
        } else {
            mColonWidth = (int) mTextPaint.measureText(":");
            mColonHeight = mTextHeight;
        }
    }

    /**
     * 开始倒计时
     *
     * @param startTime 要倒计时的时间
     */
    public void startWithTime(long startTime) {
        this.startSecond = startTime;
        removeCallbacks(this);
        post(this);
    }

    /**
     * 取消倒计时
     */
    public void cancelWithTime() {
        startSecond = 0;
        removeCallbacks(this);
    }

    /**
     * 获取时间文字，小于10则首位用0
     *
     * @param number 时间数字
     * @return 时间文字
     */
    private String getTimeText(int number) {
        if (number < 0)
            return "00";
        if (number < 10) {
            return String.format(ZERO_FORMAT, number);
        } else {
            return Integer.toString(number);
        }
    }

    @Override
    public void run() {
        int hour = (int) (startSecond / ONE_HOUR);
        int minute = (int) (startSecond % ONE_HOUR / ONE_MINUTE);
        int second = (int) (startSecond % ONE_HOUR % ONE_MINUTE);
        mHourText = getTimeText(hour);
        mMinuteText = getTimeText(minute);
        mSecondText = getTimeText(second);
        invalidate();
        if (hour <= 0 && minute <= 0 && second <= 0) {
            if (mCountDownListener != null) {
                mCountDownListener.onEnd();
            }
            return;
        }
        startSecond--;
        postDelayed(this, 1000);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawHour(canvas);
        drawColon(canvas);
        drawMinute(canvas);
        drawColon(canvas);
        drawSecond(canvas);
    }

    /**
     * 画小时
     *
     * @param canvas 画布
     */
    private void drawHour(Canvas canvas) {
        mRect.left = 0;
        mRect.top = 0;
        mRect.right = mBgWidth;
        mRect.bottom = mBgHeight;
        drawTextAndBg(canvas, mHourText);
    }

    /**
     * 画第一个冒号
     *
     * @param canvas 画布
     */
    private void drawColon(Canvas canvas) {
        mRect.left = mRect.right + margin;
        mRect.top = (mBgHeight - mColonHeight) / 2;
        mRect.right = mRect.left + mColonWidth;
        mRect.bottom = mRect.top + mColonHeight;
        if (mColonDrawable != null) {
            mColonDrawable.setBounds(mRect);
            mColonDrawable.draw(canvas);
        } else {
            int textX = mRect.centerX();
            int textY = (int) ((mRect.height() - fm.ascent) / 2);
            canvas.drawText(":", textX, textY, mTextPaint);
        }
    }

    /**
     * 画分钟
     *
     * @param canvas 画布
     */
    private void drawMinute(Canvas canvas) {
        mRect.left = mRect.right + margin;
        mRect.top = 0;
        mRect.right = mRect.left + mBgWidth;
        mRect.bottom = mBgHeight;
        drawTextAndBg(canvas, mMinuteText);
    }

    /**
     * 画秒
     *
     * @param canvas 画布
     */
    private void drawSecond(Canvas canvas) {
        mRect.left = mRect.right + margin;
        mRect.top = 0;
        mRect.right = mRect.left + mBgWidth;
        mRect.bottom = mBgHeight;
        drawTextAndBg(canvas, mSecondText);
    }

    /**
     * 画文字和背景
     *
     * @param canvas 画布
     * @param text   文字
     */
    private void drawTextAndBg(Canvas canvas, String text) {
        if (mTextBGDrawable != null) {
            mTextBGDrawable.setBounds(mRect);
            mTextBGDrawable.draw(canvas);
        }
        int textX = mRect.centerX();
        int textY = mRect.centerY() + (mTextHeight - 8) / 2;
        canvas.drawText(text.replace("", " "), textX, textY, mTextPaint);
    }

    /**
     * dip转px
     *
     * @param context 上下文
     * @param dip     the dip
     * @return
     */
    private int dip2px(Context context, int dip) {
        return (int) (TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dip, context.getResources().getDisplayMetrics()) + 0.5f);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        removeCallbacks(this);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int width;
        if (mTextBGDrawable != null) {
            width = mBgWidth * 3 + (margin * 2 + mColonWidth) * 2;
        } else {
            width = mTextWidth * 3 + (margin * 2 + mColonWidth) * 2;
        }
        int makeMeasureSpec = MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY);
        super.onMeasure(makeMeasureSpec, heightMeasureSpec);
    }

    public OnCountDownListener getCountDownListener() {
        return mCountDownListener;
    }

    public void setCountDownListener(OnCountDownListener countDownListener) {
        mCountDownListener = countDownListener;
    }
}
