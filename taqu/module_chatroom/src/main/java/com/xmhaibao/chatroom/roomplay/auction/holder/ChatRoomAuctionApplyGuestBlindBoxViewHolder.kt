package com.xmhaibao.chatroom.roomplay.auction.holder

import android.view.ViewGroup
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.databinding.ChatroomAuctionApplyGuestBlindBoxItemBinding
import com.xmhaibao.chatroom.roomplay.auction.bean.ChatRoomAuctionApplyBlindBoxInfoBean
import dp
import hb.utils.ColorUtils
import hb.xadapter.XBaseViewHolder

/**
 * 拍拍会嘉宾申请盲盒设置
 *
 * <AUTHOR>
 * @date 2023-02-02
 */
class ChatRoomAuctionApplyGuestBlindBoxViewHolder(parent: ViewGroup?) :
        XBaseViewHolder<ChatRoomAuctionApplyBlindBoxInfoBean.Info>(parent, R.layout.chatroom_auction_apply_guest_blind_box_item) {

    private val binding = ChatroomAuctionApplyGuestBlindBoxItemBinding.bind(itemView)

    override fun onBindView(item: ChatRoomAuctionApplyBlindBoxInfoBean.Info?) {
        binding.apply {
            item?.apply {
                imgIcon.setImageFromUrl(icon)
                tvApplyConfig.text = title
                tvApplyConfig.setTextColor(ColorUtils.getColor(if (isSelect) "#FFFFFF" else "#B7B7B7"))
                tvApplyConfig.isEnabled = true
                tvApplyConfig.isSelected = isSelect
                imgTag.setImageFromUrl(tagIcon)
            }
        }

    }
}