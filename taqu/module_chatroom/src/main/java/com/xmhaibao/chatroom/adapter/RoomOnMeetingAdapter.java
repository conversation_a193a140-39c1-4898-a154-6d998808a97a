package com.xmhaibao.chatroom.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.xmhaibao.chatroom.R;
import com.xmhaibao.chatroom.activity.ChatRoomActivity;
import com.xmhaibao.chatroom.activity.ChatRoomCommonTempActivity;
import com.xmhaibao.chatroom.api.constants.ChatRoomType;
import com.xmhaibao.chatroom.bean.ChatRoomOnlineInfo;
import com.xmhaibao.chatroom.constants.ReportSource;
import com.xmhaibao.chatroom.fragment.RoomUserDialogFragment;
import com.xmhaibao.chatroom.helper.ChatRoomManageHelper;
import com.xmhaibao.chatroom.honorlevel.holder.ChatRoomHonorLevelConfigHelper;
import com.xmhaibao.chatroom.honorlevel.view.ChatRoomHonorLevelIconImageView;
import com.xmhaibao.chatroom.utils.ChatRoomTypeUtils;
import com.xmhaibao.chatroom.view.ChatRoomUserSexAndAgeView;
import com.xmhaibao.gift.bean.SendGiftUserInfo;

import java.util.List;

import cn.taqu.lib.base.adapter.BaseLoadMoreRecyclerAdapter2;
import cn.taqu.lib.base.utils.SexTypeUtils;
import hb.common.data.AccountHelper;
import hb.utils.StringUtils;
import hb.ximage.fresco.AvatarDraweeView;
import hb.ximage.fresco.BaseDraweeView;

/**
 * 在线麦上用户
 * Created by laixuejun on 2017/12/29.
 */
public class RoomOnMeetingAdapter extends BaseLoadMoreRecyclerAdapter2 {

    private List<ChatRoomOnlineInfo> onlineInfoList;
    private OnForbidSpeakCallBack onApplyMicCallBack;
    private Context mContext;
    private String mChatUuid;
    private String mManageType;
    private int mChatRoomType;
    private String mSource;

    public RoomOnMeetingAdapter(Context context, String chatUuid,
                                List<ChatRoomOnlineInfo> onlineInfoList,
                                String manageType, int chatRoomType, String source) {
        this.onlineInfoList = onlineInfoList;
        mContext = context;
        mChatUuid = chatUuid;
        mManageType = manageType;
        mChatRoomType = chatRoomType;
        mSource = source;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolderProxy(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.chatroom_room_on_meeting_list_item, parent, false);
        return new OnlineViewHolder(mContext, view, onApplyMicCallBack, mChatUuid, mManageType, mChatRoomType);
    }

    @Override
    public void onBindViewHolderProxy(RecyclerView.ViewHolder holder, int position) {
        OnlineViewHolder viewHolder = (OnlineViewHolder) holder;
        ChatRoomOnlineInfo info = onlineInfoList.get(position);

        final boolean isFamilySource = TextUtils.equals("1", mSource);
        final String avatar = isFamilySource ? info.getRealAvatar() : info.getAvatar();
        if (!StringUtils.isEmpty(avatar)) {
            viewHolder.imgAvatar.setImageFromUrl(avatar);
        } else {
            viewHolder.imgAvatar.setImageFromResource(hb.common.R.drawable.ic_default_avatar_user);
        }
        if (SexTypeUtils.hasSexTypelimit(info.getSexType())) {
            viewHolder.imgSexType.setData(info.getSexType(), "");
        } else {
            viewHolder.imgSexType.setVisibility(View.GONE);
        }
        viewHolder.tvNickName.setText(isFamilySource ? info.getRealNickname() : info.getNickName());
        viewHolder.itemView.setTag(info);
        if (info.isForbidVoice()) {
            viewHolder.btnForbid.setText("恢复");
        } else {
            viewHolder.btnForbid.setText("禁麦");
        }

        //大屏房-房主显示下大屏按钮
        if (ChatRoomTypeUtils.isBigScreenRoomType(mChatRoomType)
                && ChatRoomManageHelper.isRoleOwner(mManageType)
                && TextUtils.equals(info.getAccountUuid(), AccountHelper.getAccountUuid())) {
            viewHolder.btnLeaveBigScreen.setVisibility(View.VISIBLE);
            viewHolder.btnForbid.setVisibility(View.GONE);
            viewHolder.btnLeafMic.setVisibility(View.GONE);
        } else {
            viewHolder.btnLeaveBigScreen.setVisibility(View.GONE);
            viewHolder.btnForbid.setVisibility(View.VISIBLE);
            viewHolder.btnLeafMic.setVisibility(View.VISIBLE);
        }

        if (!StringUtils.isEmpty(info.getSeatNum())) {
            String text = info.getSeatNum();
            if (ChatRoomTypeUtils.isBigScreenRoomAndVideoSeat(mChatRoomType, StringUtils.stringToInt(info.getSeatNum()))) {
                text = "大屏位";
            }
            viewHolder.mTvSpecialSeatNum.setText(text);
            viewHolder.mTvSeatNum.setVisibility(View.GONE);
            viewHolder.mTvSpecialSeatNum.setVisibility(View.VISIBLE);
        } else {
            viewHolder.mTvSeatNum.setVisibility(View.GONE);
            viewHolder.mTvSpecialSeatNum.setVisibility(View.GONE);
        }

        if (ChatRoomManageHelper.isRoleOwner(info.getManageType())) {
            viewHolder.mIvRoleTag.setVisibility(View.VISIBLE);
            viewHolder.mIvRoleTag.setImageResource(R.drawable.chatroom_seat_role_host_ic);
        } else if (ChatRoomManageHelper.isRoomManager(info.getManageType())) {
            viewHolder.mIvRoleTag.setVisibility(View.VISIBLE);
            viewHolder.mIvRoleTag.setImageResource(R.drawable.chatroom_seat_role_manage_ic);
        } else {
            viewHolder.mIvRoleTag.setVisibility(View.GONE);
        }
        //财富等级icon
        ChatRoomHonorLevelConfigHelper.getInstance().setCommonLevelIconView(info.getCommonLevel(), viewHolder.mIvCommonLevelIcon);
        viewHolder.mIvHonorLevelIcon.setLevelConfig(info.getGloryKey(), false, View.GONE);
    }

    @Override
    public int getItemCountProxy() {
        return onlineInfoList != null ? onlineInfoList.size() : 0;
    }

    public void setOnApplyMicCallBack(OnForbidSpeakCallBack onApplyMicCallBack) {
        this.onApplyMicCallBack = onApplyMicCallBack;
    }

    static class OnlineViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        AvatarDraweeView imgAvatar;
        TextView tvNickName;
        ChatRoomUserSexAndAgeView imgSexType;
        Button btnForbid;
        Button btnLeafMic;
        Button btnLeaveBigScreen;
        TextView mTvSeatNum;
        TextView mTvSpecialSeatNum;
        ImageView mIvRoleTag;
        BaseDraweeView mIvCommonLevelIcon;
        ChatRoomHonorLevelIconImageView mIvHonorLevelIcon;

        OnForbidSpeakCallBack onApplyMicCallBack;
        Context context;
        String chatUuid;
        String manageType;
        private final int mChatRoomType;

        OnlineViewHolder(Context context, View view, OnForbidSpeakCallBack onApplyMicCallBack,
                         String chatUuid, String manageType, int chatRoomType) {
            super(view);
            this.context = context;
            this.chatUuid = chatUuid;
            imgAvatar = view.findViewById(R.id.imgAvatar);
            tvNickName = view.findViewById(R.id.tvNickName);
            imgSexType = view.findViewById(R.id.imgSexType);
            btnForbid = view.findViewById(R.id.btnForbid);
            btnLeafMic = view.findViewById(R.id.btnLeafMic);
            btnLeaveBigScreen = view.findViewById(R.id.btnLeaveBigScreen);
            mTvSeatNum = view.findViewById(R.id.tvSeatNum);
            mTvSpecialSeatNum = view.findViewById(R.id.tvSpecialSeatNum);
            mIvRoleTag = view.findViewById(R.id.ivRoleTag);
            mIvCommonLevelIcon = view.findViewById(R.id.ivCommonLevelIcon);
            mIvHonorLevelIcon = view.findViewById(R.id.ivHonorLevelIcon);

            tvNickName.setOnClickListener(this);
            imgAvatar.setOnClickListener(this);
            btnForbid.setOnClickListener(this);
            btnLeafMic.setOnClickListener(this);
            btnLeaveBigScreen.setOnClickListener(this);
            this.onApplyMicCallBack = onApplyMicCallBack;
            this.manageType = manageType;
            mChatRoomType = chatRoomType;
        }

        @Override
        public void onClick(View v) {
            final Object tag = itemView.getTag();
            if (tag instanceof ChatRoomOnlineInfo) {
                final ChatRoomOnlineInfo info = (ChatRoomOnlineInfo) tag;
                if (v.getId() == R.id.imgAvatar || v.getId() == R.id.tvNickName) {
                    if (TextUtils.equals(info.getAccountUuid(), AccountHelper.getAccountUuid())) {
                        //麦位管理列表
                        RoomUserDialogFragment roomUserDialogFragment = RoomUserDialogFragment.newInstance
                                (chatUuid, info.getAccountUuid(), manageType, ReportSource.VOICE);
                        roomUserDialogFragment.show(((FragmentActivity) context).getSupportFragmentManager(), RoomUserDialogFragment.TAG);
                    } else {
                        final SendGiftUserInfo userInfo = new SendGiftUserInfo(info.getAccountUuid(), info.getNickName(), info.getAvatar(), info.getSexType(), manageType);
                        if (context instanceof ChatRoomActivity) {
                            ((ChatRoomActivity) context).showGiftListDialogFragment(false, false, userInfo, ReportSource.NORMAL);
                        }
                        if (context instanceof ChatRoomCommonTempActivity) {
                            ((ChatRoomCommonTempActivity) context).showGiftListDialogFragment(false, false, userInfo, ReportSource.NORMAL, "", null);
                        }
                    }
                } else if (v.getId() == R.id.btnForbid) {
                    if (onApplyMicCallBack != null) {
                        onApplyMicCallBack.onForbid(!info.isForbidVoice(), info);
                    }
                } else if (v.getId() == R.id.btnLeafMic) {
                    if (onApplyMicCallBack != null) {
                        onApplyMicCallBack.onRejectAudioMeeting(info.getAccountUuid());
                    }
                } else if (v.getId() == R.id.btnLeaveBigScreen) {
                    if (onApplyMicCallBack != null) {
                        onApplyMicCallBack.onLeaveBigScreen();
                    }
                }
            }
        }
    }

    public interface OnForbidSpeakCallBack {

        void onForbid(boolean forbid, ChatRoomOnlineInfo info);

        void onRejectAudioMeeting(String accountUuid);

        /**
         * 大屏房-下大屏位
         */
        void onLeaveBigScreen();
    }
}
