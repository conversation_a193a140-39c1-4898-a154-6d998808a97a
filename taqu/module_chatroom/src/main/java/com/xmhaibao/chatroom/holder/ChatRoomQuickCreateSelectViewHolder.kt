package com.xmhaibao.chatroom.holder

import android.view.ViewGroup
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.bean.ChatRoomQuickCreateSelectBean
import com.xmhaibao.chatroom.databinding.ChatroomQuickCreateSelectItemBinding
import hb.xadapter.XBaseViewHolder

/**
 * 聊天室-快速建房选择房间类型弹窗列表ViewHolder
 *
 * <AUTHOR>
 * @date 2023/4/6
 */
class ChatRoomQuickCreateSelectViewHolder(parent: ViewGroup) :
    XBaseViewHolder<ChatRoomQuickCreateSelectBean.SelectItemBean>(parent, R.layout.chatroom_quick_create_select_item) {

    private val mBinding = ChatroomQuickCreateSelectItemBinding.bind(itemView)

    override fun onBindView(item: ChatRoomQuickCreateSelectBean.SelectItemBean?) {
        itemView.tag = item
        item?.apply {
            mBinding.ivIcon.setImageFromUrl(icon)
            mBinding.tvTitle.text = title
            mBinding.tvDesc.text = describe
        }
    }
}