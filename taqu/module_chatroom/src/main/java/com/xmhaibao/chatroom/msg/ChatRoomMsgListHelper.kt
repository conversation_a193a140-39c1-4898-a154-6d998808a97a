package com.xmhaibao.chatroom.msg

import android.text.TextUtils
import androidx.annotation.MainThread
import cn.taqu.lib.base.live.model.event.EventMsgBase
import com.xmhaibao.chatroom.adapter.ChatRoomMsgListAdapter
import com.xmhaibao.chatroom.api.bean.ChatRoomAppConfigBean
import com.xmhaibao.chatroom.model.event.EventMsgIntoRoom
import com.xmhaibao.chatroom.model.event.EventMsgRemindFollow
import com.xmhaibao.gift.model.event.EventMsgGift
import hb.utils.Loger
import hb.utils.StringUtils
import kotlinx.coroutines.Runnable

/**
 * 聊天室消息列表Helper
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
class ChatRoomMsgListHelper : BaseMsgListHelper() {

    /**
     * 进房消息时间
     */
    private var intoRoomTime: Long = 0

    /**
     * 延迟滚动到底部runnable
     */
    private var delayScrollToBottomRunnable: Runnable? = null

    /**
     * 未读消息更新Callback
     */
    var onUnReadTipsViewCallback: (() -> Unit)? = null

    init {
        val time = StringUtils.stringToLong(ChatRoomAppConfigBean.getAppConfig().getChatroomMsgListRefreshTime())
        if (time > 0) {
            diffRefreshDuration = time
        }
    }

    /**
     * 设置Adapter数据
     */
    fun setAdapterData(adapter: ChatRoomMsgListAdapter?) {
        adapter?.setData(list)
    }

    /**
     * 插入消息
     */
    @MainThread
    fun addMsg(event: EventMsgBase?) {
        if (event == null) {
            return
        }
        if (isGiftComboMsg(event)) {
            // 同个礼物连击，直接更新最后一条消息
            return
        }
        if (checkMsgValid(event)) {
            insertItemAndDelay(event)
        }
    }

    /**
     * 最小化回来恢复数据展示
     */
    fun reloadOldData(oldList: MutableList<EventMsgBase>) {
        Loger.i(TAG, "reload old data list, size: ${oldList.size}")
        list.clear()
        list.addAll(oldList)
        notifyDataSetChanged()
        if (delayScrollToBottomRunnable == null) {
            delayScrollToBottomRunnable = Runnable {
                scrollToBottom()
            }
        }
        recyclerView?.postDelayed(delayScrollToBottomRunnable, 400)
    }

    /**
     * 设置礼物连击消息折叠
     */
    private fun isGiftComboMsg(event: EventMsgBase): Boolean {
        if (event is EventMsgGift) {
            var isUpdateFromWaitList = false
            val lastEvent = if (waitList.isNotEmpty()) {
                isUpdateFromWaitList = true
                waitList.lastOrNull()
            } else {
                list.lastOrNull()
            }
            if (lastEvent is EventMsgGift
                && TextUtils.equals(lastEvent.giftId, event.giftId)
                && TextUtils.equals(lastEvent.sendAccountUuid, event.sendAccountUuid)
                && TextUtils.equals(lastEvent.accountUuid, event.accountUuid)
            ) {
                // 同个礼物，直接更新
                var count = lastEvent.msgFoldUpCount
                val giftNum = StringUtils.stringToInt(event.giftNum)
                val lastEventGiftNum = StringUtils.stringToInt(lastEvent.giftNum)
                if (count == 0) {
                    count = lastEventGiftNum + giftNum
                    Loger.i(
                        TAG,
                        "update same gift msg first, oriCount: ${lastEventGiftNum}, newCount: $count, fromWait: $isUpdateFromWaitList"
                    )
                } else {
                    Loger.i(
                        TAG,
                        "update same gift msg, oriCount: ${count}, newCount: ${(count + giftNum)}, fromWait: $isUpdateFromWaitList"
                    )
                    count += giftNum
                }
                lastEvent.discountRatio = event.discountRatio
                lastEvent.msgFoldUpCount = count
                if (!isUpdateFromWaitList) {
                    // 不是缓冲消息列表，刷新view
                    notifyItemChange(list.size - 1)
                }
                return true
            }
        }
        return false
    }

    /**
     * 检查消息有效性，有效则添加
     */
    private fun checkMsgValid(event: EventMsgBase): Boolean {
        if (event is EventMsgIntoRoom) {
            // 进房提醒
            val intoRoomLastPosition = list.lastIndex
            // 最后一个Item是否是进房提醒
            if (list.isNotEmpty() && intoRoomLastPosition >= 0 && list[intoRoomLastPosition] is EventMsgIntoRoom) {
                val currentTime = System.currentTimeMillis()
                if (intoRoomTime + 100 > currentTime) {
                    // 每秒最多变换10个昵称
                    Loger.i(TAG, "update into room msg limit")
                    return false
                }
                if (intoRoomTime + 300 >= currentTime) {
                    // <=300ms,覆盖在前一条进房提醒
                    list[intoRoomLastPosition] = event
                    checkIsComputingLayout {
                        notifyItemChange(intoRoomLastPosition)
                        intoRoomTime = currentTime
                        Loger.i(TAG, "update into room msg, notify change")
                    }
                    return false
                } else {
                    // > 300, 另起一行进房提醒
                    return true
                }
            }
        }
        return true
    }

    /**
     * 更新关注房主消息展示
     * @param isFollow 是否关注
     */
    public fun updateFollowMsgInfo(isFollow: Boolean) {
        run loop@ {
            list.forEachIndexed { index, eventMsgBase ->
                if (eventMsgBase is EventMsgRemindFollow) {
                    Loger.i(TAG, "update follow msg info, isFollow: $isFollow")
                    checkIsComputingLayout {
                        eventMsgBase.isFollow = isFollow
                        notifyItemChange(index)
                    }
                    return@loop
                }
            }
        }
    }

    override fun onDataChangeNotInBottomCallback() {
        super.onDataChangeNotInBottomCallback()
        onUnReadTipsViewCallback?.invoke()
    }

    override fun destroy() {
        super.destroy()
        onUnReadTipsViewCallback = null
        recyclerView?.removeCallbacks(delayScrollToBottomRunnable)
        delayScrollToBottomRunnable = null
    }

}