package com.xmhaibao.chatroom.roomplay.wedding.viewmodel

import android.app.Application
import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import cn.taqu.lib.base.utils.SexTypeUtils
import com.xmhaibao.chatroom.bean.ChatRoomInviteFamilyInfo
import com.xmhaibao.chatroom.bean.ChatRoomInviteFriendsInfo
import com.xmhaibao.chatroom.bean.ChatRoomInviteListBean
import com.xmhaibao.chatroom.constants.ChatRoomConstants
import com.xmhaibao.chatroom.repository.ChatRoomIMRepository
import com.xmhaibao.chatroom.roomplay.wedding.bean.ChatRoomWeddingInviteFinishBean
import com.xmhaibao.chatroom.roomplay.wedding.constants.ChatRoomWeddingInviteType
import com.xmhaibao.chatroom.roomplay.wedding.fragment.ChatRoomWeddingInviteFragment
import com.xmhaibao.chatroom.roomplay.wedding.repository.ChatRoomWeddingRepository
import com.xmhaibao.message.api.constants.ImTypeConstants
import com.xmhaibao.message.api.repository.MessagePinsRepository
import hb.common.data.AccountHelper
import hb.common.xstatic.HttpParams
import hb.common.xstatic.viewmodel.BaseViewModel
import hb.message.HBMessageHelper
import hb.utils.CollectionUtils
import hb.utils.ToastUtils
import hb.xrequest.await
import hb.xrequest.launchHttp
import hb.xstatic.list4page.IListHttpDataSource
import hb.xstatic.list4page.XListHttpDataSource
import hb.xstatic.list4page.XListResultDispose
import hb.xstatic.mvp.interf.IListBean
import hb.xstatic.tools.http.BaseHttpParams

/**
 * 聊天室婚礼房邀请viewModel
 *
 * <AUTHOR>
 * @date 2022-06-16
 */
class ChatRoomWeddingInviteViewModel(application: Application) : BaseViewModel(application) {
    val selectList = mutableListOf<ChatRoomInviteListBean>()
    val inviteUserListLiveData = MutableLiveData(selectList)
    val inviteSuccessLiveData = MutableLiveData<List<ChatRoomInviteListBean>>()
    val inviteFamilyLiveData = MutableLiveData<Boolean>()
    var inviteType: @ChatRoomWeddingInviteType Int = ChatRoomWeddingInviteType.PARTNER
    var filterUuidList: List<String?>? = null
    var maxSelectNum = ChatRoomConstants.CHAT_ROOM_WEDDING_DEFAULT_MAX_SELECT_NUM
    private var mFilterEmptyCount = 0
    private val mRepo by lazy { ChatRoomWeddingRepository() }
    private val mImRepo by lazy { ChatRoomIMRepository() }

    /**
     * 判断是否选中
     */
    fun checkSelect(isSelect: Boolean): Boolean {
        if (!isSelect && inviteUserListLiveData.value?.size ?:0 >= maxSelectNum) {
            ToastUtils.show("最多只能邀请${maxSelectNum}个小伙伴~")
            return false
        }
        return true
    }

    fun handleSelect(item: ChatRoomInviteListBean) {
        item.isSelected = !item.isSelected
        if (item.isSelected) {
            selectList.add(item)
        } else {
            selectList.remove(item)
        }
        inviteUserListLiveData.value = selectList
    }

    /**
     * 请求发出邀请
     */
    fun requestInvite(weddingUuid: String?, type: @ChatRoomWeddingInviteType Int) {
        viewModelScope.launchHttp({
            val uuids = selectList.toSet().joinToString(",") { it.uuid }
            showLoadingBar()
            val data = mRepo.sendWeddingGuestInvite(
                uuids, weddingUuid, type.toString()
            ).await<ChatRoomWeddingInviteFinishBean>()
            hideLoadingBar()
            ToastUtils.show("邀请已发送")
            data.msg?.forEach {
                it?.apply {
                    HBMessageHelper.getChatHelper().addMessageContentAndConversation(this)
                }
            }
            inviteSuccessLiveData.value = selectList
        }, {
            hideLoadingBar()
            false
        })
    }

    /**
     * 判断是否有家族
     */
    fun requestHasFamily() {
        viewModelScope.launchHttp({
            showLoadingBar()
            val data = MessagePinsRepository.checkUserHasFamily().await<Boolean>()
            inviteFamilyLiveData.value = data
            hideLoadingBar()
        }, {
            inviteFamilyLiveData.value = false
            hideLoadingBar()
            false
        })
    }

    /**
     * 根据类型获取dataSource
     */
    fun getListHttpDataSourceByType(type: Int): IListHttpDataSource<*> {
        return when (type) {
            ChatRoomWeddingInviteFragment.TYPE_LOVE -> {
                getFriendLoveListHttpDataSource()
            }
            ChatRoomWeddingInviteFragment.TYPE_LATELY -> {
                getLatelyListHttpDataSource()
            }
            ChatRoomWeddingInviteFragment.TYPE_FRIEND -> {
                getFriendsListHttpDataSource()
            }
            ChatRoomWeddingInviteFragment.TYPE_FAMILY -> {
                getFamilyListHttpDataSource()
            }
            ChatRoomWeddingInviteFragment.TYPE_FANS -> {
                getFansListHttpDataSource()
            }
            else -> getFriendLoveListHttpDataSource()
        }
    }

    /**
     * 密友DataSource
     */
    private fun getFriendLoveListHttpDataSource(): IListHttpDataSource<ChatRoomInviteFamilyInfo?> {
        val dataSource: XListHttpDataSource<ChatRoomInviteFamilyInfo?> =
            object : XListHttpDataSource<ChatRoomInviteFamilyInfo?>() {}
        dataSource.setHttpParams(mImRepo.getChatRoomTodaySweet(AccountHelper.getUserTicketId()).httpParams)
        dataSource.setResultDispose(InviteResultDispose())
        return dataSource
    }

    /**
     * 最近DataSource
     */
    private fun getLatelyListHttpDataSource(): IListHttpDataSource<ChatRoomInviteFriendsInfo?> {
        val dataSource: XListHttpDataSource<ChatRoomInviteFriendsInfo?> =
            object : XListHttpDataSource<ChatRoomInviteFriendsInfo?>() {}
        dataSource.setHttpParams(mImRepo.getFriendsLatelyList("").httpParams)
        dataSource.setHttpParamsInterceptor { httpParams: BaseHttpParams ->
            httpParams.put("uuids", getLocalLatelyFriends())
            false
        }
        dataSource.setResultDispose(InviteResultDispose())
        return dataSource
    }

    /**
     * 获取本地最近会话好友uuid
     */
    private fun getLocalLatelyFriends(): String {
        val hbMessageConversationHelper =
            HBMessageHelper.getConversationHelper(ImTypeConstants.IM_MESSAGE_TYPE_PRIVATE_MSG)
        val list = hbMessageConversationHelper?.unmodifiableListConversationsList
        if (CollectionUtils.isEmpty(list)) {
            return ""
        }
        val builder = StringBuilder()
        list?.size?.coerceAtMost(100) ?.apply {
            for (i in 0 until this) {
                val messageConversation = list[i] ?: continue
                if (ImTypeConstants.IM_MESSAGE_TYPE_PRIVATE_MSG == messageConversation.msgType) {
                    //过滤自己
                    if (TextUtils.equals(messageConversation.targetId, AccountHelper.getAccountUuid())) {
                        continue
                    }
                    if (builder.isNotEmpty()) {
                        builder.append(",")
                    }
                    builder.append(messageConversation.targetId)
                }
            }
        }
        return builder.toString()
    }

    /**
     * 好友DataSource
     */
    private fun getFriendsListHttpDataSource(): IListHttpDataSource<ChatRoomInviteFriendsInfo?> {
        val dataSource: XListHttpDataSource<ChatRoomInviteFriendsInfo?> =
            object : XListHttpDataSource<ChatRoomInviteFriendsInfo?>() {}
        dataSource.setHttpParams(
            HttpParams.newBuilder()[ChatRoomIMRepository.GET_ALL_FRIEND_LIST]
                .params("ticket_id", AccountHelper.getUserTicketId())
                .build()
        )
        dataSource.setResultDispose(InviteResultDispose())
        return dataSource
    }

    /**
     * 家族DataSource
     */
    private fun getFamilyListHttpDataSource(): IListHttpDataSource<ChatRoomInviteFamilyInfo?> {
        val dataSource: XListHttpDataSource<ChatRoomInviteFamilyInfo?> =
            object : XListHttpDataSource<ChatRoomInviteFamilyInfo?>() {}
        dataSource.setHttpParams(mImRepo.getInviteFamilyMemberInfo().httpParams)
        dataSource.setResultDispose(InviteResultDispose())
        return dataSource
    }

    /**
     * 粉丝DataSource
     */
    private fun getFansListHttpDataSource(): IListHttpDataSource<List<ChatRoomInviteFriendsInfo.ListBean?>> {
        val dataSource: XListHttpDataSource<List<ChatRoomInviteFriendsInfo.ListBean?>> =
            object : XListHttpDataSource<List<ChatRoomInviteFriendsInfo.ListBean?>>() {}
        dataSource.setHttpParams(mImRepo.getMyFansList().httpParams)
        dataSource.setResultDispose(InviteResultDispose())
        return dataSource
    }

    /**
     * 结果处理
     */
    inner class InviteResultDispose<T> : XListResultDispose<T>() {
        override fun onHandleExtraInThread(isCache: Boolean, result: T) {
            super.onHandleExtraInThread(isCache, result)
            val iterator = when (result) {
                is MutableList<*> -> {
                    result.iterator()
                }
                is IListBean<*> -> {
                    result.list?.iterator()
                }
                else -> null
            } ?: return
            while (iterator.hasNext()) {
                val item = iterator.next() as? ChatRoomInviteListBean ?: continue
                // 过滤已有用户数据
                // 邀请伴郎/伴娘过滤性别
                if (filterUuidList?.contains(item.uuid) == true
                    || (inviteType == ChatRoomWeddingInviteType.GROOMSMAN && !SexTypeUtils.isSexTypeBoy(item.sexType))
                    || (inviteType == ChatRoomWeddingInviteType.BRIDESMAID && !SexTypeUtils.isSexTypeGirl(item.sexType))) {
                    iterator.remove()
                }
            }
        }

        override fun onHanldeSuccess(isCache: Boolean, result: T, vararg objs: Any?) {
            // 如果数据都过滤掉了，但原始数据有可以加载更多，触发加载更多
            val isResultEmpty = when (result) {
                is MutableList<*> -> {
                    result.isNullOrEmpty()
                }
                is IListBean<*> -> {
                    result.list.isNullOrEmpty()
                }
                else -> {
                    false
                }
            }
            if (isResultEmpty && mDataSource.isLoadMoreble) {
                mFilterEmptyCount++
                if (mFilterEmptyCount >= 10) {
                    // 过滤数据超过次数，不再触发加载更多
                    super.onHanldeSuccess(isCache, result, *objs)
                } else {
                    mFilterEmptyCount = 0
                    mDataSource.nextPage()
                    mDataSource.loadMore()
                }
            } else {
                super.onHanldeSuccess(isCache, result, *objs)
            }
        }
    }

}