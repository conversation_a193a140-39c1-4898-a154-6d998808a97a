package com.xmhaibao.chatroom.router.conver

import android.content.Context
import android.os.Bundle
import cn.taqu.lib.base.router.JustRouterConvert
import com.alibaba.android.arouter.facade.annotation.Route
import com.xmhaibao.chatroom.api.constants.ChatRoomPushConstants
import com.xmhaibao.chatroom.api.router.ChatRoomPinsRouter
import com.xmhaibao.chatroom.constants.ChatRoomRelationConstants

/**
 * 聊天室房活动入口弹窗
 *
 * <AUTHOR>
 * @date 2024/12/11
 */
@Suppress("unused")
@Route(
    path = ChatRoomPushConstants.CHATROOM_MODULE.plus(ChatRoomRelationConstants.PUSH_TYPE_ACTION_TRAVEL_ACTIVE),
    name = "聊天室房活动弹窗"
)
class ChatRoomActiveDialogConvert : JustRouterConvert() {

    override fun navigation(args: Bundle?, context: Context?) {
        //聊天室活动入口
        ChatRoomPinsRouter.chatRoomService().showTravelActiveEntrance()
    }
}