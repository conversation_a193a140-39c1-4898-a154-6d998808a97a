package com.xmhaibao.chatroom.ktv.helper

import android.content.Context
import com.xmhaibao.chatroom.api.router.ChatRoomPinsRouter
import com.xmhaibao.chatroom.event.EventChatRoomQuickCreateRoom
import com.xmhaibao.chatroom.helper.ChatRoomCreateCheckHelper
import com.xmhaibao.chatroom.ktv.bean.*
import com.xmhaibao.chatroom.ktv.constants.ChatRoomKtvQuickChooseMode
import com.xmhaibao.chatroom.ktv.constants.ChatRoomMusicSource
import com.xmhaibao.chatroom.model.ChatRoomModel
import com.xmhaibao.chatroom.repository.ChatRoomKTVRepository
import hb.common.data.AccountHelper
import hb.utils.ActivityUtils
import hb.xrequest.RequestCallback
import hb.xstyle.xdialog.XDialogUtils
import hb.xtoast.XToastUtils

/**
 * 聊天室-ktv房歌曲操作帮助类
 *
 * <AUTHOR>
 * @date 2022/7/28
 */
object ChatRoomKtvOperationHelper {

    @JvmStatic
    fun postCollectSong(
        context: Context, songId: String, source: Int, isCollect: Boolean, chatUuid: String?,
        callback: ((Boolean) -> Unit)?
    ) {
        if (isCollect) {
            requestCollectSong(songId, source, isCollect, chatUuid,callback,)
        } else {
            XDialogUtils.show(context, "是否取消“喜欢”这首歌曲？", "取消喜欢", { _, _ ->
                requestCollectSong(songId, source, isCollect,chatUuid, callback)
            }, "手滑了", null)
        }
    }

    /**
     * 收藏/取消收藏歌曲
     */
    @JvmStatic
    fun requestCollectSong(songId: String, source: Int, isCollect: Boolean, chatUuid: String?, callback: ((Boolean) -> Unit)?) {
        val request = if (isCollect) {
            ChatRoomKTVRepository.postKTVCollectSong(songId, source, chatUuid)
        } else {
            ChatRoomKTVRepository.postKTVCollectCancelSong(songId, source)
        }
        request.execute(object : RequestCallback<Any>() {
            override fun onSuccess(isCache: Boolean, obj: Any?) {
                if (isCollect) {
                    XToastUtils.show("已添加至\"我的喜欢\"")
                }
                callback?.invoke(isCollect)
            }
        })
    }

    /**
     * 是否收藏歌曲
     */
    @JvmStatic
    fun requestIsCollectSong(songId: String, source: Int, callback: ((Boolean) -> Unit)?) {
        ChatRoomKTVRepository.getKTVIsCollectSong(songId, source)
            .execute(object : RequestCallback<ChatRoomKtvIsCollectSongBean>() {
                override fun onSuccess(isCache: Boolean, obj: ChatRoomKtvIsCollectSongBean?) {
                    callback?.invoke(obj?.isCollect == "1")
                }

                override fun onFailure(code: Int, responseStatus: String?, responseMsg: String?) {
                    super.onFailure(code, responseStatus, responseMsg)
                    callback?.invoke(false)
                }
            })
    }

    /**
     * 暂停歌曲
     */
    @JvmStatic
    fun requestPauseSong(chatUuid: String?, listId: String?, isPause: Boolean, callback: ((Boolean) -> Unit)?) {
        ChatRoomKTVRepository.postKTVPauseSong(chatUuid, listId, if (isPause) "1" else "2")
            .execute(object : RequestCallback<Any>() {
                override fun onSuccess(isCache: Boolean, obj: Any?) {
                    callback?.invoke(isPause)
                }

                override fun onFailure(code: Int, responseStatus: String?, responseMsg: String?) {
                    super.onFailure(code, responseStatus, responseMsg)
                }
            })
    }

    @JvmStatic
    fun quickCreateKtvRoom(quickId: String?, quickMode: String?, source: String?) {
        if (!ChatRoomKtvQuickChooseMode.isSupperMode(quickMode)) {
            XToastUtils.show("当前版本不支持")
            return
        }
        ChatRoomKTVRepository.checkKtvQuickCreateRoom(quickId).execute(object : RequestCallback<ChatRoomQuickCreateRoomBean?>() {
            override fun onSuccess(isCache: Boolean, obj: ChatRoomQuickCreateRoomBean?) {
                if (obj == null) {
                    return
                }
                ActivityUtils.getTopActivity()?.let { activity ->
                    val helper = ChatRoomCreateCheckHelper(source)
                    if (obj.isPermission()) {
                        if (ChatRoomModel.getInstance().zoomChatRoomUuid == AccountHelper.getAccountUuid()) {
                            ChatRoomModel.getInstance().chatRoomHelper?.startRestoreChatRoom()
                            return
                        }
                        val event = EventChatRoomQuickCreateRoom()
                        event.songListUuid = quickId
                        helper.setQuickCreateData(event)
                        helper.onStartCheck(ActivityUtils.getTopActivity(), obj.roomType, obj.subType)
                    } else {
                        XDialogUtils.show(
                            activity, "抱歉，您没有建房权限", "您可以选择“直接匹配”听歌或者申请建房听歌", "申请建房",
                            { p0, p1 ->
                                helper.onStartCheck(ActivityUtils.getTopActivity(), obj.roomType, obj.subType)
                            }, "直接匹配"
                        ) { p0, p1 ->
                            requestPlayListMatchRoom(quickId, source)
                        }
                    }
                }
            }
        })
    }

    /**
     * 歌单快速匹配房间
     */
    fun requestPlayListMatchRoom(playListUuid: String?, source: String?) {
        ChatRoomKTVRepository.getPlayListMatchRoom(playListUuid)
            .execute(object : RequestCallback<ChatRoomKtvPlayListMatchBean>() {
                override fun onSuccess(isCache: Boolean, obj: ChatRoomKtvPlayListMatchBean?) {
                    obj?.chatUuid.takeUnless { it.isNullOrEmpty() }?.let {
                        ChatRoomPinsRouter.launchChatRoomActivity(it, false, false, source)
                    }
                }
            })
    }

    @JvmStatic
    fun checkBeforeChooseSong(
        chatUuid: String?,
        songBean: ChatRoomKtvSongDetailBean,
        isChooseBgm: Boolean,
        callback: ((ChatRoomKtvSongDetailBean) -> Unit)?
    ) {
        if (isChooseBgm) {
            getBGMDetailInfo(songBean, isChooseBgm, callback)
            return
        }
        ChatRoomKTVRepository.checkBeforeChooseSong(chatUuid, songBean.songId, songBean.source)
            .execute(object : RequestCallback<ChatRoomChooseSongBeforeBean>() {
                override fun onSuccess(isCache: Boolean, obj: ChatRoomChooseSongBeforeBean?) {
                    obj?.apply {
                        handlerChooseSong(songBean, this, isChooseBgm, callback)
                    }
                }
            })
    }

    /**
     * 获取bgm点歌 前校验是否可以点歌
     * @param songBean 歌曲信息
     * @param isChooseBgm 是否是bgm
     * @param callback 回调
     */

    private fun getBGMDetailInfo(songBean: ChatRoomKtvSongDetailBean, isChooseBgm: Boolean, callback: ((ChatRoomKtvSongDetailBean) -> Unit)?) {
        ChatRoomKTVRepository.getAMEMusicPlayToken(songBean.songId, songBean.source)
            .execute(object : RequestCallback<ChatRoomChooseSongBeforeBean?>() {
                override fun onSuccess(isCache: Boolean, obj: ChatRoomChooseSongBeforeBean?) {
                    obj?.apply {
                        handlerChooseSong(songBean, this, isChooseBgm, callback)
                    }
                }
            })
    }

    private fun handlerChooseSong(
        songBean: ChatRoomKtvSongDetailBean,
        beforeBean: ChatRoomChooseSongBeforeBean,
        isChooseBgm: Boolean,
        callback: ((ChatRoomKtvSongDetailBean) -> Unit)?
    ) {
        if (!isChooseBgm) {
            ChatRoomKtvSongDownloadHelper.getInstance().addAlreadySongNumber()
        }
        if (beforeBean.source == ChatRoomMusicSource.SOURCE_AME
            || beforeBean.source == ChatRoomMusicSource.SOURCE_YSD
        ) {
            songBean.playToken = beforeBean.playToken
        }
        songBean.isChooseBgm = isChooseBgm
        callback?.invoke(songBean)
    }
}