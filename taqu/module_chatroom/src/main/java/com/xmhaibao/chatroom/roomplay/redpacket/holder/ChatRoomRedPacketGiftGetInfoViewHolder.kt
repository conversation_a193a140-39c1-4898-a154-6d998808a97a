package com.xmhaibao.chatroom.roomplay.redpacket.holder

import android.view.ViewGroup
import cn.taqu.lib.base.utils.AppQuCoinHelper
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.databinding.ChatroomRedPacketGiftGetInfoItemBinding
import com.xmhaibao.chatroom.roomplay.redpacket.bean.ChatRoomRedPacketFunGiftInfoBean
import hb.xadapter.XBaseViewHolder

/**
 * 聊天室礼物红包-领取弹窗holder
 *
 * <AUTHOR>
 * @date 2024-02-02
 */
class ChatRoomRedPacketGiftGetInfoViewHolder(parent: ViewGroup) :
    XBaseViewHolder<ChatRoomRedPacketFunGiftInfoBean>(parent,
        R.layout.chatroom_red_packet_gift_get_info_item) {
    private val binding = ChatroomRedPacketGiftGetInfoItemBinding.bind(itemView)

    override fun onBindView(bean: ChatRoomRedPacketFunGiftInfoBean?) {
        bean?.apply {
            binding.apply {
                imgBg.setImageFromResource(R.drawable.chatroom_red_packet_gift_get_item_bg)
                imgGift.setImageFromUrl(icon)
                tvNum.text = "X$num"
                tvPrice.text = "$price${AppQuCoinHelper.ratioUnit}"
            }
        }
    }

}