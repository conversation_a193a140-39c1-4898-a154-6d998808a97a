package com.xmhaibao.chatroom.model.event;


import android.text.TextUtils;

import java.util.Arrays;

import cn.taqu.lib.base.im.LiveGiftSpecifierType;
import cn.taqu.lib.base.im.LiveIMLogHelper;
import cn.taqu.lib.base.live.helper.LiveMsgViewType;
import cn.taqu.lib.base.live.model.event.EventMsgBase;
import hb.qim.base.core.IResponseParse;
import hb.utils.EventBusUtils;
import hb.utils.StringUtils;

/**
 * <AUTHOR>
 * @date 2019.10.10
 * @desc 聊天室-顶部-全房间广播
 */

public class EventAllChatRoomWorldBroad extends EventMsgBase implements IResponseParse<EventAllChatRoomWorldBroad> {

    private String content;
    private String firstName;
    private String secondName;
    private String icon;
    private String activityType;
    private String iconName;
    private String isShowInMsgList;
    private String rel;
    /**
     * 是否是抽奖消息
     */
    private boolean isLotteryMsg;

    public boolean isLotteryMsg() {
        return isLotteryMsg;
    }

    public void setLotteryMsg(boolean lotteryMsg) {
        isLotteryMsg = lotteryMsg;
    }

    public String getRel() {
        return rel;
    }

    public void setRel(String rel) {
        this.rel = rel;
    }

    protected boolean isTopWorldBroadcast = true;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFirstName() {
        return firstName != null ? firstName : "";
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getSecondName() {
        return secondName != null ? secondName : "";
    }

    public void setSecondName(String secondName) {
        this.secondName = secondName;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public String getIconName() {
        return iconName != null ? iconName : "";
    }

    public void setIconName(String iconName) {
        this.iconName = iconName;
    }

    public String getIsShowInMsgList() {
        return isShowInMsgList;
    }

    public boolean isShowInMsgList() {
        return "1".equals(isShowInMsgList);
    }

    public void setIsShowInMsgList(String isShowInMsgList) {
        this.isShowInMsgList = isShowInMsgList;
    }

    public boolean isTopWorldBroadcast() {
        return isTopWorldBroadcast;
    }

    public void setTopWorldBroadcast(boolean topWorldBroadcast) {
        isTopWorldBroadcast = topWorldBroadcast;
    }

    @Override
    public String toString() {
        return "EventWorldBroadCast{" + "content='" + content + '\'' + ", firstName='" + firstName + '\''
                + ", secondName='" + secondName + '\'' + ", icon='" + icon + '\'' + ", activityType='" + activityType
                + '\'' + ", isTopWorldBroadcast=" + isTopWorldBroadcast + '}';
    }

    /**
     * 获取拼接后的文案
     */
    public static String getTextAfterSplicing(EventAllChatRoomWorldBroad event, boolean inDanmuView) {
        String content = event.getContent();
        // 先替换图片icon
        if (!StringUtils.isEmpty(content)) {
            if (content.contains(LiveGiftSpecifierType.SPECIFIER_IMAGE)) {
                content = content.replace(LiveGiftSpecifierType.SPECIFIER_IMAGE, inDanmuView ? LiveGiftSpecifierType.PLACE : event.getIconName());
            }
            // 昵称
            if (!StringUtils.isEmpty(event.getFirstName())) {
                content = content.replaceFirst(LiveGiftSpecifierType.SPECIFIER_NAME_FIRST, event.getFirstName());
            }
            if (!StringUtils.isEmpty(event.getSecondName())) {
                content = content.replaceFirst(LiveGiftSpecifierType.SPECIFIER_NAME_SECOND, event.getSecondName());
            }
        }
        return content;
    }

    public static String getTextAfterSplicing(EventAllChatRoomWorldBroad event) {
        return getTextAfterSplicing(event, false);
    }


    @Override
    public int getItemViewType() {
        return LiveMsgViewType.TYPE_ALL_ROOM_ACTIVITY_MSG;
    }

    @Override
    public EventAllChatRoomWorldBroad parseResponse(String event, Object... args) {
        LiveIMLogHelper.getInstance().log(event, args);
        if (args.length >= 9) {
            setContent(String.valueOf(args[0]));
            setFirstName(String.valueOf(args[1]));
            setSecondName(String.valueOf(args[2]));
            setIcon(String.valueOf(args[3]));
            setActivityType(String.valueOf(args[4]));
            setIsShowInMsgList(String.valueOf(args[5]));
            setIconName(String.valueOf(args[6]));
            setRel(String.valueOf(args[7]));
            setLotteryMsg(TextUtils.equals(String.valueOf(args[8]), "1"));
            EventBusUtils.post(this);
        }
        return this;
    }
}
