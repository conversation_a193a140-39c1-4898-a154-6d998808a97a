package com.xmhaibao.chatroom.active.widget.viewholder

import android.view.ViewGroup
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.active.widget.bean.style.ChatRoomWidgetStyleOneModuleBean
import com.xmhaibao.chatroom.databinding.ChatroomCommonWidgetStyleOneImgItemBinding
import com.xmhaibao.chatroom.databinding.ChatroomCommonWidgetStyleOneStatusItemBinding
import hb.kotlin_extension.noneSyncLazy
import hb.utils.ColorUtils
import hb.xadapter.XBaseViewHolder

/**
 *  活动通用 样式1状态控件
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
class ChatRoomCommonWidgetStyleOneStatusHolder(parent: ViewGroup) :
    XBaseViewHolder<ChatRoomWidgetStyleOneModuleBean>(
        parent,
        R.layout.chatroom_common_widget_style_one_status_item
    ) {


    private val mViewBinding by noneSyncLazy {
        ChatroomCommonWidgetStyleOneStatusItemBinding.bind(itemView)
    }
    override fun onBindView(item: ChatRoomWidgetStyleOneModuleBean?) {
        item?.apply {
            var textColor = ColorUtils.getColorByRGBA(item.normalColor)
            if (textColor == -1) {
                textColor = ColorUtils.getColor(hb.xstyle.R.color.white)
            }
            mViewBinding.tvStateText.setTextColor(textColor)
            mViewBinding.tvStateText.text = content
            mViewBinding.bdvStatusBg.setImageFromUrl(bgUrl)

        }


    }
}
