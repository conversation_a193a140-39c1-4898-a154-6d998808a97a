package com.xmhaibao.chatroom.model.event;

import com.google.gson.annotations.SerializedName;
import com.xmhaibao.chatroom.bean.ChatRoomMsgAuctionInfoBean;
import com.xmhaibao.chatroom.bean.ChatRoomMsgTagBean;
import com.xmhaibao.common_level.helper.CommonLevelHelper;
import com.xmhaibao.imganim.bean.XImageAnimAvatarDressInfoBean;
import com.xmhaibao.live.api.bean.LiveTitleInfo;
import com.xmhaibao.live.api.helper.LiveTitleHelper;
import com.xmhaibao.xjson.XJson;

import java.util.List;
import cn.taqu.lib.base.live.model.event.EventMsgBase;
import hb.utils.ObjectUtils;
import hb.utils.StringUtils;

/**
 * 聊天室消息Base
 *
 * <AUTHOR>
 * @date 2022-03-24
 */
public class EventChatRoomMsgBase extends EventMsgBase {
    /**
     * 麦位位置
     */
    private int seatNum;

    /**
     * 财富等级
     */
    private String commonLevel = "0";

    /**
     * 财富等级小图标url
     */
    private String commonLevelIcon;

    /**
     * 气泡背景
     */
    private String bubbleUrl;

    /**
     * 称号
     */
    private String title;

    /**
     * 称号
     */
    private LiveTitleInfo.LiveTitleItemBean mTitleItemBean;

    /**
     * 活动称号
     */
    private String activityTitle;

    /**
     * 活动称号资源数据
     */
    private LiveTitleInfo.LiveActivityTitleBean mActivityTitleBean;

    /**
     * 用户身份 1=萌新，2=房主，3=房管，4=红娘/月老
     */
    private String userType;

    /**
     * 靓号id
     */
    private String accountCardId;

    /**
     * 靓号等级
     */
    private String accountCardLevel;

    /**
     * 拍拍关系-对方昵称
     */
    private String paipaiOtherNickName;

    /**
     * 拍拍关系-关系名称
     */
    private String paipaiRelationName;

    /**
     * 拍拍关系-关系id
     */
    private String paipaiRelationId;

    /**
     * 五子棋段位
     */
    private int bigGrade;
    /**
     * 五子棋段位等级
     */
    private int smallGrade;

    /**
     * 拍拍关系bean
     */
    private ChatRoomMsgAuctionInfoBean auctionInfoBean;

    /**
     * 接入头像装扮中台后的装扮数据
     */
    @SerializedName("avatar_frame_info")
    private XImageAnimAvatarDressInfoBean imgAvatarDressInfo;

    /**
     * 身份标签url
     */
    private String identityTagUrl;

    /**
     * 消息标签
     */
    private List<ChatRoomMsgTagBean> tagListJson;

    public List<ChatRoomMsgTagBean> getTagListJson() {
        return tagListJson;
    }

    public void setTagListJson(List<ChatRoomMsgTagBean> tagListJson) {
        this.tagListJson = tagListJson;
    }

    public String getIdentityTagUrl() {
        return identityTagUrl;
    }

    public void setIdentityTagUrl(String identityTagUrl) {
        this.identityTagUrl = identityTagUrl;
    }

    public XImageAnimAvatarDressInfoBean getImgAvatarDressInfo() {
        return imgAvatarDressInfo;
    }

    public void setImgAvatarDressInfo(XImageAnimAvatarDressInfoBean animAvatarDressInfoBean) {
        this.imgAvatarDressInfo = animAvatarDressInfoBean;
    }


    public int getBigGrade() {
        return bigGrade;
    }

    public void setBigGrade(int bigGrade) {
        this.bigGrade = bigGrade;
    }

    public int getSmallGrade() {
        return smallGrade;
    }

    public void setSmallGrade(int smallGrade) {
        this.smallGrade = smallGrade;
    }

    public String getCommonLevel() {
        return commonLevel;
    }

    public void setCommonLevel(String commonLevel) {
        if (StringUtils.isEmpty(commonLevel)) {
            commonLevel = "0";
        }
        this.commonLevel = commonLevel;
        setCommonLevelIcon(CommonLevelHelper.INSTANCE.getLevelSmallIcon(commonLevel));
        setVipBubble(commonLevel); // 土豪气泡特权
    }

    public String getCommonLevelIcon() {
        return commonLevelIcon;
    }

    public void setCommonLevelIcon(String commonLevelIcon) {
        this.commonLevelIcon = commonLevelIcon;
    }


    public String getBubbleUrl() {
        return bubbleUrl;
    }

    public void setBubbleUrl(String bubbleUrl) {
        this.bubbleUrl = bubbleUrl;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
        setTitleItemBean(LiveTitleHelper.getInstance().getTitleItemBean(title));
    }

    public LiveTitleInfo.LiveTitleItemBean getTitleItemBean() {
        return mTitleItemBean;
    }

    public void setTitleItemBean(LiveTitleInfo.LiveTitleItemBean titleItemBean) {
        mTitleItemBean = titleItemBean;
    }

    public String getActivityTitle() {
        return activityTitle;
    }

    public void setActivityTitle(String activityTitle) {
        this.activityTitle = activityTitle;
        setActivityTitleBean(LiveTitleHelper.getInstance().getActivityTitleItemBean(activityTitle));
    }

    public LiveTitleInfo.LiveActivityTitleBean getActivityTitleBean() {
        return mActivityTitleBean;
    }

    public void setActivityTitleBean(LiveTitleInfo.LiveActivityTitleBean activityTitleBean) {
        mActivityTitleBean = activityTitleBean;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getAccountCardId() {
        return accountCardId;
    }

    public void setAccountCardId(String accountCardId) {
        this.accountCardId = accountCardId;
    }

    public String getAccountCardLevel() {
        return accountCardLevel;
    }

    public void setAccountCardLevel(String accountCardLevel) {
        this.accountCardLevel = accountCardLevel;
    }

    public String getPaipaiOtherNickName() {
        return paipaiOtherNickName;
    }

    public void setPaipaiOtherNickName(String paipaiOtherNickName) {
        this.paipaiOtherNickName = paipaiOtherNickName;
    }

    public String getPaipaiRelationName() {
        return paipaiRelationName;
    }

    public void setPaipaiRelationName(String paipaiRelationName) {
        this.paipaiRelationName = paipaiRelationName;
    }

    public String getPaipaiRelationId() {
        return paipaiRelationId;
    }

    public void setPaipaiRelationId(String paipaiRelationId) {
        this.paipaiRelationId = paipaiRelationId;
    }

    public void setSeatNum(int seatNum) {
        this.seatNum = seatNum;
    }

    public int getSeatNum() {
        return seatNum;
    }

    public void setAuctionInfoBean(ChatRoomMsgAuctionInfoBean auctionInfoBean) {
        if (auctionInfoBean != null) {
            auctionInfoBean.doExtra(null);
        }
        this.auctionInfoBean = auctionInfoBean;
    }

    public ChatRoomMsgAuctionInfoBean getAuctionInfoBean() {
        return auctionInfoBean;
    }

    public void setHiddenMan(boolean hiddenMan) {
        if (hiddenMan) {
            setNobleCode(0);
            setRoleType("");
            setCommonLevel(null);
            setTitle("");
            setTitleItemBean(null);
            setActivityTitle("");
            setActivityTitle(null);
            setUserType("");
            setPaipaiOtherNickName("");
            setPaipaiRelationName("");
            setPaipaiRelationId("");
            setBigGrade(0);
            setSmallGrade(0);
            setImgAvatarDressInfo(null);
            setTagListJson(null);
            setAuctionInfoBean(null);
        }
    }

    /**
     * 设置中台头像装扮
     * @param avatarDressInfoStr im 下发数据
     */
    public void setAvatarDressInfoStr(String avatarDressInfoStr) {
        if (avatarDressInfoStr != null && avatarDressInfoStr.length() > 0) {
            XImageAnimAvatarDressInfoBean dressInfoBean = XJson.fromJson(avatarDressInfoStr, XImageAnimAvatarDressInfoBean.class);
            if (dressInfoBean != null) {
                dressInfoBean.doExtra(null);
            }
            setImgAvatarDressInfo(dressInfoBean);
        }
    }

    /**
     * 设置消息标签
     * @param tagListStr im 下发数据
     */
    public void setMsgTagList(String tagListStr) {
        if (tagListStr != null && tagListStr.length() > 0) {
            List<ChatRoomMsgTagBean> list = XJson.fromJson(tagListStr, XJson.getListType(ChatRoomMsgTagBean.class));
            if (ObjectUtils.isNotEmpty(list)) {
                for (ChatRoomMsgTagBean tagBean : list) {
                    tagBean.doExtra(null);
                }
            }
            setTagListJson(list);
        } else {
            setTagListJson(null);
        }
    }
}
