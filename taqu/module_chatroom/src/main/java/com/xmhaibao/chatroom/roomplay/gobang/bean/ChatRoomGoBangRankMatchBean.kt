package com.xmhaibao.chatroom.roomplay.gobang.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import hb.common.helper.HostHelper
import hb.utils.WebpUtils

/**
 * 聊天室-五子棋段位赛匹配页面数据
 *
 * <AUTHOR>
 * @date 2022/5/23
 */
class ChatRoomGoBangRankMatchBean : IDoExtra {
    var name: String? = ""

    var cover: String? = ""

    @SerializedName("start_time")
    var startTime: String? = ""

    @SerializedName("end_time")
    var endTime: String? = ""

    @SerializedName("grade_des_url")
    var gradeDesUrl: String? = ""

    @SerializedName("big_grade")
    var bigGrade: String? = ""

    @SerializedName("small_grade")
    var smallGrade: String? = ""

    var star: String? = ""

    @SerializedName("is_max_grade")
    var isMaxGrade: String? = ""

    @SerializedName("next_big_grade")
    var nextBigGrade: String? = ""

    @SerializedName("next_small_grade")
    var nextSmallGrade: String? = ""

    @SerializedName("next_star")
    var nextStar: String? = ""

    @SerializedName("grade_top")
    var gradeTop: String? = ""

    @SerializedName("grade_top_url")
    var gradeTopUrl: String? = ""

    @SerializedName("daily_start_time")
    var dailyStartTime: String? = ""

    @SerializedName("daily_end_time")
    var dailyEndTime: String? = ""

    @SerializedName("team_uuid")
    var teamUuid: String? = ""

    /**
     * 匹配状态 0-未开始匹配 1-匹配中 2-匹配完成
     */
    @SerializedName("match_status")
    var matchStatus: String? = ""

    var captain: ChatRoomGoBangRankMatchUserBean? = null

    var teammate: ChatRoomGoBangRankMatchUserBean? = null

    @SerializedName("reward_content")
    var rewardContent: ChatRoomGoBangGradeUpgradeBean? = null

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        captain?.doExtra(response)
        teammate?.doExtra(response)

        cover = WebpUtils.getWebpUrl_4_1(cover, HostHelper.getInstance().hostImg)
        rewardContent?.doExtra(response)
    }

}