package com.xmhaibao.chatroom.roomplay.themeroom.framework.block.floating

import android.view.LayoutInflater
import android.view.View
import cn.taqu.lib.base.live.model.event.EventMsgBase
import com.xmhaibao.chatroom.databinding.ChatroomThemeBlockFloatingBlockBinding
import com.xmhaibao.chatroom.model.event.EventChatRoomMsgGift
import com.xmhaibao.chatroom.roomplay.themeroom.framework.depend.IChatRoomThemeBlockDepend
import com.xmhaibao.chatroom.roomplay.themeroom.helper.IChatRoomThemeHelper
import com.xmhaibao.chatroom.roomplay.themeroom.view.ChatRoomThemeGiftReceiveFloatView.Callback
import com.xmhaibao.chatroom.roomplay.themeroom.viewmodel.ChatRoomThemeViewModel
import com.xmhaibao.chatroom.view.ChatRoomCommonTempMsgView
import com.xmhaibao.chatroom.view.ChatRoomSwitchMsgTypeView
import com.xmhaibao.gift.bean.SendGiftUserInfo
import hb.common.data.AccountHelper
import hb.xblock.activityViewModels
import hb.xblockframework.framework.base.UIBlock
import hb.xblockframework.framework.join.IBlockContext
import hb.xblockframework.framework.utils.findDepend

/**
 * 聊天室主题房 悬浮功能Block
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
class ChatRoomThemeFloatingBlock(blockContext: IBlockContext) : UIBlock(blockContext),
    IChatRoomFloatingService {
    private lateinit var binding: ChatroomThemeBlockFloatingBlockBinding

    private val themeDepend by findDepend<IChatRoomThemeBlockDepend>()
    private val viewModel by activityViewModels<ChatRoomThemeViewModel>()


    override fun bindBlockService(): Class<*> {
        return IChatRoomFloatingService::class.java
    }

    override fun onCreateContentView(parent: View?): Any {
        binding = ChatroomThemeBlockFloatingBlockBinding.inflate(LayoutInflater.from(context))
        return binding.root
    }

    override fun onCreate() {
        super.onCreate()
        binding.giftReceiveFloatView.callback = object : Callback {
            override fun onThanksClick(nickName: String?, giftName: String?) {
                val chatRoomMsgPresenter = themeDepend?.getCommonCallback()
                    ?.getChatRoomCommonOperationHelper()?.mMsgPresenter
                chatRoomMsgPresenter?.sendMessage(
                    "谢谢${nickName}的${giftName}", ChatRoomSwitchMsgTypeView.TYPE_NORMAL
                )
            }

            override fun onReSendGift(uuid: String?) {
                val commonCallback = themeDepend?.getCommonCallback()
                commonCallback?.showGiftListDialogFragment(userInfo = SendGiftUserInfo().apply {
                    setUuid(uuid)
                })
            }

            override fun getRoomSubType(): String? {
                return viewModel.partyIdFlow.value
            }
        }
    }

    /**
     * 设置 主题信息
     */
    override fun setThemeData(helper: IChatRoomThemeHelper?) {
        binding.flBgmEntrance.setThemeData(helper, themeDepend?.getCommonCallback())
    }

    /**
     * 获取 消息view
     */
    override fun getCommonTempMsgView(): ChatRoomCommonTempMsgView? {
        return binding.rlMsgDragView.getCommonMsgView()
    }

    /**
     * 添加消息到 浮动的消息窗
     */
    override fun addMsg(event: EventMsgBase) {
        binding.rlMsgDragView.getCommonMsgView()?.addMsg(event)
    }

    /**
     * 接收礼物消息
     */
    override fun onReceiveGift(event: EventChatRoomMsgGift?) {
        event?.apply {
            if (event.sendAccountUuid != AccountHelper.getAccountUuid()) {
                return
            }
            binding.giftReceiveFloatView.onGiftReceived(event)
        }
    }


}