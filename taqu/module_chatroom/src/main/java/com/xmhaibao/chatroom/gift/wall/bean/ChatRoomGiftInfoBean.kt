package com.xmhaibao.chatroom.gift.wall.bean


import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import com.xmhaibao.chatroom.gift.wall.constant.ChatRoomGiftWallConstant
import com.xmhaibao.chatroom.gift.wall.util.ChatRoomGiftWallUtils
import hb.common.helper.HostHelper
import hb.utils.StringUtils

data class ChatRoomGiftInfoBean(
    @SerializedName("id") var id: String? = null, // 礼物ID
    @SerializedName("name") var name: String? = null, // 礼物名称
    @SerializedName("icon") var icon: String? = null, // 礼物图标.png
    /**
     * 点亮数量 0 表示未点亮
     */
    @SerializedName("num") var num: Int = 0,
    /**
     * 周礼物冠军头像.png
     */
    @SerializedName("champion_avatar") var championAvatar: String? = null,

    /**
     * /1-趣币 2-贝壳 新增
     * [ChatRoomGiftWallUtils.COIN_PRICE_TYPE]
     * [ChatRoomGiftWallUtils.SHELL_PRICE_TYPE]
     */
    @SerializedName("price_type") var priceTypeStr: String = "0",
) : IDoExtra {

    @Transient
    var priceType: Int = 0

    @Transient
    var type: Int = ChatRoomGiftWallConstant.TYPE_RECEIVE

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        priceType = StringUtils.stringToInt(priceTypeStr)
        icon = HostHelper.getImageDefaultHost().getWebpUrl_4_1(icon)
        championAvatar = HostHelper.getAvatarHost().getWebpUrl_4_1(championAvatar)
    }
}