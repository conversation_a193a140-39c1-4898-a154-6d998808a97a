package com.xmhaibao.chatroom.bottommenu.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import hb.common.helper.HostHelper
import hb.utils.WebpUtils

/**
 * 聊天室底部功能入口Bean
 *
 * <AUTHOR>
 * @date 2021-09-26
 */
class ChatRoomBottomEntryBean : IDoExtra {
    /**
     * 列表
     */
    @SerializedName("entry")
    var entryList: List<EntryList>? = null

    /**
     * 是否首次解锁站点 1-是 0-否
     */
    @SerializedName("is_first_unlock_station")
    var isFirstStation: String? = ""

    /**
     * 是否解锁新站点 1-是 0-否
     */
    @SerializedName("is_unlock_station")
    var isUnlockStation: String? = ""

    open class EntryList {
        /**
         * sn
         */
        var sn: String? = ""

        /**
         * 入口名称
         */
        var name: String? = ""

        /**
         * 图标
         */
        var icon: String? = ""

        /**
         * 图标本地资源
         */
        var iconResource: Int = 0

        /**
         * lottie资源路径
         */
        var lottiePath: String? = ""

        /**
         * lottie资源图片路径
         */
        var lottieImagePath: String? = ""

        /**
         * 提醒文案
         */
        var notice: String? = ""

        /**
         * 跳转地址
         */
        var relation: String? = ""

        /**
         * 是否显示红点
         */
        var showRedDot: Boolean = false

        /**
         * 是否显示New标签
         */
        var showNewTag: Boolean = false

        /**
         * 相亲-免费心动的引导动画lottie id
         */
        @SerializedName("lottie_id")
        var lottieId: String? = null

        /**
         * 相亲-桃花签菜单按钮lottie
         */
        @SerializedName("stick_lottie_id")
        var stickLottieId: String? = null

        /**
         * 相亲-桃花签引导lottie
         */
        @SerializedName("stick_guide_lottie_id")
        var stickGuideLottieId: String? = null

        /**
         * 未读数量
         */
        var redDotCount = 0

        /**
         * 本地插入数据的type
         */
        var localDataType: String? = null
    }

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        entryList?.forEach {
            it.icon = WebpUtils.getWebpUrl_4_1(it.icon, HostHelper.getInstance().hostImg)
        }
    }
}