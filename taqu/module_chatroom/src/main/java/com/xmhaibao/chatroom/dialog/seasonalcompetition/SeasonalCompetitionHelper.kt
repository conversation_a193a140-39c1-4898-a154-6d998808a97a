package com.xmhaibao.chatroom.dialog.seasonalcompetition

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.facebook.drawee.generic.RoundingParams
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.rank.utils.ChatRoomRanksUtils
import com.xmhaibao.chatroom.roomplay.auction.utils.AuctionViewUtils
import com.xmhaibao.chatroom.utils.ChatRoomUtils
import hb.utils.ColorUtils
import hb.ximage.fresco.BaseDraweeView
import java.math.BigDecimal

fun BaseDraweeView.toCircle() {
    var roundingParams: RoundingParams? = hierarchy.roundingParams
    if (roundingParams == null) {
        roundingParams = RoundingParams()
    }
    roundingParams.let {
        it.roundAsCircle = true
        hierarchy.roundingParams = it
    }
}


fun BaseDraweeView.toRectangle(radius: Int) {
    var roundingParams: RoundingParams? = hierarchy.roundingParams
    if (roundingParams == null) {
        roundingParams = RoundingParams()
    }
    roundingParams.let {
        it.roundAsCircle = false
        it.setCornersRadius(radius.toFloat())
        hierarchy.roundingParams = it

    }
}


/**
 *
 * 时力赛 - 帮助类
 * <AUTHOR>
 * @date 2024-04-16
 */
object SeasonalCompetitionHelper {
    private const val POSITION_1 = 1
    private const val POSITION_2 = 2
    private const val POSITION_3 = 3


    fun setRankIndex(textView: TextView, imageView: ImageView, rankIndex: String) {
        var pos = 0
        if (rankIndex.isNotEmpty()) {
            try {

                pos = rankIndex.toInt()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        if (pos == 0) {
            textView.visibility = View.VISIBLE
            imageView.visibility = View.GONE
            textView.textSize = 12f
            textView.setTextColor(
                ColorUtils.getColor(hb.xstyle.R.color.TH_Gray600)
            )
            textView.text = "未上榜"
            return
        }

        textView.textSize = 15f
        textView.setTextColor(
            ColorUtils.getColor(hb.xstyle.R.color.TH_Black100)
        )
        when (pos) {
            POSITION_1 -> {
                textView.visibility = View.GONE
                imageView.visibility = View.VISIBLE
                imageView.setImageResource(R.drawable.chatroom_seasonal_competiton_item_index1)
            }

            POSITION_2 -> {
                textView.visibility = View.GONE
                imageView.visibility = View.VISIBLE
                imageView.setImageResource(R.drawable.chatroom_seasonal_competiton_item_index2)
            }

            POSITION_3 -> {
                textView.visibility = View.GONE
                imageView.visibility = View.VISIBLE
                imageView.setImageResource(R.drawable.chatroom_seasonal_competiton_item_index3)
            }

            else -> {
                textView.visibility = View.VISIBLE
                imageView.visibility = View.GONE
                textView.text = rankIndex
            }
        }
    }

    fun TextView.setNickName(nickName: String?) {
        if (nickName.isNullOrEmpty()) {
            this.text = ""
        } else {
            this.text = ChatRoomRanksUtils.dealTextMaxLength(nickName, 12)
        }
    }

    fun ImageView.setSexType(sexType: String?) {
        ChatRoomRanksUtils.setSexType(this, sexType)

    }

    fun TextView.setSeasonalValue(hourValue: String?, isShowPrefix: Boolean = true) {
        val valueDouble = AuctionViewUtils.stringToDouble(hourValue)
        if (valueDouble < 10000) {
            if (valueDouble == 0.00) {
                if (isShowPrefix) {
                    this.text = "时力值：0.0"
                } else {
                    this.text = "0.0"
                }
            } else {
                if (isShowPrefix) {
                    this.text = "时力值：$hourValue"
                } else {
                    this.text = "$hourValue"

                }
            }


        } else {
            val hourValueStr = ChatRoomUtils.getVolumeOfW(
                valueDouble, 1, BigDecimal.ROUND_DOWN
            )
            if (isShowPrefix) {
                this.text = "时力值：$hourValueStr"
            } else {
                this.text = hourValueStr
            }
        }

    }

    fun TextView.setDistance(rankIndex: String?, distance: String?) {
        var pos: Int = -1
        if (!rankIndex.isNullOrEmpty()) {
            try {
                pos = rankIndex.toInt()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        val valueDouble = AuctionViewUtils.stringToDouble(distance)
        val distanceStr = if (valueDouble < 10000) {
            val tempValue = if (valueDouble == 0.00) {
                "0.0"
            } else {
                distance
            }
            tempValue
        } else {
            val tempValue = ChatRoomUtils.getVolumeOfW(
                valueDouble, 1, BigDecimal.ROUND_DOWN
            )
            tempValue
        }
        if (pos == POSITION_1) {
            this.text = buildString {
                append("领先第二名：")
                append(
                    distanceStr
                )
            }
        } else {
            this.text = buildString {
                append("距离上一名差：")
                append(distanceStr)
            }
        }
    }

    fun TextView.setDoing(doing: String?) {
        if (doing.isNullOrEmpty()) {
            this.visibility = View.GONE
        } else {
            this.visibility = View.VISIBLE
            this.text = ChatRoomRanksUtils.dealTextMaxLength(doing, 8)
        }
    }

    fun TextView.setCity(city: String?) {
        if (city.isNullOrEmpty()) {
            this.visibility = View.GONE
        } else {
            this.visibility = View.VISIBLE
            this.text = city
        }
    }

    /**
     * 将剩余时间转换为分钟
     */
    fun convertRemainingToMinute(remainingTime: Long): String {
        if (remainingTime <= 0) {
            return "00"
        }
        val minute = remainingTime / 60000
        return if (minute < 10) {
            "0$minute"
        } else {
            minute.toString()
        }
    }

    /**
     * 将剩余时间转换为秒
     */
    fun convertRemainingToSecond(remainingTime: Long): String {
        if (remainingTime <= 0) {
            return "00"
        }
        val second = (remainingTime / 1000) % 60
        return if (second < 10) {
            "0$second"
        } else {
            second.toString()
        }
    }
}