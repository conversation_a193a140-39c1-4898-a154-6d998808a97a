package com.xmhaibao.chatroom.gift.starpicking.event

import hb.qim.base.core.IResponseParse
import hb.utils.EventBusUtils
import hb.utils.StringUtils

/**
 *
 * 惊喜时刻Im通知

 * <AUTHOR>
 * @date 2024-05-21
 */
class EventChatRoomStarPickingSurpriseTime : IResponseParse<EventChatRoomStarPickingSurpriseTime> {

    var endTime: Long = 0L
    var title: String? = null
    var needShowDialog: String? = null

    //状态 0-关闭 1-开启
    var status: String? = null
    var sendImTime: Long = 0L
    override fun parseResponse(
        event: String?,
        vararg args: Any?,
    ): EventChatRoomStarPickingSurpriseTime {
        if (args.isNotEmpty() && args.size >= 5) {
            endTime = StringUtils.stringToLong(args[0].toString())
            title = args[1].toString()
            needShowDialog = args[2].toString()
            status = args[3].toString()
            sendImTime = StringUtils.stringToLong(args[4].toString())
            EventBusUtils.post(this)
        }
        return this
    }
}