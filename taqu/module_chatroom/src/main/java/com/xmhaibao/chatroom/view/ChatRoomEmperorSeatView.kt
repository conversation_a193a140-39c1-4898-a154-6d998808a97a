package com.xmhaibao.chatroom.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.api.constants.ChatRoomType
import com.xmhaibao.chatroom.bean.ChatEmperorSeatInfoBean
import com.xmhaibao.chatroom.databinding.ChatroomEmperorSeatViewBinding
import com.xmhaibao.chatroom.model.event.EventChatEmperorSeat
import com.xmhaibao.chatroom.mvp.IRoomView
import com.xmhaibao.chatroom.utils.ChatRoomTypeUtils
import hb.utils.StringUtils

/**
 * 帝王位
 *
 * <AUTHOR>
 * @date 2022-03-18
 */
class ChatRoomEmperorSeatView : FrameLayout {

    private var mViewBinding: ChatroomEmperorSeatViewBinding? = null

    var mChatUuid: String? = null

    var mRoomView: IRoomView? = null

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        val view =
            LayoutInflater.from(context).inflate(R.layout.chatroom_emperor_seat_view, this, true)

        mViewBinding = ChatroomEmperorSeatViewBinding.bind(view)
    }


    /**
     * 显示帝王位上位弹窗
     */
    private fun showEmperorSeatDialog() {
        ChatEmperorSeatDialog(
            context, mChatUuid,
            mRoomView?.roomViewCallBack,
            mRoomView?.chatRoomOnlineCallBack
        ).show()
    }

    /**
     * 设置帝王位信息
     */
    fun setEmperorSeatInfo(
        info: ChatEmperorSeatInfoBean?,
        @ChatRoomType chatRoomType: Int,
        chatUuid: String,
    ) {
        if (ChatRoomTypeUtils.isBigScreenRoomType(chatRoomType)
        ) {
            //大屏房 不显示帝王位
            visibility = INVISIBLE
            return
        }
        info?.apply {
            mViewBinding?.apply {
                visibility = if (info.isOpenEmperorSeat) VISIBLE else INVISIBLE
                ivEmperorAvatar.visibility =
                    if (StringUtils.isNotEmpty(info.avatar)) VISIBLE else GONE
                ivEmperorAvatar.setImageURI(info.avatar)
            }
        }
        mChatUuid = chatUuid
        setOnClickListener {
            showEmperorSeatDialog()
        }
    }

    /**
     * 更新帝王位信息
     */
    fun onUpdateEmperorSeat(event: EventChatEmperorSeat) {
        mViewBinding?.apply {
            ivEmperorAvatar.setImageURI(event.avtarUrl)
            ivEmperorAvatar.visibility =
                if (StringUtils.isNotEmpty(event.avtarUrl)) VISIBLE else GONE
        }
    }


}