package com.xmhaibao.chatroom.model.event;

import cn.taqu.lib.base.im.LiveIMLogHelper;
import hb.qim.base.core.IResponseParse;
import hb.utils.EventBusUtils;
import hb.utils.WebpUtils;

/**
 * Created by <PERSON><PERSON>zhao<PERSON>e on 2020/1/10 14:17
 */
public class EventChatEmperorSeat implements IResponseParse<EventChatEmperorSeat> {
    private String accountUuid;
    private String avtarUrl;
    private String nickName;

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getAccountUuid() {
        return accountUuid;
    }

    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }

    public String getAvtarUrl() {
        return avtarUrl;
    }

    public void setAvtarUrl(String avtarUrl) {
        this.avtarUrl = avtarUrl;
    }

    @Override
    public EventChatEmperorSeat parseResponse(String event, Object... args) {
        LiveIMLogHelper.getInstance().log(event, args);
        if (args.length >= 3) {
            setAccountUuid(String.valueOf(args[0]));
            setAvtarUrl(WebpUtils.getWebpUrl_4_1(String.valueOf(args[1])));
            setNickName(String.valueOf(args[2]));
            EventBusUtils.post(this);
        }
        return this;
    }
}
