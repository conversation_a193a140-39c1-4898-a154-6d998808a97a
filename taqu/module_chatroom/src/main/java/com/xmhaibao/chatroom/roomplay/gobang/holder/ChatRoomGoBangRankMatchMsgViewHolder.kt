package com.xmhaibao.chatroom.roomplay.gobang.holder

import android.view.ViewGroup
import com.xmhaibao.chatroom.roomplay.gobang.event.EventChatRoomGoBangRankMatchMsg
import hb.xadapter.XBaseViewHolder
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.databinding.ChatroomGobangRankMatchMsgItemBinding

/**
 * 聊天室-五子棋段位赛匹配赛消息列表
 *
 * <AUTHOR>
 * @date 2022/5/24
 */
class ChatRoomGoBangRankMatchMsgViewHolder(parent: ViewGroup?) :
    XBaseViewHolder<EventChatRoomGoBangRankMatchMsg>(parent, R.layout.chatroom_gobang_rank_match_msg_item) {

    private val mViewBinding = ChatroomGobangRankMatchMsgItemBinding.bind(itemView)

    override fun onBindView(item: EventChatRoomGoBangRankMatchMsg?) {
        if (item != null) {
            mViewBinding.ivAvatar.setImageFromUrl(item.senderAvatar)
            mViewBinding.tvContent.text = item.content
        }
    }
}