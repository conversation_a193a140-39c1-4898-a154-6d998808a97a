package com.xmhaibao.chatroom.viewmodel

import android.app.Application
import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import cn.taqu.lib.base.bean.CommonBannerBean
import cn.taqu.lib.base.router.RouterLaunch
import cn.taqu.lib.okhttp.callback.GsonCallBack
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.xmhaibao.chatroom.active.love.bean.ChatRoomAuctionRelationInMicBean
import com.xmhaibao.chatroom.active.love.repository.ChatRoomActiveLoveRepository
import com.xmhaibao.chatroom.adpop.repository.ChatRoomAdPopRepository
import com.xmhaibao.chatroom.api.constants.ChatRoomEntryFromSource
import com.xmhaibao.chatroom.api.constants.ChatRoomType
import com.xmhaibao.chatroom.bean.ChatRoomHeatRankListBean
import com.xmhaibao.chatroom.bean.ChatRoomJoinFamilyBean
import com.xmhaibao.chatroom.bean.ChatRoomOwnerGrowthStrategyBean
import com.xmhaibao.chatroom.model.ChatRoomModel
import com.xmhaibao.chatroom.model.event.EventChatRoomHeatValueTask
import com.xmhaibao.chatroom.model.event.EventChatRoomJoinFamilyGuideBean
import com.xmhaibao.chatroom.repository.ChatRoomHotTaskRepository
import com.xmhaibao.chatroom.repository.ChatRoomMsgRepository
import com.xmhaibao.chatroom.repository.ChatRoomOwnerStrategyRepository
import com.xmhaibao.chatroom.repository.ChatRoomRepository
import com.xmhaibao.family.api.router.FamilyPinsRouter
import com.xmhaibao.message.api.router.MessagePinsRouter
import hb.common.data.AccountHelper
import hb.common.xstatic.viewmodel.BaseViewModel
import hb.kotlin_extension.noneSyncLazy
import hb.utils.Loger
import hb.utils.StringUtils
import hb.xrequest.asAwait
import hb.xrequest.await
import hb.xrequest.awaitNullable
import hb.xrequest.launchHttp
import hb.xstyle.xdialog.XLoadingDialog
import hb.xtoast.XToastUtils
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * 聊天室ViewModel
 *
 * <AUTHOR>
 * @date 2021-06-08
 */
class ChatRoomViewModel(application: Application) : BaseViewModel(application) {
    companion object {
        private const val TAG = "ChatRoomViewModel"
    }
    private val mHotTaskRepo by lazy { ChatRoomHotTaskRepository() }
    private val mMsgRepo by lazy { ChatRoomMsgRepository() }
    private val mActiveLoveRepo by lazy { ChatRoomActiveLoveRepository() }
    private val mAdPopRepo by noneSyncLazy { ChatRoomAdPopRepository() }


    var mChatUuid: String? = ""
    var mRoomType: Int = ChatRoomType.CHAT_ROOM_NORMAL

    fun initData(chatUuid: String?, roomType: Int) {
        mChatUuid = chatUuid
        mRoomType = roomType
    }

    var mFamilyInviteGuideData = MutableLiveData<EventChatRoomJoinFamilyGuideBean>()


    /**
     * 聊天室广告数据
     */
    var mChatRoomBannerData = MutableLiveData<MutableList<CommonBannerBean>?>()

    /**
     * 热度任务数据
     */
    var mChatRoomHeatValueTaskData = MutableLiveData<EventChatRoomHeatValueTask>()

    /**
     * 热度引导数据
     */
    var mChatRoomHeatGuideData = MutableLiveData<ChatRoomHeatRankListBean>()

    /**
     * 520活动，用户和麦上用户邀请信息
     */
    var mAuctionRelationInMicBean= MutableLiveData<ChatRoomAuctionRelationInMicBean>()


    /**
     * 房主养成策略
     */
    val mOwnerStrategyData = MutableLiveData<ChatRoomOwnerGrowthStrategyBean>()

    /**
     * 聊天室家族引导消息
     */
    fun getFamilyInviteGuide() {
        if (!AccountHelper.isUserLogined()) {
            return
        }
        viewModelScope.launchHttp({
            val data =  ChatRoomRepository
                .getFamilyInviteGuide(AccountHelper.getUserTicketId(), mChatUuid)
                .await<EventChatRoomJoinFamilyGuideBean>()
            if (data != null) {
                mFamilyInviteGuideData.value = data
            }
        }, {
            true
        })
    }

    /**
     * 加入家族
     */
    fun requestJoinFamily(familyUuid: String?) {
        MessagePinsRouter.messageService().requestJoinMessageFamily(familyUuid, "", object : GsonCallBack<ChatRoomJoinFamilyBean?>() {
            override fun onSuccess(isCache: Boolean, obj: ChatRoomJoinFamilyBean?, response: IResponseInfo<out IResponseInfo<*>>) {
                obj?.apply {
                    if (StringUtils.equalsIgnoreCase(applyStatus, "1") || StringUtils.equalsIgnoreCase(applyStatus, "3")) {
                        conversationId.takeUnless { conversationId.isNullOrEmpty() }?.apply {
                            XToastUtils.show("您已成功加入家族")
                            FamilyPinsRouter.familyService().launchMessageFamilyChatActivity(this)
                        }

                    } else {
                        XToastUtils.show("申请已发送，等待通过")
                    }
                }
            }

            override fun onFailure(isServiceFailure: Boolean, response: IResponseInfo<out IResponseInfo<*>>) {
                XToastUtils.show(response.getResponseMsg("申请失败，请稍后再试"))
            }
        })
    }


    /**
     * 获取聊天室广告
     */
    suspend fun getChatRoomBanner(): MutableList<CommonBannerBean>? {
        return suspendCoroutine { continuation ->
            viewModelScope.launchHttp({
                val data = ChatRoomRepository.getChatRoomBanner(mRoomType.toString()).await<MutableList<CommonBannerBean>>()
                continuation.resume(data)
            }, {
                continuation.resume(null)
                true
            })
        }
    }

    /**
     * 获取聊天室banner接口合并后的数据
     * 业务接口一起请求
     */
    fun getChatRoomBannerCombine(isRoomOwner: Boolean) {
        viewModelScope.launchHttp({
            val bannerData = getChatRoomBanner()
            var roomOwnerStrategy: ChatRoomOwnerGrowthStrategyBean? = null
            if (isRoomOwner) {
                roomOwnerStrategy = requestRoomOwnerGrowthStrategy()
            }
            val bannerList = mutableListOf<CommonBannerBean>()
            if (bannerData != null && bannerData.isEmpty().not()) {
                bannerList.addAll(bannerData)
            }
            if (roomOwnerStrategy != null && roomOwnerStrategy.showAd == "1") {
                bannerList.add(0, roomOwnerStrategy.copyToCommonBannerBean())
            }
            mChatRoomBannerData.value = bannerList
        })
    }


    /**
     * 邀请麦上用户换麦
     */
    fun inviteChangeMic(inviteUuid: String, seatNum: Int) {
        viewModelScope.launchHttp({
            ChatRoomRepository
                .inviteChangeMic(AccountHelper.getUserTicketId(), mChatUuid, inviteUuid, seatNum.toString())
                .await<Any>()
            XToastUtils.show("邀请成功")
        }, {
            XToastUtils.show(it.responseMsg.ifEmpty { "邀请失败" })
            true
        })
    }


    /**
     * 美颜设置状态
     */
    fun setBeautyStatus(isSetting: Boolean) {
        ChatRoomModel.getInstance().isLocalVideoPreview = isSetting
        ChatRoomRepository.updateBeautySettingStatus(AccountHelper.getUserTicketId(), mChatUuid, isSetting)
            .execute(object : GsonCallBack<Any>() {
                override fun onSuccess(isCache: Boolean, obj: Any?, response: IResponseInfo<out IResponseInfo<*>>) {
                    //ignore
                }

                override fun onFailure(isServiceFailure: Boolean, response: IResponseInfo<out IResponseInfo<*>>) {
                    //ignore
                }
            })
    }

    /**
     * 聊天室-设置声音美化
     */
    fun setVoiceBeautyStatus(isOpen: Boolean, success: (() -> Unit)? = null) {
        ChatRoomRepository.setVoiceBeautifyStatus(AccountHelper.getUserTicketId(), if (isOpen) 1 else 0)
            .execute(object : GsonCallBack<Any>() {
                override fun onSuccess(isCache: Boolean, obj: Any?, response: IResponseInfo<out IResponseInfo<*>>) {
                    success?.invoke()
                }

                override fun onFailure(isServiceFailure: Boolean, response: IResponseInfo<out IResponseInfo<*>>) {
                    //ignore
                }
            })
    }

    /**
     * 请求房间热度任务信息
     */
    fun requestHeatValueTaskInfo() {
        viewModelScope.launchHttp({
            val data = mHotTaskRepo.getHeatValueTaskInfo(mChatUuid).await<EventChatRoomHeatValueTask>()
            mChatRoomHeatValueTaskData.value = data
        }, {
            true
        })
    }

    /**
     * 请求房间热度引导信息
     */
    fun requestHeatGuideInfo() {
        viewModelScope.launchHttp({
            val data = mHotTaskRepo.getHeatValueGuideInfo(mChatUuid).await<ChatRoomHeatRankListBean>()
            mChatRoomHeatGuideData.value = data
        }, {
            true
        })
    }


    /**
     * 主要用于滑动切换房间时，移除数据回调
     */
    fun removeAllObservers(owner: LifecycleOwner?) {
        owner?.apply {
            mChatRoomBannerData.removeObservers(this)
            mFamilyInviteGuideData.removeObservers(this)
            mChatRoomHeatValueTaskData.removeObservers(this)
            mChatRoomHeatGuideData.removeObservers(this)

            mChatRoomBannerData.value = null
            mFamilyInviteGuideData.value = null
            mChatRoomHeatValueTaskData.value = null
            mChatRoomHeatGuideData.value = null
        }
    }

    /**
     * 获取520活动，麦上和用户的邀请信息
     */
    fun getActiveLoveInviteInfo() {
        viewModelScope.launchHttp({
            val relationInMicInfo = mActiveLoveRepo.getAuctionRelationInMic(mChatUuid)
            mAuctionRelationInMicBean.postValue(relationInMicInfo)
        }, {
            true
        })

    }

    /**
     * 用户进房主动，获取一次进房欢迎消息
     */
    fun requestIntoRoomIM() {
        viewModelScope.launchHttp({
            mMsgRepo.postIntoChatRoomTips(mChatUuid).await<Any>()
        }, {
            true
        })
    }

    /**
     * 如果是从家族正在玩那边进来的，会给房间内同家族用户发一个消息
     * [需求地址](https://o15vj1m4ie.feishu.cn/wiki/BQA3wkJVgi3Qb1kRktZcEaj7nif)
     */
    fun welcomeFamilyMember(entryFromSource: Int, reviewUuidIntoRoom: String?) {
        if (entryFromSource == ChatRoomEntryFromSource.FAMILY_PLAYING_PAGE || entryFromSource == ChatRoomEntryFromSource.FAMILY_TOP_PLAYING) {
            if (reviewUuidIntoRoom.isNullOrEmpty()) {
                if (Loger.isDebug()) {
                    Loger.d(TAG, "welcomeFamilyMember 从家族正在玩那边进来 reviewUuidIntoRoom 为null 不触发")
                }
                return
            }
            viewModelScope.launchHttp({
                mMsgRepo.welcomeFamilyMember(mChatUuid, reviewUuidIntoRoom).await<Any>()
            }, {
                true
            })
        }
    }


    /**
     * 房主养成策略
     */
    private suspend fun requestRoomOwnerGrowthStrategy(): ChatRoomOwnerGrowthStrategyBean? {
        return suspendCoroutine { continuation ->
            viewModelScope.launchHttp({
                val data = ChatRoomOwnerStrategyRepository().getStrategyInfo("room").awaitNullable()
                if (data != null) {
                    mOwnerStrategyData.value = data
                }
                continuation.resume(data)
            }, {
                continuation.resume(null)
                true
            })
        }
    }

    /**
     * 请求跳转到聊天室事件上报
     */
    fun reportChatRoomPositionFromRouter(position: String?) {
        viewModelScope.launchHttp({
            mAdPopRepo.reportChatRoomPosition(mChatUuid, position, mRoomType.toString()).asAwait().awaitNullable()
        }, {
            true
        })
    }


    /**
     * 家族跳转
     * @param chatUuid 房间 host uuid
     * @param context context
     */
    fun jumpToFamily(chatUuid: String?, context: Context) {
        showLoadingBar()
        viewModelScope.launchHttp({
            val familyInfo = ChatRoomRepository.getFamilyBaseInfo(chatUuid ?: "").asAwait().awaitNullable()
            if (familyInfo?.targetFamilyUUID.isNullOrEmpty()) {
                if (Loger.isDebug()) {
                    Loger.d(TAG, "jumpToFamily targetFamilyUUID is null")
                }
                // 跳转 家族广场
                RouterLaunch.dealJumpData(
                    context, "m=message&a=familyHall"
                )
            } else {
                if (familyInfo?.accountFamilyUUID == familyInfo?.targetFamilyUUID) {
                    // 跳转 家族聊天
                    RouterLaunch.dealJumpData(
                        context, "m=message&a=familyChat&source=聊天室顶部信息入口"
                    )
                } else {
                    //- 跳转 家族详情
                    RouterLaunch.dealJumpData(
                        context, "m=message&a=familyDetail&familyId=" + familyInfo?.targetFamilyUUID
                    )
                }
            }
            hideLoadingBar()
        }, {
            hideLoadingBar()
            false
        })
    }

    /**
     * 更多面板家族跳转家族跳转
     * @param context context
     */
    fun moreJumpToFamily(context: Context?) {
        XLoadingDialog.showLoadingbar(context)
        viewModelScope.launchHttp({
            val mFamilyInfo =
                ChatRoomRepository.getFamilyBaseInfo(AccountHelper.getAccountUuid()).asAwait().awaitNullable()
            //- 已经加入家族，跳转至所在家族
            if (!mFamilyInfo?.targetFamilyUUID.isNullOrEmpty()) {
                RouterLaunch.dealJumpData(
                    context, "m=message&a=familyChat&source=聊天室底部点击更多入口"
                )
            } else {
                //- 没有家族，跳转至所在家族广场
                RouterLaunch.dealJumpData(context, "m=message&a=familyHall")
            }
            XLoadingDialog.hideLoadingbar()
        }, {
            XLoadingDialog.hideLoadingbar()
            false
        })
    }

}
