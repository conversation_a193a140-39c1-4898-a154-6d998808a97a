package com.xmhaibao.chatroom.roomplay.wedding.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.databinding.ChatroomWeddingApplyProgressViewBinding
import com.xmhaibao.chatroom.roomplay.wedding.viewmodel.ChatRoomWeddingApplyViewModel
import hb.utils.ColorUtils

/**
 * 婚礼申请进度条View
 *
 * <AUTHOR>
 * @date 2022-08-10
 */
class ChatRoomWeddingApplyProgressView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    ConstraintLayout(context, attrs, defStyleAttr) {
    private val mBinding = ChatroomWeddingApplyProgressViewBinding.inflate(LayoutInflater.from(context), this)
    private val mLightColor = ColorUtils.getColor(hb.xstyle.R.color.white)
    private val mDarkNormalColor = ColorUtils.getColor("#696969")
    private val mDarkTextColor = ColorUtils.getColor("#A0A0A0")

    fun onShowStep(step: Int) {
        mBinding.apply {
            when (step) {
                ChatRoomWeddingApplyViewModel.STEP_ONE -> {
                    tvNumStepTwo.shaper().solid(mDarkNormalColor)
                    tvStepTwo.setTextColor(mDarkTextColor)
                    viewStepTwo.shaper().solid(mDarkNormalColor)
                    tvNumStepThree.shaper().solid(mDarkNormalColor)
                    tvStepThree.setTextColor(mDarkTextColor)
                    viewStepThree.shaper().solid(mDarkNormalColor)
                }
                ChatRoomWeddingApplyViewModel.STEP_TWO -> {
                    tvNumStepTwo.shaper().solid(mLightColor)
                    tvStepTwo.setTextColor(mLightColor)
                    viewStepTwo.shaper().solid(mLightColor)
                    tvNumStepThree.shaper().solid(mDarkNormalColor)
                    tvStepThree.setTextColor(mDarkTextColor)
                    viewStepThree.shaper().solid(mDarkNormalColor)
                }
                ChatRoomWeddingApplyViewModel.STEP_THREE -> {
                    tvNumStepTwo.shaper().solid(mLightColor)
                    tvStepTwo.setTextColor(mLightColor)
                    viewStepTwo.shaper().solid(mLightColor)
                    tvNumStepThree.shaper().solid(mLightColor)
                    tvStepThree.setTextColor(mLightColor)
                    viewStepThree.shaper().solid(mLightColor)
                }
            }
        }
    }

}