package com.xmhaibao.chatroom.music.helper

import com.xmhaibao.chatroom.model.ChatRoomModel
import hb.utils.kvcache.KVCacheUtils

/**
 * 聊天室-背景音乐帮助类
 *
 * <AUTHOR>
 * @date 2022/3/25
 */
class ChatRoomBGMHelper {
    companion object {
        /**
         * 背景音乐搜索缓存key
         */
        private const val CACHE_HISTORY_LIST = "cache_bgm_search_history_list"
        private const val MAX_SEARCH_HISTORY_NUM = 30

        private var mSearchHistoryList: MutableList<String>? = null

        @JvmStatic
        fun getSearchHistoryList(): MutableList<String>? {
            if (mSearchHistoryList.isNullOrEmpty()) {
                val cache = KVCacheUtils.getString(CACHE_HISTORY_LIST, "")
                mSearchHistoryList = cache?.split(",")?.toMutableList()
            }
            return mSearchHistoryList
        }

        @JvmStatic
        fun setSearchHistoryList(list: MutableList<String>?) {
            mSearchHistoryList = list
            val sb = StringBuilder()
            list?.forEach {
                if (sb.isNotEmpty()) {
                    sb.append(",")
                }
                sb.append(it)
            }
            KVCacheUtils.putString(CACHE_HISTORY_LIST, sb.toString())
        }

        @JvmStatic
        fun addSearchHistoryList(content: String?) {
            if (content.isNullOrEmpty()) {
                return
            }
            if (mSearchHistoryList == null) {
                mSearchHistoryList = mutableListOf()
            }
            mSearchHistoryList?.apply {
                if (contains(content)) {
                    remove(content)
                }
                if (size >= MAX_SEARCH_HISTORY_NUM) {
                    removeLast()
                }
                add(0, content)
                setSearchHistoryList(this)
            }
        }

        @JvmStatic
        fun getBGMPlayHelper() = ChatRoomModel.getInstance().chatRoomHelper?.bgmPlayHelper

        @JvmStatic
        fun isBGMHaveChoose(songId: String?): Boolean {
            getBGMPlayHelper()?.getBGMPlayList()?.forEach {
                if (songId == it.songId) {
                    return true
                }
            }
            return false
        }
    }
}