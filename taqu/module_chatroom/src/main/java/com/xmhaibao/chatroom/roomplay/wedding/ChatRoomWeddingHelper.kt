package com.xmhaibao.chatroom.roomplay.wedding

import android.os.CountDownTimer
import com.xmhaibao.chatroom.api.constants.ChatRoomType
import com.xmhaibao.chatroom.constants.ChatRoomConstants
import com.xmhaibao.chatroom.constants.ChatRoomJoinChannelAnchor
import com.xmhaibao.chatroom.constants.ChatRoomJoinChannelScene
import com.xmhaibao.chatroom.helper.ChatRoomCommonOperationHelper
import com.xmhaibao.chatroom.roomplay.wedding.bean.ChatRoomWeddingInfoBean
import com.xmhaibao.chatroom.roomplay.wedding.constants.ChatRoomWeddingRole
import com.xmhaibao.chatroom.roomplay.wedding.event.EventChatRoomWeddingMicChange
import com.xmhaibao.chatroom.roomplay.wedding.event.EventChatRoomWeddingStop
import com.xmhaibao.chatroom.roomplay.wedding.helper.ChatRoomWeddingBGMHelper
import com.xmhaibao.chatroom.roomplay.wedding.inter.IChatRoomWeddingWindow
import com.xmhaibao.chatroom.roomplay.wedding.utils.ChatRoomWeddingUtil
import com.xmhaibao.chatroom.router.ChatRoomRouter
import com.xmhaibao.chatroom.utils.ChatRoomPermissionUtils
import hb.common.data.AccountHelper
import hb.utils.ActivityUtils
import hb.utils.EventBusUtils
import hb.xtoast.XToastUtils
import kotlin.math.absoluteValue

/**
 * 聊天室 婚礼房间帮助类
 *
 * <AUTHOR>
 * @date 2022-06-09
 */
class ChatRoomWeddingHelper {

    /**
     * 聊天室临时房间帮助类
     */
    var mCommonOperationHelper: ChatRoomCommonOperationHelper? = null

    /**
     * 和婚礼房视图交互接口
     */
    var mRoomView: IChatRoomWeddingWindow? = null

    /**
     * 红包领取倒计时
     */
    private var mRedPacketCountDownTimer: CountDownTimer? = null

    /**
     * 当前红包倒计时剩余时间
     */
    var mCurrentRedPacketTime: Long = 0

    /**
     * 房间信息数据
     */
    var mWeddingRoomBean: ChatRoomWeddingInfoBean? = null

    /**
     * 背景音乐播放帮助类
     */
    var mWeddingBgmHelper: ChatRoomWeddingBGMHelper? = null

    /**
     * 是否有自动上麦过了
     */
    var mIsAutoMic = false

    /**
     * bgm 是否暂停了
     */
    var mBgmIsPause = false

    init {
        EventBusUtils.register(this)
    }

    fun setWeddingRoomInfo(bean: ChatRoomWeddingInfoBean?) {
        mWeddingRoomBean = bean
        if (ChatRoomWeddingUtil.isMc(bean?.weddingRole ?: ChatRoomWeddingRole.NOT)) {
            if (mWeddingBgmHelper == null) {
                mWeddingBgmHelper = ChatRoomWeddingBGMHelper(
                    mCommonOperationHelper?.mChatUuid,
                    mCommonOperationHelper?.mAudioPlayHelper,
                )
            }
            //当前用户身份是司仪，直接开始播放背景音乐
            if (mWeddingBgmHelper?.mIsPlaySuccess == false){
                mWeddingBgmHelper?.startPlayBgmMusic(mWeddingRoomBean?.bgmBean)
            }
        }
    }

    /**
     * 开始红包领取倒计时
     */
    fun startRedPacketCountDownTimer(time: Long) {
        if (time <= 0L || (time - mCurrentRedPacketTime).absoluteValue <= 2) {
            return
        }
        cancelRedPacketCountDownTimer()
        mRedPacketCountDownTimer = object : CountDownTimer(time * 1000, 1000) {
            override fun onTick(millis: Long) {
                mCurrentRedPacketTime = millis
                mRoomView?.onRedPacketTime(millis)
            }

            override fun onFinish() {
                mCurrentRedPacketTime = 0
                mRoomView?.onRedPacketTime(0)
            }
        }
        mRedPacketCountDownTimer?.start()
    }

    /**
     * 关闭红包领取倒计时
     */
    fun cancelRedPacketCountDownTimer() {
        mRedPacketCountDownTimer?.cancel()
        mRedPacketCountDownTimer = null
        mCurrentRedPacketTime = 0
    }

    /**
     * 暂停播放背景音乐
     */
    fun pauseBgmMusic(): Boolean {
        mBgmIsPause = true
        return mWeddingBgmHelper?.pauseBgmMusic() ?: false
    }

    /**
     * 重新开始播放背景音乐
     */
    fun resumeBgmMusic(): Boolean {
        mBgmIsPause = false
        return mWeddingBgmHelper?.resumeBgmMusic() ?: false
    }

    /**
     * 显示流水明细
     */
    fun onShowWeddingEarnings() {
        val role = mWeddingRoomBean?.weddingRole ?: ChatRoomWeddingRole.NOT
        //司仪
        if (ChatRoomWeddingUtil.isMc(role)) {
            ChatRoomRouter.launchChatRoomWeddingEarningsActivity(
                mCommonOperationHelper?.mChatUuid,
                mWeddingRoomBean?.weddingUuid
            )
        }
    }

    /**
     * 婚礼解散关闭房间
     */
    fun onEventMainThread(event: EventChatRoomWeddingStop) {
        mCommonOperationHelper?.onCloseRoom()
        XToastUtils.show("婚礼已结束")
    }

    /**
     * 婚礼司仪发生变化了
     */
    fun onEventMainThread(event: EventChatRoomWeddingMicChange) {
        //说明我的司仪角色被取消了
        if(event.preMicUuid == AccountHelper.getAccountUuid() && ChatRoomWeddingUtil.isMc( mWeddingRoomBean?.weddingRole ?: ChatRoomWeddingRole.NOT)){
            mWeddingRoomBean?.weddingRole = ChatRoomWeddingRole.NOT
        }
        //我被设为司仪了
        if (event.currentMicUuid == AccountHelper.getAccountUuid()){
            mWeddingRoomBean?.weddingRole = ChatRoomWeddingRole.MC
        }
        mRoomView?.onMicRoleChange()

    }

    /**
     * 加入频道成功
     */
    fun onJoinChannelSuccess(isAnchor: Boolean) {
        //不是主播且是在麦上的。
        if (!isAnchor && mCommonOperationHelper?.mApplyMicStatus == ChatRoomConstants.APPLY_MICROPHOME_SUCCESS) {
            val role = mWeddingRoomBean?.weddingRole ?: ChatRoomWeddingRole.NOT
            //有角色的，需要手动获取一下权限，如果同意，切换到主播角色且开麦
            if (role != ChatRoomWeddingRole.NOT) {
                ChatRoomPermissionUtils.checkChatRoom(ActivityUtils.getTopActivity(),
                    mCommonOperationHelper?.mChatRoomType ?: ChatRoomType.WEDDING,
                    object : ChatRoomPermissionUtils.SimplePermissionListener() {
                        override fun onAllGranted() {
                            mCommonOperationHelper?.onJoinChannel(
                                ChatRoomJoinChannelScene.SWITCH_MIC,
                                ChatRoomJoinChannelAnchor.ANCHOR
                            )
                        }

                        override fun onDenied(perms: MutableList<String>?) {
                            super.onDenied(perms)
                            mCommonOperationHelper?.apply {
                                upLoadMicStatus(!mIsMuteLocalVoice)
                            }
                        }
                    })
            }
        }
    }

    /**
     * 注销释放
     */
    fun destroy() {
        EventBusUtils.unregister(this)
        mWeddingBgmHelper?.stopBgmMusic()
        mWeddingBgmHelper?.destroy()
        cancelRedPacketCountDownTimer()
        mRoomView = null
        mCommonOperationHelper = null

    }
}