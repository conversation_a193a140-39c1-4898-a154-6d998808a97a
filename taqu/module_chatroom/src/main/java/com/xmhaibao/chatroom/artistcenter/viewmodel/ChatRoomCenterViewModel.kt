
package com.xmhaibao.chatroom.artistcenter.viewmodel
import android.app.Application
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.xmhaibao.chatroom.artistcenter.bean.ChatRoomCenterBannerBean
import com.xmhaibao.chatroom.artistcenter.bean.ChatRoomCenterConfigBean
import com.xmhaibao.chatroom.artistcenter.bean.ChatRoomCenterInfoBean
import com.xmhaibao.chatroom.artistcenter.constants.ChatRoomCenterConstants
import com.xmhaibao.chatroom.artistcenter.repository.ChatRoomCenterDataRepository
import com.xmhaibao.chatroom.groupchat.ChatRoomGroupChatHelper
import com.xmhaibao.chatroom.groupchat.bean.ChatRoomUserGroupInfoBean
import hb.common.data.AccountHelper
import hb.kotlin_extension.noneSyncLazy
import hb.xrequest.launchHttp
import hb.xstatic.mvvm.XBaseViewModel
/**
 * 聊天室房主- 艺人中心
 * @see 2023-08-11 新增需求：<a href="https://o15vj1m4ie.feishu.cn/wiki/QOAJwvb7jilm8Kk5lqVcjO9Ln21">艺人中心</a>
 * <AUTHOR>
 * @date 2023-08-11
 */
class ChatRoomCenterViewModel(application: Application) : XBaseViewModel(application) {
    val mCenterViewModelData = MutableLiveData<ChatRoomCenterInfoBean>()
    val mCenterBannerViewModelData = MutableLiveData<MutableList<ChatRoomCenterBannerBean>>()
    val mUserGroupInfoBean = MutableLiveData<ChatRoomUserGroupInfoBean?>()
    val mCenterConfig = MutableLiveData<ChatRoomCenterConfigBean?>()
    val repo by noneSyncLazy { ChatRoomCenterDataRepository() }


    /**
     * 获取艺人中心数据
     * @param pageKey 页面key
     */
    fun getCenterInfo(pageKey: String?) {
        viewModelScope.launchHttp({
            val data = repo.getChatRoomCenterInfo(pageKey)
            mCenterViewModelData.value = data
        },{
            hideLoadingBar()
            return@launchHttp false
        })
    }
    /**
     * 获取艺人中心-广告数据
     */
    fun getAddInfo() {
        viewModelScope.launchHttp({
            val bannerData = repo.getChatRoomCenterBanner()
            mCenterBannerViewModelData.value = bannerData
        })
    }

    /**
     * 获取当前用户 群聊信息
     */
    fun getUserGroupInfo() {
        ChatRoomGroupChatHelper.getUserGroupInfo(AccountHelper.getAccountUuid()) {
            mUserGroupInfoBean.value = it
        }
    }

    /**
     * 艺人中心-首页配置（含tab配置）
     */
    fun getCenterConfigBean() {
        viewModelScope.launchHttp({
            val data = repo.getCenterConfigBean()
            mCenterConfig.value = data
        })
    }
}