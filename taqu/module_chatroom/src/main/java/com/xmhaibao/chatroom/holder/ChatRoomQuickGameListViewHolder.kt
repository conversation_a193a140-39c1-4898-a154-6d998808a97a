package com.xmhaibao.chatroom.holder

import android.view.ViewGroup
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.bean.ChatRoomGameListBean
import com.xmhaibao.chatroom.databinding.ChatroomQuickGameListItemBinding
import hb.xadapter.XBaseViewHolder

/**
 * 聊天室游戏列表ViewHolder
 *
 * <AUTHOR>
 * @date 2021-11-22
 */
class ChatRoomQuickGameListViewHolder(parent: ViewGroup)
    : XBaseViewHolder<ChatRoomGameListBean.GameList>(parent, R.layout.chatroom_quick_game_list_item) {
    private val mBinding = ChatroomQuickGameListItemBinding.bind(itemView)

    override fun onBindView(item: ChatRoomGameListBean.GameList?) {
        item?.apply {
            mBinding.imgIcon.setImageFromUrl(cover)
            mBinding.tvGameName.text = title
        }
    }
}