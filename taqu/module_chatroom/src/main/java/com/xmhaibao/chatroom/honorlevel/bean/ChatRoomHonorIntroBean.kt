package com.xmhaibao.chatroom.honorlevel.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import hb.common.helper.HostHelper

/**
 * 聊天室-荣耀等级bean
 *
 * <AUTHOR>
 * @date 2022-09-08
 */
class ChatRoomHonorIntroBean : IDoExtra {

    var list: MutableList<Detail>? = null

    @SerializedName("glory_text")
    var gloryText: String? = ""

    @SerializedName("honor_level_title")
    var honorPageInfo: HonorPageInfo? = null

    class Detail : IDoExtra {
        @SerializedName("glory_key")
        var gloryKey: String? = ""
        var name: String? = ""
        var min: String? = ""
        var max: String? = ""
        var icon: String? = ""
        var progress: Float = 0f

        override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
            icon = HostHelper.getImageDefaultHost().getWebpUrl_4_1(icon)
        }
    }

    class HonorPageInfo {
        @SerializedName("level_table_detail")
        var levelTabDetail: String? = ""
        @SerializedName("level_table_title")
        var levelTabTitle: String? = ""
        @SerializedName("what_body_first_box")
        var whatBodyFirstBox: String? = ""
        @SerializedName("what_body_pack")
        var whatBodyPack: String? = ""
        @SerializedName("what_title")
        var whatTitle: String? = ""
    }

    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        list?.forEach { it.doExtra(response) }
    }
}