package com.xmhaibao.chatroom.dialog

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.CountDownTimer
import android.view.*
import com.xmhaibao.chatroom.databinding.ChatroomVideoPreviewDialogBinding
import com.xmhaibao.chatroom.mvp.ChatRoomTRTCVideoPresenter
import hb.utils.Loger
import hb.xstyle.xdialog.XLifecycleDialog

/**
 * <AUTHOR>
 * @date 2020/9/1
 * @desc 上麦时，倒计时预览
 */
public class ChatRoomVideoPreviewDialog : XLifecycleDialog {

    companion object {
        fun getInstance(context: Context, showTip: Boolean): ChatRoomVideoPreviewDialog {
            Loger.i("ChatRoomVideoPreviewDialog","上麦视频预览倒计时弹窗-触发")
            return  ChatRoomVideoPreviewDialog(context, showTip)
        }
    }

    var mChatRoomVideoPresenter: ChatRoomTRTCVideoPresenter? = null
    private var mShowTip: Boolean = false
    private lateinit var mBinding: ChatroomVideoPreviewDialogBinding

    constructor(context: Context, showTip: Boolean) : super(context) {
        mShowTip = showTip;
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = ChatroomVideoPreviewDialogBinding.inflate(LayoutInflater.from(context))
        setContentView(mBinding.root)
        setCanceledOnTouchOutside(false)
        setCancelable(false)
        mBinding.tvTipInfo.visibility = if (mShowTip) View.VISIBLE else View.GONE

        mBinding.mVideoViewChatPreview.post {
            mChatRoomVideoPresenter?.apply {
                stopLocalPreview()
                muteLocalVideoStream(true)
                muteLocalAudioStream(true)

                startLocalPreviewNoCheck(mBinding.mVideoViewChatPreview)

                object : CountDownTimer(6000, 1000) {
                    override fun onFinish() {
                        stopLocalPreview()
                        dismiss()
                    }

                    override fun onTick(millisUntilFinished: Long) {
                        mBinding.mTvChatPreviewTimeNum.text = "${millisUntilFinished / 1000}"
                    }
                }.start()
            }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        Loger.i("ChatRoomVideoPreviewDialog","上麦视频预览倒计时弹窗-关闭")

    }

    override fun show() {
        super.show()
        val window = window
        if (window != null) {
            val params = window.attributes
            params.gravity = Gravity.TOP
            params.width = WindowManager.LayoutParams.MATCH_PARENT
            params.height = WindowManager.LayoutParams.WRAP_CONTENT
            window.attributes = params
            params.dimAmount = 0.0f
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        }
    }
}