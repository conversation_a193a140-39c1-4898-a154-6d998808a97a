package com.xmhaibao.chatroom.feature.booklet.ui.home.viewmodel

import android.app.Application
import androidx.lifecycle.viewModelScope
import cn.taqu.lib.base.bean.UiState
import com.xmhaibao.chatroom.feature.booklet.bean.ChatRoomBookletHomeBroadBean
import com.xmhaibao.chatroom.feature.booklet.bean.ChatRoomBookletHomeInfoBean
import com.xmhaibao.chatroom.feature.booklet.repository.ChatRoomBookletRepository
import com.xmhaibao.common.api.compliance.model.GameAnnouncementBean
import hb.common.xstatic.viewmodel.BaseViewModel
import hb.kotlin_extension.noneSyncLazy
import hb.xrequest.awaitNullable
import hb.xrequest.launchHttp
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 聊天室/家族 闪光纪念册主页ViewModel
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
class ChatRoomBookletHomeViewModel(application: Application) : BaseViewModel(application) {
    private val repo by noneSyncLazy { ChatRoomBookletRepository() }

    companion object {
        const val BOOKLET_SN = "booklet"
    }

    /**
     * 主页信息数据
     */
    private val _homeInfoFlow = MutableStateFlow<ChatRoomBookletHomeInfoBean?>(null)
    val homeInfoFlow = _homeInfoFlow.asStateFlow()

    /**
     * 轮播数据
     */
    private val _broadInfoFlow = MutableStateFlow<ChatRoomBookletHomeBroadBean?>(null)
    val broadInfoFlow = _broadInfoFlow.asStateFlow()

    /**
     * 公告数据
     */
    private val _announcementFlow = MutableSharedFlow<UiState<GameAnnouncementBean>>(0, 1, BufferOverflow.DROP_OLDEST)
    val announcementFlow = _announcementFlow.asSharedFlow()

    /**
     * 请求首页数据
     */
    fun requestHomeInfo(accountUuid: String?) {
        viewModelScope.launchHttp({
            showLoadingBar()
            val data = repo.requestHomeInfo(accountUuid).awaitNullable()
            _homeInfoFlow.value = data
            hideLoadingBar()
        }, {
            hideLoadingBar()
            false
        })
    }

    /**
     * 请求轮播数据
     */
    fun requestBroadInfo() {
        viewModelScope.launchHttp({
            val data = repo.requestHomeBroadcastInfo().awaitNullable()
            _broadInfoFlow.value = data
        }, {
            true
        })
    }

    /**
     * 请求公告
     */
    fun requestAnnouncement() {
        viewModelScope.launchHttp({
            showLoadingBar()
            val data = repo.requestAnnouncement(BOOKLET_SN).awaitNullable()
            if (data != null) {
                _announcementFlow.tryEmit(UiState.Success(data))
            } else {
                _announcementFlow.tryEmit(UiState.Empty)
            }
            hideLoadingBar()
        }, {
            _announcementFlow.tryEmit(UiState.Empty)
            hideLoadingBar()
            false
        })
    }

}