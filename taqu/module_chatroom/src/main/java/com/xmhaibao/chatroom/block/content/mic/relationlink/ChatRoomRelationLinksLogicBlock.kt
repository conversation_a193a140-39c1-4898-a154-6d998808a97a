package com.xmhaibao.chatroom.block.content.mic.relationlink

import com.xmhaibao.chatroom.interf.IChatRoomSeatGroupView
import com.xmhaibao.chatroom.roomplay.auction.helper.ChatRoomRelationLinksHelper
import com.xmhaibao.chatroom.utils.ChatRoomTypeUtils
import hb.xblockframework.framework.base.BaseBlock
import hb.xblockframework.framework.join.IBlockContext
import hb.xblockframework.framework.utils.blockHandler

/**
 *
 * 聊天室座位关系链 展示 Block (拍拍关系置顶的处理)
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
class ChatRoomRelationLinksLogicBlock(blockContext: IBlockContext) : BaseBlock(blockContext),
    IChatRoomRelationLinksService {


    private val mRelationLinksHelper by lazy {
        blockHandler().pageLifecycleOwner?.let {
            ChatRoomRelationLinksHelper(
                it
            )
        }
    }

    override fun bindBlockService(): Class<*>? {
        return IChatRoomRelationLinksService::class.java
    }

    override fun onMicPositionChange(chatRoomType: Int, seatGroupView: IChatRoomSeatGroupView?) {
        // 目前 仅支持普通房和KTV房 主题房
        val support =
            ChatRoomTypeUtils.isNormalRoomType(chatRoomType) || ChatRoomTypeUtils.isKtvRoomType(
                chatRoomType
            ) || ChatRoomTypeUtils.isThemeRoom(chatRoomType)
        if (support) {
            mRelationLinksHelper?.onMicPositionChange(chatRoomType, seatGroupView)
        }

    }

    override fun onReset() {
        mRelationLinksHelper?.onReset()
    }

    override fun onDestroy() {
        mRelationLinksHelper?.destroy()
        super.onDestroy()
    }


}