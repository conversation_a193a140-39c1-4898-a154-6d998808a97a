package com.xmhaibao.chatroom.rank.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import cn.taqu.lib.base.router.RouterLaunch
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.databinding.ChatroomFamilyRanksTopThirdViewBinding
import com.xmhaibao.chatroom.databinding.ChatroomRanksTopThirdStyleOneViewBinding
import com.xmhaibao.chatroom.rank.bean.ChatRoomFamilyRanksItemBean
import com.xmhaibao.chatroom.rank.bean.ChatRoomRanksItemBean
import com.xmhaibao.chatroom.rank.bean.ChatRoomRanksRoomInnerItemBean
import com.xmhaibao.chatroom.rank.constants.ChatRoomRankTabType
import com.xmhaibao.chatroom.rank.tracker.ChatRoomRanksTracker
import com.xmhaibao.chatroom.rank.utils.ChatRoomRanksUtils
import com.xmhaibao.chatroom.roomplay.auction.utils.AuctionViewUtils
import com.xmhaibao.chatroom.utils.ChatRoomUtils
import hb.drawable.shape.view.HbTextView
import hb.utils.ColorUtils
import hb.utils.SizeUtils
import hb.utils.StringUtils
import hb.ximage.fresco.BaseDraweeView
import java.math.BigDecimal

/**
 * 聊天室 家族榜单优化 top 3
 * @date 2024-10-15
 */
class ChatRoomFamilyRanksTopThirdView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr),OnClickListener {

    private var mBinding: ChatroomFamilyRanksTopThirdViewBinding? = null
    private var mRankTabType: String? = ""
    private var mSource: String? = null

    init {
        mBinding = ChatroomFamilyRanksTopThirdViewBinding.inflate(LayoutInflater.from(context), this)
        mBinding?.apply {
            bdvTopOneImage.setOnClickListener(this@ChatRoomFamilyRanksTopThirdView)
            bdvTopTwoImage.setOnClickListener(this@ChatRoomFamilyRanksTopThirdView)
            bdvTopThirdImage.setOnClickListener(this@ChatRoomFamilyRanksTopThirdView)
        }
    }



    /**
     * 设置top3数据
     *
     * @param topThirdInfo 数据
     */
    fun setTopThirdInfo(
        topThirdInfo: MutableList<ChatRoomFamilyRanksItemBean>?, rankTabType: String?, source: String?,
    ) {
        mRankTabType = rankTabType
        mSource = source
        for (index in 0..2) {
            var itemData: ChatRoomFamilyRanksItemBean? = null
            topThirdInfo?.apply {
                if (this.size > index) {
                    itemData = this[index]
                }
            }
            mBinding?.apply {
                when (index) {
                    0 -> {
                        updateUi(
                            0, itemData,
                            bdvTopOneImage, bdvTopOneImageEmpty, tvTopOneValue,
                            tvTopOneName, tvTopOnePlayName,ivTopOneSexType,tvTopOneCityName,tvTopOneEmpty,
                            tvTopOneRoomStatus,oneFamilyLevelView
                        )
                    }

                    1 -> {
                        updateUi(
                            1, itemData,
                            bdvTopTwoImage, bdvTopTwoImageEmpty, tvTopTwoValue,
                            tvTopTwoName, tvTopTwoPlayName,ivTopTwoSexType,tvTopTwoCityName,tvTopTwoEmpty,
                            tvTopTwoRoomStatus,twoFamilyLevelView

                        )
                    }

                    2 -> {
                        updateUi(
                            2, itemData,
                            bdvTopThirdImage, bdvTopThirdImageEmpty, tvTopThirdValue,
                            tvTopThirdName, tvTopThirdPlayName,ivTopThirdSexType,tvTopThirdCityName,tvTopThirdEmpty,
                            tvTopThirdRoomStatus,thirdFamilyLevelView
                        )
                    }
                }
            }
        }


    }
    /**
     * 更新ui
     */
    private fun updateUi(
        index: Int,
        dataBean: ChatRoomFamilyRanksItemBean?,
        bdvTopOneImage: BaseDraweeView,
        bdvTopOneImageEmpty: BaseDraweeView,
        tvTopOneValue: TextView,
        tvTopOneName: TextView,
        tvTopOnePlayName: TextView,
        ivTopOneSexType: ImageView,
        tvTopOneCityName: TextView,
        tvTopOneEmpty: TextView,
        tvTopOneRoomStatus: TextView,
        oneFamilyLevelView: ChatRoomFamilyRanksFamilyLevelView,
    ) {
        bdvTopOneImage.tag = dataBean
        if (mRankTabType == ChatRoomRankTabType.PERSON_CONTRIBUTION_RANK) {
            bdvTopOneImage.setCornersRadius(SizeUtils.dp2px(64f).toFloat())
        } else {
            bdvTopOneImage.setCornersRadius(SizeUtils.dp2px(8f).toFloat())
        }
        if (dataBean == null) {
            tvTopOneEmpty.isVisible = true
            bdvTopOneImage.setImageFromResource(R.drawable.chatroom_family_ranks_empty_ic)
            bdvTopOneImage.isVisible = true

            bdvTopOneImageEmpty.isInvisible = true
            tvTopOneValue.isInvisible = true
            tvTopOneName.isInvisible = true
            tvTopOnePlayName.isInvisible = true
            ivTopOneSexType.isInvisible = true
            tvTopOneCityName.isInvisible = true
            tvTopOneRoomStatus.isVisible = false
            oneFamilyLevelView.isVisible = false
            return
        }
        tvTopOneEmpty.isVisible = false
        bdvTopOneImage.isVisible = true
        bdvTopOneImageEmpty.isVisible = true
        tvTopOneValue.isVisible = true
        tvTopOneName.isVisible = true
        dataBean.apply {
            if (mRankTabType == ChatRoomRankTabType.PERSON_CONTRIBUTION_RANK) {
                bdvTopOneImage.setImageFromUrl(avatar)
                when (index) {
                    1 -> {
                        bdvTopOneImageEmpty.setImageFromResource(R.drawable.chatroom_family_ranks_top_2_2_bg)
                    }

                    2 -> {
                        bdvTopOneImageEmpty.setImageFromResource(R.drawable.chatroom_family_ranks_top_2_3_bg)
                    }

                    else -> {
                        bdvTopOneImageEmpty.setImageFromResource(R.drawable.chatroom_family_ranks_top_2_1_bg)
                    }
                }
            } else {
                bdvTopOneImage.setImageFromUrl(coverUrl)
                when (index) {
                    1 -> {
                        bdvTopOneImageEmpty.setImageFromResource(R.drawable.chatroom_family_ranks_top_1_2_bg)
                    }

                    2 -> {
                        bdvTopOneImageEmpty.setImageFromResource(R.drawable.chatroom_family_ranks_top_1_3_bg)
                    }

                    else -> {
                        bdvTopOneImageEmpty.setImageFromResource(R.drawable.chatroom_family_ranks_top_1_1_bg)
                    }
                }
            }
            tvTopOneValue.text = "家族值·${ChatRoomUtils.getCommonVolumeOfW(StringUtils.stringToLong(score), BigDecimal.ROUND_DOWN)}"
            tvTopOneName.text = ChatRoomRanksUtils.dealTextMaxLength(name, 10)
            tvTopOnePlayName.isVisible = roomStatus == "1"
            tvTopOneRoomStatus.isVisible = roomStatus == "1"
            ChatRoomRanksUtils.setSexType(ivTopOneSexType, sexType)
            tvTopOneCityName.isVisible = !city.isNullOrEmpty()
            tvTopOneCityName.text = city
            oneFamilyLevelView.setData(this)
        }
    }

    override fun onClick(v: View?) {
        mBinding?.apply {
            if (v == bdvTopOneImage || v == bdvTopTwoImage || v == bdvTopThirdImage) {
                val data = v.tag as? ChatRoomFamilyRanksItemBean
                if (!data?.relation.isNullOrEmpty()) {
                    RouterLaunch.dealJumpData(context, data?.relation)
                    ChatRoomRanksTracker().trackFamilyRanksItemClick(mSource)
                }
            }
        }
    }
}
