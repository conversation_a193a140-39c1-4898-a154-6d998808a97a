package com.xmhaibao.chatroom.view;

import android.content.Context;
import android.os.Bundle;
import android.view.View;

import com.ushengsheng.widget.BaseBottomDialog;
import com.xmhaibao.chatroom.R;
import com.xmhaibao.chatroom.model.event.EventGameTool;
import com.xmhaibao.chatroom.msg.ChatRoomMsgPresenter;


/**
 * 功能:
 * Created by hqy on 2018/2/27.
 */

public class ChatRoomGameToolDiaolg extends BaseBottomDialog implements View.OnClickListener {

    private ChatRoomMsgPresenter mEventPresenter;

    public ChatRoomGameToolDiaolg(Context context, ChatRoomMsgPresenter eventPresenter) {
        super(context);
        this.mEventPresenter = eventPresenter;
    }

    public ChatRoomGameToolDiaolg(Context context, int theme) {
        super(context, theme);
    }

    public ChatRoomGameToolDiaolg(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setDimAmount(0);
        findViewById(R.id.imgRockScissorsPaper).setOnClickListener(this);
        findViewById(R.id.imgDice).setOnClickListener(this);
        findViewById(R.id.imgRandom).setOnClickListener(this);
        findViewById(R.id.imgLightsUp).setOnClickListener(this);
        findViewById(R.id.imgLightsOut).setOnClickListener(this);

    }

    @Override
    protected int getLayoutResId() {
        return R.layout.chatroom_game_tool_dialog;
    }


    @Override
    public void onClick(View view) {
        int i = view.getId();
        if (mEventPresenter == null) {
            return;
        }
        if (i == R.id.imgRockScissorsPaper) {
            mEventPresenter.requestGame(EventGameTool.TYPE_ROCK_SCISSORS_PAPER);
        } else if (i == R.id.imgDice) {
            mEventPresenter.requestGame(EventGameTool.TYPE_DICE);
        } else if (i == R.id.imgRandom) {
            mEventPresenter.requestGame(EventGameTool.TYPE_RANDOM);
        } else if (i == R.id.imgLightsUp) {
            mEventPresenter.requestGame(EventGameTool.TYPE_LIGHTS, "1");
        } else if (i == R.id.imgLightsOut) {
            mEventPresenter.requestGame(EventGameTool.TYPE_LIGHTS, "0");
        }
    }
}
