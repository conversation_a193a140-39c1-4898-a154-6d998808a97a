package com.xmhaibao.chatroom.roomplay.sud.model

import com.xmhaibao.chatroom.constants.ChatRoomGameSn
import com.xmhaibao.library_game_sud_mgp.model.GameConfigModel

/**
 * 聊天室Sud游戏配置类
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
class ChatRoomSudGameConfigModel {

    companion object {
        fun get(gameSn: String?): GameConfigModel {
            if (gameSn == ChatRoomGameSn.SUD_WHO_IS_THE_SPY) {
                return WhoIsTheSpyConfigModel()
            }
            return getDefault()
        }

        fun getDefault() = GameConfigModel()
    }

    /**
     * 谁是卧底游戏配置
     */
    private class WhoIsTheSpyConfigModel : GameConfigModel() {
        init {
            ui.apply {
                // 分享按钮自定义点击处理
                share_btn.custom = true
                // 隐藏分享按钮
                share_btn.hide = false
                // 加入按钮自定义点击处理
                join_btn.custom = true
                // 接管开始游戏按钮
                start_btn.custom = true
                // 取消加入按钮自定义点击处理
                cancel_join_btn.custom = true
                // 大厅头像自定义点击处理
                lobby_players.custom = true
                // 隐藏踢人按钮
                lobby_player_kickout_icon.hide = true
                // app接管声音播放
                gameSoundControl = 1
                // 隐藏版本号
                version.hide = true
            }
        }
    }

}