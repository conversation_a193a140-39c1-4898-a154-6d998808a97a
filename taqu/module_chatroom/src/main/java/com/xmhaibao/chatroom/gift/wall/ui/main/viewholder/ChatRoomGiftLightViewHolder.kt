package com.xmhaibao.chatroom.gift.wall.ui.main.viewholder

import android.view.ViewGroup
import androidx.core.view.updateLayoutParams
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.databinding.ChatroomGiftWallGiftLightBlueItemBinding
import com.xmhaibao.chatroom.gift.wall.bean.ChatRoomGiftWallLightBean
import com.xmhaibao.chatroom.gift.wall.constant.ChatRoomGiftWallConstant
import com.xmhaibao.chatroom.rank.utils.ChatRoomRanksUtils
import dp
import hb.kotlin_extension.gone
import hb.kotlin_extension.visible
import hb.utils.ScreenUtils
import hb.utils.StringUtils
import hb.xadapter.XBaseViewHolder


/**
 *
 * [礼物墙-礼物列表 已点亮 item viewHolder](文档地址url)
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
class ChatRoomGiftLightViewHolder(parent: ViewGroup) : XBaseViewHolder<ChatRoomGiftWallLightBean>(
    parent, R.layout.chatroom_gift_wall_gift_light_blue_item
) {
    private val binding = ChatroomGiftWallGiftLightBlueItemBinding.bind(itemView)

    init {
        val screenWidth = ScreenUtils.getScreenWidth()
        val itemWidth = if (screenWidth > 0) {
            (screenWidth - (24 + 30).dp) / 4
        } else {
            80.dp
        }
        binding.root.updateLayoutParams {
            width = itemWidth
        }
    }

    override fun onBindView(item: ChatRoomGiftWallLightBean?) {
        if (item == null) {
            return
        }
        binding.apply {
            tvGiftNum.visible()
            tvGiftNum.text = item.num.toString()
            val name = ChatRoomRanksUtils.dealTextMaxLength(item.name, 8)
            tvGiftName.text = name
            ivGiftIcon.setImageFromUrl(item.icon)
            tvGiftNum.text = StringUtils.getString(R.string.chatroom_x_count, item.num.toString())
            if (item.type == ChatRoomGiftWallConstant.TYPE_SEND) {
                llGiftRankDesc.gone()
                ratingBar.gone()
            } else {
                ivAvatar.setImageFromUrl(item.namingAvatar)
                llGiftRankDesc.visible()
                ratingBar.visible()
                ratingBar.rating = item.stars.toFloat()
            }
            tvGiftStatus.text = StringUtils.getString(R.string.chatroom_already_light)

        }

    }
}