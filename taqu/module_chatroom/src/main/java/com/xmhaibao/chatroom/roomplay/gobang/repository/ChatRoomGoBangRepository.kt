package com.xmhaibao.chatroom.roomplay.gobang.repository

import cn.taqu.lib.base.api.UrlBase
import cn.taqu.lib.base.common.XjbApplicationHelper
import com.xmhaibao.chatroom.roomplay.gobang.bean.*
import hb.common.xstatic.HttpParams
import hb.utils.RandomUtil
import hb.xrequest.XRequest
import hb.xrequest.await

/**
 * 聊天室-五子棋相关接口
 *
 * <AUTHOR>
 * @date 2022/5/5
 */
class ChatRoomGoBangRepository : UrlBase() {
        /**
         * 聊天室五子棋-首页
         */
        fun getGoBangMatchHome(): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .get("$API_LIVE/ChatGobang/home")
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋 匹配
         */
        fun getGoBangMatch(matchTimes: Int): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .get("$API_LIVE/ChatGobang/match")
                .params("match_times", matchTimes.toString())
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋 倒计时结束开始游戏
         */
        fun setGoBangStart(chatUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_LIVE/ChatGobangProcess/normalStartGame")
                .needTicketId(true)
                .params("chat_uuid", chatUuid)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋 用户落子
         */
        fun setGoBangFall(chatUuid: String?, groupId: String?, x: Int, y: Int): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_LIVE/ChatGobangProcess/accountFall")
                .needTicketId(true)
                .params("chat_uuid", chatUuid)
                .params("group_id", groupId)
                .params("x", x.toString())
                .params("y", y.toString())
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋 重开游戏
         */
        fun setGoBangRestart(chatUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_LIVE/ChatGobangProcess/restartGame")
                .needTicketId(true)
                .params("chat_uuid", chatUuid)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋 落子超时
         */
        fun setGoBangTimeout(chatUuid: String?, fallAccountUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_LIVE/ChatGobangProcess/accountFallTimeout")
                .needTicketId(true)
                .params("chat_uuid", chatUuid)
                .params("fall_account", fallAccountUuid)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室 -  五子棋关闭游戏
         */
        fun goBangClose(chatUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder().post("$API_LIVE/ChatGobangProcess/closeGameMode")
                .params("chat_uuid", chatUuid)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }


        /**
         * 聊天室 -  五子棋游戏结果
         */
        suspend fun goBangGetResultInfo(chatUuid: String?): ChatRoomGoBangResultBean {
            return XRequest.get("$API_LIVE/GobangRank/showGobangResult")
                .params("chat_uuid", chatUuid)
                .needTicketId(true)
                .asRequest<ChatRoomGoBangResultBean>().await()
        }

        /**
         * 聊天室 -  五子棋投降认输 或者拒绝
         */
        fun goBangGetSurrender(status: String, chatUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder().post("$API_LIVE/ChatGobangProcess/agreeGiveUpGame")
                .params("value", status)
                .params("chat_uuid", chatUuid)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室 - 五子棋段位赛 投降认输 或者拒绝
         */

        fun goBangGradeGetSurrender(status: String, chatUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder().post("$API_CHATROOM/GobangGradeGame/agreeGiveUpGame")
                .params("opera", status)
                .params("chat_uuid", chatUuid)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室 -  五子棋投降认输数据
         */
        suspend fun goBangGetSurrenderInfo(chatUuid: String?): ChatRoomGoBangSurrenderBean {
            return XRequest.post("$API_LIVE/ChatGobangProcess/giveUpGame")
                .params("chat_uuid", chatUuid)
                .needTicketId(true)
                .asRequest<ChatRoomGoBangSurrenderBean>()
                .await()
        }

        /**
         * 聊天室 - 五子棋段位赛 投降认输数据
         */
        suspend fun goBangGradeGetSurrenderInfo(chatUuid: String?): ChatRoomGoBangSurrenderBean {
            return XRequest.post("$API_CHATROOM/GobangGradeGame/giveUpGame")
                .params("chat_uuid", chatUuid)
                .needTicketId(true)
                .asRequest<ChatRoomGoBangSurrenderBean>()
                .await()
        }

        /**
         * 聊天室 -  五子棋上麦
         */
        fun goBangJoinMic(seatNum: String?, chatUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder().post("$API_LIVE/ChatGobangProcess/joinGame")
                .params("seat_num", seatNum)
                .params("chat_uuid", chatUuid)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室 -  五子棋准备
         */
        fun goBangPrepare(chatUuid: String?, status: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder().post("$API_LIVE/ChatGobangProcess/setReadyStatus")
                .params("status", status)
                .params("chat_uuid", chatUuid)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室 -  五子棋游戏开始，倒计时5s
         */
        fun goBangStartGame(chatUuid: String?): XRequest<ChatRoomGoBangStartGameBean> {
            val httpParams = HttpParams.newBuilder().post("$API_LIVE/ChatGobangProcess/startGame")
                .params("chat_uuid", chatUuid)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室 -  五子棋开启
         */
        fun goBangOpen(chatUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder().post("$API_LIVE/ChatGobangProcess/openGameMode")
                .params("chat_uuid", chatUuid)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋段位赛 落子超时
         */
        fun setGoBangGradeTimeout(chatUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_CHATROOM/GobangGradeGame/fallTimeout")
                .needTicketId(true)
                .params("chat_uuid", chatUuid)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室 -  五子棋悔棋需要的数据
         */
        suspend fun goBangGetBackInfo(chatUuid: String?): ChatRoomGoBangBackInfoBean {
            return XRequest.post("$API_LIVE/ChatGobangProcess/getRevokeInfo")
                .params("chat_uuid", chatUuid)
                .needTicketId(true)
                .asRequest<ChatRoomGoBangBackInfoBean>()
                .await()
        }

        /**
         * 聊天室 -  五子棋段位赛 悔棋需要的数据
         */
        suspend fun goBangGradeGetBackInfo(chatUuid: String?): ChatRoomGoBangBackInfoBean {
            return XRequest.post("$API_CHATROOM/GobangGradeGame/revokeInfo")
                .params("chat_uuid", chatUuid)
                .needTicketId(true)
                .asRequest<ChatRoomGoBangBackInfoBean>()
                .await()
        }

        /**
         * 聊天室 -  五子棋悔棋
         */
        fun goBangBack(chatUuid: String?, count: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder().post("$API_LIVE/ChatGobangProcess/accountRevoke")
                .params("chat_uuid", chatUuid)
                .params(
                    "code",
                    hb.utils.StringUtils.changeStringToMD5(
                        XjbApplicationHelper.getInstance().submitMark + RandomUtil.getRandom(9999)
                    )
                )
                .params("count", count)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室 -  五子棋段位赛悔棋
         */
        fun goBangGradeBack(chatUuid: String?, count: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder().post("$API_CHATROOM/GobangGradeGame/revoke")
                .params("chat_uuid", chatUuid)
                .params(
                    "code",
                    hb.utils.StringUtils.changeStringToMD5(
                        XjbApplicationHelper.getInstance().submitMark + RandomUtil.getRandom(9999)
                    )
                )
                .params("count", count)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋段位赛 用户落子
         */
        fun setGoBangGradeFall(chatUuid: String?, x: Int, y: Int): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_CHATROOM/GobangGradeGame/fall")
                .needTicketId(true)
                .params("chat_uuid", chatUuid)
                .params("x", x.toString())
                .params("y", y.toString())
                .build()
            return XRequest.newRequest(httpParams)
        }


        /**
         *  聊天室-五子棋段位赛 游戏结果
         */
        suspend fun goBangGradeGetResultInfo(chatUuid: String?): ChatRoomGoBangGradeResultBean {
            return XRequest.get("$API_CHATROOM/GobangGrade/gameResult")
                .params("chat_uuid", chatUuid)
                .needTicketId(true)
                .asRequest<ChatRoomGoBangGradeResultBean>()
                .await()
        }

        /**
         * 聊天室-五子棋段位赛 首页
         */
        fun getGoBangGradeHome(teamUuid: String?): XRequest<ChatRoomGoBangRankMatchBean?> {
            val httpParams = HttpParams.newBuilder()
                .get("$API_CHATROOM/GobangGrade/home")
                .needTicketId(true)
                .params("team_uuid", teamUuid)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 五子棋段位赛，玩游戏的用户退出房间
         */
        fun onQuitGoBangGrade(chatUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_CHATROOM/GobangGradeGame/quitGame")
                .needTicketId(true)
                .params("chat_uuid", chatUuid)
                .build()
            return XRequest.newRequest(httpParams)
        }


        /**
         * 聊天室-五子棋段位赛 开启匹配
         */
        fun startGoBangGradeMatch(teamUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_CHATROOM/GobangGrade/startMatch")
                .params("team_uuid", teamUuid)
                .params("version", "1")
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋段位赛 取消匹配
         */
        fun cancelGoBangGradeMatch(teamUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_CHATROOM/GobangGrade/cancelMatch")
                .params("team_uuid", teamUuid)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋段位赛 队员准备
         */
        fun startGoBangGradePrepare(teamUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_CHATROOM/GobangGrade/teammatePrepare")
                .params("team_uuid", teamUuid)
                .params("version", "1")
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋段位赛 队员取消准备
         */
        fun cancelGoBangGradePrepare(teamUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_CHATROOM/GobangGrade/teammateCancelPrepare")
                .params("team_uuid", teamUuid)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋段位赛 接受匹配组队邀请
         */
        fun acceptGoBangGradeMatchInvite(teamUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_CHATROOM/GobangGrade/acceptInvite")
                .params("team_uuid", teamUuid)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋段位赛 接受匹配组队邀请
         */
        fun refuseGoBangGradeMatchInvite(teamUuid: String?, inviteAccountUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_CHATROOM/GobangGrade/refuseInvite")
                .params("team_uuid", teamUuid)
                .params("invite_account_uuid", inviteAccountUuid)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋段位赛 接受匹配组队邀请
         */
        fun inviteGoBangGradeMatchTeammate(teamUuid: String?, inviteAccountUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_CHATROOM/GobangGrade/inviteTeammate")
                .params("team_uuid", teamUuid)
                .params("invite_account_uuid", inviteAccountUuid)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 五子棋段位赛-游戏成员准备信息
         */
        fun getGoBangGradePrepareInfo(pkuuid: String?,times:String): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .get("$API_CHATROOM/GobangGradeGame/membersPrepareInfo")
                .params("chat_uuid", pkuuid)
                .params("times", times)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋段位赛匹配页面 发送消息
         */
        fun sendGoBangGradeMatchMessage(message: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_CHATROOM/GobangGrade/sendMatchChatMsg")
                .params("message", message)
                .params("check_device_id", XjbApplicationHelper.getInstance().smDeviceId)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋段位赛匹配 退出队伍
         */
        fun quitGoBangGradeMatchTeam(teamUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_CHATROOM/GobangGrade/quitTeam")
                .params("team_uuid", teamUuid)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋段位赛 上报队伍中心跳接口
         */
        fun reportGoBangGradeMatchTeam(isReset: Boolean, teamUuid: String?, captainUuid: String?, memberUuid: String?): XRequest<ChatRoomGoBangGradeReportTeamBean?> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_CHATROOM/GobangGrade/reportTeamInfo")
                .needTicketId(true)
                .params("team_uuid", teamUuid)
                .params("captain_uuid", captainUuid)
                .params("member_uuid", memberUuid)
                .params("reset", if (isReset) "1" else "0")
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 聊天室-五子棋段位赛 房间信息
         */
        fun getGoBangGradeGameInfo(chatUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .get("$API_CHATROOM/GobangGradeGame/gameInfo")
                .needTicketId(true)
                .params("chat_uuid", chatUuid)
                .build()
            return XRequest.newRequest(httpParams)
        }


        /**
         * 聊天室-五子棋段位赛 设置段位赛开启提醒
         */
        fun setGoBangGradeNotice(isOpen: Boolean): XRequest<Any> {
            val httpParams = HttpParams.newBuilder()
                .post("$API_CHATROOM/GobangGrade/switchSeasonNotice")
                .params("switch", if (isOpen) "1" else "0")
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

        /**
         * 邀请别人来聊天室 加入游戏-五子棋
         */
        fun inviteFriendJoinGameGoBang(chatUuid: String?, friendUuid: String?): XRequest<Any> {
            val httpParams = HttpParams.newBuilder().post("$API_LIVE/GobangInvite/invitePlayGobang")
                .params("chat_uuid", chatUuid)
                .params("friend_uuids", friendUuid)
                .needTicketId(true)
                .build()
            return XRequest.newRequest(httpParams)
        }

}