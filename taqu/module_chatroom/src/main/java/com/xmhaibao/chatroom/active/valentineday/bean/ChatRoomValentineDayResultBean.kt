package com.xmhaibao.chatroom.active.valentineday.bean

import cn.taqu.lib.okhttp.model.IDoExtra
import cn.taqu.lib.okhttp.model.IResponseInfo
import com.google.gson.annotations.SerializedName
import hb.common.helper.HostHelper

/**
 *

 * <AUTHOR>
 * @date 2024-07-10
 */
class ChatRoomValentineDayResultBean : IDoExtra {
    @SerializedName("team_name")
    var teamName: String? = null
    var prize: String? = null
    var animation: String? = null
    @SerializedName("animation_stub")
    var animationStub: String? = null


    @SerializedName("reward_content")
    var rewardContent: String? = null
    var list: MutableList<ChatRoomValentineDayResultItemBean>? = null
    override fun doExtra(response: IResponseInfo<out IResponseInfo<*>>?) {
        list?.forEach {
            it.doExtra(response)
        }
        animationStub = HostHelper.getImageDefaultHost().getWebpUrl_4_1(animationStub)
    }
}