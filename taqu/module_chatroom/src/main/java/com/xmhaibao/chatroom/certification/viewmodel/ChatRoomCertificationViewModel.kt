package com.xmhaibao.chatroom.certification.viewmodel

import android.app.Application
import androidx.lifecycle.viewModelScope
import com.xmhaibao.chatroom.certification.ChatRoomCertificationLogicLayer
import com.xmhaibao.chatroom.certification.repository.ChatRoomCertificationRepository
import hb.common.xstatic.viewmodel.BaseViewModel
import hb.kotlin_extension.noneSyncLazy
import hb.utils.Loger
import hb.utils.ToastUtils
import hb.xrequest.asAwait
import hb.xrequest.launchHttp

/**
 *
 * [聊天室-上麦实名认证](文档地址url)
 *
 * <AUTHOR>
 * @date 2024-08-01
 */
class ChatRoomCertificationViewModel(application: Application) : BaseViewModel(application) {
    companion object {
        const val TAG = "ChatRoomCertificationViewModel"


    }

    val repo by noneSyncLazy { ChatRoomCertificationRepository() }

    /**
     * 上麦实名认证 领取奖励
     */
    fun postRewardRealName(action: Int, chatRoomType: String) {
        Loger.d(TAG, "postRewardRealName request: chatRoomType $chatRoomType, action $action")
        viewModelScope.launchHttp({
            val data = repo.postRewardRealName(action, chatRoomType).asAwait().awaitNullable()
            Loger.i(TAG, "postRewardRealName success:${data?.tips}")
            if (data?.tips?.isEmpty() != true) {
                ToastUtils.show(data?.tips)
            }
        }, { responseException ->
            Loger.i(TAG, "postRewardRealName error:${responseException.message}")
            false
        })
    }

    /**
     * 检测实名认证
     */
    fun postCheckRealName(action: Int, chatRoomType: String, onSuccess: () -> Unit) {
        viewModelScope.launchHttp({
            repo.postCheckRealName(action, chatRoomType).asAwait().awaitNullable()
            onSuccess()
        }, { responseException ->
            Loger.i(TAG, "postCheckRealName error:${responseException.message}")
            ChatRoomCertificationLogicLayer.launchRealName(
                responseException.responseStatus, action
            )

            false
        })
    }

}