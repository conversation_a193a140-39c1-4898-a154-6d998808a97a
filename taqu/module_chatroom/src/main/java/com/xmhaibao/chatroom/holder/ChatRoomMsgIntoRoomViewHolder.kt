package com.xmhaibao.chatroom.holder

import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import cn.taqu.lib.base.im.OnMsgItemClickListener
import cn.taqu.lib.base.im.widght.MyNameClickableSpan
import cn.taqu.lib.base.live.model.event.EventMsgBase
import com.xmhaibao.chatroom.databinding.ChatroomMsgIntoRoomViewBinding
import com.xmhaibao.chatroom.model.event.EventMsgIntoRoom
import com.xmhaibao.chatroom.radiant.ChatRoomRadiantHelper
import com.xmhaibao.chatroom.radiant.ChatRoomRadiantHelper.nickName
import com.xmhaibao.chatroom.tracker.ChatRoomCommonTracker
import hb.utils.ColorUtils
import hb.utils.SizeUtils
import hb.utils.SpanUtils
import hb.utils.StringUtils

/**
 * 聊天室-进场消息ViewHolder
 *
 * <AUTHOR>
 * @date 2022-03-24
 */
class ChatRoomMsgIntoRoomViewHolder(
    var mViewBinding: ChatroomMsgIntoRoomViewBinding,
    var mOnMsgClickListener: OnMsgItemClickListener?,
) : RecyclerView.ViewHolder(mViewBinding.root) {
    private val clickSpan = MyNameClickableSpan(mOnMsgClickListener)

    constructor(parent: ViewGroup, onMsgItemClickListener: OnMsgItemClickListener) :
            this(ChatroomMsgIntoRoomViewBinding.inflate(LayoutInflater.from(parent.context)
                , parent, false), onMsgItemClickListener)

    fun setData(event: EventMsgBase?) {
        if (event is EventMsgIntoRoom) {
            mViewBinding.apply {
                clickSpan.setEventMsgBase(event)
                imgAvatar.setImageFromUrl(event.avatar)
                imgAvatar.setOnClickListener {
                    ChatRoomCommonTracker.trackChatRoomMsgHeadClick(event.accountUuid)
                    clickSpan.onClick(it)
                }
                imgAvatar.isHapticFeedbackEnabled = false
                imgAvatar.setOnLongClickListener {
                    mOnMsgClickListener?.onItemLongClick(event.accountUuid, event)
                    true
                }
                tvContent.isHapticFeedbackEnabled = false
                tvContent.setOnLongClickListener {
                    mOnMsgClickListener?.onItemLongClick(event.accountUuid, event)
                    true
                }
                val content = if (StringUtils.isNotEmpty(event.weddingRoomIntoMsg)) {
                    //婚礼房
                    event.weddingRoomIntoMsg
                } else {
                    if (StringUtils.isNotEmpty(event.weddingIntoSlogan)) {
                        event.weddingIntoSlogan
                    } else {
                        if (StringUtils.isNotEmpty(event.blindDateRoomIntoMsg)) {
                            //相亲房进房消息
                            event.blindDateRoomIntoMsg
                        } else {
                            "来了"
                        }
                    }
                }
                // 进场消息 公屏里面的
                tvContent.text = SpanUtils()
                    .append(event.nickName)
                    .setForegroundColor(ColorUtils.getColor("#FFCA44"))
                    .setClickSpan(clickSpan)
                    .appendSpace(SizeUtils.dp2px(2f))
                    .append(content)
                    .create()
                tvContent.movementMethod = LinkMovementMethod.getInstance()

                //萌新标签
                ivLabel.isVisible = event.blindDateNewcomerIcon?.isNotEmpty() == true
                ivLabel.setImageFromUrl(event.blindDateNewcomerIcon)

                //看看Ta
                btnLook.isVisible = ivLabel.isVisible && event.isNeedShowLookBtn
                btnLook.setOnClickListener {
                    mOnMsgClickListener?.onLookHimClick(event.accountUuid,event)
                }
            }
        }
    }


    /**
     * 更新消息字体大小
     */
    fun updateMsgTextSize(size: Int) {
        mViewBinding?.apply {
            tvContent.textSize = size.toFloat()
        }
    }
}