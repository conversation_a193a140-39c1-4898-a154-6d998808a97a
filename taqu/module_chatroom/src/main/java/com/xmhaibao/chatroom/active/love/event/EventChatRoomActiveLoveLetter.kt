package com.xmhaibao.chatroom.active.love.event

import hb.common.helper.HostHelper
import hb.qim.base.core.IResponseParse
import hb.utils.EventBusUtils
import hb.utils.WebpUtils

/**
 * 聊天室-520活动 送礼后告白弹窗
 *
 * <AUTHOR>
 * @date 2022/4/22
 */
class EventChatRoomActiveLoveLetter : IResponseParse<EventChatRoomActiveLoveLetter> {
    companion object {
        /**
         * 告白情书最大填写时间
         */
        const val MAX_LETTER_SEND_TIME = 180_000L
    }

    var level: String? = ""
    var letterUuid: String? = ""
    var avatar: String? = ""
    var nickname: String? = ""

    /**
     * 记录当前告白情书开始填写时间
     */
    var time: Long = 0

    /**
     * 剩余告白情书填写时间（3分钟倒计时后服务端直接推送告白情书，本地不再显示告白情书填写弹窗。）
     */
    fun getCountDownTime() = MAX_LETTER_SEND_TIME - (System.currentTimeMillis() - time)

    /**
     * 告白情书填写时间是否超时(剩余时间小于1时则不显示，避免闪屏
     */
    fun isTimeOutShow() = getCountDownTime() <= 1

    override fun parseResponse(event: String?, vararg args: Any?): EventChatRoomActiveLoveLetter {
        if (args.size >= 4) {
            time = System.currentTimeMillis()
            level = args[0].toString()
            letterUuid = args[1].toString()
            avatar = WebpUtils.getWebpUrl_3_1(args[2].toString(), HostHelper.getInstance().hostAvatar)
            nickname = args[3].toString()
            EventBusUtils.post(this)
        }
        return this
    }
}