package com.xmhaibao.chatroom.holder;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.xmhaibao.chatroom.R;
import com.xmhaibao.chatroom.model.event.EventNormalReward;

import androidx.recyclerview.widget.RecyclerView;

/**
 * <AUTHOR>  Linfc
 * create time : 2020/10/16
 * desc : 默认奖励消息
 */
public  class ChatNormalRewardViewHolder extends RecyclerView.ViewHolder   {
    private TextView mTvMsgContent;
    Context mContext;

    public ChatNormalRewardViewHolder(View view) {
        super(view);
        mContext = view.getContext();
        mTvMsgContent = itemView.findViewById(R.id.tvMsgContent);
    }

    public void setData(EventNormalReward event) {
        if (event!=null){
            mTvMsgContent.setText(event.getMsgContent());
        }
    }


}
