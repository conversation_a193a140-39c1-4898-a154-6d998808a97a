package com.xmhaibao.chatroom.roomplay.redpacket.holder

import android.view.ViewGroup
import cn.taqu.lib.base.utils.AppQuCoinHelper
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.databinding.ChatroomRedPacketBonusReceiveGiftItemBinding
import com.xmhaibao.chatroom.roomplay.redpacket.bean.ChatRoomRedPacketFunGiftInfoBean
import hb.xadapter.XBaseViewHolder

/**
 * 聊天室收到福利红包 - 礼物itemView
 *
 * <AUTHOR>
 * @date 2022-09-07
 */
class ChatRoomRedPacketBonusReceiveGiftItemViewHolder(parent: ViewGroup) :
    XBaseViewHolder<ChatRoomRedPacketFunGiftInfoBean>(parent,
        R.layout.chatroom_red_packet_bonus_receive_gift_item) {
    private val mBinding = ChatroomRedPacketBonusReceiveGiftItemBinding.bind(itemView)

    override fun onBindView(item: ChatRoomRedPacketFunGiftInfoBean?) {
        mBinding.apply {
            item?.apply {
                bdvGiftIcon.setImageFromUrl(icon)
                bdvGiftLay.setImageFromResource(R.drawable.chatroom_red_packet_bonus_receive_gift_lay)
                tvGiftName.text = name
                tvGiftPrice.text ="$price${ AppQuCoinHelper.ratioUnit}"
                tvGiftNum.text = "x$num"
            }
        }
    }
}