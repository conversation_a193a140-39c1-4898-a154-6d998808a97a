package com.xmhaibao.chatroom.fragment;

import android.content.DialogInterface;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.xmhaibao.beauty.interf.SampleResCheckListener;
import com.xmhaibao.chatroom.R;
import com.xmhaibao.chatroom.api.constants.ChatRoomIntentConstants;
import com.xmhaibao.chatroom.api.constants.ChatRoomPinsConstants;
import com.xmhaibao.chatroom.bean.ChatRoomOwnerInviteBean;
import com.xmhaibao.chatroom.constants.ChatRoomConstants;
import com.xmhaibao.chatroom.databinding.ChatroomInviteOnSeatFragmentBinding;
import com.xmhaibao.chatroom.dialog.ChatRoomMysteriousDialog;
import com.xmhaibao.chatroom.holder.ChatRoomOwnerInviteViewHolder;
import com.xmhaibao.chatroom.repository.ChatRoomRepository;
import com.xmhaibao.chatroom.utils.ChatRoomPermissionUtils;
import com.xmhaibao.chatroom.utils.ChatRoomTypeUtils;
import com.xmhaibao.chatroom.viewmodel.ChatRoomSeatManageViewModel;
import com.xmhaibao.forum.api.router.ForumPinsRouter;

import java.util.ArrayList;
import java.util.List;


import cn.taqu.lib.base.helper.AppBeautifyManager;
import cn.taqu.lib.okhttp.callback.GsonCallBack;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import hb.common.data.AccountHelper;
import hb.common.xstatic.HttpParams;
import hb.xstatic.core.XBaseFragment;
import hb.xstatic.list4page.IListHttpDataSource;
import hb.xstatic.list4page.XListHttpDataSource;
import hb.xstatic.list4page.XListPage;
import hb.xstatic.list4page.XListResultDispose;
import hb.xstatic.mvvm.XViewModelProviders;
import hb.xtoast.XToastUtils;

/**
 * 聊天室房主邀请上麦
 * Created by Linfc on 2019/8/23.
 */
public class ChatRoomInviteOnSeatFragment extends XBaseFragment implements View.OnClickListener {
    private ChatroomInviteOnSeatFragmentBinding mBinding;

    public static final String TAG = "ChatRoomOwnerInviteDialogFragment";

    private String mChatUuid;
    private boolean mIsCloseTip = false;
    private int mChatRoomType;
    private int mSeatNum;
    private DialogInterface.OnDismissListener mDismissListener;
    private ChatRoomSeatManageViewModel mSeatManageViewModel;

    public static ChatRoomInviteOnSeatFragment newInstance(String chatUuid, int chatRoomType, int seatNum, DialogInterface.OnDismissListener dismissListener) {
        ChatRoomInviteOnSeatFragment f = new ChatRoomInviteOnSeatFragment();
        Bundle bundle = new Bundle();
        bundle.putString(ChatRoomIntentConstants.INTENT_CHAT_ROOM_UUID, chatUuid);
        bundle.putInt(ChatRoomIntentConstants.INTENT_CHAT_ROOM_TYPE, chatRoomType);
        bundle.putInt(ChatRoomIntentConstants.INTENT_CHAT_ROOM_SEAT_NUM, seatNum);
        f.setArguments(bundle);
        f.setDismissListener(dismissListener);
        return f;
    }

    @Override
    protected Object onCreateContentView() {
        mBinding = ChatroomInviteOnSeatFragmentBinding.inflate(getLayoutInflater());
        return mBinding.getRoot();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        final Bundle arguments = getArguments();
        if (arguments != null) {
            mChatUuid = arguments.getString(ChatRoomIntentConstants.INTENT_CHAT_ROOM_UUID);
            mChatRoomType = arguments.getInt(ChatRoomIntentConstants.INTENT_CHAT_ROOM_TYPE);
            mSeatNum = arguments.getInt(ChatRoomIntentConstants.INTENT_CHAT_ROOM_SEAT_NUM);
        }
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        if (getActivity() != null) {
            mSeatManageViewModel = XViewModelProviders.of(getActivity()).get(ChatRoomSeatManageViewModel.class);
        }
    }

    @Override
    public void initViews() {
        super.initViews();
        mBinding.rlCloseRoot.setVisibility(mIsCloseTip ? View.GONE : View.VISIBLE);
        XListPage.create(getContext())
                .setDataSource(initHttpListDataSource())
                .attachToView(mBinding.flContentRootView)
                .setOnViewHolderCreatedListener(holder -> {
                    if (holder instanceof ChatRoomOwnerInviteViewHolder) {
                        final ChatRoomOwnerInviteViewHolder cvHolder = ((ChatRoomOwnerInviteViewHolder) holder);
                        cvHolder.setChatRoomType(mChatRoomType);
                        cvHolder.mBinding.tvInviteToMic.setOnClickListener(ChatRoomInviteOnSeatFragment.this);
                        cvHolder.mBinding.tvInviteToGuestMic.setOnClickListener(ChatRoomInviteOnSeatFragment.this);
                        cvHolder.mBinding.imgAvatar.setOnClickListener(ChatRoomInviteOnSeatFragment.this);
                    }
                })
                .registerItem(ChatRoomOwnerInviteBean.class, ChatRoomOwnerInviteViewHolder.class)
                .setEmptyView(R.drawable.chatroom_room_list_empty_ic, "当前没有在线用户~");
        initListener();
    }

    private void initListener() {
        mBinding.ivClose.setOnClickListener(view -> onViewClicked());
    }

    private IListHttpDataSource<List<ChatRoomOwnerInviteBean>> initHttpListDataSource() {
        final IListHttpDataSource<List<ChatRoomOwnerInviteBean>> dataSource =
                new XListHttpDataSource<List<ChatRoomOwnerInviteBean>>() {
                };
        dataSource.setResultDispose(new XListResultDispose<List<ChatRoomOwnerInviteBean>>() {
            @Override
            public void onHanldeSuccess(boolean isCache, List<ChatRoomOwnerInviteBean> list, Object... objs) {
                if (ChatRoomTypeUtils.isBigScreenRoomType(mChatRoomType)) {
                    if (list == null) {
                        list = new ArrayList<>();
                    }
                    //大屏房-邀请列表需要过滤掉当前用户的信息
                    filterMyInfo(list);
                    //大屏房-插入一条自己的信息，用于上麦
                    if (dataSource.isFirstPage() && ChatRoomTypeUtils.isBigScreenRoomType(mChatRoomType)) {
                        list.add(0, getMyUserInfo());
                    }
                }
                super.onHanldeSuccess(isCache, list, objs);
            }
        });
        dataSource.setHttpParams(HttpParams.newBuilder()
                .get(ChatRoomRepository.CHAT_ROOM_OWNER_INVITE_LIST)
                .params("ticket_id", AccountHelper.getUserTicketId())
                .params("chat_uuid", mChatUuid)
                .params("limit", "10")
                .build());
        return dataSource;
    }


    private void toInvite(ChatRoomOwnerInviteBean chatRoomOwnerInviteBean, int seatNum) {
        toInvite(chatRoomOwnerInviteBean,seatNum,ChatRoomPinsConstants.INVITE_ON_THE_MIC_NORMAL);
    }

    private void toInvite(ChatRoomOwnerInviteBean chatRoomOwnerInviteBean, int seatNum,int inviteType) {
        if (chatRoomOwnerInviteBean != null && !TextUtils.isEmpty(mChatUuid)) {
            if (!chatRoomOwnerInviteBean.isMeeting()) {
                return;
            }
            ChatRoomRepository.chatRoomOwnerInvite(AccountHelper.getUserTicketId(), mChatUuid, chatRoomOwnerInviteBean.getAccountUuid(), seatNum,inviteType)
                    .execute(new GsonCallBack<Object>() {
                        @Override
                        public void onSuccess(boolean isCache, @Nullable Object obj, @NonNull IResponseInfo response) {
                            XToastUtils.show("邀请已发送");
                            mDismissListener.onDismiss(null);
                        }

                        @Override
                        public void onFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {
                            XToastUtils.show(response.getResponseMsg("邀请失败～"));
                        }
                    });
        }
    }

    /**
     * 获取当前用户的信息 插入到顶部
     */
    private ChatRoomOwnerInviteBean getMyUserInfo() {
        ChatRoomOwnerInviteBean userBean = new ChatRoomOwnerInviteBean();
        userBean.setAvatar(AccountHelper.getAccountAvatar());
        userBean.setNickname(AccountHelper.getNickname());
        userBean.setAccountUuid(AccountHelper.getAccountUuid());
        userBean.setMyInfo(true);
        userBean.setRoomOwner(TextUtils.equals(AccountHelper.getAccountUuid(), mChatUuid));
        userBean.setApplySeatNum(mSeatNum);
        return userBean;
    }

    /**
     * 邀请列表需要过滤当前的用户
     */
    private void filterMyInfo(List<ChatRoomOwnerInviteBean> list) {
        if (list == null) {
            return;
        }
        int index = -1;
        for (int i = 0; i < list.size(); i++) {
            final ChatRoomOwnerInviteBean info = list.get(i);
            if (TextUtils.equals(AccountHelper.getAccountUuid(), info.getAccountUuid()) && !info.isMyInfo()) {
                index = i;
                break;
            }
        }
        if (index >= 0) {
            list.remove(index);
        }
    }

    public void onViewClicked() {
        mIsCloseTip = true;
        mBinding.rlCloseRoot.setVisibility(View.GONE);
    }

    public ChatRoomOwnerInviteBean getItemViewTagBean(View view) {
        if (view.getTag() instanceof ChatRoomOwnerInviteBean) {
            return (ChatRoomOwnerInviteBean) view.getTag();
        }
        return null;
    }

    public void setDismissListener(DialogInterface.OnDismissListener dismissListener) {
        mDismissListener = dismissListener;
    }

    @Override
    public void onClick(View v) {
        final int id = v.getId();
        final ChatRoomOwnerInviteBean bean = getItemViewTagBean(v);
        if (id == R.id.imgAvatar) {
            if (bean != null) {
                if (bean.getIsHidden()) {
                    final ChatRoomMysteriousDialog dialog = new ChatRoomMysteriousDialog(v.getContext());
                    dialog.show();
                } else {
                    ForumPinsRouter.launchPersonalHomePageActivity(v.getContext(), bean.getAccountUuid());
                }
            }
        } else if (id == R.id.tvInviteToMic) {
            //普通麦位
            if (bean != null) {
                if (bean.isMyInfo()) {
                    onBigScreenNormalSeat(bean);
                } else {
                    toInvite(bean, 1);
                }
            }
        } else if (id == R.id.tvInviteToGuestMic) {
            //特殊麦位
            if (bean != null) {
                 if (ChatRoomTypeUtils.isBigScreenRoomType(mChatRoomType)) {
                    if (bean.isMyInfo()) {
                        onBigScreenSpecialSeat(bean);
                    } else {
                        toInvite(bean, ChatRoomConstants.CHAT_ROOM_BIG_SCREEN_MIC_NUM);
                    }
                } else {
                    toInvite(bean, 1);
                }
            }
        }
    }

    /**
     * 大屏房-上普通位
     */
    private void onBigScreenNormalSeat(ChatRoomOwnerInviteBean bean) {
        ChatRoomPermissionUtils.checkChatRoom(getContext(), mChatRoomType, mSeatNum, new ChatRoomPermissionUtils.SimplePermissionListener() {
            @Override
            public void onAllGranted() {
                if (mSeatManageViewModel == null) {
                    return;
                }
                if (mSeatManageViewModel.isCurrentSeat()) {
                    mSeatManageViewModel.changeSeatNum(mSeatNum);
                } else {
                    int seatNum = ChatRoomTypeUtils.isBigScreenSeat(mSeatNum) ? 1 : bean.getApplySeatNum();
                    mSeatManageViewModel.applyAudioMeeting(seatNum, "", false);
                }
            }
        });
    }

    /**
     * 大屏房-上大屏位
     */
    private void onBigScreenSpecialSeat(ChatRoomOwnerInviteBean bean) {
        ChatRoomPermissionUtils.checkChatRoom(getContext(), mChatRoomType, mSeatNum, new ChatRoomPermissionUtils.SimplePermissionListener() {
            @Override
            public void onAllGranted() {
                AppBeautifyManager.getInstance().checkResource(getContext(),false,new SampleResCheckListener(){
                    @Override
                    public void onSuccess() {
                        if (mSeatManageViewModel == null) {
                            return;
                        }
                        if (mSeatManageViewModel.getMIsRoomOwner() || mSeatManageViewModel.isCurrentSeat()) {
                            mSeatManageViewModel.jumpBigScreenSeat();
                        } else {
                            mSeatManageViewModel.applyAudioMeeting(ChatRoomConstants.CHAT_ROOM_BIG_SCREEN_MIC_NUM, "", false);
                        }
                    }

                    @Override
                    public void onFailure(String failureCode) {
                        XToastUtils.show("初始化美颜失败");
                    }
                });
            }
        });
    }
}
