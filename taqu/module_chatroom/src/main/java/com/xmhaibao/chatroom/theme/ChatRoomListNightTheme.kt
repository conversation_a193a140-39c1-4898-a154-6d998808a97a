package com.xmhaibao.chatroom.theme

import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.constants.ChatRoomConstants
import hb.utils.ColorUtils
import hb.utils.SizeUtils

/**
 * 聊天室大厅-暗黑主题
 *
 * <AUTHOR>
 * @date 2022-07-20
 */
class ChatRoomListNightTheme : ChatRoomListTheme {
    override fun getId(): Int {
        return ChatRoomConstants.CHAT_ROOM_LIST_THEME_NIGHT
    }

    override fun getBackgroundColor(): Int {
        return ColorUtils.getColor("#18191A")
    }

    override fun getTopBackgroundDrawable(): Drawable {
        return GradientDrawable(
            GradientDrawable.Orientation.LEFT_RIGHT,
            intArrayOf(ColorUtils.getColor("#18191A"), ColorUtils.getColor("#18191A")))
    }

    override fun getBackBtnRes(): Int {
        return cn.taqu.lib.base.R.drawable.btn_back_white
    }

    override fun getThemeColor(): Int {
        return ColorUtils.getColor("#DFE2E8")
    }

    override fun getTabColor(): Int {
        return ColorUtils.getColor("7D7F85")
    }

    override fun getTabSelectedColor(): Int {
        return ColorUtils.getColor("#DFE2E8")
    }

    override fun getTabIndicatorRes(): Int {
        return cn.taqu.lib.base.R.drawable.bg_white_corner_10dp
    }

    override fun getTopSmallLayoutBgColor(): Int {
        return ColorUtils.getColor("#171623")
    }

    override fun getItemBgColor(): Int {
        return ColorUtils.getColor(hb.xstyle.R.color.white_alpha_05)
    }

    override fun getItemBgCornerSize(): Float {
        return SizeUtils.dp2px(16f).toFloat()
    }

    override fun getItemTextColor(): Int {
        return ColorUtils.getColor("#DFE2E8")
    }

}