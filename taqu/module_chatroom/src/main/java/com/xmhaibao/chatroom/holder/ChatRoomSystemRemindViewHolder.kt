package com.xmhaibao.chatroom.holder

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import cn.taqu.lib.base.live.model.event.EventMsgBase
import cn.taqu.lib.base.router.RouterLaunch
import com.xmhaibao.chatroom.databinding.ChatroomMsgSystemRemindItemBinding
import com.xmhaibao.chatroom.model.event.EventChatRoomSystemRemindMsg
import com.xmhaibao.chatroom.roomplay.auction.tracker.ChatRoomAuctionTracker
import hb.utils.StringUtils

/**
 * 聊天室-系统提醒消息
 *
 * <AUTHOR>
 * @date 2020-11-02
 */
public class ChatRoomSystemRemindViewHolder(var mViewBinding: ChatroomMsgSystemRemindItemBinding) : RecyclerView.ViewHolder(mViewBinding.root) {

    constructor(parent: ViewGroup) :
            this(ChatroomMsgSystemRemindItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
    private var msgBase: EventChatRoomSystemRemindMsg? = null

    init {
        itemView.setOnClickListener {
            if (StringUtils.isNotEmpty(msgBase?.releaction)) {
                RouterLaunch.dealJumpData(itemView.context, msgBase?.releaction)
                ChatRoomAuctionTracker.trackPublicFloatViewOrMessage(msgBase?.trackBean?.clickEvent, msgBase?.trackBean?.clickVariable)
            }
        }
    }

    fun setData(base: EventMsgBase?) {
        msgBase = base as? EventChatRoomSystemRemindMsg
        base?.apply {
            var msg = ""
            if (base is EventChatRoomSystemRemindMsg) {
                val eventRemind: EventChatRoomSystemRemindMsg = base
                msg = eventRemind.message
                when (eventRemind.type) {
                    EventChatRoomSystemRemindMsg.TypeValue.COMMON -> {
                        msg = eventRemind.message
                    }
                }
                mViewBinding.tvMsgSystemRemindContent.text = msg
                if (StringUtils.isNotEmpty(eventRemind.releaction)) {
                    mViewBinding.ivMsgSystemRemindArrow.visibility = View.VISIBLE
                } else {
                    mViewBinding.ivMsgSystemRemindArrow.visibility = View.GONE
                }
                if (!base.isTracked) {
                    base.isTracked = true
                    ChatRoomAuctionTracker.trackPublicFloatViewOrMessage(eventRemind.trackBean?.exposureEvent, eventRemind.trackBean?.exposureVariable)
                }
            }
        }
    }

    /**
     * 更新消息字体大小
     */
    fun updateMsgTextSize(titleSize:Int,size: Int) {
        mViewBinding.tvMsgSystemName.textSize = titleSize.toFloat()
        mViewBinding.tvMsgSystemRemindContent.textSize = size.toFloat()
    }

}