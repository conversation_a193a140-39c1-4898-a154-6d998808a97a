package com.xmhaibao.chatroom.mentorship.event

import cn.taqu.lib.base.live.helper.LiveMsgViewType
import cn.taqu.lib.base.live.model.event.EventMsgBase
import hb.common.helper.HostHelper
import hb.qim.base.core.IResponseParse
import hb.utils.EventBusUtils

/**
 * 聊天室-师傅收徒邀请event
 * [需求文档][https://o15vj1m4ie.feishu.cn/wiki/wikcn6pHa3oVBQyL78b9zCymtxe]
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
class EventChatRoomMentorshipApprenticeInvite : EventMsgBase(), IResponseParse<EventChatRoomMentorshipApprenticeInvite> {
    var apprenticeUuid: String? = ""

    var apprenticeAvatar: String? = ""

    var apprenticeNickName: String? = ""

    var content: String? = ""

    var isTracked = false

    override fun parseResponse(
        event: String?, vararg args: Any?
    ): EventChatRoomMentorshipApprenticeInvite {
        if (args.isNotEmpty()) {
            apprenticeUuid = args[0].toString()
            apprenticeAvatar = HostHelper.getAvatarHost().getWebpUrl_4_1(args[1].toString())
            apprenticeNickName = args[2].toString()
            content = args[3].toString()
            EventBusUtils.post(this)
        }
        return this
    }

    override fun getItemViewType(): Int {
        return LiveMsgViewType.TYPE_CHATROOM_MENTORSHIP_INVITE
    }

}