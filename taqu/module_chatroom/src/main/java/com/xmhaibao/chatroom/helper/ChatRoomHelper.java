package com.xmhaibao.chatroom.helper;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.telephony.PhoneStateListener;
import android.telephony.TelephonyManager;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.fragment.app.FragmentActivity;

import com.xmhaibao.chatroom.active.webplay.event.EventChatRoomWebPlayClose;
import com.xmhaibao.chatroom.active.webplay.event.EventChatRoomWebPlayStart;
import com.xmhaibao.chatroom.active.widget.ChatRoomCommonWidgetHelper;
import com.xmhaibao.chatroom.activity.ChatRoomActivity;
import com.xmhaibao.chatroom.api.constants.ChatRoomType;
import com.xmhaibao.chatroom.artistcenter.constants.ChatRoomCenterConstants;
import com.xmhaibao.chatroom.bean.AudioVolumeBean;
import com.xmhaibao.chatroom.bean.ChatRoomAccountInChatInfo;
import com.xmhaibao.chatroom.bean.ChatRoomDetailInfo;
import com.xmhaibao.chatroom.bean.ChatRoomOnlineInfo;
import com.xmhaibao.chatroom.bean.ChatRoomRunningServiceBean;
import com.xmhaibao.chatroom.block.ChatRoomCoreCenterHelper;
import com.xmhaibao.chatroom.block.model.ChatRoomMicAction;
import com.xmhaibao.chatroom.block.viewmodel.ChatRoomMicViewModel;
import com.xmhaibao.chatroom.constants.ChatRoomConstants;
import com.xmhaibao.chatroom.event.EventChatRoomWarning;
import com.xmhaibao.chatroom.event.EventMsgSystemBroadcast;
import com.xmhaibao.chatroom.gift.starpicking.helper.ChatRoomStarPickingTimeHelper;
import com.xmhaibao.chatroom.honorlevel.holder.ChatRoomHonorLevelConfigHelper;
import com.xmhaibao.chatroom.interf.IChatRoomVoicePresenter;
import com.xmhaibao.chatroom.interf.IChatRoomWatchListener;
import com.xmhaibao.chatroom.interf.OnChatRoomListener;
import com.xmhaibao.chatroom.ktv.ChatRoomKtvTRTCMusicPlayImpl;
import com.xmhaibao.chatroom.ktv.ChatRoomMusicLibraryHelper;
import com.xmhaibao.chatroom.ktv.ChatRoomMusicPlayHelper;
import com.xmhaibao.chatroom.ktv.bean.ChatRoomKtvLyricsSycBean;
import com.xmhaibao.chatroom.ktv.bean.ChatRoomKtvSongDetailBean;
import com.xmhaibao.chatroom.ktv.constants.ChatRoomKtvCutSongScene;
import com.xmhaibao.chatroom.ktv.event.EventChatRoomKtvBullyWheat;
import com.xmhaibao.chatroom.ktv.event.EventChatRoomKtvCancelBullyWheat;
import com.xmhaibao.chatroom.ktv.event.EventChatRoomKtvCutSong;
import com.xmhaibao.chatroom.ktv.event.EventChatRoomKtvSongEmpty;
import com.xmhaibao.chatroom.ktv.event.EventChatRoomKtvStopMusic;
import com.xmhaibao.chatroom.ktv.helper.ChatRoomKtvMusicDurationHelper;
import com.xmhaibao.chatroom.mentorship.helper.ChatRoomMentorshipHelper;
import com.xmhaibao.chatroom.model.ChatRoomModel;
import com.xmhaibao.chatroom.model.event.EventAudioClose;
import com.xmhaibao.chatroom.model.event.EventAudioForbidInfo;
import com.xmhaibao.chatroom.model.event.EventAudioRoomDetailInfo;
import com.xmhaibao.chatroom.model.event.EventCancelRoomApplyMicrophone;
import com.xmhaibao.chatroom.model.event.EventChatFirstAcceptGift;
import com.xmhaibao.chatroom.model.event.EventChatRoomChangeSeat;
import com.xmhaibao.chatroom.model.event.EventChatRoomInvite;
import com.xmhaibao.chatroom.model.event.EventChatRoomMsgGift;
import com.xmhaibao.chatroom.model.event.EventChatRoomNotice;
import com.xmhaibao.chatroom.model.event.EventChatroomMsgDan;
import com.xmhaibao.chatroom.model.event.EventMsgAudioFree;
import com.xmhaibao.chatroom.model.event.EventMsgChat;
import com.xmhaibao.chatroom.model.event.EventMsgFollowed;
import com.xmhaibao.chatroom.model.event.EventMsgIntoRoom;
import com.xmhaibao.chatroom.model.event.EventMsgJin;
import com.xmhaibao.chatroom.props.helper.ChatRoomPropLoudspeakerHelper;
import com.xmhaibao.chatroom.roomplay.pkroom.bean.ChatRoomPKInRoomPkConfigBean;
import com.xmhaibao.chatroom.roomplay.redpacket.event.EventMsgPersonRedPacketBean;
import com.xmhaibao.chatroom.roomplay.redpacket.event.EventRedPacketBonusReceive;
import com.xmhaibao.chatroom.roomplay.redpacket.event.EventRedPacketOpenNotice;
import com.xmhaibao.chatroom.model.event.EventRejectApplyMicrophone;
import com.xmhaibao.chatroom.model.event.EventRemoveUser;
import com.xmhaibao.chatroom.model.event.EventRoomAgreeMicrophone;
import com.xmhaibao.chatroom.model.event.EventRoomApplyMicrophone;
import com.xmhaibao.chatroom.model.event.EventRoomKickMicrophone;
import com.xmhaibao.chatroom.msg.ChatRoomMsgPresenter;
import com.xmhaibao.chatroom.music.helper.ChatRoomBGMPlayHelper;
import com.xmhaibao.chatroom.mvp.ChatRoomConnectedPresenter;
import com.xmhaibao.chatroom.mvp.ChatRoomTRTCPresenter;
import com.xmhaibao.chatroom.mvp.ChatRoomTRTCVideoPresenter;
import com.xmhaibao.chatroom.mvp.IRoomView;
import com.xmhaibao.chatroom.mvp.RoomPresenter;
import com.xmhaibao.chatroom.roomplay.pkroom.repository.ChatRoomPkRepository;
import com.xmhaibao.chatroom.repository.ChatRoomRepository;
import com.xmhaibao.chatroom.roomplay.auction.event.EventChatRoomAuctionMeeting;
import com.xmhaibao.chatroom.roomplay.auction.helper.ChatRoomAuctionHelper;
import com.xmhaibao.chatroom.roomplay.gobang.constants.ChatRoomGoBangStatus;
import com.xmhaibao.chatroom.roomplay.gobang.event.EventChatRoomGoBangOpen;
import com.xmhaibao.chatroom.roomplay.gobang.event.EventChatRoomGoBangQuit;
import com.xmhaibao.chatroom.roomplay.gobang.helper.ChatRoomGoBangHelper;
import com.xmhaibao.chatroom.roomplay.pkroom.bean.ChatRoomPkConnectBean;
import com.xmhaibao.chatroom.roomplay.pkroom.bean.ChatRoomPkResultBean;
import com.xmhaibao.chatroom.roomplay.pkroom.dialog.ChatRoomPKInviteConfirmDialogFragment;
import com.xmhaibao.chatroom.roomplay.pkroom.event.EventChatRoomPKRequestBean;
import com.xmhaibao.chatroom.roomplay.pkroom.event.EventChatRoomPKStartBean;
import com.xmhaibao.chatroom.roomplay.pkroom.event.EventChatRoomPkEndBean;
import com.xmhaibao.chatroom.roomplay.webgame.event.EventChatRoomCloseWebGame;
import com.xmhaibao.chatroom.roomplay.webgame.event.EventChatRoomOpenWebGame;
import com.xmhaibao.chatroom.event.EventChatRoomInviteJoinGame;
import com.xmhaibao.chatroom.router.ChatRoomRouter;
import com.xmhaibao.chatroom.service.ChatRoomRunningService;
import com.xmhaibao.chatroom.tracker.ChatRoomCommonTracker;
import com.xmhaibao.chatroom.trtc.TRTCCloudListenerImpl;
import com.xmhaibao.chatroom.utils.ChatRoomCenterUtil;
import com.xmhaibao.chatroom.utils.ChatRoomDialogUtils;
import com.xmhaibao.chatroom.utils.ChatRoomLogUploadUtil;
import com.xmhaibao.chatroom.utils.ChatRoomPreferencesUtils;
import com.xmhaibao.chatroom.utils.ChatRoomTypeUtils;
import com.xmhaibao.common_level.helper.CommonLevelHelper;
import com.xmhaibao.gift.bean.LiveGiftInfo;
import com.xmhaibao.gift.bean.LiveGiftSendResultBean;
import com.xmhaibao.matchmaker.event.EventChatRoomMatchMakerWidgetInfoBean;
import com.xmhaibao.starshine.helper.StarShineAttachScene;
import com.xmhaibao.starshine.helper.StarShineCommonHelper;

import java.util.ArrayList;
import java.util.List;

import cn.iwgang.countdownview.CustomCountDownTimer;
import cn.taqu.lib.base.callback.MyPhoneStateListener;
import cn.taqu.lib.base.common.AppAccountBean;
import cn.taqu.lib.base.constants.ChatRoomSourceConstants;
import cn.taqu.lib.base.event.EventStopOldChatRoom;
import cn.taqu.lib.base.event.EventTicketExpire;
import cn.taqu.lib.base.event.EventXjbUserLoginOut;
import cn.taqu.lib.base.im.IMType;
import cn.taqu.lib.base.live.model.event.EventMsgBase;
import cn.taqu.lib.base.model.BaseModel;
import cn.taqu.lib.base.router.ARouterManager;
import cn.taqu.lib.okhttp.callback.GsonCallBack;
import cn.taqu.lib.okhttp.model.IResponseInfo;
import hb.common.data.AccountHelper;
import hb.utils.ActivityUtils;
import hb.utils.AppUtils;
import hb.utils.EventBusUtils;
import hb.utils.Loger;
import hb.utils.StringUtils;
import hb.utils.ToastUtils;
import hb.xrequest.RequestCallback;
import hb.xstyle.xdialog.XDialogUtils;
import hb.xtoast.XToastUtils;

/**
 * 聊天室最小化帮助类
 * Created by laixuejun on 2018/2/9.
 */
public class ChatRoomHelper implements OnChatRoomListener, IChatRoomWatchListener {
    private final String TAG = this.getClass().getSimpleName();

    private IChatRoomVoicePresenter mVoicePresenter;
    private ChatRoomConnectedPresenter mRoomConnectedPresenter;//房间轮询Presenter
    private RoomPresenter mRoomPresenter;//房间其他业务Presenter
    private TelephonyManager mTelephonyManager;
    private MyPhoneStateListener mPhoneStateListener;
    private boolean isPhoneMuteAudio = false;

    private EventAudioRoomDetailInfo mEventAudioRoomDetailInfo;
    private EventRoomApplyMicrophone mEventRoomApplyMicrophone;

    private EventChatRoomWebPlayStart mEventChatRoomWebPlayStart;


    private boolean isForbided;
    private int mApplyMicStatus = ChatRoomConstants.APPLY_MICROPHOME_NORMAL; //申请上麦状态
    private boolean mHaveApplyMicSuccess = false; // 是否有上麦过
    private String mChatUuid;
    private boolean mIsRoomOwner;
    List<EventMsgBase> mMsgList;
    private List<ChatRoomOnlineInfo> mHaveMicroPhoneList = new ArrayList<>();
    private boolean isMuteLocalVoice; //自己静音
    private boolean mRoomOwnerForbidVoice;//房主把你在麦上静音。true:静音
    private boolean isJoinRoomSuccess;
    private boolean isAddFollowTip;//是否已经在聊天里面显示出关注房主，防止重复。true 已经显示
    private FragmentActivity mActivity;
    private ChatRoomBGMPlayHelper mBGMPlayHelper;

    private boolean mIsPlayingMusic;//是否正在播放音乐。 true：正在播放。false：停止播放
    public boolean mIsZoom;
    private @ChatRoomType
    int mChatRoomType;
    private boolean isMinAgreeMic = false;//是否在最小化的时候被同意上麦了
    private ChatRoomMusicPlayHelper mKtvMusicPlayHelper;
    private EventChatRoomKtvCutSong mMySingSongInfo;//当前我正在唱歌歌曲信息
    private EventChatRoomKtvCutSong mCurrentPlaySong;//当前播放的歌曲信息
    private boolean mNeedShowStartSongDialog = false;//是否需要弹，轮到你开唱了dialog
    private CustomCountDownTimer mBullyWheatCountDownTimer;//霸麦倒计时
    private boolean mIsBullyWheatCountDown = false;//是否在霸麦倒计时中
    private int mChatRoomKtvMeetingCheckTime;//霸麦检测时长
    private boolean isRoomOwnerInvite = false;//是否是房主邀请上麦

    /**
     * 大屏房，上麦时是否显示过了美颜预览弹窗
     */
    private boolean mIsShowBigScreenBeautify;

    /**
     * 礼物连击-开始时间
     */
    private long mGiftComboStarTime;
    /**
     * 上一次送的礼物信息
     */
    private LiveGiftInfo mTopSendGiftInfo;
    private List<LiveGiftSendResultBean> mTopGiftSendResultList;
    private ChatRoomPKInviteConfirmDialogFragment mPKInviteConfirmDialog;
    /**
     * pk结束弹窗
     */
    private ChatRoomPkResultBean mPkResultBean;
    private EventChatFirstAcceptGift mEventChatFirstAcceptGift;
    private ChatRoomWatchTimeHelper mWatchTimeHelper;
    private ChatRoomInviteGuideHelper mInviteGuideHelper;
    private ChatRoomMentorshipHelper mMentorshipHelper;

    private ChatRoomStarPickingTimeHelper mChatRoomStarPickingTimeHelper;

    private ChatRoomCommonWidgetHelper mCommonWidgetHelper;
    private StarShineCommonHelper mStarShineCommonHelper;


    /**
     * 度宇小游戏是否是正在玩
     */
    private boolean mIsShowDuYuGameView = true;

    private String mMatchType;
    private String mTabId;
    /**
     * 是否是点击最小化进入聊天室的
     */
    private boolean mIsClickMinLaunch;

    /**
     * 拍拍房，帮助类
     */
    private ChatRoomAuctionHelper mChatRoomAuctionHelper = null;

    /**
     * 当前房间是否弹过上麦邀请，
     * 注意：如果弹过 ChatRoomActiveLoveInviteMicDialog，
     * ChatRoomActiveLoveInviteSetAuctionDialog 也会把这个值设置为true
     */
    private boolean isShowAutoInviteMicDialog = false;

    private ChatRoomGoBangHelper mChatRoomGoBangHelper = null;

    private String mEnterRoomSource = "";
    /**
     * 进入大厅来源
     */
    private String mHallSource = "";

    private ServiceConnection mRunningConnection;

    /**
     * webGame是否开启
     */
    private EventChatRoomOpenWebGame mCurrentWebGameInfo;


    private ChatRoomPkRepository mPkRepository;

    /**
     * 通用活动入口
     */
    private ChatRoomCommonActivityEntranceHelper  mCommonActivityEntranceHelper;

    /**
     * 收到福利红包
     */
    private EventRedPacketBonusReceive mRedPacketBonusReceive;

    /**
     * pk数据
     */
    private ChatRoomPKInRoomPkConfigBean mPKInRoomPkConfigBean;

    /**
     * 月老祈福挂件
     */
    private EventChatRoomMatchMakerWidgetInfoBean mMatchMakerWidgetInfoBean;

    /**
     * 广播大喇叭
     */
    private ChatRoomPropLoudspeakerHelper mPropLoudspeakerHelper;

    public ChatRoomPKInRoomPkConfigBean getPKInRoomPkConfigBean() {
        return mPKInRoomPkConfigBean;
    }

    public void setPKInRoomPkConfigBean(ChatRoomPKInRoomPkConfigBean PKInRoomPkConfigBean) {
        mPKInRoomPkConfigBean = PKInRoomPkConfigBean;
    }

    public ChatRoomHelper(String chatUuid, boolean isRoomOwner) {
        this(chatUuid, isRoomOwner, false);
    }

    public ChatRoomHelper(String chatUuid, boolean isRoomOwner, boolean isRegisterEvent) {
        this(chatUuid, isRoomOwner, isRegisterEvent, -1);
    }

    public ChatRoomHelper(String chatUuid, boolean isRoomOwner, boolean isRegisterEvent,int chatRoomType) {
        if (isRegisterEvent && !EventBusUtils.isRegistered(this)) {
            EventBusUtils.register(this);
        }
        mChatUuid = chatUuid;
        mIsRoomOwner = isRoomOwner;
        if (chatRoomType != -1) {
            mChatRoomType = chatRoomType;
        }
        // IM加入房间
        ARouterManager.imOptionService().join(mChatUuid, IMType.AUDIO_CHAT);
        mRoomConnectedPresenter = new ChatRoomConnectedPresenter(mChatUuid);
        mRoomPresenter = new RoomPresenter();
        BaseModel.getInstance().setOpenChatRoom(true);
        BaseModel.getInstance().setRoomOwnerOpenChatRoom(mIsRoomOwner);
        registerPhoneCall();
        ChatRoomKtvMusicDurationHelper.INSTANCE.setMChatUuid(chatUuid);
        CommonLevelHelper.INSTANCE.init();
        ChatRoomHonorLevelConfigHelper.getInstance().init();
        ChatRoomHonorLevelConfigHelper.getInstance().setMChatUuid(mChatUuid);
        if (mIsRoomOwner) {
            initInviteGuideHelper();
        }
        mMentorshipHelper = new ChatRoomMentorshipHelper();
        mPropLoudspeakerHelper = new ChatRoomPropLoudspeakerHelper();
        mChatRoomStarPickingTimeHelper = new ChatRoomStarPickingTimeHelper();
        mCommonWidgetHelper = new ChatRoomCommonWidgetHelper();
    }


    public ChatRoomStarPickingTimeHelper getChatRoomGiftWallHelper() {
        return mChatRoomStarPickingTimeHelper;
    }

    private ChatRoomPkRepository getPkRepository() {
        if (mPkRepository == null) {
            mPkRepository = new ChatRoomPkRepository();
        }
        return mPkRepository;
    }

    public EventChatRoomOpenWebGame getCurrentWebGameInfo() {
        return mCurrentWebGameInfo;
    }

    public void setCurrentWebGameInfo(EventChatRoomOpenWebGame currentWebGameInfo) {
        mCurrentWebGameInfo = currentWebGameInfo;
    }

    public boolean isShowAutoInviteMicDialog() {
        return isShowAutoInviteMicDialog;
    }

    public void setShowAutoInviteMicDialog(boolean showAutoInviteMicDialog) {
        isShowAutoInviteMicDialog = showAutoInviteMicDialog;
    }

    public String getChatUuid() {
        return mChatUuid;
    }

    public boolean isRoomOwner() {
        return mIsRoomOwner;
    }

    public boolean isMinAgreeMic() {
        return isMinAgreeMic;
    }

    public void setMinAgreeMic(boolean minAgreeMic) {
        isMinAgreeMic = minAgreeMic;
    }

    public void initVoicePresenter(@ChatRoomType int chatRoomType, String enterRoomSource, String hallSource,String listTab) {
        if (ChatRoomTypeUtils.isVideoRoomType(chatRoomType)) {
            mVoicePresenter = new ChatRoomTRTCVideoPresenter(mIsRoomOwner, mChatUuid, chatRoomType);
        } else {
            mVoicePresenter = new ChatRoomTRTCPresenter(mIsRoomOwner, mChatUuid, chatRoomType);
        }
        mEnterRoomSource = enterRoomSource;
        mHallSource = hallSource;
        mVoicePresenter.setEnterRoomSource(enterRoomSource);
        mVoicePresenter.setEnterHallSource(hallSource);
        mVoicePresenter.setListTab(listTab);


        //初始化拍拍房帮助类
        if (ChatRoomTypeUtils.isAuctionRoomType(chatRoomType)) {
            mChatRoomAuctionHelper = new ChatRoomAuctionHelper();
            mChatRoomAuctionHelper.setMVoicePresenter(mVoicePresenter);
            mChatRoomAuctionHelper.setMChatUuid(mChatUuid);
            mChatRoomAuctionHelper.setMIsRoomOwner(mIsRoomOwner);
            mChatRoomAuctionHelper.setMConnectedPresenter(getConnectedPresenter());
        }

        //传递变量，成功加入房间上麦后开启轮询
        mVoicePresenter.setRoomConnectedPresenter(getConnectedPresenter());
        mBGMPlayHelper = new ChatRoomBGMPlayHelper(mVoicePresenter);
        mKtvMusicPlayHelper = new ChatRoomMusicPlayHelper(new ChatRoomKtvTRTCMusicPlayImpl(mVoicePresenter,
                chatRoomType, ChatRoomPreferencesUtils.isKTVOriginMusic()));

        //星耀成长体系
        mStarShineCommonHelper = new StarShineCommonHelper(new StarShineAttachScene.CHATROOM(mChatUuid));
    }

    public StarShineCommonHelper getStarShineCommonHelper() {
        return mStarShineCommonHelper;
    }

    public ChatRoomAuctionHelper getChatRoomAuctionHelper() {
        return mChatRoomAuctionHelper;
    }

    public void setWatchTimeHelper(int reportInterval, String chatUuid, IRoomView roomView, boolean reStart) {
        if (mWatchTimeHelper == null) {
            mWatchTimeHelper = new ChatRoomWatchTimeHelper();
            mWatchTimeHelper.setMRoomView(roomView);
            mWatchTimeHelper.setReportInterval(reportInterval, chatUuid, mIsRoomOwner);
            mWatchTimeHelper.start();
        } else {
            mWatchTimeHelper.setMRoomView(roomView);
            if (reStart) {
                mWatchTimeHelper.setReportInterval(reportInterval, chatUuid, mIsRoomOwner);
                mWatchTimeHelper.start();
            }
        }
    }

    public ChatRoomWatchTimeHelper getWatchTimeHelper() {
        return mWatchTimeHelper;
    }

    public void startRoomRunningService(ChatRoomDetailInfo info) {
        if (mRunningConnection == null) {
            mRunningConnection = new ServiceConnection() {
                @Override
                public void onServiceConnected(ComponentName name, IBinder service) {

                }

                @Override
                public void onServiceDisconnected(ComponentName name) {

                }
            };
        }
        if (info != null) {
            ChatRoomRunningService.launch(
                    new ChatRoomRunningServiceBean(mChatUuid, info.getCover_url(), info.getRoom_type(), info.getSubType(), info.getTitle()),
                    mRunningConnection
            );
        }
    }

    public void stopRoomRunningService() {
        if (mRunningConnection != null) {
            ChatRoomRunningService.stop(mRunningConnection);
        }
    }

    public void register() {
        if (!EventBusUtils.isRegistered(this)) {
            EventBusUtils.register(this);
        }
        if (mWatchTimeHelper != null) {
            mWatchTimeHelper.setMRoomView(null);
            mWatchTimeHelper.setMChatRoomWatchListener(this);
        }
        if (mVoicePresenter != null) {
            mVoicePresenter.setChatRoomListener(this);
        }
        if (mChatRoomGoBangHelper != null) {
            mChatRoomGoBangHelper.setMGoBangSeatOperation(null);
        }
    }

    public void unregister() {
        if (EventBusUtils.isRegistered(this)) {
            EventBusUtils.unregister(this);
        }
        if (mWatchTimeHelper != null) {
            mWatchTimeHelper.setMChatRoomWatchListener(null);
        }
        if (mVoicePresenter != null) {
            mVoicePresenter.removeChatRoomListener(this);
        }
    }

    private ChatRoomMinimizeViewHelper mMinimizeViewHelper = null;

    public ChatRoomMinimizeViewHelper getMinimizeViewHelper() {
        return mMinimizeViewHelper;
    }

    /**
     * @param isFamilyStart 是否是家族页面进入最小化的
     */
    public void startChatRoomService() {
        mIsZoom = true;
        mIsClickMinLaunch = false;
        register();

        if (mMinimizeViewHelper == null) {
            mMinimizeViewHelper = new ChatRoomMinimizeViewHelper();
        }
        mMinimizeViewHelper.setMIsMinimizeRoom(true);
        mMinimizeViewHelper.setMChatUuid(mChatUuid);
        mMinimizeViewHelper.setMIsRoomOwner(mIsRoomOwner);
        mMinimizeViewHelper.setMChatRoomType(mChatRoomType);
        if (mRoomDetailInfo != null) {
            mMinimizeViewHelper.setMChatRoomSubType(mRoomDetailInfo.getSubType());
        }
        mMinimizeViewHelper.setMKtvMicMeetingType(getKtvApplyMicType());
        mMinimizeViewHelper.setMRoomVoicePresenter(getVoicePresenter());
        setZoomChatRoomCover();
        mMinimizeViewHelper.setMChatRoomHelper(this);
        if (mActivity != null) {
            mActivity.finish();
            mActivity = null;
        }
    }

    public void unbindService() {
        mIsZoom = false;
        unregister();
        ChatRoomModel.getInstance().setLiveGiftMap(null);
        if (mMinimizeViewHelper != null) {
            mMinimizeViewHelper.destroy();
            mMinimizeViewHelper = null;
        }
    }

    /**
     * 恢复正常房间
     */
    public void startRestoreChatRoom() {
        if (mMinimizeViewHelper != null) {
            mMinimizeViewHelper.startRestoreChatRoom();
        }
    }

    public void startChatRoomVideo() {
        if (mMinimizeViewHelper == null) {
            return;
        }
        if (mRoomDetailInfo != null) {
            final int type = mRoomDetailInfo.getRoom_type();
            if (ChatRoomTypeUtils.isBigScreenRoomType(type)) {
                startBigScreenVideo();
            }
        }
    }

    /**
     * 初始化房主邀请帮助类
     */
    private void initInviteGuideHelper() {
        mInviteGuideHelper = new ChatRoomInviteGuideHelper(new ChatRoomInviteGuideHelper.OnInviteGuideListener() {
            @Override
            public int getChatRoomType() {
                return mChatRoomType;
            }

            @Nullable
            @Override
            public String getGameSn() {
                return getCurrentGameSn();
            }

            @Nullable
            @Override
            public FragmentActivity getActivity() {
                return mActivity;
            }

            @Nullable
            @Override
            public String getManageType() {
                return mRoomDetailInfo != null ? mRoomDetailInfo.getIdentityType() : null;
            }
        }, mChatUuid);
    }


    /**
     * 大屏房-播放大屏位视频
     */
    private void startBigScreenVideo() {
        startBigScreenVideo(0);
    }

    /**
     * 大屏房-播放大屏位视频
     *
     * @param downTime 是否需要开启推流倒计时
     */
    private void startBigScreenVideo(int downTime) {
        if (mRoomDetailInfo == null) {
            return;
        }
        final String avatarUrl = mRoomDetailInfo.getAvatar();
        List<ChatRoomOnlineInfo> list = new ArrayList<>(mHaveMicroPhoneList);
        if (mEventAudioRoomDetailInfo != null) {
            list = mEventAudioRoomDetailInfo.getHaveMicrophoneList();
        }
        final ChatRoomOnlineInfo info = ChatRoomTypeUtils.getBigScreenSeatInfo(list);
        if (info != null && mMinimizeViewHelper != null) {
            mMinimizeViewHelper.startVideo(info.getAccountUuid(), avatarUrl, downTime);
        }
    }

    public void setZoomChatRoomCover() {
        String avatar = null;
        if (mRoomDetailInfo != null) {
            if (StringUtils.isNotEmpty(mRoomDetailInfo.getCover_url())) {
                avatar = mRoomDetailInfo.getCover_url();
            } else {
                avatar = mRoomDetailInfo.getAvatar();
            }
        }
        if (StringUtils.isNotEmpty(avatar) && mMinimizeViewHelper != null) {
            mMinimizeViewHelper.setMinCover(avatar);
        }
    }

    public ChatRoomInviteGuideHelper getInviteGuideHelper() {
        return mInviteGuideHelper;
    }

    public ChatRoomMentorshipHelper getMentorshipHelper() {
        return mMentorshipHelper;
    }

    public ChatRoomPropLoudspeakerHelper getPropLoudspeakerHelper() {
        return mPropLoudspeakerHelper;
    }

    public String getMatchType() {
        return mMatchType;
    }

    public void setMatchType(String matchType) {
        mMatchType = matchType;
    }

    public String getTabId() {
        return mTabId;
    }

    public void setTabId(String tabId) {
        mTabId = tabId;
    }

    public boolean isClickMinLaunch() {
        return mIsClickMinLaunch;
    }

    public void setClickMinLaunch(boolean clickMinLaunch) {
        mIsClickMinLaunch = clickMinLaunch;
    }

    public long getGiftComboStarTime() {
        return mGiftComboStarTime;
    }

    public void setGiftComboStarTime() {
        mGiftComboStarTime = System.currentTimeMillis();
    }

    public LiveGiftInfo getTopSendGiftInfo() {
        return mTopSendGiftInfo;
    }

    public void setTopSendGiftInfo(LiveGiftInfo topSendGiftInfo) {
        mTopSendGiftInfo = topSendGiftInfo;
    }

    public List<LiveGiftSendResultBean> getTopGiftSendResultList() {
        return mTopGiftSendResultList;
    }

    public void setTopGiftSendResultList(List<LiveGiftSendResultBean> topGiftSendResultList) {
        mTopGiftSendResultList = topGiftSendResultList;
    }

    public int getChatRoomType() {
        return mChatRoomType;
    }

    public void setChatRoomType(int chatRoomType) {
        mChatRoomType = chatRoomType;
    }

    public boolean isIsPlayingMusic() {
        return mIsPlayingMusic;
    }

    public void setIsPlayingMusic(boolean mIsPlayingMusic) {
        this.mIsPlayingMusic = mIsPlayingMusic;
    }

    public RoomPresenter getRoomPresenter() {
        return mRoomPresenter;
    }

    public ChatRoomBGMPlayHelper getBGMPlayHelper() {
        return mBGMPlayHelper;
    }

    public ChatRoomMusicPlayHelper getKtvMusicPlayHelper() {
        return mKtvMusicPlayHelper;
    }

    public IChatRoomVoicePresenter getVoicePresenter() {
        return mVoicePresenter;
    }

    public ChatRoomConnectedPresenter getConnectedPresenter() {
        return mRoomConnectedPresenter;
    }

    public boolean isRoomOwnerForbidVoice() {
        return mRoomOwnerForbidVoice;
    }

    @Override
    public boolean minRoomOwnerForbidVoice() {
        return false;
    }

    public void setmRoomOwnerForbidVoice(boolean mRoomOwnerForbidVoice) {
        this.mRoomOwnerForbidVoice = mRoomOwnerForbidVoice;
    }


    public boolean isAddFollowTip() {
        return isAddFollowTip;
    }

    public void setAddFollowTip(boolean addFollowTip) {
        isAddFollowTip = addFollowTip;
    }

    public List<EventMsgBase> getMsgList() {
        return mMsgList;
    }

    public void setMsgList(List<EventMsgBase> mMsgList) {
        this.mMsgList = mMsgList;
    }

    public List<ChatRoomOnlineInfo> getHaveMicroPhoneList() {
        return mHaveMicroPhoneList;
    }

    public void setHaveMicroPhoneList(List<ChatRoomOnlineInfo> mHaveMicroPhoneList) {
        this.mHaveMicroPhoneList = mHaveMicroPhoneList;
    }


    public ChatRoomCommonWidgetHelper getCommonWidgetHelper() {
        return mCommonWidgetHelper;
    }

    public int getApplyMicStatus() {
        if (ChatRoomTypeUtils.isAuctionRoomType(mChatRoomType) && mChatRoomAuctionHelper != null) {
            return mChatRoomAuctionHelper.getMApplyMicStatus();
        }
        return mApplyMicStatus;
    }

    /**
     * 是否上过麦
     * @return true
     */
    public boolean isHaveApplyMisSuccess() {
        if (ChatRoomTypeUtils.isAuctionRoomType(mChatRoomType) && mChatRoomAuctionHelper != null) {
            return mChatRoomAuctionHelper.getMHaveApplyMicSuccess();
        }
        return mHaveApplyMicSuccess;
    }

    @Override
    public int minApplyMicStatus() {
        return getApplyMicStatus();
    }

    public void setApplyMicStatus(int mApplyMicStatus) {
        this.mApplyMicStatus = mApplyMicStatus;
        if (mApplyMicStatus == ChatRoomConstants.APPLY_MICROPHOME_SUCCESS) {
            mHaveApplyMicSuccess = true;
        }
    }

    public boolean isJoinRoomSuccess() {
        return isJoinRoomSuccess;
    }

    public void setJoinRoomSuccess(boolean joinRoomSuccess) {
        isJoinRoomSuccess = joinRoomSuccess;
    }

    public void setActivity(FragmentActivity activity) {
        mActivity = activity;
    }

    private ChatRoomDetailInfo mRoomDetailInfo;

    public ChatRoomDetailInfo getRoomDetailInfo() {
        return mRoomDetailInfo;
    }

    public void setRoomDetailInfo(ChatRoomDetailInfo roomDetailInfo) {
        this.mRoomDetailInfo = roomDetailInfo;
    }

    public boolean isMuteLocalVoice() {
        return isMuteLocalVoice;
    }

    @Override
    public boolean minMuteLocalVoice() {
        return isMuteLocalVoice();
    }

    public void setMuteLocalVoice(boolean muteLocalVoice) {
        isMuteLocalVoice = muteLocalVoice;
    }

    public boolean isForbided() {
        return isForbided;
    }

    public void setForbided(boolean forbided) {
        isForbided = forbided;
    }


    public EventAudioRoomDetailInfo getEventAudioRoomDetailInfo() {
        return mEventAudioRoomDetailInfo;
    }

    public void setEventAudioRoomDetailInfo(EventAudioRoomDetailInfo mEventAudioRoomDetailInfo) {
        this.mEventAudioRoomDetailInfo = mEventAudioRoomDetailInfo;
    }

    public EventRoomApplyMicrophone getEventRoomApplyMicrophone() {
        return mEventRoomApplyMicrophone;
    }

    public void setEventRoomApplyMicrophone(EventRoomApplyMicrophone mEventRoomApplyMicrophone) {
        this.mEventRoomApplyMicrophone = mEventRoomApplyMicrophone;
    }

    public ChatRoomPkResultBean getPkResultBean() {
        return mPkResultBean;
    }

    public void setPkResultBean(ChatRoomPkResultBean pkResultBean) {
        mPkResultBean = pkResultBean;
    }

    /**
     * 房主邀请换麦Event
     */
    public void onEventMainThread(EventChatRoomChangeSeat event) {
        if (!TextUtils.equals(event.getUserUuid(), AccountHelper.getAccountUuid())) {
            return;
        }
        Activity activity = ActivityUtils.getTopActivity();
        if (activity != null) {
            ChatRoomDialogUtils.showChangeSeatDialog(activity, event, (dialog, which) -> {
                if (getRoomPresenter() != null) {
                    getRoomPresenter().acceptChangeSeatInvite(mChatUuid);
                }
            });
        }
    }

    /**
     * 收到PK邀请
     */
    public void onEventMainThread(EventChatRoomPKRequestBean event) {
        final Activity activity = ActivityUtils.getTopActivity();
        if (activity instanceof FragmentActivity) {
            ChatRoomLogUploadUtil.postPKReceiveRequestLog(mChatUuid, event.getPkUuid(), event.getOtherUuid(), true);
            mPKInviteConfirmDialog = ChatRoomPKInviteConfirmDialogFragment.newInstance(mChatUuid);
            mPKInviteConfirmDialog.setMPkRequestBean(event);
            mPKInviteConfirmDialog.showDialog(((FragmentActivity) activity).getSupportFragmentManager().beginTransaction());
        }
    }

    /**
     * pk开始
     */
    public void onEventMainThread(EventChatRoomPKStartBean event) {
        if (mPKInviteConfirmDialog != null) {
            mPKInviteConfirmDialog.dismiss();
        }
        //连接跨房
        if (getVoicePresenter() != null && event != null) {
            ChatRoomPkConnectBean connectBean = new ChatRoomPkConnectBean();
            connectBean.setRoomNumber(event.getOtherChatNo());
            connectBean.setOtherUuid(event.getOtherUuid());
            connectBean.setStatus(true);
            connectBean.setMicStatus(event.getCurrentMicStatus());
            getVoicePresenter().connectOtherRoom(connectBean);
        }
    }

    /**
     * pk结束
     */
    public void onEventMainThread(EventChatRoomPkEndBean event) {
        //断开跨房
        if (getVoicePresenter() != null && mIsRoomOwner) {
            getVoicePresenter().disconnectOtherRoom();
        }
        if (event == null || StringUtils.isEmpty(event.getPkUuid())) {
            return;
        }
        getPkRepository().requestRoomPkResult(AccountHelper.getUserTicketId(), mChatUuid, event.getPkUuid())
                .execute(new GsonCallBack<ChatRoomPkResultBean>() {
                    @Override
                    public void onSuccess(boolean isCache, @Nullable ChatRoomPkResultBean obj, @NonNull IResponseInfo response) {
                        mPkResultBean = obj;
                    }

                    @Override
                    public void onFailure(boolean isServiceFailure, @NonNull IResponseInfo response) {

                    }
                });
    }


    //聊天室KTV start

    public void setKtvMeetingCheckTime(int chatRoomKtvMeetingCheckTime) {
        mChatRoomKtvMeetingCheckTime = chatRoomKtvMeetingCheckTime;
    }

    public String getKtvApplyMicType() {
        if (getVoicePresenter() != null) {
            return getVoicePresenter().getKtvApplyMicType();
        }
        return "";
    }


    public void setMySingSongInfo(EventChatRoomKtvCutSong mySingSongInfo) {
        mMySingSongInfo = mySingSongInfo;
    }

    public EventChatRoomKtvCutSong getMySingSongInfo() {
        return mMySingSongInfo;
    }

    public void setCurrentPlaySong(EventChatRoomKtvCutSong currentPlaySong) {
        mCurrentPlaySong = currentPlaySong;
    }

    public boolean isNeedShowStartSongDialog() {
        return mNeedShowStartSongDialog;
    }

    public void setNeedShowStartSongDialog(boolean needShowStartSongDialog) {
        mNeedShowStartSongDialog = needShowStartSongDialog;
    }

    /**
     * 设置耳返
     *
     * @param isEnable 是否开启
     */
    public void enableVoiceEarMonitor(boolean isEnable) {
        if (!isMuteLocalVoice && getVoicePresenter() != null && !mRoomOwnerForbidVoice) {
            getVoicePresenter().enableVoiceEarMonitor(isEnable);
        }
    }

    /**
     * 切歌了,如果是自己的歌，直接进聊天室
     */
    public void onEventMainThread(EventChatRoomKtvCutSong event) {
        if (event != null) {
            final ChatRoomMusicPlayHelper musicPlayHelper = getKtvMusicPlayHelper();
            //有人切歌了，并且之前唱歌是自己，停掉歌曲
            if (musicPlayHelper != null && mMySingSongInfo != null
                    && TextUtils.equals(mMySingSongInfo.getSongUuid(), AccountHelper.getAccountUuid())) {
                setMySingSongInfo(null);
                getKtvMusicPlayHelper().stopMusic();
                if (mMySingSongInfo != null && mRoomDetailInfo != null) {
                    mWatchTimeHelper.onReportSingTime(mMySingSongInfo, mChatRoomType,
                            mRoomDetailInfo.getSubType(), "被人切歌");
                }
            }
            //如果当前歌唱者是自己，直接进聊天室
            if (TextUtils.equals(event.getSongUuid(), AccountHelper.getAccountUuid())) {
                if (ChatRoomModel.getInstance().isKtvSongSubType()) {
                    //需要弹窗提示，到你唱歌了
                    mNeedShowStartSongDialog = "1".equals(event.getNeedConfirm());
                    ChatRoomRouter.launchChatRoomActivity(mChatUuid, mIsRoomOwner,
                            false, ChatRoomSourceConstants.NOT_TRACK);
                } else {
                    //听歌模式不需要打开聊天室页面，直接进行播放歌曲。
                    final ChatRoomKtvSongDetailBean songBean = new ChatRoomKtvSongDetailBean(event.getSongId(),
                            event.getLyricsUrl(), event.getMusicUrl(), event.getSource());
                    if (musicPlayHelper != null) {
                        if (musicPlayHelper.startMusic(songBean)) {
                            setMySingSongInfo(event);
                            enableVoiceEarMonitor(true);
                        } else {
                            int errorCode = -1;
                            if (!ChatRoomMusicLibraryHelper.isSupportSource(songBean.getSource())) {
                                errorCode = -2;
                            }
                            onMusicErrorCut(event, errorCode);
                        }
                        if (getRoomPresenter() != null) {
                            getRoomPresenter().requestConfirmSong(mChatUuid, event.getHaveSongId());
                        }
                    }
                }
                //根据演唱者判断是否需要开启补黑帧
                TRTCCloudListenerImpl.getSingleton().enableBlackStream(TextUtils.equals(event.getSongUuid(),
                        AccountHelper.getAccountUuid()));
            } else {
                //不是自己唱歌了关闭耳返
                if (getVoicePresenter() != null) {
                    getVoicePresenter().enableVoiceEarMonitor(false);
                }
            }
            ChatRoomKtvMusicDurationHelper.INSTANCE.posDuration(event, event.getScene());
        }
        mCurrentPlaySong = event;
    }

    /**
     * 暂停歌曲
     */
    public void onEventMainThread(EventChatRoomKtvStopMusic event) {
        //收到暂停/和恢复播放，
        if (TextUtils.equals(event.getPlayStatus(), "1")) {
            ChatRoomKtvMusicDurationHelper.INSTANCE.posDuration(null, ChatRoomKtvMusicDurationHelper.SCENE_PAUSE);
        } else if (TextUtils.equals(event.getPlayStatus(), "2")) {
            ChatRoomKtvMusicDurationHelper.INSTANCE.posDuration(mCurrentPlaySong, ChatRoomKtvMusicDurationHelper.SCENE_RESUME);
        }
        if (mMySingSongInfo == null) {
            //说明当前不是自己唱的歌
            return;
        }
        if (TextUtils.equals(mMySingSongInfo.getSongId(), event.getSongId())
                && TextUtils.equals(mMySingSongInfo.getHaveSongId(), event.getSongListId())) {
            if (mKtvMusicPlayHelper != null) {
                if (TextUtils.equals(event.getPlayStatus(), "1")) {
                    mKtvMusicPlayHelper.pauseMusic();
                    if (mWatchTimeHelper != null) {
                        mWatchTimeHelper.setMySingSongInfo(null, "最小化暂停");
                    }
                } else if (TextUtils.equals(event.getPlayStatus(), "2")) {
                    mKtvMusicPlayHelper.resumeMusic();
                    if (mWatchTimeHelper != null) {
                        mWatchTimeHelper.setMySingSongInfo(mMySingSongInfo, "最小化恢复");
                    }
                }
            }
        }
    }

    /**
     * 当前没有人点歌了
     */
    public void onEventMainThread(EventChatRoomKtvSongEmpty event) {
        //没人点歌了
        if (getKtvMusicPlayHelper() != null) {
            getKtvMusicPlayHelper().stopMusic();
            //前面那首是自己唱的，关闭耳返
            if (mMySingSongInfo != null
                    && TextUtils.equals(mMySingSongInfo.getSongUuid(), AccountHelper.getAccountUuid())) {
                getVoicePresenter().enableVoiceEarMonitor(false);
            }
            if (mWatchTimeHelper != null && mRoomDetailInfo != null) {
                mWatchTimeHelper.onReportSingTime(
                        mMySingSongInfo, mChatRoomType, mRoomDetailInfo.getSubType(), "当前没有歌了"
                );
            }
            setMySingSongInfo(null);
            ChatRoomKtvMusicDurationHelper.INSTANCE.posDuration(null, 1);
        }
    }

    /**
     * 霸麦，如果是最小化的，直接
     */
    public void onEventMainThread(EventChatRoomKtvBullyWheat event) {
        if (event != null && TextUtils.equals(event.getUuid(), AccountHelper.getAccountUuid())) {
            if (!event.isShowDialog() && !isBullyWheatCountDown()) {
                startBullyWheatCountDown(mChatRoomKtvMeetingCheckTime);
            }
            //如果当前是最小化，霸麦 5分钟倒计时结束，toast提示，下麦
            if (event.isShowDialog() && ChatRoomModel.getInstance().isZoomChatRoom()) {
                XToastUtils.show("由于您长时间为点歌，系统暂时将您自动下麦了");
                //霸麦提醒，下麦操作
                if (getRoomPresenter() != null && getApplyMicStatus() == ChatRoomConstants.APPLY_MICROPHOME_SUCCESS) {
                    getRoomPresenter().leaveAudioMeeting(mChatUuid, false);
                }
                //状态重制
                isMuteLocalVoice = false;
                mApplyMicStatus = ChatRoomConstants.APPLY_MICROPHOME_NORMAL;
                if (getVoicePresenter() != null) {
                    getVoicePresenter().setKtvApplyMicType("");
                    getVoicePresenter().doSwitchToBroadcaster(false);
                }
                if (getConnectedPresenter() != null) {
                    getConnectedPresenter().removeMessage();
                }
                onGoBangExitGame();
            }
        }
    }

    /**
     * 霸麦机制条件不满足了关闭倒计时
     */
    public void onEventMainThread(EventChatRoomKtvCancelBullyWheat event) {
        stopBullyWheatCountDown();
    }

    @Override
    public void onKtvMusicStart(int id, int errCode) {
        if (errCode != 0) {
            onMusicErrorCut(mMySingSongInfo, errCode);
        } else {
            if (mWatchTimeHelper != null) {
                mWatchTimeHelper.setMySingSongInfo(mMySingSongInfo, "最小化开始播放");
            }
        }
    }

    @Override
    public void onKtvMusicPlayProgress(int id, long curPtsMS, long durationMS) {
        if (mVoicePresenter != null) {
            ChatRoomKtvLyricsSycBean ktyLyricsSycBean = new ChatRoomKtvLyricsSycBean();
            ktyLyricsSycBean.setDuration(curPtsMS);
            ktyLyricsSycBean.setTotalDuration(durationMS);
            ktyLyricsSycBean.setUuid(AccountHelper.getAccountUuid());
            ktyLyricsSycBean.setSongId(mMySingSongInfo != null ? mMySingSongInfo.getSongId() : "");
            ktyLyricsSycBean.setHaveSongId(mMySingSongInfo != null ? mMySingSongInfo.getHaveSongId() : "");
            ktyLyricsSycBean.setScene("1");
            mVoicePresenter.sendLyricsInfo(ktyLyricsSycBean);
        }
    }

    @Override
    public void onKtvMusicComplete(int id, int errCode) {
        //歌曲唱完了，
        if (mMySingSongInfo != null) {
            if (mRoomDetailInfo != null && mWatchTimeHelper != null) {
                mWatchTimeHelper.onReportSingTime(mMySingSongInfo, mChatRoomType,
                        mRoomDetailInfo.getSubType(), "最小化播放完" + errCode);
            }
            requestCuttingSong(mMySingSongInfo.getHaveSongId(), ChatRoomKtvCutSongScene.CUT_END);
        }
    }

    /**
     * 伴奏文件错误，切歌，日志上报
     */
    private void onMusicErrorCut(EventChatRoomKtvCutSong bean, int errCode) {
        if (bean != null) {
            ChatRoomLogUploadUtil.postKtvMusicErrorLog(bean, errCode, mChatRoomType);
            requestCuttingSong(bean.getHaveSongId(), ChatRoomKtvCutSongScene.CUT_ERROR);

        }
    }

    /**
     * 切歌
     */
    private void requestCuttingSong(String haveSongId, String cutSongScene) {
        if (mRoomPresenter == null) {
            return;
        }
        if (ChatRoomModel.getInstance().isKtvListenSubType()) {
            mRoomPresenter.requestPlayNextSong(mChatUuid, haveSongId, cutSongScene);
        } else {
            mRoomPresenter.requestCuttingSong(mChatUuid, haveSongId, cutSongScene);
        }
    }

    /**
     * 开启霸麦倒计时，然后弹窗
     */
    public void startBullyWheatCountDown(int checkTime) {
        if (checkTime <= 0) {
            return;
        }
        mBullyWheatCountDownTimer = new CustomCountDownTimer(checkTime * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {

            }

            @Override
            public void onFinish() {
                mIsBullyWheatCountDown = false;
                EventBusUtils.post(new EventChatRoomKtvBullyWheat(true));
            }
        };
        mIsBullyWheatCountDown = true;
        mBullyWheatCountDownTimer.start();
    }

    /**
     * 停止霸麦倒计时
     */
    public void stopBullyWheatCountDown() {
        if (mBullyWheatCountDownTimer != null) {
            mBullyWheatCountDownTimer.stop();
            mBullyWheatCountDownTimer = null;
        }
        mIsBullyWheatCountDown = false;
    }

    /**
     * 当前是否在霸麦倒计时中
     *
     * @return true 是
     */
    public boolean isBullyWheatCountDown() {
        return mIsBullyWheatCountDown;
    }

    //聊天室KTV end


    //语音聊天室房间信息
    public void onEventMainThread(EventAudioRoomDetailInfo event) {
        mEventAudioRoomDetailInfo = event;
        //大屏房时，更新最小化视频播放状态
        setBigScreenVideoStatus();
        if (mChatRoomAuctionHelper != null) {
            mChatRoomAuctionHelper.onCheckUserInMic(event.getHaveMicrophoneList(),event);
        }
    }

    //有人申请上麦
    public void onEventMainThread(EventRoomApplyMicrophone event) {
        mEventRoomApplyMicrophone = event;
        requestApplyMicNum();
    }

    /**
     * 用户取消上麦
     *
     * @param event
     */
    public void onEventMainThread(EventCancelRoomApplyMicrophone event) {
        requestApplyMicNum();
    }

    private void requestApplyMicNum() {
        if (mRoomDetailInfo != null) {
            if (mIsRoomOwner || mRoomDetailInfo.isRoomManager()) {
                if (getRoomPresenter() != null) {
                    getRoomPresenter().requestApplyMicList(mChatUuid);
                }
            }
        }
    }

    /**
     * 大屏房时，更新最小化视频播放状态
     */
    private void setBigScreenVideoStatus() {
        if (mMinimizeViewHelper == null || mEventAudioRoomDetailInfo == null) {
            return;
        }
        if (!ChatRoomTypeUtils.isBigScreenRoomType(mChatRoomType)) {
            return;
        }
        //大屏房-最小化时 大屏位用户变更了，需要刷新播放状态
        final String playUserId = mMinimizeViewHelper.getPlayUuid();
        final ChatRoomOnlineInfo bigScreenInfo = ChatRoomTypeUtils.getBigScreenSeatInfo(mEventAudioRoomDetailInfo.getHaveMicrophoneList());
        if (!StringUtils.isEmpty(playUserId)) {
            if (bigScreenInfo == null) {
                //大屏位上没人了，停止播放
                mMinimizeViewHelper.stopVideo();
            } else {
                if (!TextUtils.equals(bigScreenInfo.getAccountUuid(), playUserId)) {
                    //当前大屏位上的用户变动了，关闭视频播放
                    mMinimizeViewHelper.stopVideo();
                }
            }
        }
        if (bigScreenInfo != null) {
            if (!TextUtils.equals(playUserId, AccountHelper.getAccountUuid())
                    && TextUtils.equals(bigScreenInfo.getAccountUuid(), AccountHelper.getAccountUuid())) {
                //当前用户上大屏位了，开启加载本地视频录制
                startBigScreenVideo(5000);
            }
        }
    }

    /**
     * 获取用户在聊天室内的信息
     */
    public void getAccountInChatInfo(ChatRoomMsgPresenter msgPresenter) {
        ChatRoomRepository.getAccountInChatInfo().execute(new RequestCallback<ChatRoomAccountInChatInfo>() {
            @Override
            public void onSuccess(boolean isCache, ChatRoomAccountInChatInfo obj) {
                ChatRoomHonorLevelConfigHelper.getInstance().setAccountInfo(obj);
                if (msgPresenter != null && obj != null) {
                    msgPresenter.setAvatarDressInfo(obj.getAvatarDressInfoBean());
                    msgPresenter.setTagListJson(obj.getTagListJson());
                }
                if (mMentorshipHelper != null && obj != null) {
                    mMentorshipHelper.setUserMentorshipInfoBean(obj.getMentorshipInfo());
                }
            }

            @Override
            public void onFailure(int code, String responseStatus, String responseMsg) {

            }
        });
    }

    //房主同意连麦通知
    public void onEventMainThread(EventRoomAgreeMicrophone event) {
        if (!mIsRoomOwner && AccountHelper.getAccountUuid().equals(event.getAccountUuid())) {
            applyMicrophoneBack(true);
        }
    }

    //房主拒绝申请连麦通知
    public void onEventMainThread(EventRejectApplyMicrophone event) {
        if (!mIsRoomOwner && AccountHelper.getAccountUuid().equals(event.getAccountUuid())) {
            applyMicrophoneBack(false);
        }
    }

    /**
     * 房主对上麦申请回执（通过im回调发出的event）
     *
     * @param agree true：同意上麦，false：拒绝上麦申请
     */
    public void applyMicrophoneBack(boolean agree) {
        if (agree) {
            ChatRoomCommonTracker.INSTANCE.trackChatRoomMike(mChatUuid, mEnterRoomSource, ChatRoomTypeUtils.getRoomTypeName(mChatRoomType),
                    mVoicePresenter != null ? mVoicePresenter.getListTab() : "");
            mApplyMicStatus = ChatRoomConstants.APPLY_MICROPHOME_SUCCESS;
            mHaveApplyMicSuccess = true;
            String tipMsg = "房主已同意你的上麦申请，可以和其他人语音聊天啦";
            if ((mChatRoomType == ChatRoomType.KTV_VIDEO
                    && ChatRoomConstants.CHAT_ROOM_KTV_APPLY_VIDEO.equals(getKtvApplyMicType()))) {
                tipMsg = "房主已同意你的上麦申请，可以和其他人视频聊天啦";
            } else if (ChatRoomTypeUtils.isBigScreenRoomType(mChatRoomType)) {
                tipMsg = "连麦申请已通过，5秒后开启大屏连麦";
            }
            // 游戏中，最小化不提示
            if (mChatRoomGoBangHelper != null && mChatRoomGoBangHelper.isOpenGoBang()) {
                tipMsg = "";
            }
            if (!StringUtils.isEmpty(tipMsg)) {
                ToastUtils.makeToast(AppUtils.getApp(), tipMsg);
            }
            if (getVoicePresenter() != null) {
                getVoicePresenter().doSwitchToBroadcaster(true);
            }
        } else {
            mApplyMicStatus = ChatRoomConstants.APPLY_MICROPHOME_NORMAL;
            String msg = "很抱歉，您的上麦申请未通过";
            if (ChatRoomTypeUtils.isBigScreenRoomType(mChatRoomType)) {
                msg = "房管拒绝了你的上麦申请";
            }
            ToastUtils.makeToast(AppUtils.getApp(), msg);
            if (getVoicePresenter() != null) {
                getVoicePresenter().setKtvApplyMicType("");
            }
        }
        //通知上麦成功状态
        ChatRoomCoreCenterHelper.INSTANCE.getViewModel(ChatRoomMicViewModel.class)
                .sendMicAction(new ChatRoomMicAction.UpMicResult(agree));
    }

    //用户被下麦
    public void onEventMainThread(EventRoomKickMicrophone event) {
        if (!mIsRoomOwner && AccountHelper.getAccountUuid().equals(event.getAccountUuid())) {
            if (mChatRoomType == ChatRoomType.KTV_VIDEO && getVoicePresenter() != null) {
                getVoicePresenter().enableVoiceEarMonitor(false);
            }
            isMuteLocalVoice = false;
            mApplyMicStatus = ChatRoomConstants.APPLY_MICROPHOME_NORMAL;
            if (getVoicePresenter() != null) {
                getVoicePresenter().setKtvApplyMicType("");
                getVoicePresenter().doSwitchToBroadcaster(false);
            }
            if (getConnectedPresenter() != null) {
                getConnectedPresenter().removeMessage();
            }
            if (StringUtils.isNotEmpty(event.getMsg())) {
                ToastUtils.makeToast(AppUtils.getApp(), event.getMsg());
            }
            if (mMinimizeViewHelper != null) {
                mMinimizeViewHelper.stopVideo();
            }
        }
    }

    //聊天室禁言
    public void onEventMainThread(EventMsgJin event) {
        addMsg(event);
        if (!mIsRoomOwner && AccountHelper.getAccountUuid().equals(event.getAccountUuid())) {
            isForbided = true;
            if (!mIsRoomOwner && getApplyMicStatus() == ChatRoomConstants.APPLY_MICROPHOME_SUCCESS) {
                mApplyMicStatus = ChatRoomConstants.APPLY_MICROPHOME_NORMAL;
                if (mRoomPresenter != null) {
                    mRoomPresenter.leaveAudioMeeting(mChatUuid, false);
                }
                if (mMinimizeViewHelper != null) {
                    mMinimizeViewHelper.stopVideo();
                }
                onGoBangExitGame();
            }
            ToastUtils.makeToast(AppUtils.getApp(), "你已被禁言");
        }
    }

    //聊天室解禁
    public void onEventMainThread(EventMsgAudioFree event) {
        if (!mIsRoomOwner && AccountHelper.getAccountUuid().equals(event.getAccountUuid())) {
            isForbided = false;
            ToastUtils.makeToast(AppUtils.getApp(), "你的禁言已解除");
        }
    }

    //房主关闭聊天室
    public void onEventMainThread(EventAudioClose event) {
        if (!mIsRoomOwner && getApplyMicStatus() == ChatRoomConstants.APPLY_MICROPHOME_SUCCESS) {
            if (ChatRoomTypeUtils.isAuctionRoomType(mChatRoomType) && mChatRoomAuctionHelper != null) {
                EventChatRoomAuctionMeeting auctionMeeting = new EventChatRoomAuctionMeeting();
                auctionMeeting.setMeeting(false);
                auctionMeeting.setUuid(AccountHelper.getAccountUuid());
                mChatRoomAuctionHelper.onEventMainThread(auctionMeeting);
            }
            mApplyMicStatus = ChatRoomConstants.APPLY_MICROPHOME_NORMAL;
            if (getVoicePresenter() != null) {
                getVoicePresenter().setKtvApplyMicType("");
                getVoicePresenter().exitRoom();
            }
            if (getConnectedPresenter() != null) {
                getConnectedPresenter().removeMessage();
            }
        }
        if (mIsZoom) {
            destroy(true);
        }
    }

    //房主邀请上麦：最小化可能走
    public void onEventMainThread(EventChatRoomInvite event) {
        if (event != null
                && TextUtils.equals(event.getTargetUuid(), AccountHelper.getAccountUuid())) {
            String content = "房主邀请您上麦参与连麦互动~同意后将自动上麦";
            Activity topActivity = ActivityUtils.getTopActivity();
            if (!(topActivity instanceof FragmentActivity)) {
                return;
            }
            XDialogUtils.show(topActivity, "上麦邀请", content,
                    "同意", (dialog, which) -> {
                        isRoomOwnerInvite = true;
                        ChatRoomRouter.launchChatRoomActivity(mChatUuid, mIsRoomOwner,
                                false, ChatRoomSourceConstants.NOT_TRACK);
                    },
                    "忽略", (dialog, which) -> {
                        if (!ChatRoomPreferencesUtils.isShowCloseMeetingInviteTip()) {
                            XToastUtils.show("可在【个人中心 -设置 - 消息设置】中关闭聊天室邀请推送");
                            ChatRoomPreferencesUtils.setShowCloseMeetingInviteTip();
                        }
                        dialog.dismiss();
                    });

        }
    }

    //你说我猜 start

    /**
     * 你说我猜，那边邀请加入游戏，最小化的化直接打开页面，然后走自动上麦流程
     */
    public void onEventMainThread(EventChatRoomInviteJoinGame event) {
        if (event == null || !TextUtils.equals(event.getChatUuid(), mChatUuid)) {
            return;
        }
        isRoomOwnerInvite = true;
        ChatRoomRouter.launchChatRoomActivity(mChatUuid, mIsRoomOwner,
                false, ChatRoomSourceConstants.NOT_TRACK);
    }


    public boolean isShowDuYuGameView() {
        return mIsShowDuYuGameView;
    }

    public void setShowDuYuGameView(boolean showDuYuGameView) {
        mIsShowDuYuGameView = showDuYuGameView;
    }

    /**
     * 获取当前房间游戏SN
     *
     * @return 当前房间游戏SN
     */
    public String getCurrentGameSn() {
        if (mActivity instanceof ChatRoomActivity) {
            final ChatRoomActivity activity = (ChatRoomActivity) mActivity;
            if (activity.getChatRoomView() != null) {
                return activity.getChatRoomView().getCurrentGameSn();
            }
        }
        return "";
    }

    public String getRoomRole(){
        if (mActivity instanceof ChatRoomActivity) {
            final ChatRoomActivity activity = (ChatRoomActivity) mActivity;
            if (activity.getChatRoomView() != null) {
                return activity.getChatRoomView().getManageType();
            }
        }
        return "";
    }


    public boolean isRoomOwnerInvite() {
        return isRoomOwnerInvite;
    }


    public void setRoomOwnerInvite(boolean roomOwnerInvite) {
        isRoomOwnerInvite = roomOwnerInvite;
    }

    public boolean isShowBigScreenBeautify() {
        return mIsShowBigScreenBeautify;
    }

    public void setShowBigScreenBeautify(boolean showBigScreenBeautify) {
        mIsShowBigScreenBeautify = showBigScreenBeautify;
    }

    @Override
    public void onAudioVolume(List<AudioVolumeBean> audioVolumeBeans) {
        // do nothing
    }

    @Override
    public void onError(int error, String description) {
        // do nothing
    }

    @Override
    public void onJoinChannelSuccess(boolean isAnchor) {
        if (isAnchor && mIsZoom) {
            if (!(mChatRoomGoBangHelper != null && mChatRoomGoBangHelper.isOpenGoBang())) {
                isMinAgreeMic = true;
            }
            if (!ChatRoomTypeUtils.isBigScreenRoomType(mChatRoomType)
                    || (mChatRoomGoBangHelper != null && mChatRoomGoBangHelper.isOpenGoBang())) {
                isMuteLocalVoice = true;
                final ChatRoomHelper chatRoomHelper = ChatRoomModel.getInstance().getChatRoomHelper();
                if (chatRoomHelper != null && chatRoomHelper.getVoicePresenter() != null) {
                    chatRoomHelper.getVoicePresenter().muteLocalAudioStream(true);

                    if (mRoomPresenter != null) {
                        mRoomPresenter.requestSwitchMike(mChatUuid, false);
                    }
                }
            }
        }
    }

    /**
     * 关闭聊天室
     *
     * @param event
     */
    public void onEventMainThread(EventStopOldChatRoom event) {
        destroy(true);
    }

    /**
     * 账号被登出时关闭页面
     *
     * @param event
     */
    public void onEventMainThread(EventTicketExpire event) {
        ToastUtils.makeToast(AppUtils.getApp(), "您的账号在其他设备上登录");
        destroy(mIsZoom);
    }

    public void onEventMainThread(EventXjbUserLoginOut event) {
        destroy(mIsZoom);
    }

    //静音
    public void onEventMainThread(EventAudioForbidInfo event) {
        if (!mIsRoomOwner && AccountHelper.getAccountUuid().equals(event.getAccountUuid())) {
            if (event.isForbid()) {
                if (!mRoomOwnerForbidVoice) {
                    ToastUtils.makeToast(AppUtils.getApp(), "房主关闭了您的麦克风喔");
                }
                mRoomOwnerForbidVoice = true;
                if (ChatRoomModel.getInstance().getChatRoomHelper() != null && !isMuteLocalVoice()) {
                    ChatRoomModel.getInstance().getChatRoomHelper().getVoicePresenter().muteLocalAudioStream(true);
                    if (!ChatRoomTypeUtils.isBigScreenRoomType(mChatRoomType)) {
                        ChatRoomModel.getInstance().getChatRoomHelper().getVoicePresenter().muteLocalVideoStream(true);
                        if (mMinimizeViewHelper != null) {
                            mMinimizeViewHelper.stopVideo();
                        }
                    }
                }

            } else {
                if (mRoomOwnerForbidVoice) {
                    ToastUtils.makeToast(AppUtils.getApp(), "房主恢复了您的麦克风喔");
                }
                mRoomOwnerForbidVoice = false;
                if (ChatRoomModel.getInstance().getChatRoomHelper() != null && !isMuteLocalVoice()) {
                    ChatRoomModel.getInstance().getChatRoomHelper().getVoicePresenter().muteLocalAudioStream(false);
                    if (!ChatRoomTypeUtils.isBigScreenRoomType(mChatRoomType)) {
                        ChatRoomModel.getInstance().getChatRoomHelper().getVoicePresenter().muteLocalVideoStream(false);
                        startChatRoomVideo();
                    }
                }
            }
            //如果是ktv房静音的时候不开启耳返
            if (ChatRoomType.KTV_VIDEO == mChatRoomType && getVoicePresenter() != null) {
                if (event.isForbid()) {
                    getVoicePresenter().enableVoiceEarMonitor(false);
                } else {
                    if (!isMuteLocalVoice && getVoicePresenter() != null && !mRoomOwnerForbidVoice) {
                        getVoicePresenter().enableVoiceEarMonitor(ChatRoomPreferencesUtils.getChatRoomKtvEarStatus());
                    }
                }
            }
        }
    }

    /**
     * 聊天消息
     */
    public void onEventMainThread(EventMsgChat event) {
        // 过滤自己的发言
        if (isUserSelf(event.getAccountUuid())) {
            return;
        }
        addMsg(event);
    }

    /**
     * 弹幕消息
     */
    public void onEventMainThread(EventChatroomMsgDan event) {
        addMsg(event);
    }

    /**
     * 礼物消息
     */
    public void onEventMainThread(EventChatRoomMsgGift event) {
        // 过滤自己送的礼物
        if (isUserSelf(event.getAccountUuid()) && !event.isDisplaySelf()) {
            return;
        }

        if (ChatRoomModel.getInstance().getLiveGiftMap() == null) {
            return;
        }
        ArrayMap<String, LiveGiftInfo> giftMap = ChatRoomModel.getInstance().getLiveGiftMap();
        LiveGiftInfo info = giftMap != null ? giftMap.get(event.getGiftId()) : null;
        if (ChatRoomModel.getInstance().isZoomChatRoom() && info == null) {
            giftMap = ChatRoomModel.getInstance().getLiveGiftMap();
            info = giftMap != null ? giftMap.get(event.getGiftId()) : null;
        }
        if (info == null) {
            return;
        }
        event.setGiftIcon(info.getGiftUrl());
        if (info.isAdGift()) {
            event.setIsAdGift(true); // 是高级动效
        }
        if (event.isRelationTop() && StringUtils.isNotEmpty(info.getCoupleEffectId())) {
            // 双人特效
            event.setHbEffectId(info.getCoupleEffectId());
        } else if (info.isHbEffect()) {
            event.setHbEffectId(info.getHbEffectId());
        }
        addMsg(event);
    }

    /**
     * 是否是本人uuid
     */
    public boolean isUserSelf(String uuid) {
        return TextUtils.equals(uuid, AccountHelper
                .getAccountUuid());
    }

    /**
     * 警告消息
     */
    public void onEventMainThread(EventChatRoomWarning event) {
        addMsg(event);
    }

    /**
     * 系统广播消息
     */
    public void onEventMainThread(EventMsgSystemBroadcast event) {
        addMsg(event);
    }

    /**
     * 公告消息
     */
    public void onEventMainThread(EventChatRoomNotice event) {
        addMsg(event);
        if (mActivity instanceof ChatRoomActivity) {
            final ChatRoomActivity activity = (ChatRoomActivity) mActivity;
            if (activity.getChatRoomView() != null && activity.getChatRoomView().mNoticeTips != null) {
                activity.getChatRoomView().mNoticeTips = event.getNotice();
            }
        }

        if (mRoomDetailInfo != null) {
            mRoomDetailInfo.setCustomizeRoomNotice(event.getNotice());
        }
    }

    /**
     * 普通进房提醒
     */
    public void onEventMainThread(EventMsgIntoRoom event) {
        if (AccountHelper.isUserLogined() && AccountHelper
                .getAccountUuid()
                .equals(event.getAccountUuid()) && !StringUtils.isEmpty(
                event.getCommonLevel())) {
            // 获取我的财富等级, 用于本地消息更新,如礼物消息
            AppAccountBean.get().setWealthLevel(event.getCommonLevel());
            if (StringUtils.equalsIgnoreCase(event.getAccountUuid(), mChatUuid)) {
                event.setIsHidden(false);
            }
        }
        if (mIsRoomOwner && mInviteGuideHelper != null) {
            //有人进房，重新记录时间
            mInviteGuideHelper.setInviteStartTime(System.currentTimeMillis());
        }
        addMsg(event);
    }

    /**
     * 关注主播
     */
    public void onEventMainThread(EventMsgFollowed event) {
        addMsg(event);
    }

    /**
     * 个人红包消息
     */
    public void onEventMainThread(EventMsgPersonRedPacketBean event) {
        if (!event.isChat()) {
            return;
        }
        event.setHostUuid(mChatUuid);
        addMsg(event);
    }


    public void onEventMainThread(EventRedPacketOpenNotice event) {
        if (!event.isChat()) {
            return;
        }
        event.setHostUuid(mChatUuid);
        addMsg(event);
    }


    public void onEventMainThread(EventChatRoomWebPlayStart event) {
        mEventChatRoomWebPlayStart = event;
    }

    public void onEventMainThread(EventChatRoomWebPlayClose event) {
        mEventChatRoomWebPlayStart = null;
    }

    public EventChatRoomWebPlayStart getEventChatRoomWebPlayStart() {
        return mEventChatRoomWebPlayStart;
    }

    public void setEventChatRoomWebPlayStart(EventChatRoomWebPlayStart eventChatRoomWebPlayStart) {
        mEventChatRoomWebPlayStart = eventChatRoomWebPlayStart;
    }

    public void addMsg(EventMsgBase event) {
        if (event == null) {
            return;
        }
        if (mMsgList == null) {
            mMsgList = new ArrayList<>();
        }
        if (mMsgList.size() > 10) {
            mMsgList.remove(0);
        }
        mMsgList.add(event);
    }


    /**
     * 聊天室首次收礼分成提醒
     */
    public void onEventMainThread(EventChatFirstAcceptGift event) {
        if (event != null && TextUtils.equals(event.getChatUuid(), mChatUuid)) {
            mEventChatFirstAcceptGift = event;
        }
    }


    /**
     * 踢人
     */
    public void onEventMainThread(EventRemoveUser event) {
        if (event != null
                && TextUtils.equals(event.getAccountUuid(), AccountHelper.getAccountUuid())) {
            if (StringUtils.isNotEmpty(event.getMsg())) {
                XToastUtils.show(event.getMsg());
            }
            destroy(mIsZoom);
        }
    }

    /**
     * 收到福利红包
     */
    public void onEventMainThread(EventRedPacketBonusReceive bonusReceive) {
        mRedPacketBonusReceive = bonusReceive;
    }

    public EventRedPacketBonusReceive getRedPacketBonusReceive() {
        return mRedPacketBonusReceive;
    }

    public void setRedPacketBonusReceive(EventRedPacketBonusReceive redPacketBonusReceive) {
        mRedPacketBonusReceive = redPacketBonusReceive;
    }

    /**
     * 月老祈福挂件
     */
    public void onEventMainThread(EventChatRoomMatchMakerWidgetInfoBean bonusReceive) {
        mMatchMakerWidgetInfoBean = bonusReceive;
    }

    public EventChatRoomMatchMakerWidgetInfoBean getMatchMakerWidgetInfoBean() {
        return mMatchMakerWidgetInfoBean;
    }

    public void setMatchMakerWidgetInfoBean(EventChatRoomMatchMakerWidgetInfoBean matchMakerWidgetInfoBean) {
        mMatchMakerWidgetInfoBean = matchMakerWidgetInfoBean;
    }



    /**
     * 注册监听来电状态
     */
    protected void registerPhoneCall() {
        //输出来电号码
        mPhoneStateListener = new MyPhoneStateListener(new MyPhoneStateListener.PhoneRingingCallBack() {
            @Override
            public void onPhoneRinging(String incomingNumber) {
                Loger.e(TAG, "isPhoneMuteAudio=" + isPhoneMuteAudio);
                isPhoneMuteAudio = true;
                if (getVoicePresenter() != null)
                    getVoicePresenter().setMuteAudio(true);
                if (mActivity == null) {
                    return;
                }
                mActivity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtils.makeToast(AppUtils.getApp(), "你有来电，已经帮您静音聊天室");
                    }
                });
            }

            @Override
            public void onPhoneEnd() {
                Loger.e(TAG, "isPhoneMuteAudio=" + isPhoneMuteAudio);
                if (isPhoneMuteAudio) {
                    isPhoneMuteAudio = false;
                    if (getVoicePresenter() != null)
                        getVoicePresenter().setMuteAudio(false);
                    if (mActivity == null) {
                        return;
                    }
                    mActivity.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            ToastUtils.makeToast(AppUtils.getApp(), "来电结束，已经帮您开启聊天室声音");
                        }
                    });
                }
            }
        });
        mTelephonyManager = (TelephonyManager) AppUtils.getApp().getSystemService(Context.TELEPHONY_SERVICE);
        if (mTelephonyManager != null)
            try {
                mTelephonyManager.listen(mPhoneStateListener, PhoneStateListener.LISTEN_CALL_STATE);
            } catch (Exception e) {
                e.printStackTrace();
            }
    }

    private void unRegisterPhoneCall() {
        if (mTelephonyManager != null && mPhoneStateListener != null) {
            try {
                mTelephonyManager.listen(mPhoneStateListener, PhoneStateListener.LISTEN_NONE);
            } catch (Exception e) {
                e.printStackTrace();
            }
            mTelephonyManager = null;
        }
        if (mPhoneStateListener != null) {
            mPhoneStateListener.destroy();
            mPhoneStateListener = null;
        }
    }

    public EventChatFirstAcceptGift getEventChatFirstAcceptGift() {
        return mEventChatFirstAcceptGift;
    }

    public void setEventChatFirstAcceptGift(EventChatFirstAcceptGift eventChatFirstAcceptGift) {
        mEventChatFirstAcceptGift = eventChatFirstAcceptGift;
    }


    /**
     * 五子棋开启事件
     */
    public void onEventMainThread(EventChatRoomGoBangOpen event) {
        onGoBangOpen(false);

    }

    /**
     * 五子棋关闭
     */
    public void onEventMainThread(EventChatRoomGoBangQuit event) {
        if (mChatRoomGoBangHelper != null) {
            mChatRoomGoBangHelper.onUnregister();
        }
        if (getVoicePresenter() != null) {
            getVoicePresenter().onSwitchPublicCDNType(true, true);
        }
    }

    /**
     * 开启了五子棋游戏
     */
    public ChatRoomGoBangHelper onGoBangOpen(boolean isReLoad) {
        if (mChatRoomGoBangHelper == null) {
            mChatRoomGoBangHelper = new ChatRoomGoBangHelper();
        }
        if (getVoicePresenter() != null && !isReLoad) {
            getVoicePresenter().onSwitchPublicCDNType(false, false);
        }
        mChatRoomGoBangHelper.setMGoBangStatus(ChatRoomGoBangStatus.OPEN);
        mChatRoomGoBangHelper.onRegister();
        return mChatRoomGoBangHelper;
    }

    public ChatRoomGoBangHelper getChatRoomGoBangHelper() {
        return mChatRoomGoBangHelper;
    }


    /**
     * 退出了游戏
     */
    private void onGoBangExitGame() {
        if (mChatRoomGoBangHelper != null && mChatRoomGoBangHelper.isOpenGoBang()) {
            mChatRoomGoBangHelper.onGoBangExitGame();
        }
    }

    public String getEnterRoomSource() {
        return mEnterRoomSource;
    }

    public void resetEnterRoomSource() {
        mEnterRoomSource = "";
    }

    public String getHallSource() {
        return mHallSource;
    }

    /**
     * 是否在游戏中
     */
    public boolean isOpeGoBangOrWordGuess() {
        return (mChatRoomGoBangHelper != null && mChatRoomGoBangHelper.isOpenGoBang());
    }

    public void onEventMainThread(EventChatRoomCloseWebGame event) {
        mCurrentWebGameInfo = null;
    }

    public ChatRoomCommonActivityEntranceHelper getCommonActivityEntranceHelper() {
        if (mCommonActivityEntranceHelper == null) {
            mCommonActivityEntranceHelper = new ChatRoomCommonActivityEntranceHelper();
        }
        return mCommonActivityEntranceHelper;
    }

    /**
     * @param isMinimize 是否是在最小化的时候
     */
    public void destroy(boolean isMinimize) {
        unregister();
        //最小化的时候关闭了房间，主动上报用户听歌时长
        if (mWatchTimeHelper != null && mMySingSongInfo != null && mRoomDetailInfo != null) {
            mWatchTimeHelper.onReportSingTime(mMySingSongInfo, mChatRoomType, mRoomDetailInfo.getSubType(), isMinimize ? "最小化关闭" : "关闭");
        }
        ChatRoomKtvMusicDurationHelper.INSTANCE.posDuration(null, ChatRoomKtvMusicDurationHelper.SCENE_LEAVE);
        ChatRoomKtvMusicDurationHelper.INSTANCE.setMChatUuid("");
        ChatRoomHonorLevelConfigHelper.getInstance().setMChatUuid("");
        ChatRoomModel.getInstance().setZoomChatRoomUuid("");
        ARouterManager.imOptionService().leave(mChatUuid, IMType.AUDIO_CHAT);
        ChatRoomHelper chatRoomHelper = ChatRoomModel.getInstance().getChatRoomHelper();
        if (chatRoomHelper != null) {
            chatRoomHelper.unbindService();
        }
        BaseModel.getInstance().setOpenChatRoom(false);
        BaseModel.getInstance().setRoomOwnerOpenChatRoom(false);
        //主播下播统计面板
        if (isMinimize) {
            if (mIsRoomOwner) {
                ChatRoomCenterUtil.requestRoomEndStatBoard(ChatRoomCenterConstants.ROOM_STAT, mChatUuid, 0);
            }
            //上麦过的工会会艺人
            else if (isHaveApplyMisSuccess() && mRoomDetailInfo != null && mRoomDetailInfo.isViewerIsConsortia()) {
                long applyMeetingTotalTime = getWatchTimeHelper() != null ? getWatchTimeHelper().getApplyMeetingTotalTime() : 0l;
                ChatRoomCenterUtil.requestRoomEndStatBoard(ChatRoomCenterConstants.USER_STAT, mChatUuid, applyMeetingTotalTime);
            }
        }
        if (mRoomConnectedPresenter != null) {
            mRoomConnectedPresenter.onDestroy();
            mRoomConnectedPresenter = null;
        }
        if (mRoomPresenter != null && isMinimize) {
            if (mIsRoomOwner) {
                //如果强行下线聊天室间
                mRoomPresenter.requestCloseChatRoom(true);
                //那么要注意将邀请的用户uuid给清空
                ChatRoomInviteFriendsHelper.destroy();
            } else {
                if (getApplyMicStatus() == ChatRoomConstants.APPLY_MICROPHOME_SUCCESS) {
                    mRoomPresenter.leaveAudioMeeting(mChatUuid, false);
                }
            }
            mRoomPresenter.accountLeaveRoom(mChatUuid);
        }
        if (mRoomPresenter != null) {
            mRoomPresenter.onDestroy();
        }
        if (mBGMPlayHelper != null) {
            mBGMPlayHelper.destroy();
            mBGMPlayHelper = null;
            mIsPlayingMusic = false;
        }
        if (mKtvMusicPlayHelper != null) {
            mKtvMusicPlayHelper.stopMusic();
            mKtvMusicPlayHelper.destroy();
        }
        stopBullyWheatCountDown();

        if (mVoicePresenter != null) {
            mVoicePresenter.onDestroy();
            if (!isMinimize) {
                mVoicePresenter.resetCameraDirection();
            }
            mVoicePresenter = null;
        }
        if (mMsgList != null) {
            mMsgList.clear();
        }
        if (mPKInviteConfirmDialog != null) {
            mPKInviteConfirmDialog.dismiss();
            mPKInviteConfirmDialog = null;
        }
        isRoomOwnerInvite = false;
        mMySingSongInfo = null;
        mNeedShowStartSongDialog = false;
        isJoinRoomSuccess = false;
        mRoomDetailInfo = null;
        mPkResultBean = null;
        mApplyMicStatus = ChatRoomConstants.APPLY_MICROPHOME_NORMAL;
        if (!isMinimize) {
            mActivity = null;
        }
        mMatchType = "";
        mTabId = "";
        mIsClickMinLaunch = false;
        unRegisterPhoneCall();
        mChatRoomType = ChatRoomType.CHAT_ROOM_NORMAL;
        ChatRoomModel.getInstance().setChatRoomHelper(null);
        if (mWatchTimeHelper != null) {
            mWatchTimeHelper.onDestroy();
            mWatchTimeHelper = null;
        }
        if (mChatRoomAuctionHelper != null) {
            mChatRoomAuctionHelper.onDestroy();
        }
        mChatRoomAuctionHelper = null;
        if (mChatRoomGoBangHelper != null) {
            mChatRoomGoBangHelper.onUnregister();
        }
        if (mInviteGuideHelper != null) {
            mInviteGuideHelper.cleanParam();
        }
        if (mCommonActivityEntranceHelper != null) {
            mCommonActivityEntranceHelper.destroy();
        }
        mChatRoomGoBangHelper = null;
        isShowAutoInviteMicDialog = false;
        resetEnterRoomSource();
        stopRoomRunningService();
        mRunningConnection = null;
        mCurrentWebGameInfo = null;
        mPKInRoomPkConfigBean=null;
        if (mMentorshipHelper != null) {
            mMentorshipHelper.destroy();
        }
        if (mChatRoomStarPickingTimeHelper != null) {
            mChatRoomStarPickingTimeHelper.destroy();
        }
        mChatRoomStarPickingTimeHelper = null;
        ChatRoomMusicLibraryHelper.getMInstance().cancelAllDownload();
        ChatRoomMusicLibraryHelper.getMInstance().destroy();
        if (mPropLoudspeakerHelper != null) {
            mPropLoudspeakerHelper.destroy();
        }
        if (mCommonWidgetHelper != null) {
            mCommonWidgetHelper.onDestroy();
            mCommonWidgetHelper = null;
        }
        if (mStarShineCommonHelper != null) {
            mStarShineCommonHelper.onDestroy();
            mStarShineCommonHelper = null;
        }
        ChatRoomCoreCenterHelper.INSTANCE.leaveRoom();

    }
}
