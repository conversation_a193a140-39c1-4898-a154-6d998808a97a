package com.xmhaibao.chatroom.ktv.dialog

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.databinding.ChatroomKtvListenHaveSongListDialogBinding
import com.xmhaibao.chatroom.ktv.bean.ChatRoomKtvHaveSongBean
import com.xmhaibao.chatroom.ktv.holder.ChatRoomKtvListenHaveSongViewHolder
import com.xmhaibao.chatroom.ktv.interf.OnChatRoomKtvOperation
import com.xmhaibao.chatroom.repository.ChatRoomKTVRepository
import hb.utils.SizeUtils
import hb.xstatic.list4page.XListHttpDataSource
import hb.xstatic.list4page.XListPage
import hb.xstatic.list4page.XListResultDispose
import hb.xstyle.xdialog.XLifecycleDialog

/**
 * 聊天室-KTV房听歌模式听歌列表
 *
 * <AUTHOR>
 * @date 2022/3/2
 */
class ChatRoomKtvListenHaveSongListDialog(context: Context) : XLifecycleDialog(context, cn.taqu.lib.base.R.style.Bottom_Dialog) {

    companion object {
        @JvmStatic
        fun newInstance(
            context: Context, chatUuid: String?,
            operation: OnChatRoomKtvOperation? = null,
        ): ChatRoomKtvListenHaveSongListDialog {
            return ChatRoomKtvListenHaveSongListDialog(context).apply {
                mChatUuid = chatUuid
                mOnChatRoomKtvOperation = operation
            }
        }
    }

    private lateinit var mViewBinding: ChatroomKtvListenHaveSongListDialogBinding
    private lateinit var mListPage: XListPage
    private var mChatUuid: String? = ""
    private var mOnChatRoomKtvOperation: OnChatRoomKtvOperation? = null

    var mCurrentPosition = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mViewBinding = ChatroomKtvListenHaveSongListDialogBinding.inflate(LayoutInflater.from(context))
        setContentView(mViewBinding.root)
        window?.apply {
            setLayout(ViewGroup.LayoutParams.MATCH_PARENT, SizeUtils.dp2px(500f))
            setGravity(Gravity.BOTTOM)
        }
        mViewBinding.ivAddSong.setOnClickListener {
            mOnChatRoomKtvOperation?.onChooseSong("听歌列表")
            dismiss()
        }
        initListPage()
    }

    private fun initListPage() {
        mListPage = XListPage.create(context)
            .setDataSource(initHttpListDataSource())
            .attachToView(mViewBinding.rvListRoot)
            .setEnableLoadMore(false)
            .setOnViewHolderCreatedListener {
                if (it is ChatRoomKtvListenHaveSongViewHolder) {
                    it.mOnChatRoomKtvOperation = mOnChatRoomKtvOperation
                }
            }
            .registerItem(ChatRoomKtvHaveSongBean::class.java, ChatRoomKtvListenHaveSongViewHolder::class.java)
            .setEmptyView(cn.taqu.lib.base.R.drawable.base_empty_list_ic, "暂无已点歌曲～")
    }

    private fun initHttpListDataSource(): XListHttpDataSource<MutableList<ChatRoomKtvHaveSongBean>> {
        val dataSource = object : XListHttpDataSource<MutableList<ChatRoomKtvHaveSongBean>>() {

        }
        dataSource.setResultDispose(object : XListResultDispose<MutableList<ChatRoomKtvHaveSongBean>>() {
            override fun onHanldeSuccess(isCache: Boolean, result: MutableList<ChatRoomKtvHaveSongBean>?, vararg objs: Any?) {
                mOnChatRoomKtvOperation?.onHaveSongList(result)
                result?.forEachIndexed { index, bean ->
                    if (mListPage != null) {
                        if (bean.isSinging) {
                            mCurrentPosition = index
                            mListPage.recyclerView.scrollToPosition(mCurrentPosition)
                        }
                    }
                }
                super.onHanldeSuccess(isCache, result, *objs)
            }
        })
        dataSource.setHttpParams(ChatRoomKTVRepository.chatroomKtvHaveSongList(mChatUuid).httpParams)
        return dataSource
    }

    fun setManageTypeChange() {
        mListPage.adapter?.notifyDataSetChanged()
    }

    fun refreshData() {
        mListPage.loadData()
    }
}