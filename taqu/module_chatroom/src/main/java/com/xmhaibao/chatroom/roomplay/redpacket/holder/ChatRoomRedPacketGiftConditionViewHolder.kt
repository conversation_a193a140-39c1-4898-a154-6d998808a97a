package com.xmhaibao.chatroom.roomplay.redpacket.holder

import android.view.ViewGroup
import com.xmhaibao.chatroom.R
import com.xmhaibao.chatroom.databinding.ChatroomRedPacketGiftConditionItemBinding
import com.xmhaibao.chatroom.roomplay.redpacket.bean.ChatRoomRedPacketGiftInfoBean
import hb.xadapter.XBaseViewHolder

/**
 * 聊天室礼物红包领取条件ViewHolder
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
class ChatRoomRedPacketGiftConditionViewHolder(parent: ViewGroup) :
    XBaseViewHolder<ChatRoomRedPacketGiftInfoBean.Condition>(parent,
        R.layout.chatroom_red_packet_gift_condition_item) {
    private val binding = ChatroomRedPacketGiftConditionItemBinding.bind(itemView)

    override fun onBindView(bean: ChatRoomRedPacketGiftInfoBean.Condition?) {
        bean?.apply {
            binding.apply {
                tvTitle.text = title
                tvSubTitle.text = desc
            }
        }
    }

}