<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="8dp">

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivRewardIcon"
        android:layout_width="70dp"
        android:layout_height="70dp"
        app:actualImageScaleType="fitCenter"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:placeholderImage="@color/transparent" />

    <TextView
        android:id="@+id/tvRewardName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/TH_Gray900"
        android:textSize="@dimen/t6"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivRewardIcon"
        tools:text="四叶草" />

    <TextView
        android:id="@+id/tvRewardTips"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:gravity="center"
        android:textColor="@color/black_alpha_55"
        android:textSize="@dimen/t6_2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvRewardName"
        tools:text="有效期：3天" />

</androidx.constraintlayout.widget.ConstraintLayout>