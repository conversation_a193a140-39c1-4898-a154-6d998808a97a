<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="40dp"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/transparent"
    tools:background="@color/black_alpha_50"
    android:minWidth="50dp">

    <ImageView
        android:id="@+id/ivDivider"
        android:layout_width="0.5dp"
        android:layout_height="8dp"
        android:background="@color/white_alpha_40"
        android:layout_gravity="center_vertical"
        android:visibility="invisible" />

    <TextView
        android:id="@+id/tvGiftName"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:gravity="center"
        android:orientation="vertical"
        android:textColor="@color/chatroom_gift_tab_text_color"
        android:textSize="@dimen/t6"
        tools:text="死党" />

    <hb.drawable.shape.view.HbView
        android:id="@+id/vRed"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_gravity="end"
        android:layout_marginTop="9dp"
        android:layout_marginEnd="9dp"
        app:solid="@color/c4"
        app:shape="oval"
        android:layout_width="6dp"
        android:layout_height="6dp"/>

</FrameLayout>
