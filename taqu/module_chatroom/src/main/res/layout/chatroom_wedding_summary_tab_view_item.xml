<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:background="@color/black_alpha_55"
    android:orientation="vertical"
    android:gravity="center"
    tools:ignore="MissingDefaultResource">

    <TextView
        style="@style/Text.H3.White"
        android:id="@+id/title"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        tools:text="推荐" />

    <hb.drawable.shape.view.HbView
        app:corner="10dp"
        app:solid="#FFCA44"
        android:visibility="gone"
        tools:visibility="visible"
        android:id="@+id/viewUnderline"
        android:background="@drawable/bg_white_corner_10dp"
        android:layout_width="36dp"
        android:layout_height="2dp"/>


</LinearLayout>


