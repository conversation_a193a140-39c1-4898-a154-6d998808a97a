<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <com.xmhaibao.chatroom.roomplay.auction.widget.ChatRoomAuctionPrivilegeItemView
        android:id="@+id/viewPrivilege1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintEnd_toStartOf="@id/viewPrivilege2"
        app:layout_constraintStart_toStartOf="parent" />

    <com.xmhaibao.chatroom.roomplay.auction.widget.ChatRoomAuctionPrivilegeItemView
        android:id="@+id/viewPrivilege2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_marginStart="24dp"
        app:layout_constraintEnd_toStartOf="@id/viewPrivilege3"
        app:layout_constraintStart_toEndOf="@id/viewPrivilege1" />

    <com.xmhaibao.chatroom.roomplay.auction.widget.ChatRoomAuctionPrivilegeItemView
        android:id="@+id/viewPrivilege3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_marginStart="24dp"
        app:layout_constraintEnd_toStartOf="@id/viewPrivilege4"
        app:layout_constraintStart_toEndOf="@id/viewPrivilege2" />

    <com.xmhaibao.chatroom.roomplay.auction.widget.ChatRoomAuctionPrivilegeItemView
        android:id="@+id/viewPrivilege4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_marginStart="24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/viewPrivilege3" />

</merge>