<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <hb.drawable.shape.view.HbView
        android:id="@+id/viewUnderline"
        android:layout_width="wrap_content"
        android:layout_height="26dp"
        android:gravity="center"
        android:layout_centerVertical="true"
        android:layout_alignStart="@+id/title"
        android:layout_alignEnd="@+id/title"
        app:corner="20dp"
        app:solid="@color/TH_Gray150" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:textSize="@dimen/t6"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        tools:text="推荐" />

</RelativeLayout>
