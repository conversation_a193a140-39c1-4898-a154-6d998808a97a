<?xml version="1.0" encoding="utf-8"?>
<com.xmhaibao.chatroom.api.widget.ChatRoomHoleLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_alpha_60">

    <!--============ 第一个引导 start ↓ ===========-->
    <!--这里的大小和位置在代码中会动态调整-->
    <hb.drawable.shape.view.HbView
        android:id="@+id/placeholder1"
        android:layout_width="77dp"
        android:layout_height="40dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="600dp"
        app:corner="80dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:stroke_color="@color/white"
        app:stroke_width="1dp" />

    <hb.drawable.shape.view.HbView
        android:id="@+id/arrow1"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:layout_marginBottom="10dp"
        android:rotation="45"
        app:layout_constraintBottom_toTopOf="@id/placeholder1"
        app:layout_constraintEnd_toEndOf="@id/placeholder1"
        app:layout_constraintStart_toStartOf="@id/placeholder1"
        app:solid="#FFE4E3" />

    <hb.drawable.shape.view.HbConstraintLayout
        android:id="@+id/clGuide1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:paddingTop="12dp"
        android:paddingBottom="11dp"
        app:corner="12dp"
        app:gradient_color_end="#FFE0E4"
        app:gradient_color_start="#FFFADB"
        app:gradient_linear_orientation="right_left"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/arrow1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/placeholder1">

        <TextView
            android:id="@+id/tvGuideText1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="打个招呼，交个朋友！"
            android:textColor="@color/TH_Gray990_Normal"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <hb.drawable.shape.view.HbTextView
            android:id="@+id/tvGuideQuestion1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="28dp"
            android:layout_marginTop="4dp"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:gravity="start"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constrainedWidth="true"
            app:corner="14dp"
            app:layout_constraintEnd_toStartOf="@id/btnSeyHello"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvGuideText1"
            app:solid="@color/black_alpha_10"
            tools:text="怎么上麦呀？" />

        <hb.drawable.shape.view.HbTextView
            android:id="@+id/btnSeyHello"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_marginStart="4dp"
            android:gravity="center"
            android:paddingLeft="14dp"
            android:paddingRight="14dp"
            android:text="打招呼"
            android:textColor="@color/black"
            android:textSize="11sp"
            android:textStyle="bold"
            app:corner="14dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvGuideQuestion1"
            app:layout_constraintTop_toTopOf="@id/tvGuideQuestion1"
            app:layout_constraintBottom_toBottomOf="@id/tvGuideQuestion1"
            app:solid="@color/TH_Yellow600_Normal" />

    </hb.drawable.shape.view.HbConstraintLayout>

    <ImageView
        android:id="@+id/ivPic1"
        android:layout_width="100dp"
        android:layout_height="48dp"
        android:layout_marginEnd="7dp"
        android:background="@drawable/message_new_girl_s_guide_banana_ic"
        app:layout_constraintBottom_toTopOf="@id/clGuide1"
        app:layout_constraintEnd_toEndOf="@id/clGuide1" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupGuide1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="placeholder1,arrow1,clGuide1,ivPic1"
        tools:visibility="visible" />

    <!--============ 第一个引导 end ↑ ===========-->


    <!--============ 第二个引导 start ↓ ===========-->
    <!--这里的大小和位置在代码中会动态调整-->
    <hb.drawable.shape.view.HbView
        android:id="@+id/placeholder2"
        android:layout_width="152dp"
        android:layout_height="33dp"
        android:layout_marginStart="200dp"
        android:layout_marginTop="42dp"
        app:corner="80dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:stroke_color="@color/white"
        app:stroke_width="1dp" />

    <Space
        android:id="@+id/placeholder2Space"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        app:layout_constraintEnd_toStartOf="@id/placeholder2"
        app:layout_constraintTop_toTopOf="@id/placeholder2" />

    <ImageView
        android:id="@+id/ivPic2"
        android:layout_width="100dp"
        android:layout_height="48dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/message_new_girl_s_guide_banana_ic"
        app:layout_constraintBottom_toTopOf="@id/clGuide2"
        app:layout_constraintEnd_toEndOf="@id/clGuide2" />

    <Space
        android:id="@+id/space2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        app:layout_constraintStart_toStartOf="@id/clGuide2"
        app:layout_constraintTop_toTopOf="@id/clGuide2" />

    <hb.drawable.shape.view.HbView
        android:id="@+id/arrow2"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:layout_marginStart="31dp"
        android:rotation="45"
        app:layout_constraintBottom_toBottomOf="@id/space2"
        app:layout_constraintStart_toStartOf="@id/clGuide2"
        app:solid="#FFE4E3" />

    <hb.drawable.shape.view.HbConstraintLayout
        android:id="@+id/clGuide2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="56dp"
        android:layout_marginBottom="5dp"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:paddingTop="12dp"
        android:paddingBottom="11dp"
        app:corner="12dp"
        app:gradient_color_end="#FFE0E4"
        app:gradient_color_start="#FFFADB"
        app:gradient_linear_orientation="right_left"
        app:layout_constraintStart_toStartOf="@id/placeholder2Space"
        app:layout_constraintTop_toBottomOf="@id/placeholder2">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="打个招呼，交个朋友！"
            android:textColor="@color/TH_Gray990_Normal"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </hb.drawable.shape.view.HbConstraintLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupGuide2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="placeholder2,ivPic2,space2,arrow2,clGuide2"
        tools:visibility="visible" />
    <!--============ 第二个引导 end ↑ ===========-->


    <!--============ 第三个引导 start ↓ ===========-->
    <!--这里的大小和位置在代码中会动态调整-->
    <hb.drawable.shape.view.HbView
        android:id="@+id/placeholder3"
        android:layout_width="118dp"
        android:layout_height="44dp"
        android:layout_marginStart="250dp"
        android:layout_marginTop="350dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivPic3"
        android:layout_width="100dp"
        android:layout_height="48dp"
        android:layout_marginStart="12dp"
        android:rotationY="180"
        android:background="@drawable/message_new_girl_s_guide_banana_ic"
        app:layout_constraintBottom_toTopOf="@id/clGuide3"
        app:layout_constraintStart_toStartOf="@id/clGuide3" />

    <Space
        android:id="@+id/space3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="25dp"
        app:layout_constraintEnd_toEndOf="@id/clGuide3"
        app:layout_constraintTop_toBottomOf="@id/clGuide3" />

    <hb.drawable.shape.view.HbView
        android:id="@+id/arrow3"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:rotation="45"
        app:layout_constraintBottom_toBottomOf="@id/space3"
        app:layout_constraintEnd_toEndOf="@id/space3"
        app:layout_constraintStart_toStartOf="@id/space3"
        app:solid="#FFF0DE" />

    <hb.drawable.shape.view.HbConstraintLayout
        android:id="@+id/clGuide3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="14dp"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:paddingTop="12dp"
        android:paddingBottom="11dp"
        app:corner="12dp"
        app:gradient_color_end="#FFE0E4"
        app:gradient_color_start="#FFFADB"
        app:gradient_linear_orientation="right_left"
        app:layout_constraintBottom_toTopOf="@id/placeholder3"
        app:layout_constraintEnd_toEndOf="@id/placeholder3">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="免费上麦，上麦互动 ！"
            android:textColor="@color/TH_Gray990_Normal"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </hb.drawable.shape.view.HbConstraintLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupGuide3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="placeholder3,ivPic3,space3,arrow3,clGuide3"
        tools:visibility="visible" />
    <!--============ 第三个引导 end ↑ ===========-->

</com.xmhaibao.chatroom.api.widget.ChatRoomHoleLayout>