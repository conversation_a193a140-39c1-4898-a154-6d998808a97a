<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="78dp">

    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/ivGuestAvatar"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:layout_marginStart="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvGuestName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:includeFontPadding="false"
        android:maxWidth="100dp"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="@dimen/t5_2"
        app:layout_constraintBottom_toTopOf="@+id/ivGuestRelationIcon"
        app:layout_constraintStart_toEndOf="@+id/ivGuestAvatar"
        app:layout_constraintTop_toTopOf="@+id/ivGuestAvatar"
        tools:text="用户昵称" />

    <com.xmhaibao.chatroom.view.ChatRoomUserSexAndAgeView
        android:id="@+id/llGuestSex"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        app:boy_color="@color/white_alpha_10"
        app:boy_image="@drawable/chatroom_sex_male_bule_ic"
        app:corner_radius="2dp"
        app:stroke_width="0.5dp"
        app:sex_width_height="16dp"
        app:stroke_color="#555555"
        app:view_shape="rectangle"
        app:girl_color="@color/white_alpha_10"
        app:girl_image="@drawable/chatroom_sex_female_red_ic"
        app:layout_constraintBottom_toBottomOf="@+id/tvGuestName"
        app:layout_constraintStart_toEndOf="@+id/tvGuestName"
        app:layout_constraintTop_toTopOf="@+id/tvGuestName" />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivGuestRelationIcon"
        android:layout_width="28dp"
        android:layout_height="15dp"
        app:actualImageScaleType="centerInside"
        app:layout_constraintBottom_toBottomOf="@+id/ivGuestAvatar"
        app:layout_constraintStart_toStartOf="@+id/tvGuestName"
        app:layout_constraintTop_toBottomOf="@+id/tvGuestName"
        app:placeholderImage="@color/transparent"
        tools:background="@color/c1" />


    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvGuestAuctionValue"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:layout_marginStart="6dp"
        android:gravity="center"
        android:paddingStart="18dp"
        android:paddingEnd="6dp"
        android:textColor="@color/white_alpha_60"
        android:textSize="12sp"
        app:gradient_linear_orientation="left_right"
        app:gradient_color_end="#33FEE400"
        app:gradient_color_start="@color/translucent"
        app:corner="2dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivGuestRelationIcon"
        app:layout_constraintStart_toEndOf="@+id/ivGuestRelationIcon"
        app:layout_constraintTop_toTopOf="@+id/ivGuestRelationIcon"
        tools:text="23123亲密值" />
    <ImageView
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="6dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivGuestRelationIcon"
        app:layout_constraintStart_toEndOf="@+id/ivGuestRelationIcon"
        app:layout_constraintTop_toTopOf="@+id/ivGuestRelationIcon"
        android:src="@drawable/chatroom_auction_ic" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvAgree"
        style="@style/Text"
        android:layout_width="56dp"
        android:layout_height="32dp"
        android:layout_marginEnd="16dp"
        android:gravity="center"
        android:textSize="12sp"
        android:text="同意"
        android:textColor="@color/white"
        app:corner="6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:solid="#FF2056" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvRefuse"
        style="@style/Text"
        android:layout_width="56dp"
        android:layout_height="32dp"
        android:textSize="12sp"
        android:layout_marginEnd="11dp"
        android:gravity="center"
        android:text="拒绝"
        android:textColor="@color/white_alpha_60"
        app:corner="6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvAgree"
        app:layout_constraintTop_toTopOf="parent"
        app:solid="@color/white_alpha_10" />
</androidx.constraintlayout.widget.ConstraintLayout>