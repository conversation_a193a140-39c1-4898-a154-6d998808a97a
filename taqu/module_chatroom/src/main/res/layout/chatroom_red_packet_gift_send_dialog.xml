<?xml version="1.0" encoding="utf-8"?>
<hb.drawable.shape.view.HbConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    app:corner_top_left="16dp"
    app:corner_top_right="16dp"
    app:solid="#161722"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tvTitle"
        style="@style/Text.H2.White"
        android:text="礼物红包"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tvRecord"
        style="@style/Text.B3.G600"
        android:text="记录"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="15dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tvRule"
        style="@style/Text.B3.G600"
        android:text="规则"
        android:layout_marginEnd="24dp"
        android:layout_marginTop="15dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvRecord"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tvConditionTitle"
        style="@style/Text.B2.G100"
        android:text="领取条件"
        android:layout_marginTop="24dp"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        app:layout_constraintStart_toStartOf="@id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <hb.drawable.shape.view.HbView
        android:id="@+id/viewCondition"
        app:corner="6dp"
        app:solid="#22232D"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginTop="12dp"
        app:layout_constraintTop_toBottomOf="@id/tvConditionTitle"
        android:layout_width="match_parent"
        android:layout_height="40dp" />

    <TextView
        android:id="@+id/tvConditionName"
        style="@style/Text.B2.White"
        android:text="选择领取条件"
        android:layout_marginStart="12dp"
        app:layout_constraintTop_toTopOf="@id/viewCondition"
        app:layout_constraintBottom_toBottomOf="@id/viewCondition"
        app:layout_constraintStart_toStartOf="@id/viewCondition"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/ivArrow"
        android:src="@drawable/chatroom_red_packet_gift_down_ic"
        android:layout_marginEnd="12dp"
        app:layout_constraintTop_toTopOf="@id/viewCondition"
        app:layout_constraintBottom_toBottomOf="@id/viewCondition"
        app:layout_constraintEnd_toEndOf="@id/viewCondition"
        android:layout_width="20dp"
        android:layout_height="20dp" />

    <hb.drawable.shape.view.HbEditText
        android:id="@+id/etToken"
        android:visibility="gone"
        tools:visibility="visible"
        style="@style/Text.B2"
        android:lineSpacingExtra="0dp"
        android:enabled="false"
        android:textColor="@color/white_alpha_55"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        app:layout_constraintTop_toBottomOf="@id/viewCondition"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvChangeToken"
        android:layout_marginTop="12dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="12dp"
        app:corner="6dp"
        app:solid="#22232D"
        app:stroke_color="@color/white_alpha_10"
        app:stroke_width="1dp"
        android:layout_width="0dp"
        android:layout_height="44dp" />

    <TextView
        android:id="@+id/tvChangeToken"
        style="@style/Text.N1.G600"
        android:visibility="gone"
        tools:visibility="visible"
        android:gravity="center"
        android:text="换一换"
        android:drawablePadding="2dp"
        android:drawableEnd="@drawable/chatroom_red_packet_gift_refresh_ic"
        android:layout_marginEnd="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/etToken"
        app:layout_constraintBottom_toBottomOf="@id/etToken"
        android:layout_width="wrap_content"
        android:layout_height="30dp" />

    <TextView
        android:id="@+id/tvRedTitle"
        style="@style/Text.B2.G100"
        android:text="红包礼物"
        android:layout_marginTop="24dp"
        app:layout_constraintTop_toBottomOf="@id/etToken"
        app:layout_constraintStart_toStartOf="@id/tvConditionTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvPacketList"
        android:overScrollMode="never"
        app:layout_constraintTop_toBottomOf="@id/tvRedTitle"
        android:layout_marginTop="8dp"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_width="match_parent"
        android:layout_height="204dp" />

    <hb.drawable.shape.view.HbView
        app:gradient_color_end="#161722"
        app:gradient_color_start="#00161722"
        app:gradient_linear_orientation="top_bottom"
        app:layout_constraintBottom_toBottomOf="@id/rvPacketList"
        app:layout_constraintStart_toStartOf="@id/rvPacketList"
        app:layout_constraintEnd_toEndOf="@id/rvPacketList"
        android:layout_width="0dp"
        android:layout_height="30dp" />

    <hb.drawable.shape.view.HbRecycleView
        android:id="@+id/rvCondition"
        app:layout_constraintTop_toBottomOf="@id/viewCondition"
        android:visibility="gone"
        tools:visibility="gone"
        app:solid="#22232D"
        app:corner_bottom_left="6dp"
        app:corner_bottom_right="6dp"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        app:layout_constraintHeight_max="200dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/tvSend"
        android:layout_marginBottom="30dp"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:paddingTop="14dp"
        android:paddingBottom="14dp"
        style="@style/Text.H3.White"
        app:solid="#FF2076"
        app:corner="10dp"
        android:gravity="center"
        android:text="发红包"
        app:layout_constraintTop_toBottomOf="@id/rvPacketList"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <hb.xloadingbar.XLoadingBar
        android:id="@+id/loadingBar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</hb.drawable.shape.view.HbConstraintLayout>