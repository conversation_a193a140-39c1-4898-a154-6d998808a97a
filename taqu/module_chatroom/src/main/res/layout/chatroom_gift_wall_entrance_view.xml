<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout"
    tools:background="@color/black_alpha_80"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvGiftWallTitleEng"
        android:textStyle="bold|italic"
        android:text="GIFT WALL "
        android:textSize="7sp"
        android:textColor="@color/white_alpha_05"
        android:translationY="-7dp"
        app:layout_constraintStart_toStartOf="@id/tvGiftWallTitle"
        app:layout_constraintTop_toBottomOf="@id/tvGiftWallTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tvGiftWallTitle"
        android:text="礼物墙"
        android:textSize="11sp"
        android:textColor="@color/white"
        android:layout_marginStart="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />


    <ImageView
        android:id="@+id/ivGiftWallTotalIc"
        android:layout_marginStart="5dp"
        app:layout_constraintStart_toEndOf="@+id/tvGiftWallTitleEng"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/chatroom_gift_wall_total_ic"/>

    <TextView
        android:id="@+id/tvHaveGet"
        android:text="已点亮"
        android:layout_marginStart="4dp"
        android:textSize="@dimen/TH_FONT_N1"
        android:textColor="@color/white_alpha_55"
        app:layout_constraintStart_toEndOf="@+id/ivGiftWallTotalIc"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />



    <TextView
        android:id="@+id/tvHaveGetNum"
        android:text="1000/10000"
        android:layout_marginStart="4dp"
        android:textSize="@dimen/TH_FONT_N1"
        android:textColor="@color/white"
        app:layout_constraintStart_toEndOf="@+id/tvHaveGet"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />



    <ImageView
        android:id="@+id/ivGiftWallWeekTotalIc"
        android:layout_marginStart="6dp"
        app:layout_constraintStart_toEndOf="@+id/tvHaveGetNum"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/chatroom_gift_wall_week_total_ic"/>

    <TextView
        android:id="@+id/tvHaveWeekGet"
        android:text="已点亮"
        android:layout_marginStart="4dp"
        android:textSize="@dimen/TH_FONT_N1"
        android:textColor="@color/white_alpha_55"
        app:layout_constraintStart_toEndOf="@+id/ivGiftWallWeekTotalIc"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />



    <TextView
        android:gravity="start"
        android:layout_marginEnd="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:id="@+id/tvHaveWeekGetNum"
        android:text="1000/10000"
        android:layout_marginStart="4dp"
        android:textSize="@dimen/TH_FONT_N1"
        android:textColor="@color/white"
        app:layout_constraintStart_toEndOf="@+id/tvHaveWeekGet"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />


</merge>