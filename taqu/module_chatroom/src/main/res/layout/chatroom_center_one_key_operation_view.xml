<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="270dp"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <com.ushengsheng.multinestlistview.PagerSlidingTabStripWithTextSize
        android:id="@+id/oneKeyOperationTabLayout"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:layout_marginStart="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvSelectAllOrCancel"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/oneKeyOperationTabLayout"
        app:layout_constraintBottom_toBottomOf="@+id/oneKeyOperationTabLayout"
        android:id="@+id/tvSelectAllOrCancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:visibility="visible"
        android:visibility="gone"
        tools:text="一键全选"
        android:textColor="@color/TH_Blue600"
        android:textSize="@dimen/t6_2" />

    <TextView
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/oneKeyOperationTabLayout"
        app:layout_constraintBottom_toBottomOf="@+id/oneKeyOperationTabLayout"
        android:id="@+id/tvSelectAllToMore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:visibility="visible"
        android:visibility="gone"
        android:text="查看其他可私信的用户"
        android:textColor="@color/TH_Blue600"
        android:textSize="@dimen/t6_2" />

    <cn.taqu.lib.base.widget.NoScrollViewPager
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/oneKeyOperationTabLayout"
        android:id="@+id/oneKeyOperationViewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp" />


</merge>