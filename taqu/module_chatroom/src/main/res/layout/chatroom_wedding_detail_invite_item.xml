<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="54dp"
    android:layout_height="wrap_content"
    android:layout_marginTop="16dp"
    android:layout_gravity="center"
    tools:background="@color/black_alpha_90">


    <hb.drawable.shape.view.HbImageView
        android:id="@+id/ivInvite"
        android:layout_width="44dp"
        android:layout_height="44dp"
        app:layout_constraintRight_toRightOf="parent"
        android:src="@drawable/chatroom_wedding_detail_invite"
        app:corner="30dp"
        app:layout_constraintTop_toTopOf="parent"
        app:solid="#80519dFE" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="邀请"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="@id/ivInvite"
        app:layout_constraintRight_toRightOf="@id/ivInvite"
        app:layout_constraintTop_toBottomOf="@id/ivInvite" />

</androidx.constraintlayout.widget.ConstraintLayout>