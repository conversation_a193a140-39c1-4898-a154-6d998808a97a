<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    

    <com.xmhaibao.chatroom.rank.view.ChatRoomRanksTopThirdStyleTwoView
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/topThirdView"
        android:layout_width="match_parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_height="wrap_content"/>


    <hb.drawable.shape.view.HbView
        android:id="@+id/hbBgView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        app:corner_bottom_left="0dp"
        app:corner_bottom_right="0dp"
        app:corner_top_right="16dp"
        app:corner_top_left="16dp"
        android:layout_marginBottom="60dp"
        app:layout_goneMarginBottom="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/clBottomRootInfo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/topThirdView"
        app:solid="@color/white" />


    <TextView
        android:layout_marginStart="12dp"
        android:drawablePadding="4dp"
        android:id="@+id/tvUpdateTimeTip"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:drawableStart="@drawable/chatroom_ranks_update_time_ic"
        android:gravity="center_vertical"
        android:textColor="@color/TH_Gray600_Normal"
        android:textSize="@dimen/TH_FONT_B4"
        app:layout_constraintStart_toStartOf="@+id/hbBgView"
        app:layout_constraintTop_toTopOf="@+id/hbBgView"
        tools:text="每60分钟更新1次" />

    <TextView
        android:layout_marginEnd="12dp"
        android:id="@+id/tvRankRelation"
        android:layout_width="wrap_content"
        android:drawableEnd="@drawable/chatroom_ranks_right_arrow_ic"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:textColor="@color/TH_Gray990_Normal"
        android:textSize="@dimen/TH_FONT_B4"
        app:layout_constraintEnd_toEndOf="@+id/hbBgView"
        app:layout_constraintTop_toTopOf="@+id/hbBgView"
        android:text="榜单说明" />


    <FrameLayout
        android:id="@+id/flRankListRoot"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="44dp"
        app:layout_constraintBottom_toBottomOf="@+id/hbBgView"
        app:layout_constraintEnd_toEndOf="@+id/hbBgView"
        app:layout_constraintStart_toStartOf="@+id/hbBgView"
        app:layout_constraintTop_toBottomOf="@+id/topThirdView" />


    <hb.ximage.fresco.BaseDraweeView
        app:layout_constraintBottom_toBottomOf="parent"
        app:placeholderImage="@color/transparent"
        android:id="@+id/bdvBottomBg"
        app:actualImageScaleType="fitXY"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="82dp"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_marginEnd="12dp"
        android:layout_marginStart="12dp"
        android:id="@+id/clBottomRootInfo"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="72dp"
        tools:background="#FF95BB">

        <TextView
            android:id="@+id/tvMyRankIndexStatus"
            android:layout_width="40dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/TH_Gray990_Normal"
            android:textSize="@dimen/TH_FONT_B1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="10" />


        <hb.ximage.fresco.AvatarDraweeView
            android:visibility="gone"
            android:id="@+id/bdvRoomRightCorner"
            android:layout_width="40dp"
            android:layout_marginStart="30dp"
            android:layout_height="40dp"
            app:roundingBorderColor="@color/w0"
            app:roundingBorderWidth="2dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/bdvRoomLeftCorner"
            app:layout_constraintTop_toTopOf="parent" />

        <hb.ximage.fresco.AvatarDraweeView
            android:id="@+id/bdvRoomLeftCorner"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:roundingBorderColor="@color/w0"
            app:roundingBorderWidth="2dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tvMyRankIndexStatus"
            app:layout_constraintTop_toTopOf="parent" />

        <hb.ximage.fresco.BaseDraweeView
            android:layout_width="30dp"
            android:layout_height="21dp"
            android:id="@+id/bdvTopThirdImage"
            app:actualImageScaleType="centerInside"
            app:layout_constraintBottom_toBottomOf="@+id/bdvRoomLeftCorner"
            app:layout_constraintEnd_toEndOf="@+id/bdvRoomRightCorner"
            app:layout_constraintStart_toStartOf="@+id/bdvRoomLeftCorner"
            app:placeholderImage="@color/transparent" />


        <LinearLayout
            android:gravity="center_vertical"
            app:layout_goneMarginStart="52dp"
            android:layout_marginStart="12dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/bdvRoomRightCorner"
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                android:drawablePadding="2dp"
                android:id="@+id/tvLeftName"
                android:layout_width="wrap_content"
                android:singleLine="true"
                android:layout_height="wrap_content"
                android:textColor="@color/TH_Gray990_Normal"
                android:textSize="@dimen/TH_FONT_B4"
                tools:text="房间名放假吗ing" />

            <TextView
                android:drawablePadding="2dp"
                android:id="@+id/tvRightName"
                android:layout_width="wrap_content"
                android:singleLine="true"
                android:layout_height="wrap_content"
                android:textColor="@color/TH_Gray990_Normal"
                android:textSize="@dimen/TH_FONT_B4"
                tools:text="房间名放假吗ing" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:gravity="end"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvRankUnit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/TH_Gray600_Normal"
                android:textSize="@dimen/TH_FONT_N1"
                tools:text="魅力值" />

            <TextView
                android:id="@+id/tvRankValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:gravity="center|end"
                android:textColor="@color/TH_Gray600_Normal"
                android:textSize="@dimen/TH_FONT_N1"
                tools:text="123w" />

        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>




</androidx.constraintlayout.widget.ConstraintLayout>